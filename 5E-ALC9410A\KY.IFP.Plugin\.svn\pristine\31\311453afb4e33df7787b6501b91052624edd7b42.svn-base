﻿; define([
    "ELEMENT",
    "util",
    "platform/vue",
    "iofp/api",
    "moment",
    "css!/kyadmin/css/layout.css"
], function (ELEMENT, util, pVue, API, moment) {

    const components = pVue.createAsyncComponents({
        UserCardDetail: "./userCardMx.js",
        IfpUserDetail: "./detail.js",//对应标签ifp-user-detail
        IfpUserRole: "./userRole.js"//对应标签ifp-user-role
    });

    return {
        el: '#app',
        components: components,
        data() {
            return {
                tableData: [],
                query: { UsiName_like: null, UsiNumber_like: null },
                //分页信息
                paging: {
                    size: 20,
                    page: 1,
                    records: 20
                },
                editor: {
                    header: "用户",
                    action: "查看",
                    dialog: false,
                    dialog2: false,
                    source: null
                },
                cardDetail: {
                    title: "绑定ID卡",
                    show: false,
                    userid: null,
                },
                selectedRow: null
            }
        },
        methods: {
            sexFormatter(row, column, cellValue, index) {
                if (cellValue == null) {
                    return null;
                }
                return cellValue == 0 ? "男" : "女";
            },
            //显示条目变化
            sizeChange(v) {
                this.paging.size = v;
                this.OnQuery();
            },
            //选择页面
            pageChange(v) {
                this.paging.page = v;
                this.OnQuery();
            },
            onSelect: function (row, event, column) {
                this.selectedRow = row;
            },
            onAdd() {
                this.editor.action = "新增";
                this.editor.source = null;
                this.editor.dialog = true;
            },
            checkSelect() {
                if (this.selectedRow == null) {
                    this.$message.warning("请选择一行数据");
                    return false;
                }
                return true;
            },
            onUpdate() {
                var _this = this;
                if (_this.checkSelect() == false) {
                    return;
                }
                if (_this.selectedRow.Gid == "admin") {
                    _this.$message.warning("该用户为超级管理员，不允许修改");
                    return;
                }
                this.editor.action = "修改";
                this.editor.source = this.selectedRow;
                this.editor.dialog = true;
            },
            onDelete() {
                var _this = this;
                if (_this.checkSelect() == false) {
                    return;
                }
                if (_this.selectedRow.Gid == "admin") {
                    _this.$message.warning("该用户为超级管理员，不允许删除");
                    return;
                }
                API.GetAction("API/IFP/Rights/User/DelUser", { gid: _this.selectedRow.Gid }).then(x => {
                    if (x.success) {
                        _this.$message.success("删除成功");
                        //_this.paging.records -= 1;
                        //_this.paging.page = Math.ceil(_this.paging.records / _this.paging.size);
                        _this.OnQuery();
                    } else {
                        _this.$message.error("删除失败。" + x.msg);
                    }
                }).catch(function (e) {
                    _this.$message.error("删除出错");
                    console.error(e);
                });
            },
            resetPwd() {
                var _this = this;
                if (_this.checkSelect() == false) {
                    return;
                }
                API.GetAction("API/IFP/Rights/User/ResetPwd", { gid: _this.selectedRow.Gid }).then(x => {
                    if (x.success) {
                        _this.$message.success("密码重置成功");
                        _this.OnQuery();
                    } else {
                        _this.$message.warning("密码重置失败。" + x.msg);
                    }
                }).catch(function (e) {
                    _this.$message.warning(e);
                });
            },
            userRight() {
                var _this = this;
                if (_this.checkSelect() == false) {
                    return;
                }
                if (_this.selectedRow.Gid == "admin") {
                    _this.$message.warning("该用户为超级管理员，所有权限均可使用,不允许修改");
                    return;
                }
                this.editor.source = this.selectedRow;
                this.editor.dialog2 = true;
                this.editor.header = this.editor.source.UsiName + "权限配置";
            },
            OnQuery() {
                var _this = this;
                //API.GetAction("API/IFP/Rights/User/UserList", {
                //    UsiName_LIKE: _this.query.UsiName_like,
                //    UsiNumber_LIKE: _this.query.UsiNumber_like,
                //    page: _this.paging.page,
                //    rows: _this.paging.size,
                //    sidx: "USINUMBER",
                //    sord: "asc"
                //}).then(x => {
                //    _this.tableData = x.rows;
                //    _this.paging.records = x.records;
                //    _this.paging.page = x.page;
                //}).catch(e => {
                //    _this.$message.error(e);
                //});
                let param = [
                    {
                        UsiName: { Value: _this.query.UsiName_like, Match: "HAS" },
                        UsiNumber: { Value: _this.query.UsiName_like, Match: "HAS", Order: "ASC" },
                    }
                ]
                API.GetAction("API/IFP/Rights/User/UserList", {
                    filter: param, paging: _this.paging

                }).then(x => {
                    _this.tableData = x.rows;
                    _this.paging.records = x.records;
                    _this.paging.page = x.page;
                }).catch(e => {
                    _this.$message.error(e);
                });
            },

            bindCard() {
                if (this.checkSelect() == false) {
                    return;
                }
                this.cardDetail.userid = this.selectedRow.Gid;
                this.cardDetail.show = true;
            },


            onReset() {

                this.query = { UsiName_like: null, UsiNumber_like: null }
                this.OnQuery();
            },
            onClosed: function () { this.OnQuery(); },
            contactFormatter: function (row, column, cellValue, index) {
                //UsiContact

                if (cellValue == undefined) return cellValue;
                let len = cellValue.length < 6 ? 6 : cellValue.length;
                let tmp = "";

                for (var i = 0; i < len; i++) {

                    if (i > 2 && i < len - 3)
                        tmp = tmp.concat('*');
                    else {
                        if (cellValue[i] != undefined) {
                            tmp = tmp.concat(cellValue[i]);
                        }
                        else {
                            tmp = tmp.concat('*');
                        }
                    }
                }
                return tmp;
            }
        },
        watch: {
        },
        created: function () {
            this.OnQuery();
        }
    }
})