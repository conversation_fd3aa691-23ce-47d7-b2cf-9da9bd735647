# markdown - 纯文本编写规范

---------------------------

特点：

* Markdown 是一种轻量级标记语言
* 目的是为了更好的编写**纯文本文档**
* 能转换成有效的 `HTML` 文档
* 阅读和编写使用文本编辑器即可

> markdown 是纯文本编写规范，阅读和编写使用文本编辑器即可

## 任务列表

* [ ] 任务列表
* [ ] 任务列表
* [x] 任务列表
  * [ ] 任务列表
  * [ ] 任务列表
  * [ ] 任务列表

1. [ ] 任务列表
2. [ ] 任务列表
3. [ ] 任务列表

```md
* [ ] 任务列表
* [ ] 任务列表
* [x] 任务列表
  * [ ] 任务列表
  * [ ] 任务列表
  * [ ] 任务列表

1. [ ] 任务列表
2. [ ] 任务列表
3. [ ] 任务列表
```

## 表格

|  标题  |  列1 |  列2  |
|--------|------|-------|
|1       |数据  |内容   |
|2       |数据  |内容   |
|3       |数据  |内容   |
|4       |数据  |内容   |

```md
|  标题  |  列1 |  列2  |
|--------|------|-------|
|1       |数据  |内容   |
|2       |数据  |内容   |
|3       |数据  |内容   |
|4       |数据  |内容   |
```

## 标题语法

# 标题1

## 标题2

### 标题3

#### 标题4

##### 标题5

###### 标题6

```md
# 标题1
## 标题2
### 标题3
#### 标题4
##### 标题5
###### 标题6
```

# 列表

## 有序列表

1. asdfadf
1. asdfsf
1. asdfsf
1. asdfsf
1. asdfsf

## 无序列表

* asdf
* asdf
* asdf
* asdf
* asdf


## 引用

> 引用1
> 引用2

## 文字样式语法

**加粗** `**加粗**`  

__加粗方式二__ `__加粗方式二__`  

_倾斜_ `_倾斜_`

*倾斜方式二* `*倾斜方式二*`

~~删除线~~ `~~删除线~~`

``` 单行代码 ``` ` ``` 单行代码 ``` `  

[链接文字](https://ezq.gitee.io) `[链接文字](https://ezq.gitee.io)`

![图片描述文字](https://ezq.gitee.io/ico-128x128.png) `![图片描述文字](https://ezq.gitee.io/ico-128x128.png)`


## 分割线

-------------

```md
-------------
```

## 代码区

```js
var a = document
```

```md

将“~”替换为“`”

~~~js
var a = document
~~~

```

## 注释

<!--注释-->  

```html
<!--注释-->  
```

## 使用 HTML


但一般解析工具都支持直接插入html代码

<div id="html-demo">
<div style="
">
	直接插入html代码
</div>
</div>

<style> 
#html-demo {
	animation-timing-function:ease-in-out;
	animation-name:breathe;
	animation-duration:2700ms;
	animation-iteration-count:infinite;
	animation-direction:alternate;
}

 @keyframes example {
  from {background-color: red;}
  to {background-color: yellow;}
}


 @keyframes breathe {
	0% {
		opacity:1;
		box-shadow:0 1px 2px rgba(255,255,255,0.1);
	}
	100% {
		opacity:1;
		box-shadow:0 1px 30px rgba(59,255,255,1);
	}
}
</style>

```html
<div style="color:red;">直接插入html代码</div>
```

## 示例

一个规范的md文档，即使直接阅读也很赏心悦目

```md

# Markdown 文档

Markdown 是对 txt 文本的语法约束，即使不通过渲染也能一部了然看清文档结构，就像这个示例一样

## 说明

一般顶部需要有一个一级标题，其他标题级别只能依次递增

## 语法

### 列表

#### 无序列表  

* 无序列表可以使用 + - * 来标识
* 但最好只是用其中一种
* 以免影响阅读

#### 有序列表 

1. 有序列表以 数组 加 . 开头
2. 数字可以都是1，一般渲染器都会修改为正确排序
3. 但为了方便直接阅读
4. 最好还是按正确顺序排序

### 任务列表

* [ ] 任务列表
* [x] 即使不使用渲染器，直接阅读
* [ ] 也能很清晰的看出意图

## 文字样式

**加粗** *倾斜* ~~删除~~

## 表格

| 项目 | 列1 | 列2 |
|------|-----|-----|
|选项1 |bool |值   |
|选项1 |bool |值   |
|选项1 |bool |值   |
|选项1 |bool |值   |

```

通过渲染器处理的结果


# Markdown 文档

Markdown 是对 txt 文本的语法约束，即使不通过渲染也能一部了然看清文档结构，就像这个示例一样

## 说明

一般顶部需要有一个一级标题，其他标题级别只能依次递增

## 语法

### 列表

#### 无序列表  

* 无序列表可以使用 + - * 来标识
* 但最好只是用其中一种
* 以免影响阅读

#### 有序列表 

1. 有序列表以 数组 加 . 开头
2. 数字可以都是1，一般渲染器都会修改为正确排序
3. 但为了方便直接阅读
4. 最好还是按正确顺序排序

### 任务列表

* [ ] 任务列表
* [x] 即使不使用渲染器，直接阅读
* [ ] 也能很清晰的看出意图

## 文字样式

**加粗** *倾斜* ~~删除~~

## 表格

| 项目 | 列1 | 列2 |
|------|-----|-----|
|选项1 |bool |值   |
|选项1 |1    |值   |
|选项1 |bool |值   |
|选项1 |bool |值   |
