﻿//using System;
//using System.Collections.Generic;
//using System.Text;
//using System.IO.Ports;
//using System.Threading;
//using COM.IFP.PLC.Service;
//using COM.IFP.Common;
//using COM.IFP.PLC.Modbus;
//using COM.IFP.PLC.Modbus.DataPoint;
//using System.Collections;
//using System.Linq;

//namespace COM.IFP.Device
//{
//    /// <summary>
//    /// daiabin，宜科485读卡器，原煤样桶的卡。RF30-WR-Q80U/RS01，参考官网文档。
//    /// </summary>
//    public class YK485ReaderN2
//    {
//        SerialPortAdaptor spada=null;

//        public YK485ReaderN2()
//        {
//            //spada = new SerialPortAdaptor();
//        }
//        /// <summary>
//        /// 读取
//        /// </summary>
//        /// <param name="portName"></param>
//        /// <param name="baudRate"></param>
//        /// <param name="len"></param>
//        /// <returns></returns>
//        public string Read(string portName, int baudRate, int len = 17)
//        {
//            //int target = queue.FirstOrDefault(x => x > 3);
//            SerialPortAdaptor spada= AppCommon.queueSerial.FirstOrDefault(x=>x.PortName==portName);
//            if (spada == null)
//            {
//                // 创建字典存储动态对象
//                var namedObjects = new Dictionary<string, object>();

//                // 动态创建对象并命名
//                namedObjects[portName] = new SerialPortAdaptor
//                {
//                    PortName = portName,
//                    PortBaudRate = baudRate,
//                    PortDataBits = 8,
//                    PortParity = Parity.None,
//                    PortStopBits = StopBits.One
//                };



//                //重新构建串口对象； 
//                //spada = new SerialPortAdaptor();
//                AppCommon.queueSerial.Enqueue((SerialPortAdaptor)namedObjects[portName]);
//            }
//            int len2 = len;
//            //读取长度为偶数，多的忽略再返回
//            if (len % 2 != 0)
//            {
//                len2++;
//            }
//            ReadRequestN2 request = new ReadRequestN2();
//            request.CNT = Convert.ToByte(len2);
//            byte[] sendData = request.GetMessage();
//            Console.WriteLine(ValueHelper.PrintHexBytes(sendData));
//            if (spada.PortState == false)
//            {
//                spada.PortName = portName;
//                spada.PortBaudRate = baudRate;
//                spada.PortDataBits = 8;
//                spada.PortParity = Parity.None;
//                spada.PortStopBits = StopBits.One;
//                spada.Open();
//            }
//            //SerialPortAdaptor spada = new SerialPortAdaptor(portName, baudRate, 8, Parity.None, StopBits.One);

//            spada.Write(sendData);
//            byte[] receiveData = null;
//            string errorMsg = "错误";
//            for (int i = 0; i < 10; i++)
//            {
//                Thread.Sleep(50);
//                receiveData = spada.Read();
//                if (receiveData != null && receiveData.Length > 0)
//                {
//                    Console.WriteLine(ValueHelper.PrintHexBytes(receiveData));
//                    try
//                    {
//                        ReadResponseN2 response = new ReadResponseN2();
//                        byte[] value = response.ParseMessage(receiveData);
//                        string str = Encoding.ASCII.GetString(value);
//                        str=Encoding.ASCII.GetString( Encoding.ASCII.GetBytes(str));
//                        if (str.Length >= len)
//                            str = str.Substring(0, len);
//                        spada.Close();
//                        //spada.Dispose();
//                        return str;
//                    }
//                    catch (Exception ex)
//                    {
//                        errorMsg = errorMsg + "," + ex.Message;
//                        continue;
//                    }
//                }

//            }
//            //spada.Close();
//            //spada.Dispose();
//            errorMsg += ",响应超时";
//            throw new Exception(errorMsg);
//        }

//        /// <summary>
//        /// 写入
//        /// </summary>
//        /// <param name="portName"></param>
//        /// <param name="baudRate"></param>
//        /// <param name="str">ASCII编码的</param>
//        /// <returns></returns>
//        public string Write(string portName, int baudRate, string str)
//        {
//                byte[] data = null;
//                //空串，尽量清零卡片内容
//                if (string.IsNullOrEmpty(str))
//                {
//                    data = new byte[32];
//                }
//                else
//                    data = Encoding.ASCII.GetBytes(str);

//                int len = data.Length;
//                int len2 = len;
//                //写入长度为偶数，多的补0
//                if (len % 2 != 0)
//                {
//                    len2++;
//                }

//                WriteRequestN2 request = new WriteRequestN2();
//                request.CNT = Convert.ToByte(len2);

//                byte[] sendData = request.GetMessage(data);
//                Console.WriteLine(ValueHelper.PrintHexBytes(sendData));
//                if (spada.PortState == false)
//                {
//                    spada.PortName = portName;
//                    spada.PortBaudRate = baudRate;
//                    spada.PortDataBits = 8;
//                    spada.PortParity = Parity.None;
//                    spada.PortStopBits = StopBits.One;
//                    spada.Open();
//                }
//                spada.Write(sendData);
//                byte[] receiveData = null;
//                string errorMsg = "错误";
//                for (int i = 0; i < 10; i++)
//                {
//                    Thread.Sleep(50);
//                    receiveData = spada.Read();
//                    if (receiveData != null && receiveData.Length > 0)
//                    {
//                        Console.WriteLine(ValueHelper.PrintHexBytes(receiveData));
//                        try
//                        {
//                            WriteResponseN2 response = new WriteResponseN2();
//                            response.ParseMessage(receiveData);
//                            //spada.Close();
//                            //spada.Dispose();
//                            return "";
//                        }
//                        catch (Exception ex)
//                        {
//                            errorMsg = errorMsg + "," + ex.Message;
//                            continue;
//                        }
//                    }
//                }
//                //spada.Close();
//                //spada.Dispose();
//                errorMsg += ",响应超时";
//                return errorMsg;
          
//        }

//        /// <summary>
//        /// 读取
//        /// </summary>
//        /// <param name="portName"></param>
//        /// <param name="baudRate"></param>
//        /// <param name="len"></param>
//        /// <returns></returns>
//        public string ReadNew(string portName, int baudRate, byte[] data, int len = 17)
//        {
//            int len2 = len;
//            //读取长度为偶数，多的忽略再返回
//            if (len % 2 != 0)
//            {
//                len2++;
//            }
//            ReadRequestN2 request = new ReadRequestN2();
//            request.CNT = Convert.ToByte(len2);
//            byte[] sendData = request.GetMessage();
//            Console.WriteLine(ValueHelper.PrintHexBytes(sendData));
//            if (spada.PortState == false)
//            {
//                spada.PortName = portName;
//                spada.PortBaudRate = baudRate;
//                spada.PortDataBits = 8;
//                spada.PortParity = Parity.None;
//                spada.PortStopBits = StopBits.One;
//                spada.Open();
//            }
//            //SerialPortAdaptor spada = new SerialPortAdaptor(portName, baudRate, 8, Parity.None, StopBits.One);

//            spada.Write(sendData);
//            byte[] receiveData = null;
//            string errorMsg = "错误";
//            for (int i = 0; i < 10; i++)
//            {
//                Thread.Sleep(50);
//                receiveData = spada.Read();
//                if (receiveData != null && receiveData.Length > 0)
//                {
//                    Console.WriteLine(ValueHelper.PrintHexBytes(receiveData));
//                    try
//                    {
//                        ReadResponseN2 response = new ReadResponseN2();
//                        byte[] value = response.ParseMessage(receiveData);
//                        string str = Encoding.ASCII.GetString(value);
//                        str = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(str));
//                        if (str.Length >= len)
//                            str = str.Substring(0, len);
//                        spada.Close();
//                        //spada.Dispose();
//                        return str;
//                    }
//                    catch (Exception ex)
//                    {
//                        errorMsg = errorMsg + "," + ex.Message;
//                        continue;
//                    }
//                }

//            }
//            //spada.Close();
//            //spada.Dispose();
//            errorMsg += ",响应超时";
//            throw new Exception(errorMsg);
//        }

//        /// <summary>
//        /// 写入
//        /// </summary>
//        /// <param name="portName"></param>
//        /// <param name="baudRate"></param>
//        /// <param name="str">ASCII编码的</param>
//        /// <returns></returns>
//        public string WriteNew(string portName, int baudRate, string str)
//        {
//            byte[] data = null;
//            //空串，尽量清零卡片内容
//            if (string.IsNullOrEmpty(str))
//            {
//                data = new byte[32];
//            }
//            else
//                data = Encoding.ASCII.GetBytes(str);

//            int len = data.Length;
//            int len2 = len;
//            //写入长度为偶数，多的补0
//            if (len % 2 != 0)
//            {
//                len2++;
//            }
//            WriteRequestN2 request = new WriteRequestN2();
//            request.CNT = Convert.ToByte(len2);

//            byte[] sendData = request.GetMessage(data);
//            Console.WriteLine(ValueHelper.PrintHexBytes(sendData));
//            if (spada.PortState == false)
//            {
//                spada.PortName = portName;
//                spada.PortBaudRate = baudRate;
//                spada.PortDataBits = 8;
//                spada.PortParity = Parity.None;
//                spada.PortStopBits = StopBits.One;
//                spada.Open();
//            }
//            spada.Write(sendData);
//            byte[] receiveData = null;
//            string errorMsg = "错误";
//            for (int i = 0; i < 10; i++)
//            {
//                Thread.Sleep(50);
//                receiveData = spada.Read();
//                if (receiveData != null && receiveData.Length > 0)
//                {
//                    Console.WriteLine(ValueHelper.PrintHexBytes(receiveData));
//                    try
//                    {
//                        WriteResponseN2 response = new WriteResponseN2();
//                        response.ParseMessage(receiveData);
//                        //spada.Close();
//                        //spada.Dispose();
//                        return "";
//                    }
//                    catch (Exception ex)
//                    {
//                        errorMsg = errorMsg + "," + ex.Message;
//                        continue;
//                    }
//                }
//            }
//            //spada.Close();
//            //spada.Dispose();
//            errorMsg += ",响应超时";
//            return errorMsg;

//        }

//        /// <summary>
//        /// 关闭串口
//        /// </summary>
//        public void Close()
//        {
//            if (spada != null && spada.PortState == true)
//                spada.Close();
//        }
//    }
//    class BaseN2
//    {
//        /// <summary>
//        /// 起始
//        /// </summary>
//        public const byte SOF = 0xaa;
//        /// <summary>
//        /// 除起始和结尾的字节长度
//        /// </summary>
//        public byte[] LEN = new byte[2];
//        /// <summary>
//        /// crc16，跟modbus一样
//        /// </summary>
//        public byte[] CRC = new byte[2];
//        /// <summary>
//        /// 结尾
//        /// </summary>
//        public const byte EOF = 0x55;
//    }

//    /// <summary>
//    /// 宜科485读卡，请求
//    /// </summary>
//    class ReadRequestN2 : BaseN2
//    {
//        /// <summary>
//        /// 命令0x06读
//        /// </summary>
//        public const byte CMD = 0x06;
//        /// <summary>
//        /// UERS 区为 0x03，EPC估计为1
//        /// </summary>
//        public byte BANK = 0x03;
//        /// <summary>
//        /// 起始地址
//        /// </summary>
//        public byte PTR = 0x00;
//        /// <summary>
//        /// 读取数据字节长度
//        /// </summary>
//        public byte CNT = 0x20;

//        public byte[] GetMessage()
//        {
//            List<byte> lb = new List<byte>();
//            LEN[0] = 0;
//            LEN[1] = 8;
//            lb.AddRange(LEN);
//            lb.Add(CMD);
//            lb.Add(BANK);
//            lb.Add(PTR);
//            lb.Add(CNT);
//            CRC = ValueHelper.CRC16(lb.ToArray());
//            lb.AddRange(CRC);
//            lb.Add(EOF);
//            lb.Insert(0, SOF);
//            return lb.ToArray();
//        }
//    }
//    /// <summary>
//    /// 宜科485读卡，返回
//    /// </summary>
//    class ReadResponseN2 : BaseN2
//    {
//        /// <summary>
//        /// 命令0x06读
//        /// </summary>
//        public byte CMD = 0x06;
//        /// <summary>
//        /// 状态字， 0x00 表示操作成功，后面跟随数据长度和数据， 0x80 表示操作失败，
//        ///0x81 表示命令有误或是传输有误， 0x01 表示命令正在执行，处于忙状态；
//        /// </summary>
//        public byte STATUS;
//        /// <summary>
//        /// 读取数据字节长度
//        /// </summary>
//        public byte CNT;
//        /// <summary>
//        ///     
//        /// </summary>
//        public byte[] Data = null;

//        public byte[] ParseMessage(byte[] source)
//        {
//            LEN[0] = source[1];
//            LEN[1] = source[2];
//            CMD = source[3];
//            if (CMD != ReadRequestN2.CMD)
//            {
//                throw new Exception($"响应报文命令类型为{CMD}不是读卡{ReadRequestN2.CMD}");
//            }
//            STATUS = source[4];
//            if (STATUS != 0)
//            {
//                string des = string.Empty;
//                switch (STATUS)
//                {
//                    case 0x80:
//                        des = "操作失败";
//                        break;
//                    case 0x81:
//                        des = "命令有误或是传输有误";
//                        break;
//                    case 0x01:
//                        des = "命令正在执行，处于忙状态";
//                        break;
//                    default:
//                        break;
//                }
//                throw new Exception($"操作失败，响应报文状态{STATUS}({des})");
//            }
//            CNT = source[5];
//            int len = CNT;
//            Data = new byte[len];
//            Array.Copy(source, 6, Data, 0, len);

//            CRC[0] = source[6 + len];
//            CRC[1] = source[7 + len];
//            List<byte> lb = new List<byte>();
//            lb.AddRange(LEN);
//            lb.Add(CMD);
//            lb.Add(STATUS);
//            lb.Add(CNT);
//            lb.AddRange(Data);
//            byte[] crc = ValueHelper.CRC16(lb.ToArray());

//            if (BitConverter.ToUInt16(crc) != BitConverter.ToUInt16(CRC))
//            {
//                throw new Exception("响应报文的CRC值错误");
//            }
//            return Data;
//        }

//    }

//    /// <summary>
//    /// 宜科485写卡，请求
//    /// </summary>
//    class WriteRequestN2 : BaseN2
//    {
//        /// <summary>
//        /// 命令0x07写
//        /// </summary>
//        public const byte CMD = 0x07;
//        /// <summary>
//        /// UERS 区为 0x03
//        /// </summary>
//        public byte BANK = 0x03;
//        /// <summary>
//        /// 起始地址
//        /// </summary>
//        public byte PTR = 0x00;
//        /// <summary>
//        /// 写入数据字节长度，默认和读保持一样长
//        /// </summary>
//        public byte CNT = 0x20;
//        /// <summary>
//        /// 
//        /// </summary>
//        public byte[] DATA = new byte[32];

//        /// <summary>
//        /// 
//        /// </summary>
//        /// <param name="str"></param>
//        /// <returns></returns>
//        public byte[] GetMessage(byte[] data)
//        {
//            List<byte> lb = new List<byte>();
//            int len = 8 + CNT;
//            LEN = ValueHelper.Transform(BitConverter.GetBytes(Convert.ToUInt16(len)));

//            lb.AddRange(LEN);
//            lb.Add(CMD);
//            lb.Add(BANK);
//            lb.Add(PTR);
//            lb.Add(CNT);

//            DATA = new byte[(int)CNT];
//            for (int i = 0; i < (int)CNT; i++) DATA[i] = 0;
//            Array.Copy(data, 0, DATA, 0, data.Length);
//            lb.AddRange(DATA);
//            CRC = ValueHelper.CRC16(lb.ToArray());
//            lb.AddRange(CRC);
//            lb.Add(EOF);
//            lb.Insert(0, SOF);
//            return lb.ToArray();
//        }
//    }

//    /// <summary>
//    /// 宜科485写卡，返回
//    /// </summary>
//    class WriteResponseN2 : BaseN2
//    {
//        /// <summary>
//        /// 命令0x07写
//        /// </summary>
//        public byte CMD = 0x07;
//        /// <summary>
//        /// 状态字， 0x00 表示操作成功，后面跟随数据长度和数据， 0x80 表示操作失败，
//        ///0x81 表示命令有误或是传输有误， 0x01 表示命令正在执行，处于忙状态；
//        /// </summary>
//        public byte STATUS;

//        /// <summary>
//        /// 检查收到写卡返回报文，没报错则检查通过
//        /// </summary>
//        /// <param name="source"></param>
//        public void ParseMessage(byte[] source)
//        {
//            LEN[0] = source[1];
//            LEN[1] = source[2];
//            CMD = source[3];
//            if (CMD != WriteRequestN2.CMD)
//            {
//                throw new Exception($"响应报文命令类型为{CMD}不是写卡{WriteRequestN2.CMD}");
//            }
//            STATUS = source[4];
//            if (STATUS != 0)
//            {
//                string des = string.Empty;
//                switch (STATUS)
//                {
//                    case 0x80:
//                        des = "操作失败";
//                        break;
//                    case 0x81:
//                        des = "命令有误或是传输有误";
//                        break;
//                    case 0x01:
//                        des = "命令正在执行，处于忙状态";
//                        break;
//                    default:
//                        break;
//                }

//                throw new Exception($"操作失败，响应报文状态{STATUS}({des})");
//            }

//            CRC[0] = source[5];
//            CRC[1] = source[6];
//            List<byte> lb = new List<byte>();
//            lb.AddRange(LEN);
//            lb.Add(CMD);
//            lb.Add(STATUS);
//            byte[] crc = ValueHelper.CRC16(lb.ToArray());
//            if (BitConverter.ToUInt16(crc) != BitConverter.ToUInt16(CRC))
//            {
//                throw new Exception("响应报文的CRC值错误");
//            }
//        }
//    }
//}
