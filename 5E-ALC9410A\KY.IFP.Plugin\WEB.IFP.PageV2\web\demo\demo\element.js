define(["comp!./element_sub1.js","iofp/services/service.menu","dialog"],function(element_sub1,menuService,dialog){
    return {
        // 这里演示如何嵌入子页面
        components:{
            subpage1:element_sub1
        },
        el:"#app",
        data:function(){
            return {
                menulist:[],
                arg1:"来自 element 的数据",
                title:"hello",
                tableData: [{
                  date: '2016-05-02',
                  name: '王小虎',
                  address: '上海市普陀区金沙江路 1518 弄'
                }, {
                  date: '2016-05-04',
                  name: '王小虎',
                  address: '上海市普陀区金沙江路 1517 弄'
                }, {
                  date: '2016-05-01',
                  name: '王小虎',
                  address: '上海市普陀区金沙江路 1519 弄'
                }, {
                  date: '2016-05-03',
                  name: '王小虎',
                  address: '上海市普陀区金沙江路 1516 弄'
                }]
            }
        },
        created:function(){
            menuService.getMenusByCurrentUser()
            .then((data)=>{
                this.menulist = data;
            })
        },
        methods:{
            close:function() {
                // 如果使用dialog 打开本页面，这里可以返回数据并关闭
                var win = dialog.getDialog();
                win && win.returnValue("来自 element.html 的返回值");
                win && win.close();
            },
            btnclick:function(){
                alert("测试按钮");
            }
        }
    }
})