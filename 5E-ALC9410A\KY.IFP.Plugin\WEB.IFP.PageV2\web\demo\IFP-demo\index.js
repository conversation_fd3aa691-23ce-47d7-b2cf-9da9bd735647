define(['require', 'controllers/base', 'jquery', 'jclass', 'iofp/common', 'iofp/api', 'platform/vue', 'util', 'tailwindcss', 'lib/bower/mockjs/mock-min', 'moment'], function (
    require,
    base,
    $,
    jclass,
    iofp,
    API,
    pVue,
    util,
    tailwindcss,
    Mock,
    Moment,
) {
    // 创建异步组件列表，放在这里的好处是：主页面的加载不依赖子页面
    const components = pVue.createAsyncComponents({
        Regist2IofpConfig: './regist2IofpConfig.js', //对应标签regist2-iofp-config，VUE会把Regist2IofpConfig转换成全小写，大写字母全部变成小写字母并在中间加上横杠'-'
        RegistConfig: './registConfig.js',
        TestRoute: './testRoute.js',
    });
    // let sss = Mock.mock({
    //     'list|1-10': [
    //         {
    //             'id|+1': 1,
    //         },
    //     ],
    // });
    // var Random = Mock.Random;
    // Random.extend({
    //     color: function () {
    //         return Random.color();
    //     },
    // });

    return {
        el: '#app',
        components: components,
        data() {
            return {
                loading: false,
                showFormDialog: false, // 新增弹窗
                showChangeConfigDialog: false, // 修改配置弹窗
                showTestRouteDialog: false, // 测试接口弹窗
                searchForm: {
                    testDate: [],
                    startTime: '',
                    endTime: '',
                    testName: '',
                    testCode: '',
                    testStatus: '',
                }, // 测试查询表单
                testStatusList: [
                    { id: '1', text: '状态111' },
                    { id: '2', text: '状态222' },
                    { id: '3', text: '状态444' },
                    { id: '4', text: '状态444' },
                    { id: '5', text: '状态555' },
                    { id: '6', text: '状态666' },
                    { id: '7', text: '状态777' },
                    { id: '8', text: '状态888' },
                    { id: '9', text: '状态999' },
                ], // 查询状态列表
                disableForm: false, // 是否禁用表单
                formTitle: '新增',
                formInfo: {}, // 表单信息
                testTableHead: [
                    { value: 'id', name: 'id' },
                    { value: 'name', name: '名称' },
                    { value: 'city', name: '城市' },
                    { value: 'zip', name: '邮编' },
                    { value: 'email', name: '邮箱' },
                    { value: 'birthday', name: '出生日期' },
                    // { value: 'photo', name: '照片' },
                ], // 测试表格头
                testTableData: [], // 测试表格数据
                tableLoading: false, // 测试表格数据加载状态
                baseSetting: {
                    pageSize: 20,
                    pageNum: 1,
                    total: null,
                },
                selectedList: [], // 选中列表
                rules: {
                    name: [{ required: true, message: '请输入名称', trigger: 'change' }],
                    city: [{ required: true, message: '请输入城市', trigger: 'change' }],
                    requestUrlIofp: [{ required: true, message: '请输入请求地址', trigger: 'change' }],
                },
            };
        },
        computed: {
            pickerStartOptions() {
                return {
                    disabledDate: this.disabledStartMonth,
                };
            },
            pickerEndOptions() {
                return {
                    disabledDate: this.disabledEndMonth,
                };
            },
        },
        created() {
            // 定义一个对象用来保存初始化查询表单数据，用以重置查询表单
            this.initialObject = Object.assign({}, this.searchForm);
        },
        mounted() {
            // this.testTableData = { ...sss }.list;
            // this.$nextTick(() => {
            //     this['$refs']['testTable'].doLayout();
            // });
        },
        methods: {
            /**
             * 判断传入时间是否晚于或等于搜索表单中的结束时间
             *
             * @param time 待判断的时间
             * @returns 如果传入时间晚于或等于搜索表单中的结束时间则返回true，否则返回false
             */
            disabledStartMonth(time) {
                return Moment(time).isSameOrAfter(this.searchForm.endTime, 'Date');
            },
            /**
             * 判断给定时间是否在结束月份之前或相同
             *
             * @param time 给定时间
             * @returns 如果给定时间在结束月份之前或相同，则返回 true；否则返回 false
             */
            disabledEndMonth(time) {
                return Moment(time).isSameOrBefore(this.searchForm.startTime, 'Date');
            },
            // 查询
            search(page) {
                // console.log(this.searchForm);
                this.tableLoading = true;
                //还原页码
                this.baseSetting.pageNum = page ? page : this.baseSetting.pageNum;
                // let params = JSON.parse(JSON.stringify(this.formData));
                // params.pageNum = this.baseSetting.pageNum;
                // params.pageSize = this.baseSetting.pageSize;
                let mockData = Mock.mock({
                    'list|20': [
                        {
                            'id|+1': 1,
                            name: '@cname',
                            city: '@city',
                            zip: '@zip',
                            email: '@email',
                            birthday: '@date("yyyy-MM-dd")',
                            // photo: '@image(60x20,@color,@color,@cname)',
                        },
                    ],
                });
                this.testTableData = { ...mockData }.list;
                this.$nextTick(() => {
                    this['$refs']['testTable'].doLayout();
                    this.tableLoading = false;
                }, 2000);
                return;
                API.GetAction('API/IFP/Client/Request/GetProductInfo', {
                    name: '', // 姓名
                    city: '', // 城市
                    sex: '', // 性别
                    birthday: '', // 出生日期 1999-12-30
                    phone: '', // 手机号
                })
                    .then((e) => {
                        // this.formData(resp);
                        this.searchForm = {};
                        this.productTypeList.map((item) => {
                            if (item.id == e.kind) {
                                e.productTypeName = item.text;
                            }
                        });
                        this.databaseList.map((item) => {
                            if (item.id == e.dbType) {
                                e.databaseName = item.text;
                            }
                        });
                        Object.assign(this.searchForm, e);
                    })
                    .catch(function (e) {
                        this.$message.warning(e);
                    });
                // F.ajax({
                //     url: '/API/IFP/Client/Request/GetProductInfo',
                //     data: {},
                //     success: function (resp) {
                //         _this.formData(resp);
                //     },
                // });
            },
            /**
             * 操作按钮
             *
             * @param type 操作类型
             * @param optData 当前行数据，默认为空对象
             */
            operationBtn(type, optData = { row: {} }) {
                // 重置是否可编辑状态
                this.disableForm = false;
                this.formInfo = Object.assign({}, optData.row);
                if (type === 'add') {
                    //新增
                    this.showFormDialog = true;
                    this.formTitle = '新增';
                    this.formInfo = {};
                } else if (type === 'edit') {
                    // 编辑
                    this.showFormDialog = true;
                    this.formTitle = '编辑';
                } else if (type === 'check') {
                    // 查看
                    this.disableForm = true; // 查看状态下不可编辑
                    this.showFormDialog = true;
                    this.formTitle = '查看';
                } else if (type === 'download') {
                    // 下载
                } else {
                    // 删除
                    this.$confirm('是否删除?', '提示', {
                        cancelButtonClass: 'btn-custom-cancel float-right ml-2.5',
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            this.$message({
                                message: '删除成功',
                                type: 'success',
                            });
                        })
                        .catch(() => {
                            this.$message({
                                message: '取消删除',
                            });
                        });
                }
            },
            // 重置查询表单
            onReset() {
                this.searchForm = Object.assign({}, this.initialObject);
            },
            // 表格勾选
            handleSelectionChange(val) {
                this.selectedList = val;
            },
            // 批量下载
            batchDownload() {
                this.$message.info('批量下载');
                let ids = [];
                this.selectedList.map((item) => {
                    ids.push(item.id);
                });
                this.$confirm(`批量下载id为${ids.join(';')}的数据`, '批量下载', {
                    cancelButtonClass: 'btn-custom-cancel float-right ml-2.5',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                })
                    .then(() => {})
                    .catch(() => {});
            },
            /**
             * 改变分页大小
             * @param pageSize 新的分页大小
             */
            sizeChange(pageSize) {
                this.baseSetting.pageSize = pageSize;
            },
            /**
             * 页码改变
             * @param pageNum 页码
             * */
            pageChange(pageNum) {
                this.baseSetting.pageNum = pageNum;
            },
            /**
             * 根据索引计算实际位置
             *
             * @param index 索引值
             * @returns 返回实际位置
             */
            indexMethod(index) {
                return index + 1 + this.baseSetting.pageSize * (this.baseSetting.pageNum - 1);
            },
            // 注册按钮
            regBtn() {
                let regCode = _this.searchForm['regCode']; // 注册码
                if (regCode) {
                    this.$confirm('该产品已经注册，是否重新注册?', '提示', {
                        cancelButtonClass: 'btn-custom-cancel float-right ml-2.5',
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            this.showFormDialog = true;
                        })
                        .catch(() => {
                            //选否不执行
                            return;
                        });
                } else {
                }
            },

            // 路由请求按钮
            routeRequestBtn() {
                let _this = this;
                _this.loading = true;
                API.GetAction('API/IFP/Client/Request/ReqRoute', {})
                    .then((x) => {
                        _this.loading = false;
                        _this.$message.success(e);
                    })
                    .catch(function (e) {
                        _this.loading = false;
                        _this.$message.error(e);
                    });
                // F.util.showWait();
                // F.ajax({
                //     url: '/API/IFP/Client/Request/ReqRoute',
                //     data: {},
                //     success: function (resp) {
                //         F.util.hideWait();
                //         if (resp == '成功') {
                //             $.bootoast.success(resp);
                //             _this.query();
                //         } else {
                //             $.bootoast.danger(resp);
                //         }
                //     },
                //     error: function (err) {
                //         F.util.hideWait();
                //         $.bootoast.danger(err);
                //     },
                // });
            },
            // 修改配置按钮
            changeConfigBtn() {
                this.showChangeConfigDialog = true;
            },

            // 选中调用接口行
            selectRow(e) {
                this.selectRoute = {};
                Object.assign(this.selectRoute, e);
                // console.log(this.selectRoute);
            },
            // 调用接口测试
            testRoute() {
                if (Object.keys(this.selectRoute).length === 0) {
                    // $.bootoast.warning('请选择数据');
                    this.$message.warning('请选择数据');
                    return;
                }
                // var code = selectData.serviceCode;
                if (util.isEmpty(this.selectRoute.serviceCode)) {
                    this.$message.warning('没有接口编号');
                    return;
                }
                if (util.isEmpty(this.selectRoute.requestRouteProduct)) {
                    this.$message.warning('没有路由信息');
                    return;
                }
                this.showTestRouteDialog = true;
                // var win = $.showIframe({
                //     url: 'testRoute.html',
                //     title: '接口测试',
                //     width: 600,
                //     height: 400,
                //     parameter: {
                //         code: code,
                //     },
                //     onhide: function () {},
                // });
            },
            submitTestRoute() {},
        },
    };
});
