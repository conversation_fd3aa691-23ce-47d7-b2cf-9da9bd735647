﻿using COM.IFP.Common;
using COM.IFP.ComputerInfo;
using COM.IFP.Log;
using COM.IFP.UDPHelper;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace COM.IFP.Client
{
    /// <summary>
    /// 心跳，抽象类，由各个业务包实现抽象方法
    /// </summary>
    public abstract class IOFP_Heartbeat_abs
    {
        private static UDPSend udpsend;
        private static string tableName = "";
        /// <summary>
        /// 状态信息ID，每次状态发生改变时，ID发生变化
        /// </summary>
        private static Dictionary<string, string> ZtxxIDMap = new Dictionary<string, string>();

        /// <summary>
        /// 心跳包信息发送类
        /// </summary>
        public void HeartbeatPacketSend()
        {


            if (udpsend == null)
            {
                string strip = "";
                int port = 0;
                try
                {
                    JsonElement jObj_dataCenter = Config.DataCenter;
                    JsonElement jObj_product = Config.ProductRegister;
                    if (jObj_dataCenter.ValueKind == JsonValueKind.Undefined || jObj_product.ValueKind == JsonValueKind.Undefined)
                    {
                        return;
                    }
                    var v = jObj_dataCenter.GetProperty("influxServer");
                    strip = v.GetProperty("influxIP").GetString();
                    port = v.GetProperty("udpPort").GetInt32();
                    tableName = "HB_" + jObj_product.GetProperty("kind").GetString();
                }
                catch
                {
                    //配置文件错误需要抛出
                    return;
                }
                udpsend = new UDPSend();
                udpsend.Init(strip, port);

                LoggerHelper.Info($"UDP#{udpsend.ToIP.ToString()}:{udpsend.Port.ToString()}# Create Success!");
            }
            if (string.IsNullOrEmpty(tableName))
            {
                JsonElement jObj_dataCenter = Config.DataCenter;
                JsonElement jObj_product = Config.ProductRegister;
                tableName = "HB_" + jObj_product.GetProperty("kind").GetString();
            }
            if (string.IsNullOrEmpty(Config.RegCode) || string.IsNullOrEmpty(tableName))
            {
                return;
            }
            //内存使用率
            double memoryRate = MemoryEnvironment.MemoryRate();
            //警告数量
            int warningCnt = this.WaringCuntget();
            //磁盘情况
            Dictionary<string, long> diskMap = DiskInfo.AllDiskInfo();
            string diskInfo = "";
            foreach (var item in diskMap)
            {
                diskInfo += $",{item.Key}={item.Value}";
            }

            //自定义tag
            string tagStr = "";
            Dictionary<string, string> tagsMap = this.TagsMap();
            if (tagsMap != null)
            {
                foreach (KeyValuePair<string, string> one in tagsMap)
                {
                    tagStr += $",{one.Key}={one.Value}";
                }
            }

            //自定义fields
            string fieldStr = "";
            Dictionary<string, string> fieldsMap = this.FieldsMap();
            if (fieldsMap != null)
            {
                foreach (KeyValuePair<string, string> one in fieldsMap)
                {
                    fieldStr += $",{one.Key}={one.Value}";
                }
            }
            //状态信息
            string ztxxStr = "";
            Dictionary<string, string> ztxxMap = this.ZtxxMap();
            if (ztxxMap != null)
            {
                foreach (KeyValuePair<string, string> one in ztxxMap)
                {
                    string ztID = "";
                    try
                    {
                        ztID = ZtxxIDMap[one.Key];
                    }
                    catch
                    {
                        ztID = "";
                    }

                    string value = "";
                    if (string.IsNullOrEmpty(ztID))
                    {
                        ztID = ConvertObj.DateTime2UTC(DateTime.Now) + "";
                        value = one.Value + "$" + ztID;
                    }
                    else
                    {
                        string[] oldValue = ztID.Split("$");
                        if (oldValue.Length != 2)
                        {
                            ZtxxIDMap.Remove(ZtxxIDMap[one.Key]);
                            continue;
                        }
                        if (oldValue[0].Equals(one.Value))
                        {
                            value = ztID;
                        }
                        else
                        {
                            value = one.Value + "$" + ConvertObj.DateTime2UTC(DateTime.Now);
                        }
                    }
                    ZtxxIDMap[one.Key] = value;
                    ztxxStr += $",{one.Key}={one.Value},{one.Key}%T={value.Split("$")[1]}";
                }
            }

            string msg = $"{tableName},regcode={Config.RegCode}{tagStr}{ztxxStr} warningCnt={warningCnt},cpu={COM.IFP.ComputerInfo.PlatformInfo.CPUUtilization},memory={memoryRate}{diskInfo}{fieldStr}";
            udpsend.SendMsg(msg);
        }

        /// <summary>
        /// 获得心跳包的tag，各个产品自定义
        /// </summary>
        /// <returns></returns>
        protected abstract Dictionary<string, string> TagsMap();

        /// <summary>
        /// 获得心跳包的fields，各个产品自定义
        /// </summary>
        /// <returns></returns>
        protected abstract Dictionary<string, string> FieldsMap();

        /// <summary>
        /// 状态信息，一般用于实现甘特图
        /// </summary>
        /// <returns></returns>
        protected abstract Dictionary<string, string> ZtxxMap();

        /// <summary>
        /// 获得报警数量，各个产品自定义
        /// </summary>
        /// <returns></returns>
        protected abstract int WaringCuntget();
    }
}
