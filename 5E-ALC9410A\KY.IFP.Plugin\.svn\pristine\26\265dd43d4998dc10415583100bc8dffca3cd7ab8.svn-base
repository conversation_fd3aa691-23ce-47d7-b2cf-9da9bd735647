﻿<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <title>用户ID卡详情</title>
    </head>
    <body controller="userCardMx.js">
        <div class="hidden">
            <input id="ID" control="controls/textbox" class="hideInput" />
        </div>
        <div form="forms/toolbar" class="layout-h">
            <a id="saveBtn" control="controls/button" option="{}" log>保存</a>
            <a id="readBtn" control="controls/button" option="{}" log>读卡</a>
            <a id="exitBtn" control="controls/button" option="{}">退出</a>
        </div>
        <div class="layout-c">
            <div class="form-header">卡号信息</div>
            <div class="">
                <table class="form-datatable" style="width: 100%">
                    <tr>
                        <td style="padding-left: 20px">用户：</td>
                        <td colspan="3" style="padding-top: 8px">
                            <input id="userGid" control="controls/select2" option="{width:'100%',showName:'用户',controlsUses:1,disabled:true, url:'/API/IFP/Rights/User/GetUserIdTextList'}" />
                        </td>
                    </tr>
                    <tr>
                        <td style="padding-left: 20px">卡号：</td>
                        <td colspan="3" style="padding-top: 8px">
                            <input id="CardNo" control="controls/textbox" option="{width:'100%',showName:'卡号',controlsUses:1}" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </body>
    <script src="/iofp/starter.js"></script>
</html>
