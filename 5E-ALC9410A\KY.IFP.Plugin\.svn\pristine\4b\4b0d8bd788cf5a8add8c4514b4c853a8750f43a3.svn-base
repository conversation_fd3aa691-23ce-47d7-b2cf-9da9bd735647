{"version": 3, "file": "cookie.js", "sourceRoot": "", "sources": ["../../../packages/kyadmin/src/utils/cookie.ts"], "names": [], "mappings": ";;;;;;;;;;;IAAA,+BAAgC;IAEhC,IAAI,MAAM,GAAG,KAAK,CAAC;IAEnB,SAAS,UAAU,CAAC,CAAK,IAAE,OAAO,OAAO,CAAC,KAAK,UAAU,CAAA,CAAA,CAAC;IAE1D,SAAS,MAAM,CAAC,CAAK;QACjB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,SAAS,MAAM,CAAC,CAAK;QACjB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,SAAS,oBAAoB,CAAC,KAAS;QACnC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,SAAS,gBAAgB,CAAC,CAAK;QAC3B,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACtB,+DAA+D;YAC/D,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAClE;QAED,IAAI;YACA,kDAAkD;YAClD,2DAA2D;YAC3D,0DAA0D;YAC1D,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;YAC/C,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1C;QAAC,OAAM,CAAC,EAAE,GAAE;IACjB,CAAC;IAED,SAAS,IAAI,CAAC,CAAK,EAAE,SAAc;QAC/B,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACjD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5D,CAAC;IAWD,SAAS,UAAU;QACf,OAAO,MAAM,CAAC,QAAQ,CAAC;IAC3B,CAAC;IAED,IAAI,MAAM,GAAU,UAAS,GAAU,EAAE,KAAU,EAAE,OAAY;QAE7D,QAAQ;QAER,IAAI,KAAK,KAAK,SAAS,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;YAC3C,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,EAAE,OAAO,CAAC,CAAC;YAElD,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;gBACrC,IAAI,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAC7D,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC;aACjC;YAED,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG;gBACtB,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,oBAAoB,CAAC,KAAK,CAAC;gBAC7C,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE;gBACnE,OAAO,CAAC,IAAI,CAAI,CAAC,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;gBAC/C,OAAO,CAAC,MAAM,CAAE,CAAC,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBACnD,OAAO,CAAC,MAAM,CAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;aACpC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SACf;QAED,OAAO;QAEP,IAAI,MAAM,GAAO,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAEtC,IAAI,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YACjC,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE7B,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,EAAE;gBACrB,+DAA+D;gBAC/D,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBAC7B,MAAM;aACT;YAED,oDAAoD;YACpD,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,SAAS,IAAI,MAAM,EAAE;gBACzD,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;aACzB;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAW,CAAC;IAEZ,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;IAErB,MAAM,CAAC,YAAY,GAAG,UAAU,GAAU,EAAE,OAAW;QACnD,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;YAC3B,OAAO,KAAK,CAAC;SAChB;QAED,2DAA2D;QAC3D,MAAM,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5D,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC,CAAC", "sourcesContent": ["import * as zutil from \"./zutil\"\r\n\r\nvar pluses = /\\+/g;\r\n\r\nfunction isFunction(o:any){return typeof o === \"function\"}\r\n\r\nfunction encode(s:any) {\r\n    return cookie.raw ? s : encodeURIComponent(s);\r\n}\r\n\r\nfunction decode(s:any) {\r\n    return cookie.raw ? s : decodeURIComponent(s);\r\n}\r\n\r\nfunction stringifyCookieValue(value:any) {\r\n    return encode(cookie.json ? JSON.stringify(value) : String(value));\r\n}\r\n\r\nfunction parseCookieValue(s:any) {\r\n    if (s.indexOf('\"') === 0) {\r\n        // This is a quoted cookie as according to RFC2068, unescape...\r\n        s = s.slice(1, -1).replace(/\\\\\"/g, '\"').replace(/\\\\\\\\/g, '\\\\');\r\n    }\r\n\r\n    try {\r\n        // Replace server-side written pluses with spaces.\r\n        // If we can't decode the cookie, ignore it, it's unusable.\r\n        // If we can't parse the cookie, ignore it, it's unusable.\r\n        s = decodeURIComponent(s.replace(pluses, ' '));\r\n        return cookie.json ? JSON.parse(s) : s;\r\n    } catch(e) {}\r\n}\r\n\r\nfunction read(s:any, converter?:any) {\r\n    var value = cookie.raw ? s : parseCookieValue(s);\r\n    return isFunction(converter) ? converter(value) : value;\r\n}\r\n\r\n\r\ninterface Cookie {\r\n    (key:string, value?:any, options?:any): string,\r\n    defaults:{},\r\n    raw:any,\r\n    json:boolean,\r\n    removeCookie(key:string, options?:any):void\r\n}\r\n\r\nfunction getDefault(){\r\n    return cookie.defaults;\r\n}\r\n\r\nlet cookie:Cookie = function(key:string, value?:any, options?:any) {\r\n\r\n    // Write\r\n\r\n    if (value !== undefined && !isFunction(value)) {\r\n        options = zutil.extend({}, getDefault(), options);\r\n\r\n        if (typeof options.expires === 'number') {\r\n            var days = options.expires, t = options.expires = new Date();\r\n            t.setTime(+t + days * 864e+5);\r\n        }\r\n\r\n        return (document.cookie = [\r\n            encode(key), '=', stringifyCookieValue(value),\r\n            options.expires ? '; expires=' + options.expires.toUTCString() : '', // use expires attribute, max-age is not supported by IE\r\n            options.path    ? '; path=' + options.path : '',\r\n            options.domain  ? '; domain=' + options.domain : '',\r\n            options.secure  ? '; secure' : ''\r\n        ].join(''));\r\n    }\r\n\r\n    // Read\r\n\r\n    var result:any = key ? undefined : {};\r\n    \r\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\r\n\r\n    for (var i = 0, l = cookies.length; i < l; i++) {\r\n        var parts = cookies[i].split('=');\r\n        var name = decode(parts.shift());\r\n        var cookie = parts.join('=');\r\n\r\n        if (key && key === name) {\r\n            // If second argument (value) is a function it's a converter...\r\n            result = read(cookie, value);\r\n            break;\r\n        }\r\n\r\n        // Prevent storing a cookie that we couldn't decode.\r\n        if (!key && (cookie = read(cookie)) !== undefined && result) {\r\n            result[name] = cookie;\r\n        }\r\n    }\r\n\r\n    return result;\r\n} as Cookie;\r\n\r\ncookie.defaults = {};\r\n\r\ncookie.removeCookie = function (key:string, options:any) {\r\n    if (cookie(key) === undefined) {\r\n        return false;\r\n    }\r\n\r\n    // Must not alter options, thus extending a fresh object...\r\n    cookie(key, '', zutil.extend({}, options, { expires: -1 }));\r\n    return !cookie(key);\r\n};"]}