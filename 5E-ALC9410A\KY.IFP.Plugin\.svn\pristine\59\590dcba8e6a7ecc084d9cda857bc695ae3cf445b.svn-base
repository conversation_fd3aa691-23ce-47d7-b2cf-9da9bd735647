;define([
"require",
"controllers/base",
"jquery"],
function(require,base,$,Grid){
	var defaultOption = {
		
	}
	return F.class(base,{
		name:"baseInfo",

		render:function(){
			this.base.apply(this,arguments);
		},

		init:function(){
			return this.base.apply(this,arguments);
		},
		
		formData:function(v){
			var rev = this.base.apply(this,arguments);
			if(arguments.length>0&&(v&&v.ywlx)){
				this.option.ywlx = v.ywlx;
			}else{
				if(rev){
					rev.ywlx = this.option.ywlx||"";
				}
			}
			return rev;
		},
		
		/**新增按钮事件.
		 * 
		 * */
		addFun:function(){
			var _this = this;
			//更改对应的界面操作
			_this.czbz = 1;
			//清空表格信息
			_this.grid&&_this.controls["grid"].clear();
			//清空对应的控件信息  手动清除数据
			_this.clear($("#layout-c")); 
			var treeObj = this.controls["tree"].ztree; 
			var nodes  = treeObj.getSelectedNodes();
			if(nodes.length > 0) {
				if(nodes[0].id == _this.ywlx) {
					this.controls["pgid"].value("");
				} else {
					this.controls["pgid"].value(nodes[0].data.gid);
				}
			}
			//启用保存按钮
			this.controls.saveBtn.disable(false); 
			//作废标志新增时默认当前
			this.controls.zfbz.value(0);
			//启用所有控件
			this.disable($("#layout-c"),false);
			
		},
		
		/**修改按钮事件.
		 * 
		 * */
		editFun:function(){
			var _this = this;
			//更改对应的界面操作
			_this.czbz = 2;
			//启用保存按钮
			_this.controls.saveBtn.disable(false);
			//启用所有控件
			_this.disable($("#layout-c"),false);
		},

		// 动态构建表格 .
		beforeRender:function(){
			var _this=this;
			_this.base();
		},
		//校验本单位
		checkBdwAdd:function(gid,ywlx,editmc){
			var _this = this;
			// 组装对应的数据信息
			var baseinfoVO = {
					gid:gid,
					ywlx:ywlx
			};
			
			return new Promise(function(resolve,reject){
				F.ajax({
					url:'/com.kysoft.service/html/baseinfo/checkBdwAdd.action',
					data: JSON.stringify(baseinfoVO),
					success: function (resp) {
						if (!F.common.isEmpty(resp)) {
							if(resp=="false"){
								_this.controls[editmc]&&_this.controls[editmc].disable();
								resolve(resp);
							}else{
								_this.controls[editmc]&&_this.controls[editmc].disable(false);
								resolve(resp);
							}
						}
		            }
				});
			});
		},
		
		beforeinitData:function(){
			var _this = this;
			var formdata=_this.formData();
			_this.ywlx = formdata.ywlx;
			return Promise.resolve(1);
		},
		
		beforebindEvent:function(){
			var _this = this;
			//修改按钮事件
			_this.bind("editBtn","click",function(){
				_this.editFun();
			});
			//新增事件
			_this.bind("addBtn","click",function(){
				_this.addFun();
			});
		},
		
		beforeOnLoad:function(){
			// 绑定事件
			var rev = this.beforeinitData();
			this.beforebindEvent();
			return rev;
		},
		afterOnLoad:function(){
			var _this = this;
			//业务类型
			_this.controls.ywlx&&_this.controls.ywlx.value(_this.ywlx);
		}
	})
});