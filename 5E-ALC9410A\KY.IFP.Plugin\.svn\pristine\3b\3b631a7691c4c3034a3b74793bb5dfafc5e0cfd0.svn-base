define(['require', 'controllers/base', 'jquery', 'jclass', 'iofp/common', 'iofp/api', 'platform/vue', 'util'], function (require, base, $, jclass, iofp, API, pVue, util) {
    return {
        el: '#app',
        data() {
            return {
                loading: false,
                productInfo: {
                    selIP: '',
                    selPort: '',
                    requestUrlIofp: '/com.kysoft.service/receive/receiveService.action',
                    result: '',
                }, // 产品信息
                rules: {
                    selIP: [{ required: true, message: '请输入配置中心IP', trigger: 'change' }],
                    selPort: [{ required: true, message: '请输入配置中心端口', trigger: 'change' }],
                    requestUrlIofp: [{ required: true, message: '请输入请求地址', trigger: 'change' }],
                },
            };
        },
        created() {
            this.initProductInfo();
        },

        methods: {
            // 初始化注册产品信息
            initProductInfo() {
                this.$set(this.productInfo, {
                    selIP: '',
                    selPort: '',
                    requestUrlIofp: '/com.kysoft.service/receive/receiveService.action',
                    result: '',
                });
            },

            // 提交注册
            onSubmit() {
                let _this = this;
                this.$refs['regProdInfoForm'].validate((valid) => {
                    if (valid) {
                        _this.loading = true;
                        API.GetAction('API/IFP/Client/Request/Register', this.productInfo)
                            .then((e) => {
                                _this.loading = false;
                                _this.$message.success(e);
                                this.$set(this.productInfo, 'result', e);
                                // setTimeout(() => {
                                //     _this.onCancel();
                                // }, 2000);
                            })
                            .catch(function (e) {
                                _this.loading = false;
                                _this.$message.error(e);
                            });
                    }
                });
            },

            // 取消
            onCancel() {
                this.$emit('cancel');
            },
        },
    };
    // return jclass(base, {
    //     name: 'regist2IofpConfig',

    //     bindEvent: function () {
    //         var _this = this;
    //         //确定
    //         _this.controls['okBtn'].bind('click', function () {
    //             _this.regFun();
    //             //注册
    //             //if(_this.czlx=="regBtn"){
    //             //	_this.regFun();
    //             //}
    //             ////路由请求
    //             //if(_this.czlx=="lyqqBtn"){
    //             //	_this.lyqqFun();
    //             //}
    //         });
    //     },

    //     //注册
    //     regFun: function () {
    //         var _this = this;
    //         F.util.showWait();
    //         F.ajax({
    //             url: '/API/IFP/Client/Request/Register',
    //             data: {
    //                 selIP: _this.controls.responseIP.value(),
    //                 selPort: _this.controls.responsePort.value(),
    //                 requestUrlIofp: _this.controls.requestUrlIofp.value(),
    //             },
    //             success: function (resp) {
    //                 F.util.hideWait();
    //                 _this.controls.result.value(resp);
    //             },
    //         });
    //     },

    //     onLoad: function () {
    //         var _this = this;
    //         var parameter = this.getDialogWindow().parameter;
    //         //产品注册码
    //         _this.regCode = parameter.regCode;
    //         //czlx：注册，路由请求
    //         _this.czlx = parameter.czlx;
    //         this.bindEvent();
    //     },
    // });
});
