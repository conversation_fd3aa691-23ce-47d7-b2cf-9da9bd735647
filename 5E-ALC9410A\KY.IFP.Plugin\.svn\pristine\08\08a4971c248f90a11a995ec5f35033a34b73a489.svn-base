﻿using COM.IFP.Common;
using COM.IFP.Common.Reflex;
using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using DAL.IFP.Rights;
using ORM.IFP.DbModel;
using ORM.IFP.www.DbModel.SM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace DAL.IFP.Job
{

    public class Job
    {

        /// <summary>
        /// 任务信息分页查询
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="paging"></param>
        /// <returns>分页数据集合</returns>
        public object JobList(IList<IFP_SM_JOBINFO> obj, PageModel paging)
        {
            using (var db = DB.Create())
            {
                var q = db.Queryable<IFP_SM_JOBINFO>().Query(obj.ToArray());
                if (paging == null)
                {
                    var res = q.ToList();
                    return res;
                }
                else
                {
                    var res = q.Fetch(paging);
                    return res;
                }
            }
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="searchVO">查询条件实体</param>
        /// <returns></returns>
        public List<IFP_SM_JOBINFO> QueryJobList(IFP_SM_JOBINFO searchVO)
        {
            using (var db = DB.Create())
            {
                List<IFP_SM_JOBINFO> pageStruct = db.Queryable<IFP_SM_JOBINFO>().Query(new IFP_SM_JOBINFO[] { searchVO }).ToList();
                return pageStruct;
            }
        }


        /// <summary>
        /// 获取状态为启用的任务列表
        /// </summary>
        /// <param name="searchVO"></param>
        /// <returns></returns>
        public List<IFP_SM_JOBINFO> JobAllList()
        {
            using (var db = DB.Create())
            {
                //var q = dbHelper.dbClient.Queryable<IFP_SM_JOBINFO>().Where(a => a.Status == 1
                //&& (searchVO.CreateType == 0 ? (a.CreateType == null || a.CreateType == searchVO.CreateType) : a.CreateType == searchVO.CreateType));
                //pageStruct = pageStruct.Where(it => it.Status == 1&&(searchVO.C)).ToList<IFP_SM_JOBINFO>();
                var q = db.Queryable<IFP_SM_JOBINFO>().Where(x => x.Status == 1);
                List<IFP_SM_JOBINFO> pageStruct = q.ToList();
                return pageStruct;
            }
        }


        /// <summary>
        /// 根据任务id获取任务实体对象
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        public IFP_SM_JOBINFO QueryJobByGid(string Gid)
        {
            using (var db = DB.Create())
            {
                IFP_SM_JOBINFO job = db.Queryable<IFP_SM_JOBINFO>().Where(x => x.Gid == Gid).First();
                //方法有参数，才去请求数据库获取参数.
                List<IFP_SM_JOBPARAM> tmp = this.GetParamList(job);
                if (tmp != null && tmp.Count > 0)
                {
                    var q = db.Queryable<IFP_SM_JOBPARAM>().Where(a => a.PGid == Gid).OrderBy(x => x.Sort);
                    job.paramlist = q.ToList();
                }
                return job;
            }
        }

        /// <summary>
        /// 根据名称查询任务.
        /// </summary>
        /// <param name="Name"></param>
        /// <returns></returns>
        public IFP_SM_JOBINFO QueryJobByName(string Name)
        {
            using (var db = DB.Create())
            {
                var q = db.Queryable<IFP_SM_JOBINFO>().Where(a => a.JobName == Name).OrderBy(x => x.CreateTime);
                List<IFP_SM_JOBINFO> joblist = q.ToList();
                if (joblist == null || joblist.Count == 0)
                {
                    return null;
                }
                IFP_SM_JOBINFO job = joblist[0];
                var q1 = db.Queryable<IFP_SM_JOBPARAM>().Where(a => a.PGid == job.Gid).OrderBy(x => x.Sort);
                job.paramlist = q1.ToList();
                return job;
            }
        }

        /// <summary>
        /// 判断当前任务名称是否在数据库存在
        /// </summary>
        /// <param name="user">用户实体对象</param>
        /// <returns>是否存在</returns>
        private bool CheckJobNameExist(IFP_SM_JOBINFO job)
        {
            using (var db = DB.Create())
            {
                var q = db.Queryable<IFP_SM_JOBINFO>().Where(a => a.JobName == job.JobName && a.Gid != job.Gid);
                if (!job.Gid.HasValue)
                    q = db.Queryable<IFP_SM_JOBINFO>().Where(a => a.JobName == job.JobName);
                return q.Count() > 0;
            }
        }

        /// <summary>
        /// 保存或者修改任务, 根据GID是否有值
        /// </summary>
        /// <param name="jtoken"></param>
        /// <returns></returns>
        public PFActionResult SaveOrUpdateJob(IFP_SM_JOBINFO entity)
        {
            PFActionResult result = new PFActionResult();
            using (var db = DB.Create())
            {
                try
                {
                    // 开启事务
                    db.Ado.BeginTran();

                    BaseDbHelper<IFP_SM_JOBINFO> dbHelper = new BaseDbHelper<IFP_SM_JOBINFO>(db);
                    var tmp = string.IsNullOrEmpty(entity.Gid.Value) ? null : dbHelper.QueryById(entity.Gid);
                    bool isAdd = tmp == null;

                    // 检查运行状态
                    if (entity.Status == 1)
                    {
                        result.success = false;
                        result.msg = "该任务正在运行中，为防止出现异常情况，请先停止后再进行修改。";
                        return result;
                    }

                    // 检查任务名称是否存在
                    if (CheckJobNameExist(entity))
                    {
                        result.success = false;
                        result.msg = "保存失败，原因：任务名称已存在";
                        return result;
                    }

                    if (isAdd)
                    {
                        // 新增任务
                        string userID = string.Empty;
                        try
                        {
                            userID = UserCache.GetUserName();
                        }
                        catch (Exception e)
                        {
                            LoggerHelper.Error(ErrorList.E2003, "", e);
                        }
                        entity.Creator = userID;
                        entity.Status = 0;
                        entity.Gid = Guid.NewGuid().ToString();
                        entity.CreateTime = DateTime.Now;
                        entity.Delt = 0;
                        // 保存主表数据
                        dbHelper.saveEntity(entity);

                        // 保存参数
                        if (entity.paramlist?.Count > 0)
                        {
                            for (int k = 0; k < entity.paramlist.Count; k++)
                            {
                                var param = entity.paramlist[k];
                                param.Sort = k + 1;
                                param.PGid = entity.Gid;
                                // 确保每个参数都有唯一的Gid
                                if (string.IsNullOrEmpty(param.Gid.Value))
                                {
                                    param.Gid = Guid.NewGuid().ToString("N");
                                }
                                dbHelper.saveEntity<IFP_SM_JOBPARAM>(param);
                            }
                        }
                    }
                    else
                    {
                        // 更新任务
                        var entity1 = dbHelper.QueryById<IFP_SM_JOBINFO>(entity.Gid);
                        entity.Creator = entity1.Creator;
                        entity.CreateTime = entity1.CreateTime;
                        entity.Delt = entity1.Delt;

                        // 更新主表
                        dbHelper.UpdateEntity(entity);

                        // 先删除原有参数
                        db.Deleteable<IFP_SM_JOBPARAM>().Where(a => a.PGid == entity.Gid).ExecuteCommand();

                        // 重新插入参数
                        if (entity.paramlist?.Count > 0)
                        {
                            for (int k = 0; k < entity.paramlist.Count; k++)
                            {
                                var param = entity.paramlist[k];
                                param.Sort = k + 1;
                                param.PGid = entity.Gid;
                                // 确保每个参数都有唯一的Gid
                                if (string.IsNullOrEmpty(param.Gid.Value))
                                {
                                    param.Gid = Guid.NewGuid().ToString("N");
                                }
                                dbHelper.saveEntity<IFP_SM_JOBPARAM>(param);
                            }
                        }

                        // 更新调度任务
                        QuartzUtil.UpdateJob(entity);
                    }

                    // 提交事务
                    db.Ado.CommitTran();

                    result.success = true;
                    result.msg = "保存成功";
                    result.data = entity;
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    db.Ado.RollbackTran();
                    result.success = false;
                    result.msg = "保存失败，原因：" + ex.Message;
                    LoggerHelper.Error("保存调度任务失败", ex);
                }
            }

            return result;
        }

        public PFActionResult DelJob(string objId)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    // 开启事务
                    db.Ado.BeginTran();

                    // 先查询任务信息
                    var jobInfo = db.Queryable<IFP_SM_JOBINFO>()
                                   .Where(a => a.Gid == objId)
                                   .First();

                    if (jobInfo != null)
                    {
                        // 如果任务正在运行，先停止任务
                        if (jobInfo.Status == 1)
                        {
                            // 停止定时任务
                            QuartzUtil.StopJob(jobInfo);
                        }

                        // 删除相关的参数记录
                        db.Deleteable<IFP_SM_JOBPARAM>()
                          .Where(a => a.PGid == objId)
                          .ExecuteCommand();

                        // 删除主任务记录
                        db.Deleteable<IFP_SM_JOBINFO>()
                          .Where(a => a.Gid == objId)
                          .ExecuteCommand();

                        // 提交事务
                        db.Ado.CommitTran();

                        result.success = true;
                        result.msg = "删除成功";
                    }
                    else
                    {
                        result.success = false;
                        result.msg = "未找到指定的任务";
                    }
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    db.Ado.RollbackTran();

                    result.success = false;
                    result.msg = "删除失败，原因：" + ex.Message;
                    LoggerHelper.Error("删除调度任务失败", ex);
                }
            }
            return result;
        }

        /// <summary>
        /// 获取方法参数
        /// </summary>
        /// <param name="jtoken"></param>
        /// <returns></returns>
        public List<IFP_SM_JOBPARAM> GetParamList(IFP_SM_JOBINFO job)
        {
            //获取程序集所在的目录
            ReflexCustom Refcstm = new ReflexCustom();
            Refcstm.DLLPath = job.DLLPath.Value;
            Refcstm.MethodPath = job.ClassPath.Value;
            Refcstm.MethodName = job.MethodName.Value;
            //调方法名。
            Type RefT = ReflexCustom.GetReflexType(Refcstm);
            //创建实例
            Object obj = Activator.CreateInstance(RefT);
            //进参与反参必须是JToken
            MethodInfo method = RefT.GetMethod(Refcstm.MethodName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
            ParameterInfo[] paramsInfo = method.GetParameters();//得到指定方法的参数列表   
            List<IFP_SM_JOBPARAM> target = new List<IFP_SM_JOBPARAM>();
            foreach (var i in paramsInfo)
            {
                IFP_SM_JOBPARAM tmp = new IFP_SM_JOBPARAM();
                tmp.Gid = Guid.NewGuid().ToString("N");
                tmp.PGid = job.Gid;
                tmp.ParamName = i.Name;
                tmp.ParamType = i.ParameterType.ToString();
                target.Add(tmp);
            }

            return target;
        }

        /// <summary>
        /// 开始任务.
        /// </summary>
        /// <param name="job"></param>
        public void StartJob(IFP_SM_JOBINFO job)
        {
            IFP_SM_JOBINFO tmp = this.QueryJobByGid(job.Gid.Value);
            QuartzUtil.StartJob(tmp);
            tmp.Status = 1;
            using (var db = DB.Create())
            {
                db.Updateable<IFP_SM_JOBINFO>().Where(p => p.Gid == tmp.Gid)
                    .SetColumns(p => p.Status, tmp.Status).ExecuteCommand();
            }
        }

        /// <summary>
        /// 停止任务.
        /// </summary>
        /// <param name="job"></param>
        public void StopJob(IFP_SM_JOBINFO job)
        {
            IFP_SM_JOBINFO tmp = this.QueryJobByGid(job.Gid.Value);
            QuartzUtil.StopJob(tmp);
            tmp.Status = 0;
            using (var db = DB.Create())
            {
                db.Updateable<IFP_SM_JOBINFO>().Where(p => p.Gid == tmp.Gid)
                    .SetColumns(p => p.Status, tmp.Status).ExecuteCommand();
            }
        }
    }
}
