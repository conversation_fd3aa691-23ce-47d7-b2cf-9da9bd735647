﻿<!DOCTYPE html >
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EXCEL导出</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item" id="app">
        <div class="toolbar">
            <ifp-button-lxsz table="tableLmtj"></ifp-button-lxsz>
            <el-button icon="kjicon kjicon-daochu" @click="onExcelExportClick">导出</el-button>
            <el-button icon="kjicon kjicon-tuichu" @click="onExit">退出</el-button>
        </div>
        <div class="searchbar">
            运输方式：<ifp-select-ywlx v-model="filter.Ysfs1001" :ywlx="1001"></ifp-select-ywlx>
            <span style="padding-left:20px">
                <el-button icon="el-icon-search" @click="onSelect">查询</el-button>
            </span>
        </div>
        <div class="flex-item padding">
            <ifp-table ref="tableLmtj"
                       :data="tableData"
                       height="100%"
                       row-key="Gid"
                       size="medium"
                       :border="true"
                       :highlight-current-row="true">
                <el-table-column type="index"
                                 label="序号"
                                 width="50">
                </el-table-column>
                <el-table-column type="selection"
                                 width="50">
                </el-table-column>
                <el-table-column prop="Kd4001"
                                 label="矿点">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.Kd4001" :ywlx="4001" :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                    </template>
                </el-table-column>
                <el-table-column prop="Mz4010"
                                 label="煤种">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.Mz4010" :ywlx="4010" :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                    </template>
                </el-table-column>
                <el-table-column prop="Pcbm"
                                 width="150"
                                 :sortable="true"
                                 label="批次编码">
                </el-table-column>
                <el-table-column label="水份"
                                 align="center">
                    <el-table-column label="全水"
                                     align="center">
                        <el-table-column prop="Mar"
                                         width="80"
                                         align="right"
                                         label="Mar(%)">
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="内水"
                                     align="center">
                        <el-table-column prop="Mad"
                                         width="80"
                                         align="right"
                                         label="Mad(%)">
                        </el-table-column>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="灰分"
                                 align="center">
                    <el-table-column label="收到基灰分"
                                     align="center">
                        <el-table-column prop="Aar"
                                         width="100"
                                         align="right"
                                         label="Aar(%)">
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="空干基灰分"
                                     align="center">
                        <el-table-column prop="Aad"
                                         width="100"
                                         align="right"
                                         label="Aad(%)">
                        </el-table-column>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="热值"
                                 align="center">
                    <el-table-column label="收到基高位热值"
                                     align="center">
                        <el-table-column prop="QgrarMJ"
                                         align="right"
                                         width="120"
                                         label="Qgr.ar(MJ/kg)">
                        </el-table-column>
                        <el-table-column prop="QgrarKcal"
                                         align="right"
                                         width="130"
                                         label="Qgr.ar(Kcal/kg)">
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="收到基低位热值"
                                     align="center">
                        <el-table-column prop="QnetarMJ"
                                         align="right"
                                         width="120"
                                         label="Qnet.ar(MJ/kg)">
                        </el-table-column>
                        <el-table-column prop="QnetarKcal"
                                         align="right"
                                         width="130"
                                         label="Qnet.ar(Kcal/kg)">
                        </el-table-column>
                    </el-table-column>
                </el-table-column>
                <el-table-column prop="Gyzl"
                                 align="right"
                                 width="100"
                                 label="供应总量">
                </el-table-column>
                <el-table-column prop="Gyzb"
                                 align="right"
                                 width="80"
                                 label="供应占比">
                </el-table-column>
                <el-table-column label="对比差异"
                                 align="center">
                    <el-table-column label="水份"
                                     align="center">
                        <el-table-column label="全水"
                                         property="MarC"
                                         align="center">
                        </el-table-column>
                        <el-table-column label="内水"
                                         property="MadC"
                                         align="center">
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="热值"
                                     align="center">
                        <el-table-column label="收到基低位热值"
                                         property="Rzsdjdw"
                                         align="center">
                        </el-table-column>
                        <el-table-column label="收到基高位热值"
                                         property="Rzsdjgw"
                                         align="center">
                        </el-table-column>
                    </el-table-column>
                </el-table-column>
                <el-table-column prop="Jszt"
                                 align="center"
                                 width="80"
                                 label="结算状态">
                    <template slot-scope="scope">
                        <div v-if="scope.row.Jszt==1">未结算</div>
                        <div v-else>已结算</div>
                    </template>
                </el-table-column>
                <el-table-column prop="Czr"
                                 align="left"
                                 width="100"
                                 label="操作人">
                </el-table-column>
                <el-table-column prop="Czsj"
                                 align="center"
                                 width="180"
                                 label="操作时间">
                </el-table-column>
            </ifp-table>
        </div>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>