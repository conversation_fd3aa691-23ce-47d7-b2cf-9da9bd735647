define([
    "iofp/services/service.card",
	"crypto-js/md5",
	"layer",
	"jquery",
	"jquery.cookie",
	"css!/kyadmin/lib/bower/bootstrap/css/bootstrap.min.css",
	"css!/kyadmin/lib/bower/layer/theme/default/layer.css",
	"css!/kyadmin/lib/kjlib/bootstrap-theme-kykj/iconfont/dicb2/iconfont.css"
], function (cardService,MD5 ,layer){
	var loading = null;

	return function(){
		showLoading();
		var id = queryString("id");
		var pw = queryString("pwd");

		/*
		if(window.applicationCache){
			window.addEventListener("load", function(e) {
				window.applicationCache.addEventListener("updateready", function(e) {
					if (window.applicationCache.status == window.applicationCache.UPDATEREADY) {
						window.applicationCache.swapCache();
						window.location.reload();
					} else {
						//onloadHandle();
					}
				}, false);
			}, false);
		}
		*/

		if(id&&pw){
			loginCheck({
				userId:id,
				passWord:MD5(pw).toString()
			},function(data){
				var root = window.location.pathname.split("/").slice(0,2).join("/")+"/";		
				window.location=data.url;
			},log);
		}

		return {
			el:"#app",
			data:function(){
				return {
					PWDFROMECOOKIE:false,
					LoginPic:"",
					MainName:"",
					Name:"",
					userId:id||"",
					passWord:"",
					remamber:false,
					hash:"#zh",
					log_sk:"刷卡准备中", // 刷卡信息
					resk_enabled: false,// 启用重新刷卡按钮
					userlist: []
				}
			},
			mounted:function(){
				$(document.body).addClass("loaded");
			},
			created:function () {
				initResize();
				var self = this;
				this.hash = document.location.hash || "#zh"
				var data = getCookie();
				if(data.remamber){
					this.remamber = data.remamber;
					this.userId = data.userId;
					this.passWord = data.passWord;
					this.PWDFROMECOOKIE=true;
					this.$nextTick(function(){
						self.focus("btnSubmit");
					})
				}else{
					this.$nextTick(function(){
						self.focus("input_userId");
					})
				}
				if (this.hash == "#sk") {
					this.duka();
                }
				var autoreload = false;
				var run = function(){
					self.initPage()
					.then(function(){
						hideLoading();
					})
					.catch(function(err){
						//if(err && err.readyState===0){
							if(autoreload){
								layer.msg('连接失败，3秒钟后继续尝试重新连接');
								setTimeout(run,3000)
								return;
							}

							layer.confirm('服务器连接失败，请选择操作？', {
							  btn: ['自动重连',"取消"] //按钮
							}, function(id){
								autoreload=true;
								layer.close(id);
								run();
							}, function(id){
								layer.close(id);
								hideLoading();
							});
						//}
					})
				}
				run();
			},
			methods:{
				initPage:function () {
					var self = this;
					GetUser().then(function (data) {
						self.userlist = data;
					})

					return setProductInfo()
					.then(function (data) {
						hideLoading();
						self.LoginPic = data.LoginPic;
						self.MainName = data.MainName;
						self.Name = data.Name;
						

						// 读卡登录
						if(this.hash==="#sk"){
							return this.duka()
							.catch(log);
						}
						
					})
				},
				duka: function () {
					var _this = this;
					return cardService.ClientReadConfig().then(function (result1) {
						_this.log_sk = "读卡中...";
						cardService.ReadCard(result1).then(function (result2) {
							if (result2.success) {
								CardLogin(result2.data.CardNo).then(function (result3) {
									if (result3.msg != "") {
										_this.log_sk = "读卡失败，原因：" + result3.msg;
										_this.resk_enabled = true;
										log(result3.msg, function () {
											//window.location = result3.url;
										});
									} else if (queryString("f")) {
										_this.log_sk = "读卡成功";
										_this.resk_enabled = false;
										window.location = window.decodeURIComponent(queryString("f"));
									} else {
										_this.log_sk = "读卡成功";
										_this.resk_enabled = false;
										window.location = result3.url;
									}
								})
							} else {
								_this.log_sk = "读卡失败，原因：" + result2.msg;
								_this.resk_enabled = true;
							}
						})
					}).then(function () {
						
						// 刷卡成功
					})
						//.then(function (result) {
						//	alert(JSON.stringify(data));
						//})
					
				},
				chceckinput:function () {
					_this = this;
					if(this.userId==""){ 
						log("账号不能为空",function(){ _this.focus("input_userId")})
						return false;
					}
					if(this.passWord==""){
						log("密码不能为空",function(){ _this.focus("input_pwd")})
						return false;
					}
					return true;
				},
				login:function() {
					if(!this.chceckinput()){return;}
					loginCheck({
						userId:this.userId,
						passWord:this.PWDFROMECOOKIE?this.passWord:MD5(this.passWord).toString(),
						i18n:null,
						remamber:this.remamber
					},function(data){
						if(data.msg!=""){
							log(data.msg,function(){ window.location=data.url; });
						}else if(queryString("f")){
							window.location = window.decodeURIComponent(queryString("f"));
						}else{
							window.location=data.url;
						}
					},log);
				},
				focus:function(refid) {
					$(this.$refs[refid]).focus();
				},
				select:function (refid) {
					$(this.$refs[refid]).select();
				}
			}
		}
	}
	function initResize(){

		window.onresize = function(){
			var sh = 800;//设计最佳高度
			var sw = 1280;//设计最佳宽度
			var wh = $(window).height();
			var ww = $(window).width();
			var b = wh/ww>sh/sw?ww/sw:wh/sh;
			document.body.style.zoom = b;//b>1?b:1;
		}
		window.onresize();
	}

	function CardLogin(cardno) {
		return new Promise(function (resolve, reject) {
			$.ajax({
				url: "/API/IFP/Rights/User/Login",
				data: JSON.stringify({ CardNo: cardno, loginType: "card"}),
				type: "post",
				dataType: "json",
				contentType: "application/json",
				success: function (data, textStatus, jqXHR) {
					try {
						resolve(data);
					}
					catch (ex) {
						log("服务器异常");
						error("请求失败，获取到的数据为：" + data);
						reject();
					}
					//console.log(data);
				},
				error: function () {
					error("请求失败，请联系管理员");
				}
			});
		})
	}

	function GetUser() {
		return new Promise(function (resolve, reject) {
			$.ajax({
				url: "/API/IFP/Rights/User/GetUserList",
				data: JSON.stringify({}),
				type: "post",
				dataType: "json",
				contentType: "application/json",
				success: function (data, textStatus, jqXHR) {
					try {
						resolve(data);
					}
					catch (ex) {
						log("服务器异常");
						error("请求失败，获取到的数据为：" + data);
						reject();
					}
					//console.log(data);
				},
				error: function () {
					error("请求失败，请联系管理员");
				}
			});
		})
	}

	function showLoading() {
		if(!loading){
			loading = layer.load(2, {shade: [0.4, '#393D49'],shadeClose: false});
		}
	}
	function hideLoading() {
		if(loading){layer.close(loading);loading=null}
	}

	
	function loginCheck(pdata,callback,error){
		/**
		 * @update 登录
		 */
		if(window._http_login_ == null || window._http_login_ == ""){
			window._http_login_ = "/API/IFP/Rights/User/Login";//本地登录
			//window._http_login_ = "/www/Login/remoteLogin";//远程登录，调用接口0110
			
		}
		pdata.loginType = "local";
		var param = JSON.stringify(pdata);
		$.ajax({
			url: window._http_login_,
			data: param	,
			type: "post",
			dataType: "json",
			contentType: "application/json",
			success: function (data, textStatus, jqXHR) {
				if(data&&data.ErrorMsg){
					error(data.ErrorMsg);
					return;
				}
				try {
					//data = JSON.parse(data);
					if (data._data) {
						data = JSON.parse(data._data);
					}
				}
				catch (ex) {
					error("服务器异常");
					console && console.error("请求失败，获取到的数据为：" + data);
				}
				if (data.code == "2") {
					error("用户名或密码错误,请重新输入。", function () {
						setTimeout(function(){
							$("#userId").focus();
						},100)
					});
				} else if (data.code == "0") {
					setCookie(pdata);
					callback(data);
				}
			}

		});
	};
	
	function getCookie(){
		return {
			userId:$.cookie("userId"),
			passWord:$.cookie("passWord"),
			remamber:$.cookie("remamber")
		}
	}
	
	function setCookie(data){
		if(data["remamber"]){
			$.cookie("userId",data.userId);
			$.cookie("passWord",data.passWord);
			$.cookie("remamber",data.remamber);
		}else{
			$.removeCookie("userId");
			$.removeCookie("passWord");
			$.removeCookie("remamber");
		}
	}
	
	function log(msg,callback){
		var lid = layer.open({
			title:"提示",
			content: msg,
			yes: function(index, layero){
				//do something
				layer.close(index); //如果设定了yes回调，需进行手工关闭
				callback&&callback();
				console.log("yes")
			},
			success: function(layero, index){
				layero.find(".layui-layer-btn0").attr("tabindex",1).on("keydown",function(event){
					if(event.keyCode==13){
						$(this).click();
						// 停止事件捕获
						event.stopImmediatePropagation();
						event.stopPropagation();
						return false;
					}
				}).focus()
			}
		});
	}

	
	function queryString(name) {
	    var url = window.location.search ;
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = url.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
	}
	
	function setProductInfo() {
		return new Promise(function(resolve,reject) {
			$.ajax({
				url: "/API/IFP/Rights/User/ProductSetting",
				//data: JSON.stringify({ "test": "test" }),
				data : null,
				type: "post",
				dataType: "json",
				contentType: "application/json",
				success: function (data, textStatus, jqXHR) {
					try {
						resolve(data);
					}
					catch (ex) {
						log("服务器异常");
						console && console.error("请求失败，获取到的数据为：" + data);
						reject();
					}
					console.log(data);
				},
				error: function (err) {
					reject(err);
				}
			});	
		})
	}
	
})