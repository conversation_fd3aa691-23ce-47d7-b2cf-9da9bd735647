define(["service"], function (Service) {
    var service = Service.createServices({
        //获取任务列表.
        jobList: {
            url: "/API/IFP/Job/Job/JobList",
            dataType: "json",
            contentType: "application/json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        },
        //获取任务列表.
        QueryJobList: {
            url: "/API/IFP/Job/Job/QueryJobList",
            dataType: "json",
            contentType: "application/json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        },
        //保存任务.
        saveJob: {
            url: "/API/IFP/Job/Job/SaveOrUpdateJob",
            contentType: "application/json",
            dataType: "json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        },
        //删除任务.
        delJob: {
            url: "/API/IFP/Job/Job/DelJob",
            contentType: "application/json",
            dataType: "json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        },
        //获取单个任务.
        queryJobByGid: {
            url: "/API/IFP/Job/Job/QueryJobByGid",
            contentType: "application/json",
            dataType: "json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        },
        //获取方法参数列表.
        getParamList: {
            url: "/API/IFP/Job/Job/GetParamList",
            contentType: "application/json",
            dataType: "json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        },
        //开始任务
        startJob: {
            url: "/API/IFP/Job/Job/StartJob",
            contentType: "application/json",
            dataType: "json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        },
        //停止任务
        stopJob: {
            url: "/API/IFP/Job/Job/StopJob",
            contentType: "application/json",
            dataType: "json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        },
        //立即执行任务
        runJob: {
            url: "/API/IFP/Job/Job/RunJob",
            contentType: "application/json",
            dataType: "json",
            props: function (data) {
                return data;
            },
            then: function (data) {
                return data;
            },
            usemock: false
        }
    });

    return service;
})
