﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Data;
//using LinqToDB.Mapping;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{
    /// <summary>
    /// 煤场信息
    /// </summary>
    [SugarTable("IFP_BS_YWDX4012")]
    public partial class YWDX4012 : BaseData<YWDX4012>
    {
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public override Field<long> Gid { get; set; }

        [SugarColumn(ColumnName = "QB4007", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Qb4007 { get; set; }

        [SugarColumn(ColumnName = "MCLX1008", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Mclx1008 { get; set; }

        [SugarColumn(ColumnName = "SFFC", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Sffc { get; set; }

        [SugarColumn(ColumnName = "SFLX", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Sflx { get; set; }

        [SugarColumn(ColumnName = "XLENGTH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Xlength { get; set; }

        [SugarColumn(ColumnName = "WIDTH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Width { get; set; }

        [SugarColumn(ColumnName = "HEIGHT", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Height { get; set; }

        [SugarColumn(ColumnName = "RADIUS", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Radius { get; set; }

        [SugarColumn(ColumnName = "MCRL", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Mcrl { get; set; }

        [SugarColumn(ColumnName = "CKGZ1006", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Ckgz1006 { get; set; }

        [SugarColumn(ColumnName = "GDQSZ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Gdqsz { get; set; }

        [SugarColumn(ColumnName = "GDKD", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,3)")]
        public Field<decimal> Gdkd { get; set; }
    }

    //[Table(Name = "IFP_BS_YWDX4012")]
    //public partial class YWDX4012 : BaseData<YWDX4012>
    //{

    //	[Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.Int64, DbType = "bigint")]
    //	public override Field<long> Gid { get; set; }

    //	[Column(Name = "QB4007", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Qb4007 { get; set; }

    //	[Column(Name = "MCLX1008", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Mclx1008 { get; set; }

    //	[Column(Name = "SFFC", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> Sffc { get; set; }

    //	[Column(Name = "SFLX", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> Sflx { get; set; }

    //	[Column(Name = "XLENGTH", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //	public Field<decimal> Xlength { get; set; }

    //	[Column(Name = "WIDTH", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //	public Field<decimal> Width { get; set; }

    //	[Column(Name = "HEIGHT", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //	public Field<decimal> Height { get; set; }

    //	[Column(Name = "RADIUS", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //	public Field<decimal> Radius { get; set; }

    //	[Column(Name = "MCRL", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //	public Field<decimal> Mcrl { get; set; }

    //	[Column(Name = "CKGZ1006", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Ckgz1006 { get; set; }

    //	[Column(Name = "GDQSZ", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Gdqsz { get; set; }

    //	[Column(Name = "GDKD", DataType = DataType.Decimal, DbType = "decimal(18, 3)")]
    //	public Field<decimal> Gdkd { get; set; }
    //}

}
