define(["lodop"],function(LodopFuncs){

    // 生成打印 html
    async function print(html){

        /**
         * Lodop 打印控件对象
         * @官网 [官方文档](http://www.lodop.net/LodopDemo.html)
         * @API https://docs.qq.com/doc/DSFREekJOZ3RxZWhE
         * @文档 https://docs.qq.com/doc/DSFVIQ1hNUlNUd25w
         **/
        const LODOP = await LodopFuncs.loadLodop()

        // 初始化打印对象
        LODOP.PRINT_INIT("打印控件功能演示_Lodop功能_分页打印综合表格");


        /**
         * 
         * 数字型，0--普通项 1--页眉页脚 2--页号项 3--页数项 4--多页项 
         * 缺省（不调用本函数时）值0。普通项只打印一次；页眉页脚项则每页都在固定位置重复打印；
         * 页号项和页数项是特殊的页眉页脚项，其内容包含当前页号和全部页数；多页项每页都打印，直到把内容打印完毕，打印时在每页上的位置和区域大小固定一样（多页项只对纯文本有效）
         */
         //LODOP.SET_PRINT_STYLEA(0,"ItemType",0);

        // 输出一个 table html
        LODOP.ADD_PRINT_HTM('1cm',"1cm","RightMargin:1cm","BottomMargin:1cm",html);
        //LODOP.SET_PRINT_STYLEA(0,"LinkedItem",0); // 关联对象

        // 设置 table 打印样式 数字型，0--上边距锁定 1--下边距锁定 2--垂直方向居中 3--上边距和下边距同时锁定（中间拉伸），缺省值是0。
        LODOP.SET_PRINT_STYLEA(0,"Vorient",3);	


        
        //LODOP.SET_PRINT_STYLEA(0,"ItemType",0);
        //LODOP.SET_PRINT_STYLEA(0,"LinkedItem",1);	
        
        // 打印 页号
        LODOP.ADD_PRINT_HTM(1,'90%','RightMargin:10mm',100,"<font color='#0000ff' format_bak='ChineseNum'><span tdata='pageNO'>#</span>/<span tdata='pageCount'>#</span></font>");
        // 页号设置为每页展示
        LODOP.SET_PRINT_STYLEA(0,"ItemType",1);		// 0--普通项 1--页眉页脚 2--页号项 3--页数项 4--多页项

        LODOP.PREVIEW();
    }

    


    return {
        el:"#app",
        data(){
            return {
                list:[
                    {gid:"111",name:"张三"}
                ]
            }
        },
        methods:{
            print(){
                print(this.$refs.print.innerHTML);
            }
        }
    }
})