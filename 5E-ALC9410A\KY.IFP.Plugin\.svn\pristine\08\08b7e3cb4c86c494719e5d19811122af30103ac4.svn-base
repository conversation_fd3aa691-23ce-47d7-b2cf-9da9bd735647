﻿define(["iofp/api",
    "moment",
    "iofp/components/iofpsocket",
    "iofp/lib/excelExportElement",], function (API, moment, iofpsocket, excelExport) {


        return {
            el: "#app",
            data() {
                return {
                    disabled: {
                        alarmCleared: true
                    },
                    bjjbList: [
                        { id: 1, text: '警告报警' },
                        { id: 2, text: '次要报警' },
                        { id: 3, text: '主要报警' },
                        { id: 4, text: '严重报警' },
                    ],
                    bjztList: [

                        { id: 1, text: '报警中' },
                        { id: 2, text: '已解除' },
                    ],

                    filter: {
                        start: null,
                        end: null,
                        AlarmCode: null,
                        AlarmLevel: null,
                        AlarmStatus: null,
                    },

                    data: [
                        {}
                    ],
                    paging: {
                        size: 100,
                        page: 1,
                        records: 100,
                    },

                    loading: false,//
                    ctrlstate: 0,
                }
            },

            methods: {
                indexMethod: function (index) {
                    let curpage = this.paging.page;   //单前页码，具体看组件取值
                    let limitpage = this.paging.size;    //每页条数，具体是组件取值
                    return (index + 1) + (curpage - 1) * limitpage;
                },


                onSelect: function () {

                    let param = [
                        {
                            CreateTime: { Value: moment(this.filter.start).startOf("day").format(), Match: '>=', Order: 'DESC', },
                            AlarmCode: { Value: this.filter.AlarmCode, Match: 'HAS' },
                            AlarmLevel: { Value: this.filter.AlarmLevel, Match: '==' },
                            AlarmStatus: { Value: this.filter.AlarmStatus, Match: '==' },
                        },
                        {
                            CreateTime: { Value: moment(this.filter.end).startOf("day").add(1, "days").format(), Match: '<' },
                        }
                    ];
                    this.loading = true;
                    //todo
                    return API.GetAction("/API/IFP/Alarm/HistoryAlarmInfo/HistoryAlarmInfoList", {
                        "filter": param, "paging": this.paging
                    }).then(x => {
                        this.loading = false;
                        this.$set(this, "data", x.rows);
                        this.paging.records = x.records;

                        //if (x.success) {

                        //} else {
                        //    this.$message.error("查询失败");

                        //}
                    }).catch(e => {
                        this.loading = false;
                        this.$message.error("查询出错");
                        console.error(e);
                    });
                },
                //报警解除事件
                alarmClear: function () {
                    if (this.ctrlstate != 1) return;
                    let cmd = 'DEBUGGING';
                    Parameter = { PointList: [{ P: 'Man_故障复位', V: true }] };
                    var content = common.createCommandArgs({ CMD: cmd, Parameter: Parameter });
                    iofpsocket.sendCommand("SControl", content);
                },


                //导出
                onExport: function () {
                    /*
                     * 指定列模型
                     * DataType：数据类型（1 字符, 2 数字, 3 时间）
                     * Align：对齐方式（1 left, 2 center, 3 right）
                     * DecimalDigits：小数位
                     * Ywlx：基础资料的类型
                     * Formater：格式转换符号(percent：百分比格式，thousands：千分符显示，useridtoname：用户登录账号转名称)
                     * Data：自定义下拉控件的选项值
    */
                    var colModels = [
                        { Name: "AlarmCode", DataType: 1, Align: 2 },
                        { Name: "AlarmName", DataType: 1, Align: 2 },
                        { Name: "CreateTime", DataType: 3, Align: 2 },
                        { Name: "RemoveTime", DataType: 3, Align: 2, Formater: "YYYY-MM-DD HH:mm:SS" },
                        { Name: "RemoveUser", DataType: 1, Align: 2 },
                        { Name: "AlarmLevel", DataType: 1, Align: 2, Data: this.bjjbList },
                        { Name: "AlarmStatus", DataType: 1, Align: 2, Data: this.bjztList },
                        //{ Name: "YTSL", DataType: 2, Align: 3, DecimalDigits: 0 },
                        ////{ Name: "ZYLYL", DataType: 2, Align: 3, DecimalDigits: 0 },
                        //{ Name: "CYZQ", DataType: 2, Align: 3, DecimalDigits: 0 },
                        //{ Name: "CYKSSJ", DataType: 3, Align: 2, Formater: "YYYY-MM-DD HH:mm:ss" },
                        //{ Name: "CYJSSJ", DataType: 3, Align: 2, Formater: "YYYY-MM-DD  HH:mm:ss" },
                        //{ Name: "SFCS", DataType: 1, Align: 3 },
                        //{ Name: "SFJG", DataType: 1, Align: 3 },
                        //{ Name: "YCSJ", DataType: 1, Align: 3 },
                        //{ Name: "TQ1321", DataType: 1, Align: 3, Ywlx: 1321, },
                        //{ Name: "WD", DataType: 2, Align: 3, DecimalDigits: 0 },
                        //{ Name: "SD", DataType: 2, Align: 3, DecimalDigits: 2 },
                        ////{ Name: "CYY", DataType: 1, Align: 2, Formater: "useridtoname" },
                        ////{ Name: "JDY", DataType: 1, Align: 2, Formater: "useridtoname" },
                        //{ Name: "CYLX1059", DataType: 1, Align: 2, Ywlx: 1059, }
                        /*   { Name: "SJLY", DataType: 3, Align: 2, Data: [{ id: 0, text: "人工录入" }, { id: 1, text: "自动上传" }] }*/
                    ];

                    //表格头
                    var Heads = excelExport.getHeads(this.$refs["tableRef"]);

                    //表格数据
                    var Datas = excelExport.getDatas(this.$refs["tableRef"], colModels, this.data);

                    //需要合并的列索引
                    var MergeColumnIndex = [];

                    var title = "报警记录";

                    var ExcelWorkbook = {
                        FileName: title + ".xls",
                        Sheets: [
                            {
                                SheetName: title,
                                Tables: [
                                    {
                                        TableType: 1,
                                        Title: title,
                                        Heads: Heads,
                                        Datas: Datas,
                                        MergeColumnIndex: MergeColumnIndex
                                    }
                                ]
                            }
                        ]
                    };

                    //调用通用导出方法
                    excelExport.export(ExcelWorkbook);

                },


                sizeChange(v) {
                    this.paging.size = v;
                    this.onSelect();
                },

                pageChange(v) {
                    this.paging.page = v;
                    this.onSelect();
                },

                //重置
                onReset: function () {
                    this.filter.start = moment().startOf("day").format();
                    this.filter.end = moment().startOf("day").format();
                    this.filter.AlarmCode = null;
                    this.filter.AlarmLevel = null;
                    this.filter.AlarmStatus = null;
                    this.onSelect();
                },
                //当前行变化事件
                rowClick: function (row) {
                    this.selectedRow = row;
                },
            },
            created() {
                this.onReset();

                iofpsocket.onCmdMsg("Open", data => {
                    var result = JSON.parse(data.C);
                    var t = result.Auth;
                    if (t == 1) {
                        this.disabled.alarmCleared = false;
                    }
                    else {
                        this.disabled.alarmCleared = true;
                    }
                })
            },
            mounted() {
                this.$on('opened', x => {
                    this.onReset();
                });
            }
        }
    })