/**
*
* @license Guriddo jqGrid JS - v5.4.0 
* Copyright(c) 2008, <PERSON>, <EMAIL>
* 
* License: http://guriddo.net/?page_id=103334
*/
!function(a){"use strict";"function"==typeof define&&define.amd?define(["jquery","./grid.base"],a):a(jQuery)}(function(a){"use strict";a.jgrid.extend({editCell:function(b,c,d,e){return this.each(function(){var f,g,h,i,j=this,k=a(this).jqGrid("getStyleUI",j.p.styleUI+".common","highlight",!0),l=a(this).jqGrid("getStyleUI",j.p.styleUI+".common","hover",!0),m=a(this).jqGrid("getStyleUI",j.p.styleUI+".celledit","inputClass",!0);if(j.grid&&!0===j.p.cellEdit){if(c=parseInt(c,10),j.p.selrow=j.rows[b].id,j.p.knv||a(j).jqGrid("GridNav"),j.p.savedRow.length>0){if(!0===d&&b==j.p.iRow&&c==j.p.iCol)return;a(j).jqGrid("saveCell",j.p.savedRow[0].id,j.p.savedRow[0].ic)}else window.setTimeout(function(){a("#"+a.jgrid.jqID(j.p.knv)).attr("tabindex","-1").focus()},1);if(i=j.p.colModel[c],"subgrid"!==(f=i.name)&&"cb"!==f&&"rn"!==f){try{h=a(j.rows[b].cells[c])}catch(d){h=a("td:eq("+c+")",j.rows[b])}if(parseInt(j.p.iCol,10)>=0&&parseInt(j.p.iRow,10)>=0&&void 0!==j.p.iRowId){var n=a(j).jqGrid("getGridRowById",j.p.iRowId);a(n).removeClass("selected-row "+l).find("td:eq("+j.p.iCol+")").removeClass("edit-cell "+k)}if(h.addClass("edit-cell "+k),a(j.rows[b]).addClass("selected-row "+l),!0!==i.editable||!0!==d||h.hasClass("not-editable-cell")||a.isFunction(j.p.isCellEditable)&&!j.p.isCellEditable.call(j,f,b,c))g=h.html().replace(/\&#160\;/gi,""),a(j).triggerHandler("jqGridCellSelect",[j.rows[b].id,c,g,e]),a.isFunction(j.p.onCellSelect)&&j.p.onCellSelect.call(j,j.rows[b].id,c,g,e);else{try{g=a.unformat.call(j,h,{rowId:j.rows[b].id,colModel:i},c)}catch(a){g=i.edittype&&"textarea"===i.edittype?h.text():h.html()}if(j.p.autoencode&&(g=a.jgrid.htmlDecode(g)),i.edittype||(i.edittype="text"),j.p.savedRow.push({id:b,ic:c,name:f,v:g,rowId:j.rows[b].id}),("&nbsp;"===g||"&#160;"===g||1===g.length&&160===g.charCodeAt(0))&&(g=""),a.isFunction(j.p.formatCell)){var o=j.p.formatCell.call(j,j.rows[b].id,f,g,b,c);void 0!==o&&(g=o)}a(j).triggerHandler("jqGridBeforeEditCell",[j.rows[b].id,f,g,b,c]),a.isFunction(j.p.beforeEditCell)&&j.p.beforeEditCell.call(j,j.rows[b].id,f,g,b,c);var p=a.extend({},i.editoptions||{},{id:b+"_"+f,name:f,rowId:j.rows[b].id,oper:"edit",module:"cell"}),q=a.jgrid.createEl.call(j,i.edittype,p,g,!0,a.extend({},a.jgrid.ajaxOptions,j.p.ajaxSelectOptions||{}));a.inArray(i.edittype,["text","textarea","password","select"])>-1&&a(q).addClass(m),h.html("").append(q).attr("tabindex","0"),a.jgrid.bindEv.call(j,q,p),window.setTimeout(function(){a(q).focus()},1),a("input, select, textarea",h).on("keydown",function(d){if(27===d.keyCode&&(a("input.hasDatepicker",h).length>0?a(".ui-datepicker").is(":hidden")?a(j).jqGrid("restoreCell",b,c):a("input.hasDatepicker",h).datepicker("hide"):a(j).jqGrid("restoreCell",b,c)),13===d.keyCode&&!d.shiftKey)return a(j).jqGrid("saveCell",b,c),!1;if(9===d.keyCode){if(j.grid.hDiv.loading)return!1;if(d.shiftKey){!a(j).jqGrid("prevCell",b,c,d)&&j.p.editNextRowCell&&b-1>0&&j.rows[b-1]&&(b--,a(j).jqGrid("prevCell",b,j.p.colModel.length,d))}else{!a(j).jqGrid("nextCell",b,c,d)&&j.p.editNextRowCell&&j.rows[b+1]&&(b++,a(j).jqGrid("nextCell",b,0,d))}}d.stopPropagation()}),a(j).triggerHandler("jqGridAfterEditCell",[j.rows[b].id,f,g,b,c]),a.isFunction(j.p.afterEditCell)&&j.p.afterEditCell.call(j,j.rows[b].id,f,g,b,c)}j.p.iCol=c,j.p.iRow=b,j.p.iRowId=j.rows[b].id}}})},saveCell:function(b,c){return this.each(function(){var d=this,e=d.p.savedRow.length>=1?0:null,f=a.jgrid.getRegional(this,"errors"),g=a.jgrid.getRegional(this,"edit");if(d.grid&&!0===d.p.cellEdit){if(null!==e){var h,i,j=a(d).jqGrid("getGridRowById",d.p.savedRow[0].rowId),k=a("td:eq("+c+")",j),l=d.p.colModel[c],m=l.name,n=a.jgrid.jqID(m),o=a(k).offset();switch(l.edittype){case"select":if(l.editoptions.multiple){var p=a("#"+b+"_"+n,j),q=[];h=a(p).val(),h?h.join(","):h="",a("option:selected",p).each(function(b,c){q[b]=a(c).text()}),i=q.join(",")}else h=a("#"+b+"_"+n+" option:selected",j).val(),i=a("#"+b+"_"+n+" option:selected",j).text();l.formatter&&(i=h);break;case"checkbox":var r=["Yes","No"];l.editoptions&&l.editoptions.value&&(r=l.editoptions.value.split(":")),h=a("#"+b+"_"+n,j).is(":checked")?r[0]:r[1],i=h;break;case"password":case"text":case"textarea":case"button":h=a("#"+b+"_"+n,j).val(),i=h;break;case"custom":try{if(!l.editoptions||!a.isFunction(l.editoptions.custom_value))throw"e1";if(void 0===(h=l.editoptions.custom_value.call(d,a(".customelement",k),"get")))throw"e2";i=h}catch(b){"e1"===b?a.jgrid.info_dialog(f.errcap,"function 'custom_value' "+g.msg.nodefined,g.bClose,{styleUI:d.p.styleUI}):"e2"===b?a.jgrid.info_dialog(f.errcap,"function 'custom_value' "+g.msg.novalue,g.bClose,{styleUI:d.p.styleUI}):a.jgrid.info_dialog(f.errcap,b.message,g.bClose,{styleUI:d.p.styleUI})}}if(i!==d.p.savedRow[e].v){var s=a(d).triggerHandler("jqGridBeforeSaveCell",[d.p.savedRow[e].rowId,m,h,b,c]);if(s&&(h=s,i=s),a.isFunction(d.p.beforeSaveCell)){var t=d.p.beforeSaveCell.call(d,d.p.savedRow[e].rowId,m,h,b,c);t&&(h=t,i=t)}var u=a.jgrid.checkValues.call(d,h,c),v=!1;if(!0===u[0]){var w=a(d).triggerHandler("jqGridBeforeSubmitCell",[d.p.savedRow[e].rowId,m,h,b,c])||{};a.isFunction(d.p.beforeSubmitCell)&&((w=d.p.beforeSubmitCell.call(d,d.p.savedRow[e].rowId,m,h,b,c))||(w={}));var x=a(d).triggerHandler("jqGridOnSubmitCell",[d.p.savedRow[e].rowId,m,h,b,c]);if(void 0===x&&(x=!0),a.isFunction(d.p.onSubmitCell)&&void 0===(x=d.p.onSubmitCell(d.p.savedRow[e].rowId,m,h,b,c))&&(x=!0),!1===x)return;if(a("input.hasDatepicker",k).length>0&&a("input.hasDatepicker",k).datepicker("hide"),"remote"===d.p.cellsubmit)if(d.p.cellurl){var y={};d.p.autoencode&&(h=a.jgrid.htmlEncode(h)),l.editoptions&&l.editoptions.NullIfEmpty&&""===h&&(h="null",v=!0),y[m]=h;var z=d.p.prmNames,A=z.id,B=z.oper;y[A]=a.jgrid.stripPref(d.p.idPrefix,d.p.savedRow[e].rowId),y[B]=z.editoper,y=a.extend(w,y),a(d).jqGrid("progressBar",{method:"show",loadtype:d.p.loadui,htmlcontent:a.jgrid.getRegional(d,"defaults.savetext")}),d.grid.hDiv.loading=!0,a.ajax(a.extend({url:d.p.cellurl,data:a.isFunction(d.p.serializeCellData)?d.p.serializeCellData.call(d,y,m):y,type:"POST",complete:function(l,p){if(a(d).jqGrid("progressBar",{method:"hide",loadtype:d.p.loadui}),d.grid.hDiv.loading=!1,"success"===p){var q=a(d).triggerHandler("jqGridAfterSubmitCell",[d,l,y[A],m,h,b,c])||[!0,""];!0===q[0]&&a.isFunction(d.p.afterSubmitCell)&&(q=d.p.afterSubmitCell.call(d,l,y[A],m,h,b,c)),!0===q[0]?(v&&(h=""),a(k).empty(),a(d).jqGrid("setCell",d.p.savedRow[e].rowId,c,i,!1,!1,!0),a(k).addClass("dirty-cell"),a(j).addClass("edited"),a(d).triggerHandler("jqGridAfterSaveCell",[d.p.savedRow[e].rowId,m,h,b,c]),a.isFunction(d.p.afterSaveCell)&&d.p.afterSaveCell.call(d,d.p.savedRow[e].rowId,m,h,b,c),d.p.savedRow.splice(0,1)):(a(d).triggerHandler("jqGridErrorCell",[l,p]),a.isFunction(d.p.errorCell)?d.p.errorCell.call(d,l,p):a.jgrid.info_dialog(f.errcap,q[1],g.bClose,{styleUI:d.p.styleUI,top:o.top+30,left:o.left,onClose:function(){d.p.restoreCellonFail||a("#"+b+"_"+n,j).focus()}}),d.p.restoreCellonFail&&a(d).jqGrid("restoreCell",b,c))}},error:function(e,h,i){a("#lui_"+a.jgrid.jqID(d.p.id)).hide(),d.grid.hDiv.loading=!1,a(d).triggerHandler("jqGridErrorCell",[e,h,i]),a.isFunction(d.p.errorCell)?d.p.errorCell.call(d,e,h,i):a.jgrid.info_dialog(f.errcap,e.status+" : "+e.statusText+"<br/>"+h,g.bClose,{styleUI:d.p.styleUI,top:o.top+30,left:o.left,onClose:function(){d.p.restoreCellonFail||a("#"+b+"_"+n,j).focus()}}),d.p.restoreCellonFail&&a(d).jqGrid("restoreCell",b,c)}},a.jgrid.ajaxOptions,d.p.ajaxCellOptions||{}))}else try{a.jgrid.info_dialog(f.errcap,f.nourl,g.bClose,{styleUI:d.p.styleUI}),d.p.restoreCellonFail&&a(d).jqGrid("restoreCell",b,c)}catch(a){}"clientArray"===d.p.cellsubmit&&(a(k).empty(),a(d).jqGrid("setCell",d.p.savedRow[e].rowId,c,i,!1,!1,!0),a(k).addClass("dirty-cell"),a(j).addClass("edited"),a(d).triggerHandler("jqGridAfterSaveCell",[d.p.savedRow[e].rowId,m,h,b,c]),a.isFunction(d.p.afterSaveCell)&&d.p.afterSaveCell.call(d,d.p.savedRow[e].rowId,m,h,b,c),d.p.savedRow.splice(0,1))}else try{a.isFunction(d.p.validationCell)?d.p.validationCell.call(d,a("#"+b+"_"+n,j),u[1],b,c):(window.setTimeout(function(){a.jgrid.info_dialog(f.errcap,h+" "+u[1],g.bClose,{styleUI:d.p.styleUI,top:o.top+30,left:o.left,onClose:function(){d.p.restoreCellonFail||a("#"+b+"_"+n,j).focus()}})},50),d.p.restoreCellonFail&&a(d).jqGrid("restoreCell",b,c))}catch(a){alert(u[1])}}else a(d).jqGrid("restoreCell",b,c)}window.setTimeout(function(){a("#"+a.jgrid.jqID(d.p.knv)).attr("tabindex","-1").focus()},0)}})},restoreCell:function(b,c){return this.each(function(){var d=this,e=d.p.savedRow.length>=1?0:null;if(d.grid&&!0===d.p.cellEdit){if(null!==e){var f=a(d).jqGrid("getGridRowById",d.p.savedRow[e].rowId),g=a("td:eq("+c+")",f);if(a.isFunction(a.fn.datepicker))try{a("input.hasDatepicker",g).datepicker("hide")}catch(a){}a(g).empty().attr("tabindex","-1"),a(d).jqGrid("setCell",d.p.savedRow[0].rowId,c,d.p.savedRow[e].v,!1,!1,!0),a(d).triggerHandler("jqGridAfterRestoreCell",[d.p.savedRow[e].rowId,d.p.savedRow[e].v,b,c]),a.isFunction(d.p.afterRestoreCell)&&d.p.afterRestoreCell.call(d,d.p.savedRow[e].rowId,d.p.savedRow[e].v,b,c),d.p.savedRow.splice(0,1)}window.setTimeout(function(){a("#"+d.p.knv).attr("tabindex","-1").focus()},0)}})},nextCell:function(b,c,d){var e;return this.each(function(){var f,g=this,h=!1;if(g.grid&&!0===g.p.cellEdit){for(f=c+1;f<g.p.colModel.length;f++)if(!0===g.p.colModel[f].editable&&(!a.isFunction(g.p.isCellEditable)||g.p.isCellEditable.call(g,g.p.colModel[f].name,b,f))){h=f;break}!1!==h?(e=!0,a(g).jqGrid("editCell",b,h,!0,d)):(e=!1,g.p.savedRow.length>0&&a(g).jqGrid("saveCell",b,c))}}),e},prevCell:function(b,c,d){var e;return this.each(function(){var f,g=this,h=!1;if(!g.grid||!0!==g.p.cellEdit)return!1;for(f=c-1;f>=0;f--)if(!0===g.p.colModel[f].editable&&(!a.isFunction(g.p.isCellEditable)||g.p.isCellEditable.call(g,g.p.colModel[f].name,b,f))){h=f;break}!1!==h?(e=!0,a(g).jqGrid("editCell",b,h,!0,d)):(e=!1,g.p.savedRow.length>0&&a(g).jqGrid("saveCell",b,c))}),e},GridNav:function(){return this.each(function(){function b(b,c,e){if("v"===e.substr(0,1)){var f=a(d.grid.bDiv)[0].clientHeight,g=a(d.grid.bDiv)[0].scrollTop,h=d.rows[b].offsetTop+d.rows[b].clientHeight,i=d.rows[b].offsetTop;"vd"===e&&h>=f&&(a(d.grid.bDiv)[0].scrollTop=a(d.grid.bDiv)[0].scrollTop+d.rows[b].clientHeight),"vu"===e&&i<g&&(a(d.grid.bDiv)[0].scrollTop=a(d.grid.bDiv)[0].scrollTop-d.rows[b].clientHeight)}if("h"===e){var j=a(d.grid.bDiv)[0].clientWidth,k=a(d.grid.bDiv)[0].scrollLeft,l=d.rows[b].cells[c].offsetLeft+d.rows[b].cells[c].clientWidth,m=d.rows[b].cells[c].offsetLeft;l>=j+parseInt(k,10)?a(d.grid.bDiv)[0].scrollLeft=a(d.grid.bDiv)[0].scrollLeft+d.rows[b].cells[c].clientWidth:m<k&&(a(d.grid.bDiv)[0].scrollLeft=a(d.grid.bDiv)[0].scrollLeft-d.rows[b].cells[c].clientWidth)}}function c(a,b){var c,e;if("lft"===b)for(c=a+1,e=a;e>=0;e--)if(!0!==d.p.colModel[e].hidden){c=e;break}if("rgt"===b)for(c=a-1,e=a;e<d.p.colModel.length;e++)if(!0!==d.p.colModel[e].hidden){c=e;break}return c}var d=this;if(d.grid&&!0===d.p.cellEdit){d.p.knv=d.p.id+"_kn";var e,f,g=a("<div style='position:fixed;top:0px;width:1px;height:1px;' tabindex='0'><div tabindex='-1' style='width:1px;height:1px;' id='"+d.p.knv+"'></div></div>");a(g).insertBefore(d.grid.cDiv),a("#"+d.p.knv).focus().keydown(function(g){switch(f=g.keyCode,"rtl"===d.p.direction&&(37===f?f=39:39===f&&(f=37)),f){case 38:d.p.iRow-1>0&&(b(d.p.iRow-1,d.p.iCol,"vu"),a(d).jqGrid("editCell",d.p.iRow-1,d.p.iCol,!1,g));break;case 40:d.p.iRow+1<=d.rows.length-1&&(b(d.p.iRow+1,d.p.iCol,"vd"),a(d).jqGrid("editCell",d.p.iRow+1,d.p.iCol,!1,g));break;case 37:d.p.iCol-1>=0&&(e=c(d.p.iCol-1,"lft"),b(d.p.iRow,e,"h"),a(d).jqGrid("editCell",d.p.iRow,e,!1,g));break;case 39:d.p.iCol+1<=d.p.colModel.length-1&&(e=c(d.p.iCol+1,"rgt"),b(d.p.iRow,e,"h"),a(d).jqGrid("editCell",d.p.iRow,e,!1,g));break;case 13:parseInt(d.p.iCol,10)>=0&&parseInt(d.p.iRow,10)>=0&&a(d).jqGrid("editCell",d.p.iRow,d.p.iCol,!0,g);break;default:return!0}return!1})}})},getChangedCells:function(b){var c=[];return b||(b="all"),this.each(function(){var d,e=this;e.grid&&!0===e.p.cellEdit&&a(e.rows).each(function(f){var g={};a(this).hasClass("edited")&&(a("td",this).each(function(c){if("cb"!==(d=e.p.colModel[c].name)&&"subgrid"!==d)if("dirty"===b){if(a(this).hasClass("dirty-cell"))try{g[d]=a.unformat.call(e,this,{rowId:e.rows[f].id,colModel:e.p.colModel[c]},c)}catch(b){g[d]=a.jgrid.htmlDecode(a(this).html())}}else try{g[d]=a.unformat.call(e,this,{rowId:e.rows[f].id,colModel:e.p.colModel[c]},c)}catch(b){g[d]=a.jgrid.htmlDecode(a(this).html())}}),g.id=this.id,c.push(g))})}),c}})});