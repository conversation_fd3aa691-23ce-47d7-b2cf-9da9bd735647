define([],function(){
	var radio = function(v_ok,v_no,callback){
		return function(v,config,rowdata){
			//生成name 列上所有radio共用
			var name = "grid_"+this.id+"_"+config.colModel.name
			//生成id radio唯一标识
			var id = name+"_"+config.rowId;
			//选中行置为v_ok,其他行职位v_no
			var setValue = function(){
				for(var a in config.colModel.value){config.colModel.value[a]=v_no;}
				config.colModel.value[config.rowId]=v_ok;
			}
			setTimeout(function(){
				//若要绑定到input上，使用$("#"+id).on("change",setValue)

				//绑定tr的click事件
				$("#"+id).parentsUntil("tr").last().parent().on("click",function(){
					$("#"+id).prop("checked",true);
					setValue();
					callback&&callback($("#"+id),rowdata,config);
				})
			})

			//拼接html
			return '<label class="form-control-radio">'+
				'<input id="'+id+'" type="radio" name="'+name+'" ' + (v==v_ok?"checked":"") + ' /><span>&nbsp;</span></label>';
		}
	}

    return {
        radio:radio
    }
})