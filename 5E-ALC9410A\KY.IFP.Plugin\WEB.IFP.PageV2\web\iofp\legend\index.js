define(["yaml!/iofp/legend/setting.yaml","comp!./legend.js"],function(setting,zLegend){
    let ifpLegend = {
        props:{
            code:{default:''},
            value:{default:''}
        },
        data(){
            return {
                states:[],
                title:"",
            }
        },
        components:{zLegend},
        template:`<z-legend :title="title" :value="value" :states="states"></z-legend>`,
        watch:{
            code(){
                this.loadByCode(this.code);
            }
        },
        created(){
            this.loadByCode(this.code);
        },
        methods:{
            loadByCode(code){
                if(code){
                    let device = setting.find(item=>item.code===code);
                    if(device){
                        this.$set(this,"states",device.states)
                        this.$set(this,"title",device.text)
                    }else{
                        console.error("设备配置未找到："+code);
                    }
                }
            }
        }
    }
    return {
        ifpLegend,zLegend
    }
})