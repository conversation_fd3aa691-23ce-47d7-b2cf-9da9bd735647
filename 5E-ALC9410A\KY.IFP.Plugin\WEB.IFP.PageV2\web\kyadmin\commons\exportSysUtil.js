/**
 * 提供脚本公用的工具方法
 */
define(["jquery"],function(){
	//如果当前cell为FLAG，说明只做占位用途，无需处理
	var FLAG = "flag";
	/**
  	 * 形象矩阵：辅助构建单元格
  	 * <p>原型扩展
  	 * push:添加元素
  	 * toRegion 将矩阵转换成范围对象数组。
  	 * </p>
  	 */
  	Matrix.prototype = /**@lends Matrix */{
  	        /**
  	         * 第row行新增cell对象
  	         * @param {Number} row 行数
  	         * @param {Object} cell 单元格对象 
  	         */
  			push: function(row, cell){
  				var rowspan = cell.rowspan ? +cell.rowspan : 1, colspan = cell.colspan ?  +cell.colspan : 1;
  				cell.rowspan = rowspan;
  				cell.colspan = colspan;
  				// 定位坐标
  				var y = row, x = (function(matrix){
  					var rowData = matrix[row];
  					if(!rowData || !rowData.length)return 0;
  					for(var i=0, j=rowData.length; i<j; i++){
  						if(typeof rowData[i] === "undefined")return i;
  					}
  					return rowData.length;
  				})(this.matrix);
				for(var i=y; i<y+rowspan; i++){
					// 获取当前行
					var rowData = this.matrix[i];
					if(!rowData){
						this.matrix[i] = rowData = [];
					}
					for(var j=x; j<x+colspan; j++){
						rowData[j] = i===y && j === x ? cell : FLAG;
					}
				}
  			},
  			/** 
  			 * 将矩阵转换成范围对象数组
  			 *  return  {Array} regions 对象数组
  			 **/
  			toRegion: function(){
  				var rowNum = this.matrix.length, regions = [];
  				for(var i=0; i<rowNum; i++){
  					var row = [];
  					regions.push(row);
  					// 当前行
  					var rowData = this.matrix[i];
  					for(var j=0, len = rowData.length; j<len; j++){
  						// 当前单元格
  						var cell = rowData[j];
  						// 如果当前cell为FLAG，说明只做占位用途，无需处理
  						if(cell === FLAG){
  							continue
  						}
  						// 将当前单元格绘制范围放入数组
  						row.push(new Region(i, j, (cell.rowspan+i-1), (cell.colspan+j-1), cell.text, cell.style || "", cell.dataType || "", cell.dataKey || ""))
  					}
  				}
  				return regions;
  			}
  		}
  	/**
	 *	定义合并类Region对象，用于创建excell对应的Cell
	 * @class Region
	 * @property {number} rowFrom  行起始位置
	 * @property {number} colFrom  行结束位置
	 * @property {number} rowTo  列起始位置
	 * @property {number} colTo  列结束位置
	 * @property {number} text  文本信息
	 * @property {number} style  样式
	 * @property {number} dataType  类型
	 */
	
  	function Region(rowFrom,colFrom,rowTo,colTo,text,style,dataType,dataKey) {
  		this.rowFrom = rowFrom;
  		this.colFrom = colFrom;
  		this.rowTo = rowTo;
  		this.colTo = colTo;
  		this.text = text;
  		this.style = style;
  		this.dataType = dataType;
  		this.dataKey = dataKey;
  	}	
  		
   /**
  	 * 矩阵对象
  	 * @class Matrix
  	 * @property {Number} row  行数
  	 */
  	function Matrix(row){
  		this.matrix = [];
  		for(var i=0; i<row; i++){
  			this.matrix.push([]);
  		}
  	}
	
	/**
	 * 提供脚本公用的工具方法
	 */
    var exportSysUtil = {
        /**
         * 输入控件是否禁用.
         */	
    	inputDisabled : function(ids,flag) {
    		for(var i=0;i<ids.length;i++){
	    		if (flag) {
	    			 $("#"+ids[i]).attr("disabled","true");
	    		} else {
	    			 $("#"+ids[i]).removeAttr("disabled");
	    		}
    		}
    	},
        /**
         * js对象转JSON格式.
         */	
    	toJSON : function(obj) {
    		if(this.isEmpty(obj)) {
    			return null;
    		} else {
    			return JSON.stringify(obj);
    		}
    	}, 		
        /**
         * 判断是否为空.
         */
        isEmpty:function(v){ 
	    	switch (typeof v){ 
	    		case 'undefined' : return true; 
	    		case 'string' : 
	    		    if(v.replace(/^\s+|\s+$/g,"").length == 0){
	    		    	return true; 
	    		    }
	    		    break; 
	    		case 'boolean' : 
	    			if(v){
	    				return true;
	    			} 
	    			break; 
	    		case 'number' : if(0 === v){
	    			return true;
	    			} 
	    			break; 
	    		case 'object' : 
	    			if(null === v){
	    				return true; 
	    			}else	if(undefined !== v.length && v.length==0){
	    				return true;
	    			}else{ 
	    				for(var k in v){
	    					return false;
	    				} 
	    				return true; 
	    			}			
	    		break; 
	    	} 
	    	return false; 
        },
        
        /**
         * 获取树形json格式.
         */
        getTreeJson : function(arr, treeIdName, treeTextName, isLazy){
        	var newArr = [];
        	if(arr.length > 0){
	        	arr.sort(function(a, b){
	        		var rValue = +a[treeIdName] - +b[treeIdName];
	        		return isNaN(rValue) ? 1 : rValue;        		
	        	});
        	}
        	for (var i = 0, j = arr.length; i < j; i++){
        		var s = 0,
        			obj = arr[i];
        		if (obj.show !== false){
            		var id = obj[treeIdName],            			
            			nObj = {},
            			_arr = newArr,
            			treeObj = {id: id, gid:obj.gid, text: obj[treeTextName], iconCls: obj.clazz, attributes: {index: i, children:[]}, children:[]};
            		try{
            			while(id.length > s){					
    	    		   		var index = id.substring(s, s + 2),
    	    		   			nObj = _arr[index - 1];	
    	    		   		if (nObj){
    	    		   			_arr = isLazy ? nObj.attributes.children : nObj.children;
    	    		   			nObj.state = isLazy ? "closed" : "open";
    	    		   		} else {
    	    		   			_arr.push(treeObj);
    	    		   		}
    	    		   		s += 2;
    	    		   	}
            		} catch(e){
            			
            		}
	    		   	
        		} 
        	}
        	arr = null;
        	return newArr;
        },
        /**
         * 克隆传入的对象
         * @参数 target 需要克隆的对象
         */
         clone: function (target) {
         	var objClone;
         	if (target){
                 if (target.constructor == Object || target.constructor == Array) {
                     objClone = new target.constructor();
                 } else {
                     objClone = new target.constructor(target.valueOf());
                 }
                 for (var key in target) {
                     if (objClone[key] != target[key]) {
                         if (typeof (target[key]) == 'object') {
                             objClone[key] = arguments.callee(target[key]);
                         }
                         else {
                             objClone[key] = target[key];
                         }
                     }
                 }
                 objClone.toString = target.toString;
                 objClone.valueOf = target.valueOf;
         	}
             
             return objClone;
         },
         /**
          * 对象在数组中查找指定元素index
          * @参数 container：被查找的数组
          * @参数 field：对象的查找字段
          * @参数 value：查找值
          */
          arraySearch: function (container, field, value) {
              for (var i = 0; i < container.length; i++) {
                  if (container[i][field] == value) {
                      return i;
                  }
              }
              return -1;
          },
          
          /**
           * 对字符串进行格式转换，返回毫秒数
           * 转换格式： 如果字符串是数值，则返回本身，若格式不符合要求，则返回当前日期
           * @参数 str: 需转换的字符串
           * @参数 reg: 转换正则
           */
          parseDate: function(str, reg){
          	if(str === "" || str === null || str === undefined){
          		return +new Date();
          	}
          	if (!isNaN(+str) && str.length !== 4){
          		return +str;
          	}
          	var d = this.strToTimeMillis(str);
          	// 判断字符串是否为数值
          	if (!isNaN(d)){
          		return d;
          	}
          	var reg = reg ? reg : /\b\((\-*\d*)\+\b/,        	
          		arr = str.toString().match(reg);
          	return arr ? arr[1] : +new Date();        			
          },
          
          /*
  		 * 对传入的时间毫秒数转换成日期格式 2000-01-01
  		 */
          strToDate: function (str, showTime, showType,showSeconds) {
              if (str === undefined) {
                  return "";
              }
              str = this.parseDate(str);
              var d = !isNaN(str) ? new Date(+str) : new Date(),
  				month = d.getMonth() + 1,
  				day = d.getDate(),
  				hours = d.getHours(),
  				minutes = d.getMinutes(),
  				seconds = d.getSeconds(),
  				time = (hours < 10 ? "0" + hours : hours) + ":" + (minutes < 10 ? "0" + minutes : minutes);
  			if(showSeconds) {
  				time += ":" + (seconds < 10 ? "0" + seconds : seconds);
  			}
              switch(showType) {
              case "Y" :
              	return d.getFullYear();
              case "M" : case "S" :
              	return d.getFullYear() + "-" + (month < 10 ? "0" + month : month);            	
              }
              return d.getFullYear() + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day) + (showTime ? " " + time : "");
          },
          /**
           * 
           */
          strToTime: function (str, showSecond) {
              if (str === undefined) {
                  return "";
              }
              str = this.parseDate(str);
              var d = !isNaN(str) ? new Date(+str) : new Date(),
  				hours = d.getHours(),
  				minutes = d.getMinutes(),
  				seconds = d.getSeconds();
              return (hours < 10 ? "0" + hours : hours) + ":" + (minutes < 10 ? "0" + minutes : minutes) + (showSecond ? ":" + (seconds < 10 ? "0" + seconds : seconds) : "")
          },
          /**
           * 将日期转为毫秒数，传入格式为"2001-01-01"或"2001/01/01"
           * @参数 str: 传入字符串
           */
          strToTimeMillis : function(str){
          	if(!str){
          		return 0;
          	}
          	return +new Date(this.formatDate(str).replace(/-/g,"/"));
          },
          
          formatDate : function(str){
          	var reg = /^\d{4}[-\/]\d{2}$/,
          		reg1 = /^\d{4}$/;
          	if (reg.test(str)){
          		str += "-01";
          	} else if (reg1.test(str)){
          		str += "-01-01";
          	}
          	return str;        	
          },
          
          /**
           * 替换字符串
           * @参数 oriStr: 目标字符串
           * @参数 findStr: 目标字符串中需替换的字符串
           * @参数 replaceStr: 需替换成的字符串
           */
          replaceAll: function (oriStr, findStr, replaceStr) {
          	if(typeof oriStr === "string"){
          		return oriStr.replace(new RegExp(findStr, "g"), replaceStr);
          	}
              return oriStr;
          },
        /**
         * 对象在数组中查找指定元素index
         */
        getIndex : function (opt){
					var list = opt.list,
						key = opt.key,
						value = opt.value;
					return (function(par){
								for (var i = list.length; i--;) {
									if (list[i][key] == par){
										return i;
									}											
								}
								list = null;
								return -1;
							})(value);					
		},
	      /**
	       * 准备精确浮点运算
	       * @ignore
	       * @param {String|Number} arg1 浮点数
	       * @param {String|Number} arg2 浮点数
	       * @param {Integer} [precision] 精度（1-17）。具体计算方法，参见_accPcn
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	      _prepareBigDecimal: function(arg1,arg2,precision ,plan){
	    	 if(typeof arg1 == "number"){
	    	   arg1 = arg1+"";	 
	    	 }
	    	 if(typeof arg2 == "number"){
	    	   arg2 = arg2+"";	 
	    	 } 
	    	 /**
	    	  * 计算精度
	    	  * @ignore
	    	  * @param {String} arg1
	    	  * @param {String} arg2
	    	  * @return {Number} 计算的精度
	    	  */
	    	 function _accPcn() {
	    		 var arg1 = arguments[0];
	    		 var arg2 = arguments[1];
	    		 var idx1 = arg1.indexOf(".") > 0 ? arg1.indexOf(".") : arg1.length;
 		  		 var idx2 = arg2.indexOf(".") > 0 ? arg2.indexOf(".") : arg2.length; 
 		  		 //要保留的精度，不传则为计算结果的最大精度
	    		 var precision = arguments[2];
	    		 //要执行的运算 "+" "-" "*" "/"
	    		 var plan = arguments[3];
 		  		 if("+" === plan) {
 		  			var b1 = +arg1.substring(0, idx1);
 		  			var b2 = +arg2.substring(0 ,idx2);
 		  			if(precision==null) {
 		  				var e1 = +("0" + arg1.substring(idx1));
 		  				var e2 = +("0" + arg2.substring(idx2));
 		  				var res = e1 > e2 ? e1-e2 : e2-e1;
 		  				return ((b1+b2)+"").length + ((e1+e2)+"").length;
 		  			} else {
 		  				return (((b1+b2)+"").length + precision);
 		  			}
 		  		 }else if("-" === plan) {
 		  			//处理 减法特殊问题
 		  			var p = 0;
 		  			var b1 = +arg1.substring(0, idx1);
 		  			var b2 = +arg2.substring(0 ,idx2);
 		  			var resB = b1 > b2 ? b1-b2 : b2-b1 ;
 		  			 
 		  			if(precision==null) {
	   		  			var e1 = +("0" + arg1.substring(idx1));
		  				var e2 = +("0" + arg2.substring(idx2));
		  				var resE = e1 > e2 ? e1-e2 : e2-e1;
		  				p = (resB+"").length + (resE+"").length + 2;
		  			} else {
		  				if(resB === 0) {
		  					resB = "";
		  				}
		  				p = (resB+"").length + precision + 2;
		  			}
 		  			return p;
 		  		 }else if("*" === plan) {
 		  		    var b1 = +arg1.substring(0, idx1);
		  			var b2 = +arg2.substring(0 ,idx2);
	  				var e1 = +("0" + arg1.substring(idx1));
	  				var e2 = +("0" + arg2.substring(idx2));
		  			if(precision==null) {
		  				b1 = b1 === 0 ? 1 : b1;
						b2 = b2 === 0 ? 1 : b2;
		  				e1 = e1 === 0 ? 1 : e1;
		  				e2 = e2 === 0 ? 1 : e2;
		  				return ((b1*b2)+"").length + ((e1*e2)+"").length;
		  			} else {
		  				if(b1 === 0 && b2 !==0) {
		  					var s = (e1 * b2)+"";
		  					var sIdx = s.indexOf(".");
		  					var subS1 = s.substring(0, sIdx);
		  					var subS2 = s.substring(sIdx+1 ,sIdx+1+precision);
		  					return subS1.length + subS2.length;
		  				} else if (b1 !== 0 && b2 === 0) {
		  					var s = (e2 * b1)+"";
		  					var sIdx = s.indexOf(".");
		  					var subS1 = s.substring(0, sIdx);
		  					var subS2 = s.substring(sIdx+1 ,sIdx+1+precision);
		  					return subS1.length + subS2.length;
		  				} else if(b1 === 0 && b2 === 0) {
		  					if(e1<1 && e2<1 && precision > 1) {
		  						precision = precision - 1;
		  					}
		  					return precision ;
		  				} else {
		  					return (((b1*b2)+"").length + precision);
		  				}
		  			}
 		  		 }else if("/" === plan) {
 		  			var b1 = +arg1.substring(0, idx1);
		  			var b2 = +arg2.substring(0 ,idx2);
		  			b2 = b2 === 0 ? 1 : b2;
		  			if(precision==null) {
		  				var e1 = +("0" + arg1.substring(idx1));
		  				var e2 = +("0" + arg2.substring(idx2));
		  				e2 = e2 === 0 ? 1 : e2;
		  				var res = Math.floor(e1/e2);
		  				return ((b1/b2)+"").length + (res+"").length;
		  			} else {
		  				if(b1 === 0) {
		  					return precision;
		  				  //处理两个整数相除的情况
		  				} else if (b1==arg1 && b2==arg2) {
		  					var res = (b1/b2)+"";
		  					var idx = res.indexOf(".");
		  					var resB = res.substring(0 ,idx);
		  					
		  					return resB.length + precision;
		  				}
		  				return ((b1/b2)+"").length + precision;
		  			}
 		  		 }
	    	 }
	    	 
	    	  var mc;
	    	  if(typeof precision === "undefined"){
	 	           precision = _accPcn(arg1 ,arg2 ,precision ,plan);
	 	           mc = new MathContext(precision/*,0,0,MathContext.prototype.ROUND_HALF_UP*/); //精度,表示法（0为普通，1为科学）,丢失数字,舍入模式(4舍5入)
	    	   }else if( precision instanceof MathContext ){
	    		   mc = precision;	   
	    	   }else if(typeof precision =="number"){
	    		   precision = _accPcn(arg1 ,arg2 ,precision ,plan);
	    		   if(precision>17){
	    			   precision=17;
	    		   }
	    		   mc = new MathContext(precision/*,0,0,MathContext.prototype.ROUND_HALF_UP*/);  
	    	   }
	    	   var  bd1 = new BigDecimal(arg1),
	    	   		bd2 = new BigDecimal(arg2);
	    	   		
	    	  return {
	    		  mathContext: mc,
	    	      num1: bd1,
	    	      num2: bd2
	    	  };
	      },
	     
	      /**
	       *  精确乘法,返回BigDecimal类型
	       * <p>的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。
	       * 这个函数返回较为精确的乘法结果。
	       * </p>
	       * @example
	       *  var result = $.exportSysUtil.accMulB(6210.5,3.0);  
	       *  var result = $.exportSysUtil.accMulB(6210.5,3.0, 10);  
	       *  
	       * @param {String|Number} arg1 被乘数
	       * @param {String|Number} arg2 乘数
	       * @param {Integer} [precision] 精确度,在0-17之间,如果没有提供，取arg1与arg2小数位数比较多者
	       * @return {BigDecimal} arg1乘以arg2的积(具有precision精确度) 
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	      accMulB : function (arg1,arg2,precision) {   
	    	   if(arguments.length<2){
	    		 throw new Error("乘法运算没有提供乘数。");   
	    	   }
	    	   var ctx = this._prepareBigDecimal(arg1,arg2,precision ,"*");
	    	   var result = ctx.num1.multiply(ctx.num2,ctx.mathContext);
	    	   return  result; 	
	     
	      },
	      /**
	       * 精确乘法，返回浮点数的字符串
	       * @example
	       *  var result = $.exportSysUtil.accMulS(6210.5,3.0); //"18631.5"
	       *  var result = $.exportSysUtil.accMulS(6210.5,3.0, 10); //"18631.5"
	       
	       * @see $.exportSysUtil.accMulB
	       * @return {String}
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	      accMulS : function (arg1,arg2,precision) {   
		    return  this.accMulB(arg1,arg2,precision).toString();
	      },
	      /**
	       * 精确乘法，返回浮点数。
	       * @example
	       *  var result = $.exportSysUtil.accMul(6210.5,3.0); //18631.5
	       *  var result = $.exportSysUtil.accMul(6210.5,3.0, 10); //18631.5
	       * 因为Number转换可能会损失精度，因此结果不一定精确。
	       * @see $.exportSysUtil.accMulS
	       * @return {Number} 
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	      accMul : function (arg1,arg2,precision) {   
		    return 	Number( this.accMulS(arg1,arg2,precision) );
	      },
	      /**
	       * 精确除法,返回BigDecimal
	       * <p>javascript的除法结果会有误差，在两个浮点数相除的时候会比较明显。
	       * 这个函数返回较为精确的除法结果。
	       * </p>
	       * @example
	       *  var result = $.exportSysUtil.accDivB(6210.5,3.0);
	       *  var result = $.exportSysUtil.accDivB(6210.5,3.0, 10);
	       *  
	       * @param {String|Number} arg1 被除数
	       * @param {String|Number} arg2 除数
	       * @param {Integer} [precision] 精确度,默认保留最高精度
	       * @return {BigDecimal} arg1除以arg2的商(具有precision精确度)
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	      accDivB : function (arg1,arg2,precision){ 
	    	   if(arguments.length<2){
	    		 throw new Error("除法运算没有提供除数。");   
	    	   }
	    	   var ctx = this._prepareBigDecimal(arg1,arg2,precision, "/");
	    	   var result = ctx.num1.divide(ctx.num2,ctx.mathContext);
	    	   return   result ; 	
	      },
	      /**
	       * 精确除法,返回String
	       * @see $.exportSysUtil.accDivB
	       * @example
	       *  var result = $.exportSysUtil.accDivS(6210.5,3.0);  //"2070.17"
	       *  var result = $.exportSysUtil.accDivS(6210.5,3.0, 10); //"2070.166667"
	       * @param {String|Number} arg1 被除数
	       * @param {String|Number} arg2 除数
	       * @param {Integer} [precision] 精确度,默认保留最高精度
	       * @return {String}
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	      accDivS: function (arg1,arg2,precision){ 
	    	  return this.accDivB(arg1,arg2,precision).toString();
	      },
	      /**
	       * 精确除法,返回浮点数
	       * @see $.exportSysUtil.accDivS
	       * @example
	       *  var result = $.exportSysUtil.accDiv(6210.5,3.0);  // 2070.17
	       *  var result = $.exportSysUtil.accDiv(6210.5,3.0, 10); //2070.166667
	       * @param {String|Number} arg1 被除数
	       * @param {String|Number} arg2 除数
	       * @param {Integer} [precision] 精确度,默认保留最高精度 
	       * @return {Number}
	       */
	      accDiv: function (arg1,arg2,precision){ 
	    	  return Number(this.accDivS(arg1,arg2,precision));
	      },
	      /**
	    	*  精确加法,返回BigDecimal
	    	* <p>javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
	    	* </p>
	    	* @example
	    	*  var result = $.exportSysUtil.accAddB(6210.5,3.0);
	    	*  var result = $.exportSysUtil.accAddB(6210.5,3.0, 10);
	    	*  
	    	* @param {String|Number} arg1 被加数
	    	* @param {String|Number} arg2 加数
	    	* @param {Integer} [precision] 精确度,在0-17之间,如果没有提供，取arg1与arg2小数位数比较多者
	    	* @return {BigDecimal} arg1与arg2的和(具有precision精确度) 
	    	* <AUTHOR>
	       * @date 2014.8.11
	      */
	      accAddB : function (arg1,arg2,precision){   
	    	   if(arguments.length<2){
	    		 throw new Error("加法运算必须提供两个加数。");   
	    	   }
	    	   var ctx = this._prepareBigDecimal(arg1,arg2,precision,"+");
	    	   var result = ctx.num1.add(ctx.num2,ctx.mathContext);
	    	   return  result; 	
	    	  
	      },
	      /**
	       *  精确加法,返回String
	       * @seee @accAddB
	       * @example
	    	*  var result = $.exportSysUtil.accAdS(6210.5,3.0);
	    	*  var result = $.exportSysUtil.accAddS(6210.5,3.0, 10);
	       * @param {String|Number} arg1 被加数
	       * @param {String|Number} arg2 加数
	       * @param {Integer} [precision] 精确度,在0-17之间,如果没有提供，取arg1与arg2小数位数比较多者
	       * @return {String}
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	      accAddS : function (arg1,arg2,precision){   
	    	  return this.accAddB(arg1,arg2,precision).toString();
	      },
	      /**
	       * 精确加法,返回浮点数
	       * @seee @.accAddS
	       * @example
	    	*  var result = $.exportSysUtil.accAd(6210.5,3.0);
	    	*  var result = $.exportSysUtil.accAdd(6210.5,3.0, 10);
	       * @param {String|Number} arg1 被加数
	       * @param {String|Number} arg2 加数
	       * @param {Integer} [precision] 精确度,在0-17之间,如果没有提供，取arg1与arg2小数位数比较多者
	       * @return {Number}
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	      accAdd : function (arg1,arg2,precision){   
	    	  return Number(this.accAddS(arg1,arg2,precision));
	      },
	      /**
	    	*  精确减法,返回BigDecimal
	    	* <p>
	    	*  javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。 这个函数返回较为精确的减法结果。
	    	* </p>
	    	* @example
	    	*  var result = $.exportSysUtil.accSubB(6210.5,3.0);
	    	*  var result = $.exportSysUtil.accSubB(6210.5,3.0, 10);
	    	*  
	    	* @param {String|Number} arg1 被减数
	    	* @param {String|Number} arg2 减数
	    	* @param {Integer} [precision] 精确度,在0-17之间,如果没有提供，取arg1与arg2小数位数比较多者
	    	* @return {BigDecimal} arg1与arg2的差(具有precision精确度) 
	    	* <AUTHOR>
	       * @date 2014.8.11
	      */
	      accSubB : function (arg1,arg2,precision ){    
	    	  if(arguments.length<2){
	    		 throw new Error("减法运算必须提供减数。");   
	    	  }
	          var ctx = this._prepareBigDecimal(arg1,arg2,precision,"-");
	    	   var result = ctx.num1.subtract(ctx.num2,ctx.mathContext);
	    	   return  result; 
	      },
	      /**
	       * 精确减法,返回String
	       * @see $.exportSysUtil.accSubB
	       * @example
	    	*  var result = $.exportSysUtil.accSubB(6210.5,3.0);
	    	*  var result = $.exportSysUtil.accSubB(6210.5,3.0, 10);
	    	*  
	    	* @param {String|Number} arg1 被减数
	    	* @param {String|Number} arg2 减数
	    	* @param {Integer} [precision] 精确度,在0-17之间,如果没有提供，取arg1与arg2小数位数比较多者
	       * @return {String}
	       * <AUTHOR>
	       * @date 2014.8.11
	       */
	       accSubS : function (arg1,arg2,precision ){   
	    	  return this.accSubB(arg1,arg2,precision).toString();
	       },
	       /**
	        * 精确减法,返回浮点数
	        * @see $.exportSysUtil.accSubS
	        * @example
	    	*  var result = $.exportSysUtil.accSubB(6210.5,3.0);
	    	*  var result = $.exportSysUtil.accSubB(6210.5,3.0, 10);
	    	*  
	    	* @param {String|Number} arg1 被减数
	    	* @param {String|Number} arg2 减数
	    	* @param {Integer} [precision] 精确度,在0-17之间,如果没有提供，取arg1与arg2小数位数比较多者
	        * @return {Number}
	        * <AUTHOR>
	        * @date 2014.8.11
	        */
	       accSub: function (arg1,arg2,precision ){   
	    	  return Number(this.accSubS(arg1,arg2,precision));
	       },
			/**
			 * 判断安装的office版本（通过ActiveXObject("Excel.application")判断 ）
			 * @example
			 	 4种可能，参见返回值
			 	
			 * @return {String} 可能的取值：
			 *  -null 如果没有安装Excel
			 *  -office2003
			 *  -office2007
			 *  -office2010  
			 */
			readOfficeVersion : function() {  
		        var excel=null, version = null;  
		        try  
		        {  
		            excel=new ActiveXObject("Excel.application");  
		        }catch(e) {
		        	return version;
		        }  
		        if(excel.Version==="11.0"){  
		            version="office2003";  
		        } else if(excel.Version==="12.0") {  
		            version="office2007";  
		        } else if(excel.Version==="14.0") {  	              
		            version="office2010";  
		        }    
		        //及时关闭Word进程  
		        excel.Application.Quit();  
		        return version;  
		    },
		    /**
			 *导出
			 * @param {Object} option 参数对象
			 * <p>option.fileName = "test";文件名
			 * option.type = "xls" 文件类型
			 * 
			 */
			doExport: function(option){
				for(var i=0; i<option.dataList.length; i++){
					for(var j=0;j<option.dataList[i].sheetData.length;j++){
						if(option.dataList[i].sheetData[j].userData == null){
							option.dataList[i].sheetData[j].userData = [{}];
						}
						if(option.dataList[i].sheetData[j].dataKey == null){
							option.dataList[i].sheetData[j].dataKey = [];
						}
					}
				}
				var exportServlet = "/com.kysoft.service/excel/export.action";
				var fileName = option.fileName;
				var index = fileName.lastIndexOf(".");
				if(index !== -1){
					option.type = fileName.substring(index + 1, fileName.length);
					option.fileName = fileName.substring(0, index);
				} else {
					option.type = option.type || "xls";
				}
				var block = option.block;
				this.option = option;
				var that = this;
				var workbookJSON = this.getWorkbook(option);
				F.util.showWait();//显示等待
				setTimeout(function(){	
					var tjson = workbookJSON;
					//解决前端字符到服务端乱码问题
					workbookJSON = encodeURIComponent(that.toJSON(workbookJSON));
//					$.post(exportServlet, {"workbook": workbookJSON, "isTmpFile": true},
					$.ajax({
	                    url : exportServlet,
	                    data : {workbook: workbookJSON, isTmpFile: true},
	                    type : "post",
	                    dataType: "text",
	                    contentType: "application/x-www-form-urlencoded",
	                    success : function(resdata, textStatus, jqXHR) {
						  var downloadFrame = document.getElementById("downloadIframe");
						  if(downloadFrame!=null){
							  downloadFrame.parentNode.removeChild(downloadFrame);
						  }
    					  downloadFrame = document.createElement("iframe");
    					  downloadFrame.id = "downloadIframe";
    					  downloadFrame.style.display="none";
    					  document.body.appendChild(downloadFrame);

	    				  var slSrc = exportServlet+"?path="+resdata+"&fileName=" + encodeURI(encodeURI(tjson.fileName)) + "&fileType="+tjson.type;        				   
	    				  if(tjson.inline || typeof tjson.type === "string" && tjson.type.toUpperCase() != "XLS") {
	    					  slSrc += "&inline" + tjson.inline;
	    				  }
	    				  var html ='<form id="exportForm" method="post" action="'+slSrc+'"></form>';
	    				  downloadFrame.contentWindow.document.write(html);
	    				  downloadFrame.contentWindow.document.getElementById("exportForm").submit();
	    				  F.util.hideWait();//关闭等待
						},
						error : function(){
							$.bootoast.danger("数据导出异常。");
							F.util.hideWait();//关闭等待
						}
	                    
					});		
				},0);
				//移除进度条
//				if(typeof block === "undefined" || block === true){	$.unblockUI();	}

			},
			/**
			 * 生成workbook对象
			 * @ignore
			 */
			getWorkbook : function(option) {
				var workbookJSON = {
								isZip : option.isZip ? true : false,
								type: option.type, 
								inline: option.inline || false, 
								fileName: option.fileName,
								sysQuery : option.sysQuery || "0",
								sheets: []
								};
				var that = this;
				$.each(option.dataList, function(index, data) {
					data.sheetData && that.handelCustom(workbookJSON, data);
				});
				return workbookJSON;
			},
			/**
			 * 自定义数据
			 * @param {Object} workbookJSON 工作薄
			 * @param {Object} data 数据对象
			 * @ignore
			 */
			handelCustom: function(workbookJSON, data){
				var sheet = {sheetData:[]};
				for(var i=0;i<data.sheetData.length;i++){
					var sheetData = {data:[]};
					var header = data.sheetData[i].header;
					var userData = data.sheetData[i].userData; 
					var format = data.sheetData[i].format;
					var classMethod = data.sheetData[i].classMethod;
					var transform = data.sheetData[i].transform;
					var dataKey = data.sheetData[i].dataKey;
					var topImgs = data.sheetData[i].topImgs;
					var footImgs = data.sheetData[i].footImgs;
					var dataField = data.sheetData[i].dataField; 
					var headerOnly = data.sheetData[i].headerOnly;
					var titleName = data.sheetData[i].titleName;
					var topInfo = data.sheetData[i].topInfo;
					var footInfo = data.sheetData[i].footInfo;
					var mergeKey = data.sheetData[i].mergeKey;
					var topTab = data.sheetData[i].topTab;
					var footTab = data.sheetData[i].footTab;
					var style = data.sheetData[i].style==null?{}:data.sheetData[i].style;
					 if(dataKey){
						 sheetData.dataKey=dataKey;
					 }
					 if(style){
						 sheetData.style=style;
					 }
					//表头
					if(header){
					 	var matrix = new Matrix(header.length);
					 	for(var n=0;n<header.length;n++){
							for(var k=0, j=header[n].length; k<j; k++){
								matrix.push(n, {text:header[n][k], style:"head"});
							}
					 	}
					 	sheetData.data.push(matrix.toRegion());
					 }
					 // 表体
					 if(userData){
						 sheetData.data.push(this.transDataToRow(userData,dataKey,style));
					}
					 if(classMethod){
						 sheetData.classMethod=classMethod;
					 }
					 if(format){
						 sheetData.format=format;
					 }
					 if(transform){
						 sheetData.transform=transform;
					 }
					 //头部图片
					 if(topImgs){
						 sheetData.topImgs=topImgs;
					 }
					 //底部图片
					 if(footImgs){
						 sheetData.footImgs=footImgs;
					 }
					 //标题
					 if(titleName){
						 sheetData.titleName=titleName;
					 }
					 //表头
					 if(topInfo){
						 sheetData.topInfo=topInfo;
					 }
					 //表尾
					 if(footInfo){
						 sheetData.footInfo=footInfo;
					 }
					 //合并列的key
					 if(mergeKey){
						 sheetData.mergeKey=mergeKey;
					 }
					 //头部自定义表格
					 if(topTab){
						var matrix = new Matrix(topTab.rows.length);
					 	for(var n=0;n<topTab.rows.length;n++){
							for(var k=0, j=topTab.rows[n].length; k<j; k++){
								matrix.push(n, {text:topTab.rows[n][k], style:"default"});
							}
					 	}
						sheetData.topTab = topTab;
						sheetData.topTab.rows = matrix.toRegion();
					 }
					 //底部自定义表格
					 if(footTab){
						var matrix = new Matrix(footTab.rows.length);
					 	for(var n=0;n<footTab.rows.length;n++){
							for(var k=0, j=footTab.rows[n].length; k<j; k++){
								matrix.push(n, {text:footTab.rows[n][k], style:"default"});
							}
					 	}
					 	sheetData.footTab = footTab;
						sheetData.footTab.rows=matrix.toRegion();
					 }
					 
					 sheet.sheetData.push(sheetData);
				}
				
				if(!$.isEmptyObject(sheet)){
					sheet.name = data.tabName || "";
					sheet.sheetWidth = data.sheetWidth;
					workbookJSON.sheets.push(sheet);
				}
			},
			/**
			 * 数据转换成excel
			 * 
			 * @param {Array} userData 需转换数据
			 * @param {Array} dataKey 数据键值
			 * @ignore
			 */
			transDataToRow: function(userData,dataKey,style){
				var matrix = new Matrix(userData.length);
				for(var i=0, j=userData.length; i<j; i++){
					var row = userData[i];
					for(var x=0;x<dataKey.length;x++){
						var cell = row[dataKey[x]];
						var cellStyle = style[dataKey[x]]==null?"":style[dataKey[x]];
						matrix.push(i, {text:cell,dataKey:dataKey[x],style:cellStyle});
					}
				}
				return matrix.toRegion();
			}
			
    };


	$.extend({
		exportSysUtil: exportSysUtil
	});

	return exportSysUtil;
})