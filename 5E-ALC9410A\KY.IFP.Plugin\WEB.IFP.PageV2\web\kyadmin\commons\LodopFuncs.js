﻿;
define([ "require","layer" ],function(require,layer) {
	var showHtml = function(html){
		layer.open({
		  type: 1,
		  title:"打印服务",
		  //skin: 'layui-layer-demo', //样式类名
		  //closeBtn: 0, //不显示关闭按钮
		  anim: 0,
		  area: ['400px', 'auto'],
		  shadeClose: true, //开启遮罩关闭
		  content: [
			  "<div style='padding:20px;'>",
				"<h4 color='#FF00FF'>打印服务未安装启动!</h4>",
				"<p>1. 点击这里<a href='/com.kysoft.service/resource/plug/CLodop_Setup_for_Win32NT.exe' target='_self'>下载安装包</a></p>",
				"<p>2. 运行下载的文件进行安装</p>",
				"<p>3. 安装后请<a href='javascript:window.location = window.location;'>刷新页面</a></p>",
				"</div>"
			].join("")
		});
	}
	return {
		loadLodop:function(){
			F.util.showWait();
			return new Promise(function(resolve1,reject){
				if(window.getCLodop){
					resolve1(getCLodop());
					setTimeout(F.util.hideWait,3000);
				}else{
					new Promise(function(resolve, reject){
						try{
							$.getScript(["http://localhost:8000/CLodopfuncs.js?priority=1"],function(){
								resolve();
							});
							$.getScript(["http://localhost:18000/CLodopfuncs.js?priority=0"],function(){
								resolve();
							});
						}catch(ex){}
						setTimeout(function(){
							reject();
						},3000);
					}).then(function(){
						setTimeout(function(){
							resolve1(getCLodop());
							setTimeout(F.util.hideWait,3000);
						},500)
					})["catch"](function(){
						F.util.hideWait()
						showHtml();
					});
				}
			});
		}
	}
});
