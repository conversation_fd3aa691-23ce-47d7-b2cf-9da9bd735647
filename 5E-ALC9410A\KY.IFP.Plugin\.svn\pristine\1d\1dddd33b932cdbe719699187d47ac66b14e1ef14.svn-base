; define([
    "controller",
    "jclass", "iofp/common"],
    function (base, jclass, common) {
        return jclass(base, {
            name: "maintable",
            win: null,
            sdList: [],
            openWin: function () {
                this.log.info("打开弹出页");
            },
            bindEvent: function () {
                var data = win && win.parameter || {};
                var _this = this;
                //保存事件			
                this.bind("saveBtn", "click", this.saveFun, this);
                //退出事件	
                this.bind("exitBtn", "click", function () {
                    if (win) {
                        win.close();
                    }
                });
                $("#qxList").change(function () {
                    let val = _this.controls.qxList.value()
                    console.log(val);
                    _this.loadAvailableMenu(val);
                    //$(this).css("background-color", "#FFFFCC");
                });
            },
            //绑定数据
            initData: function (userid) {
                var _this = this;
                F.ajax({
                    url: '/API/IFP/Rights/Role/UserRoleList',
                    data: { Gid: userid },
                    success: function (resultObj) {
                        _this.initQxData(resultObj);
                        _this.loadAvailableMenu(resultObj)
                    }
                });
            },
            //绑定权限列表数据
            initQxData: function (qxids) {
                var _this = this;
                F.ajax({
                    url: '/API/IFP/Rights/Role/MyRoleList',
                    data: JSON.stringify({}),
                    success: function (resultObj) {

                        var qxList = resultObj.map(item => {
                            return {
                                id: item.Gid,
                                text: item.RoleName
                            }
                        });
                        _this.controls.qxList.data(qxList);
                        _this.controls.qxList.value(qxids);
                    }


                });
            },

            saveFun: function () {
                var _this = this;
                var formdata = {
                    Gid: _this.controls.userGid.value(),
                    RoleIds: _this.controls.qxList.value()
                };
                F.ajax({
                    url: '/API/IFP/Rights/Role/UpdateUserRole',
                    data: JSON.stringify(formdata),
                    success: function (resultObj) {
                        if (resultObj.success) {
                            window.parent.$.alert("设置成功。");
                            if (win) {
                                win.close();
                            }
                        } else {
                            $.alert(resultObj.data);
                        }
                    }
                });
            },
            //可用的和不可用的菜单
            loadAvailableMenu: function (roleids) {
                let _this = this;
                let param = {
                    roleIDs: roleids
                };
                F.ajax({
                    url: '/API/IFP/Rights/Menu/AvailableMenu',
                    data: JSON.stringify(param),
                    success: function (resultObj) {
                        if (resultObj.success) {
                            _this.updateTags(resultObj.data.availableMenu);
                            _this.updateTags2(resultObj.data.unavailableMenu);
                        } else {
                        }
                    }
                });
            },
            updateTags(data) {
                let box = $('#tagList').empty();
                $(data).each(function (i, item) {
                    $('<span class="label label-primary">' + item.menuName + '</span>')
                        .appendTo(box)
                })
            },
            updateTags2(data) {
                let box = $('#tagList2').empty();
                $(data).each(function (i, item) {
                    $('<span class="label label-primary">' + item.menuName + '</span>')
                        .appendTo(box)
                })
            },
            onLoad: function () {
                win = this.getDialogWindow();
                var data = win && win.parameter || {};
                this.controls.userGid.value(data.pGid);

                this.initData(data.pGid);

                this.bindEvent();
                this.updateTags();
            },

        })
    });