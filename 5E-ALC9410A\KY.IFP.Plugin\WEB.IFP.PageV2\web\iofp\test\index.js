define(["./createApi.test"],(createApiTest)=>{

    const tests = [createApiTest]

    return {
        el:"#app",
        data(){
            return {
                message:[]
            }
        },
        filters:{
            stateName(s){
                return {success:"成功",error:"失败"}[s]
            }
        },
        mounted(){
            this.test();
        },
        methods:{
            test(){
                tests.forEach(({name:groupname,test})=>test(
                    (name,msg)=>this.pushMessage(groupname,"success",name,msg),
                    (name,msg)=>this.pushMessage(groupname,"error",name,msg)
                ))
            },
            pushMessage(groupname,type,name,msg){
                let group = this.message.find(item=>item.name === groupname)
                if(!group){
                    group = {name:groupname,message:[]}
                    this.message.push(group);
                }
                group.message.push({type:type,name,content:msg});
            }
        }
    }
})