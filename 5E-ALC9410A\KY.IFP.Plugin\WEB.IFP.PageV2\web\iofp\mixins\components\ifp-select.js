/**
 * 基于 element-ui/el-select 的下拉选择组件
 * export 对象
*/

define(["exports", "configs/config.service", "ELEMENT", "vuex"], function (exports, serviceurl, Element, { mapState }) {

    const ywlxurl = serviceurl._http_getYwlxCombox_;
    const orgurl = '/API/ICS/BaseData/OrgAPI/GetList';
    const roleurl = '/API/IFP/Rights/User/GetUserByRoleCode';
    const SBURL = '/API/ICS/BaseData/Ywdx4009API/SelectSBList';

    // 阻止事件传播
    function stopPropagation(event) {
        this.parentElement[event.type]()
        event.stopPropagation();
    }

    // 生成key
    function genKey(...args) {
        return btoa(encodeURI(args.map(item => {
            if (item === null) { return '' }
            switch (typeof item) {
                case "undefined": return ''
                case "string": return item;
                case "boolean":
                case "number":
                case "symbol":
                case "array":
                case "function":
                    return item.toString()
                case "object":
                    return JSON.stringify(item)
                default:
                    toString.call(a)
            }
        }).join("") + args.length))
    }
    exports.ifpOption = {
        extends: Element.Option
    }

    // ElSelect 原始属性名列表
    const ElSelectPropsKeys = Object.keys(Element.Select.props);

    /**
     * 下拉选择控件
     */
    exports.ifpSelect = {
        model: {
            prop: 'value',
            event: 'change'
        },
        props: {
            // 继承所有属性
            ...Element.Select.props,


            // 始终显示当前值
            alwaysShowCurrentValue: { type: Boolean, default: true },
            clearable: { type: Boolean, default: true },
            showid: { type: Boolean,default: false },
            // 添加 options 字段，注意，如果配置了此字段，html 中的 option 将会清空
            items: {
                default() { return [] },
            },
            canedit: { type: Boolean,default: true },

            // 使用缓存
            cache: { type: Boolean,default: false },
            url: { default: '' },
            post: { type: [Object], default: undefined },
            labelKey: { default: '' },
            disabledKey: { default: '' },
            filter: Function,
            filterArg: { type: [String, Number, Boolean, Object], default: '' },
            valueIsNumber: { type: Boolean }
        },
        data() {
            return {
                rawurl: "",
                rawpost: undefined,
                rendered: false
                //renderItems:[]
            }
        },
        computed: {
            // 多选，且 typeof value === "string"
            isMultipleString() {
                return this.multiple && typeof this.value === "string"
            },

            // 计算属性值，用于转换 value
            // 此值用于 绑定 子元素 el-element 的 value 属性
            computed_value: {
                get() {
                    if (this.isMultipleString) {
                        if (!this.value) { return [] }
                        let rev = this.value.split(',').filter(item => item !== '');
                        if (this.valueIsNumber) {
                            return rev.map(Number)
                        } else {
                            return rev;
                        }
                    } else {
                        if (this.value === null || typeof this.value === "undefined") { return '' }
                        return this.value;
                    }
                },
                set(v) {
                    if (this.isMultipleString) {
                        if (!v && v !== 0) { 
                            this.$emit("change", '')
                            return;
                        }
                        const set = new Set(v.map(item => item.toString()));
                        this.$emit("change", [...set].join(','))
                    } else {
                        this.$emit("change", v)
                    }
                }
            },
            ElSelectProps() {
                let rev = {};
                for (let att in this.$props) {
                    if (ElSelectPropsKeys.includes(att)) {
                        rev[att] = this.$props[att]
                    }
                }
                return rev;
            },
            // 缓存key
            cacheKey() {
                if (this.rawurl) {
                    return genKey(this.rawurl, this.rawpost)
                } else {
                    return ''
                }
            },
            label() {
                if (this.multiple) {
                    return this.itemsSource
                        .filter(item => this.computed_value.includes(item[this.valueKey]))
                        .map(item => item[this.labelKey]).join(',')
                } else {
                    let item = this.itemsSource.find(item => item[this.valueKey] === this.computed_value);
                    return item && item[this.labelKey] || this.computed_value || ''
                }
            },
            ...mapState({
                itemsSource(state) {
                    let rev = [];
                    if (this.cacheKey) {
                        const item = state.ywlx.items.find(item => item.name == this.cacheKey) || null;
                        rev = item?.data || [];
                    } else {
                        rev = this.items || [];
                    }
                    return rev;
                }
            }),
            renderItems() {
                let rev = JSON.parse(JSON.stringify(this.itemsSource));
                if (this.filter) {
                    rev = this.filter(rev, this.filterArg)
                }
                rev = rev || [];
                rev = this.itemsMap(rev);

                // 下拉列表中始终显示当前值
                if (this.alwaysShowCurrentValue) {
                    // 当前值未存在于选择列表中
                    if (!rev.find(item => this.inSelected(item[this.valueKey]))) {
                        // 当前值存在于选择全部列表中
                        let oi = this.itemsSource.find(item => this.inSelected(item[this.valueKey]))
                        if (oi) {
                            let opt = Object.assign({}, oi)
                            opt.disabled = false;
                            rev.push(opt)
                        }
                    }
                }
                return rev;
            },
            canpost() {
                // console.log("can ifp-select");
                return !!this.cacheKey
            }
        },
        watch: {
            url() {
                this.rawurl = this.url;
            },
            rawurl() {
                // console.log("url changed");
                this.updateState();
            },
            post() {
                this.$set(this, "rawpost", this.post);
            },
            rawpost() {
                // console.log("post changed");
                this.updateState();
            }
        },
        mounted() {

            if (this.url) { this.rawurl = this.url; }
            if (this.post) { this.rawpost = this.post; }

            this.updateState();

            // 下拉刷新
            this.$on('visible-change', (visibled) => {
                if (this.rawurl && visibled) {
                    this.updateState();
                }
            })
        },
        methods: {
            getItemValue(i) {
                let item = this.getItem(i);
                return item ? item[this.valueKey]:null;
            },
            getItem(i) {
                if (this.renderItems.length) {
                    return this.renderItems[i];
                } else {
                    return 
                }
            },
            // 是否选择了某个值
            inSelected(v) {
                // 单选
                if (typeof this.computed_value === "string") {
                    return this.computed_value === v
                }
                // 多选
                if (this.computed_value.find) {
                    // 这里不能用 ===
                    return this.computed_value.find(item => item == v);
                }
                // 不应该执行到这
                return false;
            },
            updateState() {
                if (this.canpost) {
                    this.$store.dispatch("ywlx/update", {
                        name: this.cacheKey,
                        url: this.rawurl,
                        post: this.rawpost,
                        expires: 10000 // 缓存1s
                    }).then(_ => {
                        this.$emit("update")
                    })
                }
            },
            // 为扩展组件预留的接口，渲染前可以再处理一次
            itemsMap(list) {
                //console.log("items map")
                return list;
            },
            clearCache() {
                // 清除缓存
                if (this.url) {
                    // this.$api.cache.remove();
                }
            }
        },
        extends2: Element.Select,
        render(h) {
            let self = this;
            if (this.canedit) {
                return h(Element.Select, {
                    attrs: {
                        ...this.$attrs
                    },
                    props: {
                        ...this.ElSelectProps,
                        value: this.computed_value
                    },
                    on: {
                        ...this._events,
                        change(v) {
                            self.computed_value = v;
                        }
                    }
                }, [
                    ...this.renderItems.map(item => h('ifp-option', {
                        props: {
                            value: item[this.valueKey],
                            label: item[this.labelKey],
                            disabled: item.disabled === true
                        }
                    }, this.showid ? [
                        h('span', { style: "float: left;margin-right:1rem;" }, item[this.labelKey]),
                        h('span', { style: "float: right;color: #8492a6; font-size: 13px" }, item[this.valueKey])
                    ] : [])),
                    this.$slots.default
                ])
            } else {
                return h('div', { class: 'el-select el-select--small' }, this.label)
            }
        }
    }


    exports.ifpSelectYwlx = {
        props: {
            clearable: { type: Boolean, default: true },

            post: { default() { return {} } },
            showid: { default: true },
            ywlx: { default: '' },
            url: { default: ywlxurl },

            // 展示已作废的选项
            showzf: { default: false },
            filterable: { default: true },
            // 可选择已作废的选项
            setzf: { default: false },

            labelKey: { default: 'text' },
            valueKey: { default: 'id' },
            disabledKey: { default: 'zfbz' },
            valueIsNumber: { default: Boolean, default: true }
        },
        watch: {
            ywlx() {
                this.$set(this, "rawpost", {
                    ...this.rawpost,
                    ywlx: this.ywlx
                })
            }
        },
        mounted() {
            this.rawpost = this.rawpost || {};
            if (this.ywlx) {
                this.$set(this, "rawpost", {
                    ...this.rawpost,
                    ywlx: this.ywlx
                })
            }
        },
        computed: {
            canpost() {
                // console.log("can ifp-select-ywlx");
                return this.cacheKey && this.rawurl && this.rawpost && !!Object.keys(this.rawpost).length
            }
        },
        extends: exports.ifpSelect,
        methods: {
            // 重写 ifp-select 方法
            itemsMap(list) {
                let rev = list;

                // 不显示 zfb === 1 的
                if (!this.showzf) {
                    rev = rev.filter(item => item.zfbz !== 1)
                }

                if (!this.setzf) {
                    rev = rev.map(item => {
                        item.disabled = item.zfbz === 1;
                        return item;
                    })
                }
                return rev;
            }
        }
    }


    // 组织选择
    exports.ifpSelectOrg = {
        props: {
            valueKey: { default: 'id' },
            cache: { default: true },
            labelKey: { default: 'text' },
            post: { type: Object, default: () => ({}) },
            url: { default: orgurl }
        },
        extends: exports.ifpSelect
    }

    // 根据角色选择人员 ifp-select-roleuser
    exports.ifpSelectRoleuser = {
        props: {
            valueKey: { default: 'id' },
            cache: { default: true },
            labelKey: { default: 'text' },
            code: { default: '' },
            post: { type: Object, default: () => ({ code: "" }) },
            url: { default: roleurl }
        },
        watch: {
            code() {
                // 更新post
                this.updatePost();
            }
        },
        mounted() {
            this.rawpost = { ...this.rawpost, code: '' };
            this.updatePost();
        },
        methods: {
            updatePost(code) {
                this.$set(this, "rawpost", {
                    ...this.rawpost,
                    code: code || this.code
                })
            }
        },
        extends: exports.ifpSelect
    }

    // 选择设备
    exports.ifpSelectSb = {
        props: {
            // 设备用途 入厂、入炉 逗号分隔字符串
            sbyt: { default: '' },
            // 设备类型 采样机、制样机 逗号分隔字符串
            sblx: { default: '' },
            valueKey: { default: 'Gid' },
            cache: { default: true },
            labelKey: { default: 'Bname' },
            post: { type: Array, default: () => [] },
            url: { default: SBURL },
            valueIsNumber:{type:Boolean, default: true},
            showid: { type: Boolean, default: false },
            // 展示已作废的选项
            showzf: { default: false },
            filterable: { default: true },
            // 可选择已作废的选项
            setzf: { default: false },
        },
        watch: {
            // 更新post
            sbyt() {
                this.updatePost();
            },
            // 更新post
            sblx() {
                this.updatePost();
            }
        },
        mounted() {
            this.rawpost = [];
            this.updatePost();
        },
        methods: {
            updatePost() {
                let post = [
                    ...this.sbyt?this.sbyt.split(',').map(yt=>({
                        Sbyt1009:{"Value":yt,"Match": "HAS","Merge":"OR"}
                    })):[],
                    ...this.sblx?this.sblx.split(',').map(lx=>({
                        Sblx1002:{"Value":lx,"Match": "EQU","Merge":"AND"}
                    })):[]
                ];
                this.$set(this, "rawpost", post)
            },
            itemsMap(list) {
                let rev = list;

                // 不显示 zfb === 1 的
                if (!this.showzf) {
                    rev = rev.filter(item => item.Zfbz !== 1)
                }

                if (!this.setzf) {
                    rev = rev.map(item => {
                        item.disabled = item.Zfbz === 1;
                        return item;
                    })
                }
                return rev;
            }
        },
        extends: exports.ifpSelect
    }
})