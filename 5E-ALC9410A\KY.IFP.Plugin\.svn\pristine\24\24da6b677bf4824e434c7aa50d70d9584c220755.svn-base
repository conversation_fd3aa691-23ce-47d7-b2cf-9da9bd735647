﻿//using LinqToDB;
//using COM.IFP.LinqDB;
using COM.IFP.PLC.SiemensS7.DataPoint;
using COM.IFP.SqlSugarN;
using ORM.IFP.DbModel.PLC;
using ORM.IFP.PLC;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
//using PLC_POINTS = ORM.IFP.DbModel.PLC.PLC_POINTS;

namespace DAL.IFP.PLC
{
    public class PLCState
    {
        /// <summary>
        /// 更新PLCState
        /// TODO daiabin这个好像没用
        /// </summary>
        /// <param name="PLCStateList"></param>
        /// <returns></returns>
        public void UpDatePLCState(List<PLCPoint> PLCPointList)
        {
            List<PLC_STATE> PLCStateList = new List<PLC_STATE>();
            foreach (var i in PLCPointList)
            {
                PLCStateList.Add(new PLC_STATE
                {
                    Name = i.Name,
                    Value = (int)i.Value,
                    OperateTime = DateTime.Now
                });
            }
            using (var db = DB.Create())
            {
                BaseDbHelper<PLC_POINTS> dbHelper = new BaseDbHelper<PLC_POINTS>(db);
                //得到PLCState表所有名字
                List<string> stateNameList = dbHelper.dbClient.Queryable<PLC_STATE>().Select(f => f.Name.Value).ToList();
                //将PLCState表所有名字合起来
                string stateNameString = string.Join(",", stateNameList);
                //找到PLCState表中已有的需要更新的记录
                List<PLC_STATE> updatelist = PLCStateList.Where(a => stateNameString.Contains(a.Name.Value)).ToList();
                //找到PLCState表中没有的需要插入的记录
                List<PLC_STATE> insertlist = PLCStateList.Where(a => !stateNameString.Contains(a.Name.Value)).ToList();
                //更新
                foreach (PLC_STATE one in updatelist)
                {
                    //dbHelper.dbClient.Updateable<PLC_STATE>().Where(a => a.Name == one.Name)
                    //    .SetColumns(a => a.Value, one.Value)
                    //    .SetColumns(a => a.OperateTime, one.OperateTime);
                    dbHelper.dbClient.Updateable<PLC_STATE>()
                            .SetColumns(x => new PLC_STATE
                            {
                                Value = one.Value,
                                OperateTime = one.OperateTime
                            })
                            .Where(a => a.Name == one.Name)
                            .ExecuteCommand();
                }
                //插入
                foreach (var one in insertlist)
                    dbHelper.dbClient.Insertable(one).ExecuteCommand(); ;
            }
        }

    }
}
