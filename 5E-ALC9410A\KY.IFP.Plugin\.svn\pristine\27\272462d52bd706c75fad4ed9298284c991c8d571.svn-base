﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统操作记录</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" v-loading="loading">
        <ifp-toolbar close>
            <!--<ifp-button @click="alarmClear" :disabled="disabled.alarmCleared" log>报警解除</ifp-button>-->
            <ifp-button @click="onExport" log>导出</ifp-button>
            <div style="float:right;">
                <span class="dicb dicb-tuxing" style="font-size:16px;color:#0075D3"></span>
                <span style="font-size: 14px; font-weight: bold;">
                    操作记录
                </span>
            </div>
        </ifp-toolbar>
        <ifp-searchbar>
            <ifp-form-item label="操作日期">
                <ifp-date-picker v-model="filter.start" type="date"
                                 placeholder="选择日期" style="width: 190px"></ifp-date-picker>
                <span class="demonstration">至:</span>
                <ifp-date-picker v-model="filter.end" type="date"
                                 placeholder="选择日期" style="width: 190px"></ifp-date-picker>
            </ifp-form-item>
            <ifp-form-item label="人员姓名">
                <ifp-input v-model="filter.UserName" placeholder="支持模糊查询" clear>
                </ifp-input>
            </ifp-form-item>
            <ifp-form-item label="页面名称">
                <ifp-input v-model="filter.PageName" placeholder="支持模糊查询" clear>
                </ifp-input>
            </ifp-form-item>
            <ifp-form-item label="按钮名称">
                <ifp-input v-model="filter.BtnName" placeholder="支持模糊查询" clear>
                </ifp-input>
            </ifp-form-item>

            <ifp-button role="查询" @click="onSelect">查询</ifp-button>
            <ifp-button role="重置" @click="onReset">重置</ifp-button>
        </ifp-searchbar>
        <ifp-panel-table class="flex-item padding">
            <ifp-table ref="tableRef"
                       :data="data"
                       row-key="GID"
                       :border="true"
                       :highlight-current-row="true"
                       @row-click="rowClick">
                <el-table-column type="index"
                                 :index="indexMethod"
                                 width="50">
                </el-table-column>
                <el-table-column prop="PageName"
                                 label="页面名称"
                                 align="center"
                                 width="150">
                </el-table-column>
                <el-table-column prop="UserId"
                                 label="人员ID"
                                 align="center"
                                 width="200">
                </el-table-column>
                <el-table-column prop="UserName"
                                 label="人员姓名"
                                 align="center"
                                 width="140">
                </el-table-column>
                <el-table-column prop="BtnName"
                                 label="按钮名称"
                                 align="center"
                                 width="140">
                </el-table-column>
                <el-table-column prop="LogTime"
                                 label="操作时间"
                                 align="center"
                                 width="150">
                </el-table-column>

            </ifp-table>
            <template v-slot:pagination>
                <ifp-pagination @size-change="sizeChange"
                                @current-change="pageChange"
                                :current-page="paging.page"
                                :page-sizes="[10,20,100,10000]"
                                :page-size="paging.size"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="paging.records">
                </ifp-pagination>
            </template>
        </ifp-panel-table>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>