<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SignalR测试页面</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .card {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 15px;
        }

        button {
            padding: 8px 15px;
            margin-right: 5px;
        }

        #log {
            height: 300px;
            border: 1px solid #ccc;
            padding: 10px;
            overflow-y: scroll;
            background-color: #f5f5f5;
            font-family: monospace;
        }

        .error {
            color: red;
        }

        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>SignalR 测试页面</h1>

    <div class="card">
        <h2>连接状态: <span id="connectionStatus">未连接</span></h2>
        <button id="connectBtn">连接到服务器</button>
        <button id="disconnectBtn">断开连接</button>
        <button id="heartbeatBtn">发送心跳</button>
    </div>

    <div class="card">
        <h2>配置测试</h2>
        <div>
            页面ID: <input type="text" id="pageId" value="TestPage">
            推送间隔(ms): <input type="number" id="interval" value="1000">
            <button id="configBtn">发送配置</button>
        </div>
    </div>

    <div class="card">
        <h2>控制命令测试</h2>
        <textarea id="commandText" style="width:100%; height:100px;">
{
  "CMD": "TestCommand",
  "Parameter": {
    "param1": "value1",
    "param2": 123
  }
}
        </textarea>
        <button id="commandBtn">发送命令</button>
    </div>

    <div class="card">
        <h2>系统信息</h2>
        <div>
            <p>SignalR版本: <span id="signalrVersion"></span></p>
            <p>连接ID: <span id="connectionId">未连接</span></p>
            <button id="checkBtn">检查状态</button>
        </div>
    </div>

    <div class="card">
        <h2>日志</h2>
        <div id="log"></div>
        <button id="clearLogBtn">清空日志</button>
    </div>

    <script>
        // 显示SignalR版本
        document.getElementById('signalrVersion').textContent = signalR.VERSION || "未知";

        // 记录日志
        function log(message, type = '') {
            const logElem = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logElem.innerHTML += `<div class="${type}">[${time}] ${message}</div>`;
            logElem.scrollTop = logElem.scrollHeight;
            console.log(`[${time}] ${message}`);
        }

        // 更新连接状态显示
        function updateConnectionStatus(status, color) {
            const statusElem = document.getElementById('connectionStatus');
            statusElem.textContent = status;
            statusElem.style.color = color;
        }

        // 创建连接
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/socket")
            .withAutomaticReconnect()
            .configureLogging(signalR.LogLevel.Debug)
            .build();

        // 连接状态变化事件
        connection.onreconnecting(error => {
            updateConnectionStatus("正在重连...", "orange");
            log(`连接断开，正在尝试重连: ${error ? error.message : "未知错误"}`, "error");
        });

        connection.onreconnected(connectionId => {
            updateConnectionStatus("已连接", "green");
            document.getElementById('connectionId').textContent = connectionId;
            log(`重新连接成功，新连接ID: ${connectionId}`, "success");
        });

        connection.onclose(error => {
            updateConnectionStatus("已断开", "red");
            document.getElementById('connectionId').textContent = "未连接";
            log(`连接关闭: ${error ? error.message : "正常关闭"}`, error ? "error" : "");
        });

        // 注册回调
        connection.on("message", (message) => {
            log(`收到消息: ${message}`);
            try {
                const data = JSON.parse(message);
                log(`解析后的消息: ${JSON.stringify(data, null, 2)}`);
            } catch (e) {
                // 不是JSON格式，忽略错误
            }
        });

        connection.on("detect", (response) => {
            log(`收到心跳响应: ${response}`, "success");
        });

        connection.on("action", (response) => {
            log(`收到Action响应: ${JSON.stringify(response, null, 2)}`);
        });

        connection.on("active", (data) => {
            log(`收到Active响应: ${JSON.stringify(data, null, 2)}`);
        });

        // 连接按钮
        document.getElementById('connectBtn').addEventListener('click', async () => {
            if (connection.state === signalR.HubConnectionState.Connected) {
                log("已经连接，无需重复连接");
                return;
            }

            try {
                log("正在连接到服务器...");
                await connection.start();
                updateConnectionStatus("已连接", "green");
                document.getElementById('connectionId').textContent = connection.connectionId || "已连接但无ID";
                log("连接成功!", "success");
            } catch (err) {
                updateConnectionStatus("连接失败", "red");
                log(`连接失败: ${err}`, "error");
                console.error("连接错误详情:", err);
            }
        });

        // 断开按钮
        document.getElementById('disconnectBtn').addEventListener('click', async () => {
            if (connection.state !== signalR.HubConnectionState.Connected) {
                log("未连接，无需断开");
                return;
            }

            try {
                await connection.stop();
                updateConnectionStatus("已断开", "red");
                log("已断开连接");
            } catch (err) {
                log(`断开失败: ${err}`, "error");
                console.error("断开错误详情:", err);
            }
        });

        // 心跳按钮
        document.getElementById('heartbeatBtn').addEventListener('click', async () => {
            if (connection.state !== signalR.HubConnectionState.Connected) {
                log("未连接，无法发送心跳", "error");
                return;
            }

            try {
                log("发送心跳...");
                await connection.invoke("Detect");
                log("心跳发送成功");
            } catch (err) {
                log(`发送心跳失败: ${err}`, "error");
                console.error("心跳错误详情:", err);
            }
        });

        // 配置按钮
        document.getElementById('configBtn').addEventListener('click', async () => {
            if (connection.state !== signalR.HubConnectionState.Connected) {
                log("未连接，无法发送配置", "error");
                return;
            }

            const pageId = document.getElementById('pageId').value;
            const interval = document.getElementById('interval').value;

            try {
                const configData = {
                    PageID: pageId,
                    Interval: parseInt(interval)
                };

                const message = {
                    H: "SConfig",
                    C: JSON.stringify(configData),
                    I: "config_" + new Date().getTime()
                };

                log(`发送配置: ${JSON.stringify(message)}`);
                await connection.invoke("HandleCommand", JSON.stringify(message));
                log("配置发送成功");
            } catch (err) {
                log(`发送配置失败: ${err}`, "error");
                console.error("配置错误详情:", err);
            }
        });

        // 命令按钮
        document.getElementById('commandBtn').addEventListener('click', async () => {
            if (connection.state !== signalR.HubConnectionState.Connected) {
                log("未连接，无法发送命令", "error");
                return;
            }

            try {
                const commandText = document.getElementById('commandText').value;

                // 验证JSON格式
                JSON.parse(commandText);

                const message = {
                    H: "SControl",
                    C: commandText,
                    I: "cmd_" + new Date().getTime()
                };

                log(`发送命令: ${JSON.stringify(message)}`);
                await connection.invoke("HandleCommand", JSON.stringify(message));
                log("命令发送成功");
            } catch (err) {
                log(`发送命令失败: ${err}`, "error");
                console.error("命令错误详情:", err);
            }
        });

        // 状态检查按钮
        document.getElementById('checkBtn').addEventListener('click', () => {
            log(`当前连接状态: ${connection.state}`);
            log(`连接ID: ${connection.connectionId || "未连接"}`);
            log(`传输类型: ${connection.connection ? connection.connection.transport.name : "未知"}`);
        });

        // 清空日志按钮
        document.getElementById('clearLogBtn').addEventListener('click', () => {
            document.getElementById('log').innerHTML = '';
            log("日志已清空");
        });

        // 页面加载完成
        log("页面已加载，请点击'连接到服务器'按钮开始测试");
    </script>
</body>
</html>