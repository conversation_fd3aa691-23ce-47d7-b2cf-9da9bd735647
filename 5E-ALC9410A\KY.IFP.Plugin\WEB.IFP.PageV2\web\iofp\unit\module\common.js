define(["commons/com.kjsoft.common"],function(common){
    return function(QUnit){
        QUnit.module('common', function() {
            QUnit.test('isEmpty', function(assert) {
                assert.equal(common.isEmpty(0), false,"common.isEmpty(0) == false");
                assert.equal(common.isEmpty(), true,"common.isEmpty() == true");
                assert.equal(common.isEmpty(null), true,"common.isEmpty(null) == true");
            });
        });
    }
})