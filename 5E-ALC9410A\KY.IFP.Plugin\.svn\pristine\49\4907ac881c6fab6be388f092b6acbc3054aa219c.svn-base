﻿using COM.IFP.Client;
using COM.IFP.Common;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Text.Json;

namespace API.IFP.Client.Setting
{
    /// <summary>
    /// 产品挂接-中心发起
    /// 产品在配置中心挂接注册时使用该接口，该接口由配置中心发起。
    /// </summary>
    public class RegDown : IReceive
    {
        public IOFP_Struct ReceiveRun(IOFP_Struct iofp)
        {
            //先存入文件DataCenter.json
            JObject jo_center = JObject.Parse(iofp.msg);

            //已经注册过，使用原有的regCode
            JObject jo_center_old = JObject.Parse(Config.ReadJsonElement("DataCenter").ToString());
            if (jo_center_old != null)
            {
                if (jo_center_old["regCode"] != null)
                {
                    jo_center["regCode"] = jo_center_old["regCode"];
                }
            }
            //Config.WriteJson("DataCenter", JsonDocument.Parse(jo_center.ToString()).RootElement);
            Config.WriteJsonString("DataCenter", jo_center.ToString());
            Config.Reload();
            //JObject jObj_dataCenter = JObject.Parse(Config.ReadJson("DataCenter").ToString());
            ////获取好相应的鉴权密钥
            //IOFP_APIHelper.AuthKey = jObj_dataCenter["authKey"].ToString();

            //返回注册信息给中心
            Dictionary<string, object> jo = ProuductRegisterGet.ReadProuductRegister();

            iofp.msg = JsonSerializer.Serialize(jo);
            return iofp;
        }
    }
}
