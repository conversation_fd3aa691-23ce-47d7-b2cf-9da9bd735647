﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace KY.IFP.Runtime
{
    #region 旧版本，已注释
    //public class Config
    //{
    //    private static Dictionary<string, JToken> caching = new Dictionary<string, JToken>(StringComparer.InvariantCultureIgnoreCase);

    //    private static string storage = "Config.json";
    //    /// <summary>
    //    /// 配置文件路径，默认为Config.json（更改配置文件路径必须在访问Config的其他属性和方法之前，否则会报错）
    //    /// </summary>
    //    public static string Storage
    //    {
    //        get
    //        {
    //            return storage;
    //        }
    //        set
    //        {
    //            if (jobject != null)
    //            {
    //                throw new Exception("更改配置文件路径必须在访问Config的其他属性和方法之前");
    //            }
    //            else
    //            {
    //                storage = value;
    //            }
    //        }
    //    }

    //    private static JObject jobject;
    //    /// <summary>
    //    /// 配置文件对象
    //    /// </summary>
    //    public static JObject JObject
    //    {
    //        get
    //        {
    //            if (jobject is null)
    //            {
    //                jobject = (JObject)JsonConvert.DeserializeObject(File.ReadAllText(storage));
    //            }
    //            return jobject;
    //        }
    //    }

    //    /// <summary>
    //    /// 获取配置项
    //    /// </summary>
    //    /// <typeparam name="T"></typeparam>
    //    /// <param name="path">配置项路径</param>
    //    /// <param name="mask">是否为密文，默认非密文</param>
    //    /// <returns></returns>
    //    public static T GetValue<T>(string path, bool mask = false)
    //    {
    //        if (TryGetValue(path, out T data, mask))
    //        {
    //            return data;
    //        }
    //        else
    //        {
    //            return default;
    //        }
    //    }

    //    /// <summary>
    //    /// 获取配置项
    //    /// </summary>
    //    /// <typeparam name="T"></typeparam>
    //    /// <param name="path">配置项路径</param>
    //    /// <param name="mask">是否为密文，默认false非密文</param>
    //    /// <returns></returns>
    //    public static bool TryGetValue<T>(string path, out T data, bool mask = false)
    //    {
    //        data = default;
    //        if (caching.ContainsKey(path))
    //        {
    //            data = caching[path].ToObject<T>();
    //            return true;
    //        }
    //        JObject jObject = JObject;
    //        string[] paths = path.Split(new[] { '.' }, StringSplitOptions.RemoveEmptyEntries);
    //        for (int i = 0; i < paths.Length - 1; i++)
    //        {
    //            jObject = jObject.Property(paths[i], StringComparison.InvariantCultureIgnoreCase)?.Value as JObject;
    //            if (jObject is null) return false;
    //        }
    //        JToken jToken = jObject.Property(paths[paths.Length - 1], StringComparison.InvariantCultureIgnoreCase)?.Value;
    //        if (jToken != null)
    //        {
    //            if (mask && jToken.Type == JTokenType.String)
    //            {
    //                var value = Secure.Decrypt(jToken.Value<string>());
    //                if (typeof(T) == typeof(string))
    //                {
    //                    jToken = value;
    //                }
    //                else
    //                {
    //                    jToken = JToken.Parse(value);
    //                }
    //            }
    //            caching[path] = jToken;
    //            data = jToken.ToObject<T>();
    //            return true;
    //        }
    //        return false;
    //    }



    //    /// <summary>
    //    /// 设置配置项，注意，调用此方法将导致配置文件重新存储、缓存清空，不建议高频率调用此方法来存储数据
    //    /// </summary>
    //    /// <typeparam name="T"></typeparam>
    //    /// <param name="name">配置项路径</param>
    //    /// <param name="value">配置项对象</param>
    //    /// <param name="mask">是否存密文，默认存明文</param>
    //    public static void SetValue<T>(string name, T value, bool mask = false)
    //    {
    //        JObject jObject = JObject;
    //        string[] paths = name.Split(new[] { '.' }, StringSplitOptions.RemoveEmptyEntries);
    //        for (int i = 0; i < paths.Length - 1; i++)
    //        {
    //            jObject = jObject.Property(paths[i], StringComparison.InvariantCultureIgnoreCase)?.Value as JObject;
    //            if (jObject == null) return;
    //        }
    //        jObject[paths[paths.Length - 1]] = mask ? Secure.Encrypt((value is string) ? (value as string) : JToken.FromObject(value).ToString(Formatting.None)) : JToken.FromObject(value);
    //        File.WriteAllText(Storage, JsonConvert.SerializeObject(JObject, Formatting.Indented));
    //        //任意值更改，就清空缓存，并重新读取
    //        caching.Clear();
    //        jobject = null;
    //    }
    //}
    #endregion

    /// <summary>
    /// Json配置文件读写
    /// </summary>
    public static class Config
    {
        /// <summary>
        /// 通用格式化配置
        /// </summary>
        public class Format
        {
            /// <summary>
            /// 格式定义字符，可适配Json配置属性Define、Format、Text
            /// </summary>
            public string Define { get; set; }
            //[JsonPropertyName("Format")]
            private string format { set { Define = value; } }
            //[JsonPropertyName("Text")]
            private string text { set { Define = value; } }

            /// <summary>
            /// 填充参数列表，可适配Json配置属性Params、Inputs、Args
            /// </summary>
            public List<string> Params { get; set; }
            //[JsonPropertyName("Inputs")]
            private List<string> inputs { set { Params = value; } }
            //[JsonPropertyName("Args")]
            private List<string> args { set { Params = value; } }

            /// <summary>
            /// 转字符串重载
            /// </summary>
            /// <returns>填充后字符串</returns>
            public override string ToString() => string.Format(new Secure.Convert(), Define, Params.ToArray());

            /// <summary>
            /// 隐式转字符串
            /// </summary>
            /// <param name="format"></param>
            public static implicit operator string(Format format) => format?.ToString();

            /// <summary>
            /// 创建格式化配置
            /// </summary>
            /// <param name="text">格式字符</param>
            /// <param name="args">参数列表</param>
            /// <returns></returns>
            public static Format Create(string text, IEnumerable<string> args) => new() { Define = text, Params = args.ToList() };

            /// <summary>
            /// 创建格式化配置
            /// </summary>
            /// <param name="text">格式字符</param>
            /// <param name="args">参数列表</param>
            /// <returns></returns>
            public static Format Create(string text, params string[] args) => new() { Define = text, Params = args.ToList() };

            /// <summary>
            /// 根据字符格式加密参数
            /// </summary>
            /// <param name="text">格式字符</param>
            /// <param name="args">明文列表</param>
            /// <returns>密文列表</returns>
            public static List<string> Encode(string text, List<string> args)
            {
                var dict = Secure.Convert.Analyse(text);
                foreach (var pair in dict)
                {
                    switch (pair.Value.ToUpper())
                    {
                        case "AES":
                            args[pair.Key] = Secure.Encrypt(args[pair.Key]);
                            break;
                    }
                }
                return args;
            }

            /// <summary>
            /// 根据字符格式解密参数
            /// </summary>
            /// <param name="text">格式字符</param>
            /// <param name="args">密文参数</param>
            /// <returns>明文参数</returns>
            public static List<string> Decode(string text, List<string> args)
            {
                var dict = Secure.Convert.Analyse(text);
                foreach (var pair in dict)
                {
                    switch (pair.Value.ToUpper())
                    {
                        case "AES":
                            args[pair.Key] = Secure.Decrypt(args[pair.Key]);
                            break;
                    }
                }
                return args;
            }
        }

        /// <summary>
        /// 从默认配置文件Config.json读取配置
        /// </summary>
        /// <param name="func">配置存储表达式，func必须返回New匿名对象，对象成员的引用即为对应配置信息的存储变量</param>
        public static void Read(Expression<Func<object>> func)
        {
            Read("Config.json", func);
        }
        /// <summary>
        /// 从指定配置文件路径读取配置
        /// </summary>
        /// <param name="path">配置文件存储路径</param>
        /// <param name="func">配置存储表达式，func必须返回New匿名对象，对象成员的引用即为对应配置信息的存储变量</param>
        public static void Read(string path, Expression<Func<object>> func)
        {
            Read(func, File.ReadAllText(path));
        }
        /// <summary>
        /// 从Json读取配置
        /// </summary>
        /// <param name="func">配置存储表达式，func必须返回New匿名对象，对象成员的引用即为对应配置信息的存储变量</param>
        /// <param name="json">Json字符串</param>
        public static void Read(Expression<Func<object>> func, string json)
        {
            Read(JsonDocument.Parse(json).RootElement, func.Body as NewExpression);
        }

        /// <summary>
        /// 部分配置信息存储，根据表达式覆盖或附加部分配置信息
        /// </summary>
        /// <param name="func">配置信息表达式，func必须返回New匿名对象</param>
        public static void Save(Expression<Func<object>> func)
        {
            Save("Config.json", func);
        }
        /// <summary>
        /// 部分配置信息存储，根据表达式覆盖或附加部分配置信息
        /// </summary>
        /// <param name="path">配置文件保存路径</param>
        /// <param name="func">配置信息表达式，func必须返回New匿名对象</param>
        public static void Save(string path, Expression<Func<object>> func)
        {
            var json = File.Exists(path) ? JsonDocument.Parse(File.ReadAllText(path)) : null;
            if (func.Body is NewExpression body)
            {
                var root = json?.RootElement ?? new JsonElement();
                for (int i = 0; i < body.Members.Count; i++)
                {
                    root = root.SetValue(body.Members[i].Name, Expression.Lambda(body.Arguments[i]).Compile().DynamicInvoke());
                }
                File.WriteAllText(path, root.ToString());
            }
        }
        /// <summary>
        /// 整体配置信息存储，覆盖原配置文件
        /// </summary>
        /// <param name="data">配置信息</param>
        public static void Save(object data)
        {
            Save("Config.json", data);
        }
        /// <summary>
        /// 整体配置信息存储，覆盖原配置文件
        /// </summary>
        /// <param name="path">配置文件保存路径</param>
        /// <param name="data">配置信息</param>
        public static void Save(string path, object data)
        {
            File.WriteAllText(path, JsonSerializer.Serialize(data, new JsonSerializerOptions() { WriteIndented = true }));
        }

        #region 私有方法
        private static void Read(JsonElement json, NewExpression body)
        {
            if (json.ValueKind == JsonValueKind.Object && body != null)
            {
                var dict = json.EnumerateObject().ToDictionary(x => x.Name, x => x.Value, StringComparer.OrdinalIgnoreCase);
                for (int i = 0; i < body.Arguments.Count; i++)
                {
                    if (dict.TryGetValue(body.Members[i].Name, out JsonElement data))
                    {
                        if (body.Arguments[i] is NewExpression)
                        {
                            Read(data, body.Arguments[i] as NewExpression);
                        }
                        else if (body.Arguments[i] is MemberExpression)
                        {
                            var linq = body.Arguments[i] as MemberExpression;
                            var from = default(object);
                            if (linq.Expression != null)
                            {
                                //非静态变量
                                from = Expression.Lambda(linq.Expression).Compile().DynamicInvoke();
                            }
                            var info = linq.Member;
                            if (info != null)
                            {
                                try
                                {
                                    if (info is PropertyInfo prop)
                                    {
                                        prop.SetValue(from, data.GetValue(prop.PropertyType), null);
                                    }
                                    else if (info is FieldInfo item)
                                    {
                                        item.SetValue(from, data.GetValue(item.FieldType));
                                    }
                                }
                                catch
                                {
                                    //单个键值报错可以不影响其它
                                }
                            }
                        }
                    }
                }
            }
        }
        #endregion
    }
}
