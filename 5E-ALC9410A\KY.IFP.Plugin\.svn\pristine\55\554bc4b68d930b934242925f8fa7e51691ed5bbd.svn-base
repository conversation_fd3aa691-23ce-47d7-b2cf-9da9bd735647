﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{

    /// <summary>
    /// 设备信息
    /// </summary>
    [SugarTable("IFP_BS_YWDX4009")]
    public partial class YWDX4009 : BaseData<YWDX4009>
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public override Field<long> Gid { get; set; }

        /// <summary>
        /// 设备类型1002
        /// </summary>
        [SugarColumn(ColumnName = "SBLX1002", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Sblx1002 { get; set; }

        /// <summary>
        /// 设备用途1009
        /// </summary>
        [SugarColumn(ColumnName = "SBYT1009", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(400)")]
        public Field<string> Sbyt1009 { get; set; }

        /// <summary>
        /// 运输方式1001
        /// </summary>
        [SugarColumn(ColumnName = "YSFS1001", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Ysfs1001 { get; set; }

        /// <summary>
        /// 采样方式1059
        /// </summary>
        [SugarColumn(ColumnName = "CYFS1059", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Cyfs1059 { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        [SugarColumn(ColumnName = "SBBM", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(50)")]
        public Field<string> Sbbm { get; set; }
    }

    //   [Table(Name = "IFP_BS_YWDX4009")]
    //public partial class YWDX4009 : BaseData<YWDX4009>
    //{
    //	/// <summary>
    //	/// 
    //	/// </summary>
    //	[Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.Int64, DbType = "bigint")]
    //	public override Field<long> Gid { get; set; }

    //	/// <summary>
    //	/// 设备类型1002
    //	/// </summary>
    //	[Column(Name = "SBLX1002", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Sblx1002 { get; set; }

    //	/// <summary>
    //	/// 设备用途1009
    //	/// </summary>
    //	[Column(Name = "SBYT1009", DataType = DataType.NVarChar, DbType = "nvarchar(400)")]
    //	public Field<string> Sbyt1009 { get; set; }

    //	/// <summary>
    //	/// 运输方式1001
    //	/// </summary>
    //	[Column(Name = "YSFS1001", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Ysfs1001 { get; set; }

    //	/// <summary>
    //	/// 采样方式1059
    //	/// </summary>
    //	[Column(Name = "CYFS1059", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Cyfs1059 { get; set; }

    //	/// <summary>
    //	/// 设备编码
    //	/// </summary>
    //	[Column(Name = "SBBM", DataType = DataType.NVarChar, DbType = "nvarchar(50)")]
    //	public Field<string> Sbbm { get; set; }
    //}
}
