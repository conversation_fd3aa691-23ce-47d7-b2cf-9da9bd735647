define(["vue"], function (Vue) {


                    /*
                    scopedSlots: h([{
                        key: "default",
                        fn: function(scope) {
                            return [
                                h('ifp-select-ywlx', {
                                    attrs: {
                                        "ywlx": "1003"
                                    },
                                    model: {
                                        value: (scope.row.city),
                                        callback: function($$v) {
                                            $set(scope.row, "city", $$v)
                                        },
                                        expression: "scope.row.city"
                                    }
                                })
                            ]
                        }
                    }])
                    */
    
                    /*,
                    
                    scopedSlots: {
                        default: function (props) {
                            return h('span', 333)
                        }
                    }*/
    // 渲染列
    function renderColumns(h,columns){
        return columns.map(item=>{

            let opt = { props:item.props }

            return h(
                'el-table-column',opt,
                item.children?renderColumns(h,item.children):[]
            )
        })
    }


    const defaultTable = {
        rowKey:"id",
        height:'100%',
        border:true,
        highlightCurrentRow:true,
        data:[
            { id: 1, date: '2021-07-05' },
            { id: 2, date: '2021-07-05' },
            { id: 3, date: '2021-07-05' },
            { id: 4, date: '2021-07-05' }
        ]
    }

    const defaultPagination = {
        currentPage:1,
        pageSizes:[10,15,20,50,100],
        pageSize:20,
        layout:"total, sizes, prev, pager, next, jumper",
        total:20
    }
    
    return {
        props:{
            table:{default:()=>({})},
            pagination:{default:()=>({})},
            columns:{default:()=>[]}
        },
        computed:{
            _columns(){
                return this.columns;
            },
            _table(){
                return Object.assign({},defaultTable,this.table)
            },
            _pagination(){
                return Object.assign({},defaultPagination,this.pagination)
            }
        },
        render(h){
            return h('div',{
                class:'flex'
            },[
                h('div',{
                    class:'flex flex-item'
                },[
                    h(
                        'el-table',{
                            props:{
                                stripe:true,
                                ...this._table
                            }
                        },
                        renderColumns(h,this._columns))
                ]),
                h('el-pagination',{
                    on:{
                        sizeChange:this.updateTable,
                        currentChange:this.updateTable
                    },
                    props:this._pagination
                })
            ])
        },
        /*
        template:`
        <div class="flex">
            <div class="flex flex-item">
                <el-table :data="data" style="width:100%;" row-key="id" height="auto" border highlight-current-row>
                    <el-table-column prop="date" label="日期" width="100"></el-table-column>
                    <el-table-column label="配送信息">
                        <el-table-column label="地址" width="100">
                            <el-table-column prop="province" label="省份" width="100"></el-table-column>
                            <el-table-column prop="city" label="城市" width="100">
                                <template slot-scope="scope">
                                    <ifp-select-ywlx ywlx="1003" v-model="scope.row.city"></ifp-select-ywlx>
                                </template>
                            </el-table-column>
                            <el-table-column prop="address" label="地址" width="100"></el-table-column>
                            <el-table-column prop="zip" label="邮编"></el-table-column>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </div>
            <div>
                <el-pagination @size-change="updateTable" @current-change="updateTable"
                    :current-page="pagination.page" :page-sizes="[10,15,20,50,100]" :page-size="pagination.size"
                    layout="total, sizes, prev, pager, next, jumper" :total="pagination.records">
                </el-pagination>
            </div>
        </div>
        `,
        */
        data() {
            return {
                data: [
                    { id: 1, date: '2021-07-05' },
                    { id: 2, date: '2021-07-05' },
                    { id: 3, date: '2021-07-05' },
                    { id: 4, date: '2021-07-05' }
                ]
            }
        },
        methods: {
            updateTable() {

            }
        }
    }
})