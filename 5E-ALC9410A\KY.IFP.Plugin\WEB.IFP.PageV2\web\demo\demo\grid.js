define([
    "jclass",
    "controller",
    "mockdata"
],function(jclass,base,MockData){

    // 模拟 url:data/base.json
    MockData.jqGridUrl("data/base.json",{
        "czsj":"2020-04-10 15:43:46",
        "gid":"@guid",
        "jobName":"@city(true)",
        "name":"@cname()",
        "email":'@email',
        "sszt1315str":"@region()",
        "jcxm4501":"40002188,40002189,40002190",
        "jcxm4501str":"灰分,挥发分,全硫","bmjzrq":"2020-04-30 00:00:00","bmzt":"1","bmgid":null,
        "userId":null,"shzt":null
    },{
        records:121
    });

    return jclass(base,{
        initControl:function(){
            this.controls.selName3.data([
                {id:"111",text:"111"},
                {id:"222",text:"111"},
                {id:"333",text:"111"},
                {id:"444",text:"111"}
            ])
        },

        bindEvent:function(){
            var ts = this;
            ts.controls.selBtn.bind("click",function(){
                var inputData = controller.formData();
                alert(JSON.stringify(inputData,null,4));
            })

            ts.controls.t_addBtn.bind("click",function(){
                ts.controls.grid33.addRowData({});
            })

            ts.controls.t_delBtn.bind("click",function(){
                var selId = ts.controls.grid33.getSelectRowId();
                if(!selId){$.bootoast.warning("请先选择一行")}
                else{
                    ts.controls.grid33.delRowData(selId);
                }
            })
        },

        onLoad:function(){
            this.initControl();
            this.bindEvent();
        }
    });
})