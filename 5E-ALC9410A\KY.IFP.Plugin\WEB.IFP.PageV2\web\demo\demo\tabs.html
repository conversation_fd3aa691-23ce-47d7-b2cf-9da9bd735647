<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>tabs</title>
	</head>
	<body controller="tabs.js" style="overflow: hidden;">
		<div form="forms/toolbar" class="layout-h">
			<a id="addBtn" control="controls/button" option="{}">新增普通页签</a>
			<a id="addGroupBtn" control="controls/button" option="{}">新增合并页签</a>
			<a id="addAndActiveBtn" control="controls/button" option="{}">新增并选中</a>
			<a id="selectIndexBtn" control="controls/button" option="{}">选中第三个</a>
			<a id="getSelectBtn" control="controls/button" option="{}">获取选中</a>
			<a id="hideTabBtn" control="controls/button" option="{}">隐藏2</a>
			<a id="hideTabByidBtn" control="controls/button" option="{}">隐藏-id</a>
			<a id="showTabBtn" control="controls/button" option="{}">显示2</a>
		</div>
		<div class="layout-h padding">
			<div id="tabsPages1" class="layout-c" control="controls/tabs" option="{height:150}">
				<div option="{title:'页签1',active:true}">
					<p>我定义了高度。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
				</div>
				<div option="{title:'页签2'}">
					<p>22222222。</p>
				</div>
				<div option="{title:'页签3'}">
					<p>333333333。</p>
				</div>
				<div option="{title:'页签4',group:'合并'}">
					<p>333333333。</p>
				</div>
				<div option="{title:'页签5',group:'合并'}">
					<p>444444444。</p>
				</div>
				<div option="{title:'页签5'}">
					<p>333333333。</p>
				</div>
				<div option="{title:'页签6'}" id="t3">
					<p>333333333。</p>
				</div>
			</div>
		</div>
		<div class="layout-h padding">
			<div id="tabsPages2" control="controls/tabs" option="{}">
				<div option="{title:'页签1'}">
					<p>我是会自适应内容高度。</p>
					<p>111111。</p>
					<p>111111。</p>
					<p>111111。</p>
					<p>111111。</p>
				</div>
				<div option="{title:'页签2'}">
					<p>22222222。</p>
					<p>22222222。</p>
					<p>22222222。</p>
				</div>
				<div option="{title:'页签3',group:'合并'}">
					<p>333333333。</p>
				</div>
				<div option="{title:'页签4',group:'合并'}">
					<p>444444444。</p>
					<p>444444444。</p>
					<p>444444444。</p>
					<p>444444444。</p>
					<p>444444444。</p>
					<p>444444444。</p>
					<p>444444444。</p>
					<p>444444444。</p>
					<p>444444444。</p>
					<p>444444444。</p>
				</div>
			</div>
		</div>
		<div class="layout-c padding">
			<div id="tabsPages" control="controls/tabs" option="{autoresize:true}">
				<div option="{title:'页签1'}">
					<table id="grid" control="controls/grid" option='{"autoresize":true,
						postData : {}
					}'>
						<thead>
							<tr>
								<th>关系</th>
								<th>区间</th>
								<th>运算符</th>
								<th>指标</th>
								<th>运算符</th>
								<th>区间2</th>
								<th>颜色</th>
								<th>日期</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td option="{name:'ljgx1003',width : 100,editable:false,frozen: true,key: true }"></td>
								<td option="{name:'value1',width : 100,align : 'right',editable:true,control:'controls/textbox'}"></td>
								<td option="{name:'qjfh1',width : 100,align : 'right',editable:true,
								control:'controls/select2',controloption:{}}"></td>
								<td option="{name:'hyzj1004',width : 80,align : 'left',editable:true,control:'controls/textbox'}"></td>
								<td option="{name:'qjfh2',width : 100,align : 'right',editable:true,
								control:'controls/select',controloption:{data:[{id:'<',text:'<'},{id:'>',text:'>'}]}}"></td>
								<td option="{name:'value2',width : 150,sortable : false,editable:true,control:'controls/textbox'}"></td>
								<td option="{name:'value2',width : 150,sortable : false,editable:true,control:'controls/colorpicker'}"></td>
								<td option="{name:'value2333',width : 150,sortable : false,editable:true,control:'controls/datepicker'}"></td>
							</tr>
						</tbody>
					</table>
				</div>
				<div option="{title:'页签2'}">
					<p>22222222。</p>
				</div>
				<div option="{title:'页签3',group:'合并'}">
					<p>333333333。</p>
				</div>
				<div option="{title:'页签4',group:'合并'}">
					<p>444444444。</p>
				</div>
			</div>
		</div>
	</body>
    <script src="/kyadmin/starter.js"></script>
</html>
