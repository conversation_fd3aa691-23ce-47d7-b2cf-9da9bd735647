<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document 222</title>
    <link rel="stylesheet" href="/kyadmin/css/layout.css">
    <style>
        body,html{
            margin: 0;
            height: 100%;
            width: 100%;
        }
        .page{
            height: 100%;
        }
        .page.flex .placeholder {
            padding: 0 10px;
            background-color: #ebebeb;
            line-height: 2.3em;
            text-align: center;
            color: #cfcfcf;
        }
    </style>
</head>
<body class="flex">
    <div class="page flex">
        <div class="margin flex flex-row">
            <div class="placeholder margin-right">确认</div>
            <div class="placeholder margin-right">保存</div>
            <div class="placeholder">退出</div>
        </div>
        <div class="margin no-margin-top flex flex-row">
            <div class="placeholder margin-right flex-item">确认</div>
            <div class="placeholder margin-right">确认</div>
            <div class="placeholder">确认</div>
        </div>
        <div class="flex flex-row flex-item flex-align-stretch margin no-margin-top">
            <div class="placeholder margin-right flex flex-content-center" style="width:200px;">
                Example
            </div>
            <div class="flex-item flex">
                <div class="placeholder margin-bottom flex flex-content-center" style="height: 38%;">
                    Example
                </div>
                <div class="placeholder flex-item flex flex-content-center">
                    Example
                </div>
            </div>
        </div>
    </div>
</body>
</html>