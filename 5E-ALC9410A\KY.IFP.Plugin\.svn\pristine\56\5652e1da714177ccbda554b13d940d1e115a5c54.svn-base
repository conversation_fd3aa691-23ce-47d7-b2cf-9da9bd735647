﻿using KY.IFP.Runtime;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using static KY.IFP.Runtime.Plugin;
using Newtonsoft.Json.Linq;
using COM.IFP.Log;
using COM.IFP.Common;
using COM.IFP.Common.Secure;
using System.IO;
using API.IFP.SignalR;
using Plugin = KY.IFP.Runtime.Plugin;
using COM.IFP.Client;

namespace KY.IFP.Service
{
    /// <summary>
    /// IFP SignalR Hub 类
    /// </summary>
    public class IFPHub : Hub
    {
        #region 静态字段和属性
        /// <summary>
        /// SignalR客户端集合
        /// </summary>
        private static ConcurrentDictionary<string, SignalRClient> clientsList = new ConcurrentDictionary<string, SignalRClient>();

        /// <summary>
        /// 消息推送的取消标记
        /// </summary>
        private static CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();

        /// <summary>
        /// 推送消息的最小间隔(ms)
        /// </summary>
        private static int pushInterval = 100;

        /// <summary>
        /// 监控线程检查间隔(ms)
        /// </summary>
        private static int monitorInterval = 10000;

        /// <summary>
        /// 用于监控推送线程是否正常运行
        /// </summary>
        private static uint accumulatorValue = 1;

        /// <summary>
        /// 上次检查的累加器值
        /// </summary>
        private static uint lastAccumulatorValue = 0;

        /// <summary>
        /// Hub上下文，用于在非Hub方法中发送消息
        /// </summary>
        private static IHubContext<IFPHub> _hubContext;
        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public IFPHub(IHubContext<IFPHub> hubContext)
        {
            if (_hubContext == null)
            {
                _hubContext = hubContext;
            }
        }

        #region Hub连接管理
        /// <summary>
        /// 客户端连接到集线器时执行操作
        /// </summary>
        /// <returns></returns>
        public override async Task OnConnectedAsync()
        {
            string connectionId = Context.ConnectionId;
            string ipAddress = GetClientIpAddress();

            // 创建新的客户端连接对象
            var client = new SignalRClient(connectionId)
            {
                ClientName = ipAddress
            };

            // 添加到客户端列表
            clientsList[connectionId] = client;

            //LoggerHelper.Info($"客户端连接: {connectionId} (IP: {ipAddress})");

            await base.OnConnectedAsync();
        }

        /// <summary>
        /// 客户端断开连接时执行操作
        /// </summary>
        /// <param name="exception"></param>
        /// <returns></returns>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            string connectionId = Context.ConnectionId;

            // 从客户端列表移除
            if (clientsList.TryRemove(connectionId, out SignalRClient client))
            {
                client.b_close = true;
                //LoggerHelper.Info($"客户端断开连接: {connectionId} (IP: {client.ClientName})");
            }

            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        private string GetClientIpAddress()
        {
            // 尝试从请求中获取客户端IP
            var httpContext = Context.GetHttpContext();
            if (httpContext != null)
            {
                string ipAddress = httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
                return ipAddress;
            }
            return "unknown";
        }
        #endregion

        #region 心跳和基本方法
        /// <summary>
        /// 心跳检测
        /// </summary>
        /// <returns></returns>
        public async Task Detect()
        {
            await Clients.Caller.SendAsync("detect", "Detect");
        }
        
        /// <summary>
        /// 处理 Action 请求
        /// </summary>
        /// <param name="location"></param>
        /// <param name="identity"></param>
        /// <param name="argument"></param>
        /// <returns></returns>
        public async Task HandleAction(string location, string identity, JsonElement argument)
        {
            try
            {
                var result = Plugin.SetActive(location, argument,
                    string.IsNullOrWhiteSpace(identity) ? null : new Active(Context.ConnectionId, identity));

                await Clients.Caller.SendAsync("action", new Return().SetContent(result));
            }
            catch (Exception ex)
            {
                await Clients.Caller.SendAsync("action", new Return().SetCatched(ex));
            }
        }
        #endregion

        #region 客户端消息处理
        /// <summary>
        /// 处理客户端发送的命令
        /// </summary>
        /// <param name="message">客户端发送的消息</param>
        /// <returns>返回给客户端的响应</returns>
        public async Task HandleCommand(string message)
        {
            string connectionId = Context.ConnectionId;
            //LoggerHelper.Info($"收到客户端 {connectionId} 的消息: {message}");

            try
            {
                // 确保客户端在列表中
                if (!clientsList.TryGetValue(connectionId, out SignalRClient client))
                {
                    client = new SignalRClient(connectionId)
                    {
                        ClientName = GetClientIpAddress()
                    };
                    clientsList[connectionId] = client;
                }

                // 处理消息
                SignalRMessage receiveMsg = System.Text.Json.JsonSerializer.Deserialize<SignalRMessage>(message);
                SignalRMessage responseMsg = null;

                // 特殊消息头处理
                if (receiveMsg.H == "SAuth")
                {
                    responseMsg = HandleAuthRequest(receiveMsg, client);
                }
                else
                {
                    // 通用消息处理
                    var handler = GetMessageHandler();
                    if (handler != null)
                    {
                        responseMsg = handler.CommonRun(receiveMsg, client);
                    }
                }

                // 如果没有得到响应消息，返回一个默认的错误消息
                if (responseMsg == null)
                {
                    responseMsg = new SignalRMessage
                    {
                        H = "Null",
                        C = "未找到对应的消息处理器",
                        I = receiveMsg.I
                    };
                }
                else
                {
                    responseMsg.I = receiveMsg.I;
                }

                // 发送响应
                string responseJson = System.Text.Json.JsonSerializer.Serialize(responseMsg);
                await Clients.Caller.SendAsync("message", responseJson);
                //LoggerHelper.Info($"向客户端 {connectionId} 发送响应: {responseJson}");
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E0006, "处理客户端消息时出错", ex);

                // 发送错误响应
                var errorMsg = new SignalRMessage
                {
                    H = "Error",
                    C = $"处理消息时发生错误: {ex.Message}",
                    I = "error"
                };

                await Clients.Caller.SendAsync("message", System.Text.Json.JsonSerializer.Serialize(errorMsg));
            }
        }

        /// <summary>
        /// 处理授权请求
        /// </summary>
        private SignalRMessage HandleAuthRequest(SignalRMessage receiveMsg, SignalRClient client)
        {
            JObject content = JObject.Parse(receiveMsg.C);
            Dictionary<string, object> result = new Dictionary<string, object>();

            try
            {
                bool ok = true;
                // 非强制申请
                if (content.Value<string>("Mandatory") != "1")
                {
                    foreach (var item in clientsList.Values)
                    {
                        if (item.b_auth && item != client)
                        {
                            result["Result"] = "0";
                            result["Msg"] = "申请权限失败:" + item.ClientName + "正在控制，无法申请";
                            ok = false;
                        }
                    }
                }
                // 强制申请
                else
                {
                    foreach (var item in clientsList.Values)
                    {
                        if (item.b_auth && item != client)
                        {
                            item.b_auth = false;
                        }
                    }
                }

                if (ok)
                {
                    // 登录名UsiLoginName
                    string uid = content.Value<string>("User");
                    string pwd = content.Value<string>("Pwd");
                    string passwd = MD5Helper.Get32MD5(IOFP_APIHelper.AuthKey + uid);

                    // 判断权限
                    if (passwd != pwd.ToUpper())
                    {
                        throw new Exception("密码错误，申请权限失败");
                    }
                    else
                    {
                        client.b_auth = true;
                        COM.IFP.Common.Websocket.LoginName = uid;
                    }

                    result["Result"] = "1";
                    result["Msg"] = "申请权限成功";
                }
            }
            catch (Exception ex)
            {
                result["Result"] = "0";
                result["Msg"] = "申请权限失败:" + ex.Message;
            }

            SignalRMessage sendMsg = new SignalRMessage
            {
                H = "RAuth",
                C = System.Text.Json.JsonSerializer.Serialize(result)
            };

            return sendMsg;
        }

        /// <summary>
        /// 获取消息处理器
        /// </summary>
        private IClient2Server GetMessageHandler()
        {
            try
            {
                // 首先检查是否有注册的处理器
                var registeredHandler = SignalRComponentRegistry.GetHandler();
                if (registeredHandler != null)
                {
                    return registeredHandler;
                }

                // 如果没有注册的处理器，尝试从配置创建
                string classPath = GetHandlerClassPath();
                if (!string.IsNullOrEmpty(classPath))
                {
                    Lazy<IClient2Server> handler = COM.IFP.Common.Entity.Create<IClient2Server>(classPath);
                    return handler.Value;
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E2003, "获取消息处理器失败", ex);
            }

            // 尝试使用默认路径创建
            return COM.IFP.Common.Entity.Create<IClient2Server>().Value;
        }
        /// <summary>
        /// 获取处理器类路径
        /// </summary>
        private string GetHandlerClassPath()
        {
            string classPath = string.Empty;

            try
            {
                string path = Path.Combine(FileStore.CurrentDirectory, "Launch.json");
                string file = FileStore.Load(path);

                JsonDocumentOptions jsonOptions = new JsonDocumentOptions
                {
                    CommentHandling = JsonCommentHandling.Skip,
                    AllowTrailingCommas = true
                };

                var jsonDoc = JsonDocument.Parse(file, jsonOptions);
                classPath = jsonDoc.RootElement.GetProperty("SignalR").GetString();
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E2003, "读取处理器类路径失败", ex);
            }

            return classPath;
        }
        #endregion

        #region 服务器推送
        /// <summary>
        /// 启动消息推送服务
        /// </summary>
        public static void StartMessagePush()
        {
            // 启动消息推送线程
            StartPushTask();

            // 启动监控线程
            StartMonitorTask();
        }

        /// <summary>
        /// 启动消息推送任务
        /// </summary>
        private static void StartPushTask()
        {
            Task.Run(async () =>
            {
                //LoggerHelper.Info("启动SignalR自动推送服务");

                while (!cancellationTokenSource.IsCancellationRequested)
                {
                    try
                    {
                        // 获取消息提供者 - 将获取移到循环内部，以便在失败时重试
                        var messageProvider = GetMessageProvider();
                        if (messageProvider == null)
                        {
                            LoggerHelper.Error(ErrorList.E2003, "无法获取消息提供者，等待下一次尝试", null);
                            await Task.Delay(pushInterval);
                            continue; // 继续循环而不是返回
                        }

                        // 遍历所有客户端
                        foreach (var client in clientsList.Values.ToList()) // 创建副本防止集合修改异常
                        {
                            if (client == null || client.b_close) continue;

                            try
                            {
                                // 如果达到推送时间
                                if (DateTime.Compare(DateTime.Now, client.NextMoment) >= 0)
                                {
                                    try
                                    {
                                        // 获取要推送的消息
                                        string content = messageProvider.MessageContent(client);

                                        // 确保内容不为null
                                        if (content == null) content = "";

                                        // 构建消息对象
                                        SignalRMessage msg = new SignalRMessage
                                        {
                                            H = "Open",
                                            C = content.Replace("\r", "").Replace("\n", ""),
                                            I = client.I
                                        };

                                        // 推送消息
                                        string messageJson = System.Text.Json.JsonSerializer.Serialize(msg);
                                        await _hubContext.Clients.Client(client.ConnectionId).SendAsync("message", messageJson);

                                        // 更新下次推送时间
                                        client.SetNextMoment();
                                    }
                                    catch (Exception ex)
                                    {
                                        LoggerHelper.Error(ErrorList.E0006, $"向客户端 {client.ConnectionId} 推送消息失败", ex);

                                        // 如果出现错误，标记客户端可能断开连接
                                        client.b_close = true;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                LoggerHelper.Error(ErrorList.E0006, $"处理客户端 {client.ConnectionId} 时发生错误", ex);
                            }
                        }

                        // 清理已关闭的客户端
                        foreach (var id in clientsList.Keys.Where(k =>
                            clientsList.TryGetValue(k, out var c) && c.b_close).ToList())
                        {
                            if (clientsList.TryRemove(id, out _))
                            {
                                LoggerHelper.Info($"移除不可用的客户端: {id}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggerHelper.Error(ErrorList.E0006, "消息推送循环中发生错误", ex);
                    }
                    finally
                    {
                        // 无论出现什么情况，确保累加器总是更新
                        Interlocked.Increment(ref accumulatorValue);
                    }

                    // 等待下一个推送周期
                    await Task.Delay(pushInterval);
                }
            }, cancellationTokenSource.Token);
        }

        /// <summary>
        /// 启动监控任务，确保推送任务正常运行
        /// </summary>
        private static void StartMonitorTask()
        {
            Task.Run(async () =>
            {
                LoggerHelper.Info("启动SignalR自动推送服务监控线程");

                while (true)
                {
                    uint currentValue = accumulatorValue;

                    // 等待监控间隔
                    await Task.Delay(monitorInterval);

                    // 检查累加器值是否有变化
                    if (currentValue != accumulatorValue)
                    {
                        // 值有变化，表示推送线程正常运行
                        lastAccumulatorValue = accumulatorValue;
                    }
                    else
                    {
                        // 值没有变化，表示推送线程可能停止了
                        LoggerHelper.Error("检测到推送线程可能停止，正在重新启动");

                        // 取消旧的令牌
                        if (cancellationTokenSource != null && !cancellationTokenSource.IsCancellationRequested)
                        {
                            try { cancellationTokenSource.Cancel(); } catch { }
                        }

                        // 创建新的令牌并重启线程
                        cancellationTokenSource = new CancellationTokenSource();
                        StartPushTask();
                    }
                }
            });
        }

        /// <summary>
        /// 获取消息提供者
        /// </summary>
        private static IServer2Client GetMessageProvider()
        {
            try
            {
                // 首先尝试从注册表获取
                var registeredProvider = SignalRComponentRegistry.GetProvider();
                if (registeredProvider != null && !(registeredProvider is EmptyMessageProvider))
                {
                    //LoggerHelper.Info("使用注册的SignalR消息提供者");
                    return registeredProvider;
                }

                // 如果注册表未设置，尝试通过配置创建
                string classPath = GetProviderClassPath();
                if (!string.IsNullOrEmpty(classPath))
                {
                    try
                    {
                        Lazy<IServer2Client> provider = COM.IFP.Common.Entity.Create<IServer2Client>(classPath);
                        if (provider != null && provider.Value != null)
                        {
                            //LoggerHelper.Info($"使用配置的SignalR消息提供者: {classPath}");
                            return provider.Value;
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggerHelper.Error(ErrorList.E2003, $"通过类路径创建提供者失败: {ex.Message}", ex);
                    }
                }

                // 如果以上都失败，使用内部默认实现
                //LoggerHelper.Info("使用内部默认的SignalR消息提供者");
                return new InternalMessageProvider();
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E2003, "获取消息提供者失败", ex);
                return new EmptyMessageProvider();
            }
        }

        // 内部默认实现
        private class InternalMessageProvider : IServer2Client
        {
            public string MessageContent(SignalRClient client)
            {
                var message = new
                {
                    Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    ConnectionId = client.ConnectionId,
                    ClientName = client.ClientName,
                    Auth = client.b_auth ? "已授权" : "未授权"
                };

                return System.Text.Json.JsonSerializer.Serialize(message);
            }
        }

        // 添加一个空实现来避免NPE
        private class EmptyMessageProvider : IServer2Client
        {
            public string MessageContent(SignalRClient client)
            {
                return "{}"; // 返回空JSON对象
            }
        }

        /// <summary>
        /// 获取提供者类路径
        /// </summary>
        private static string GetProviderClassPath()
        {
            string classPath = string.Empty;

            try
            {
                string path = Path.Combine(FileStore.CurrentDirectory, "Launch.json");
                string file = FileStore.Load(path);

                JsonDocumentOptions jsonOptions = new JsonDocumentOptions
                {
                    CommentHandling = JsonCommentHandling.Skip,
                    AllowTrailingCommas = true
                };

                var jsonDoc = JsonDocument.Parse(file, jsonOptions);
                // 尝试读取新的 SignalR 键，如果不存在则尝试读取旧的 WebSocket 键
                if (jsonDoc.RootElement.TryGetProperty("SignalR", out var signalRElement))
                {
                    classPath = signalRElement.GetString();
                }
                else if (jsonDoc.RootElement.TryGetProperty("WebSocket", out var webSocketElement))
                {
                    // 使用现有的 WebSocket 配置
                    classPath = webSocketElement.GetString();
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E2003, "读取提供者类路径失败", ex);
            }

            return classPath;
        }
        #endregion
    }
    /// <summary>
    /// 适配 SignalR 的 Active类
    /// </summary>
    internal class Active : Plugin.Socket
    {
        private readonly string ConnectionId;
        internal string Unique { get; }
        private readonly IHubContext<IFPHub> _hubContext;

        internal Active(string connectionId, string unique)
        {
            ConnectionId = connectionId;
            Unique = unique;
            // 通过依赖注入获取 hubContext
            _hubContext = Server.Services.GetService<IHubContext<IFPHub>>();
        }

        public void Send(object result)
        {
            _hubContext.Clients.Client(ConnectionId)
                .SendAsync("active", new { Active = Unique, Result = result });
        }

        public Task<bool> SendAsync(object result)
        {
            return _hubContext.Clients.Client(ConnectionId)
                .SendAsync("active", new { Active = Unique, Result = result })
                .ContinueWith(t => !t.IsFaulted);
        }
    }
}
