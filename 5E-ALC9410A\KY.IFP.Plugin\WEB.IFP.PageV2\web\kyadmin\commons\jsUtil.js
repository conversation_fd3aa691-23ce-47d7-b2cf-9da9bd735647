;
define([ "require"], function(require) {

	return {
       /*
        * 排序对象数组
        * arg list 对象数组
        * arg attrName 属性名
        * returns list 排序后数组
        */
       sort:function(list,attrName){
    	   return list.sort(function(item1,item2){
				//根据顺序号排序
				return (item1[attrName]*1)> (item2[attrName]*1)?1:-1;
			})
       },
       
       /*
        * 根据元素属性名分组
        * F.common.jsUtil.groupBy(list, function (item) {
		*	return [item.xxx];
		* });
		* arg1 list 数组
        * arg2 attrName 属性名
        * returns map对象 {attrName1:[], attrName2:[]}
        */
       groupByLX : function(list,attrName) {
    	   var v = this.sort(list,attrName);
    	   var n = attrName;
			var i = -1,j = -1,r=[];
			v.forEach(function(m){
				if(i!=m[n]){
					r[++j]=[];
					i=m[n];
				}
				r[j].push(m);
				i++;
			});
			return r;
       },
       
       /*
        * 根据连续性进行分组
        * arg list 对象数组
        * arg attrName 属性名
        * returns 二维数组
        */
       groupBy:function(array, f) {
		  var groups = {};
		  array.forEach(function (o) {
			var group = JSON.stringify(f(o));
		    groups[group] = groups[group] || [];
		    groups[group].push(o);
		  });
		  return Object.keys(groups).map(function (group) {
		    return groups[group];
		  });
		},
       
		/**
		 * 返回合计行
		 * list：列表数据集(_this.controls.grid.value())
		 * hjzd：需要求和的列字段，以逗号隔开(jml,sml...)
		 * jqzd：需要求加权平均的列字段，以逗号隔开(syRz,sySf,syLf...)
		 * qzzd：加权平均字段对应的权重字段，顺序必须与加权字段一致，以逗号隔开(jml,jml,sml...)
		 * hjxsw：合计字段小数位，不指定则默认为3(按hjzd顺序，以逗号隔开)
		 * jqxsw：加权字段小数位，不指定则默认为3(按qzzd顺序，以逗号隔开)
		 */
		getHjRow:function(list,hjzd,jqzd,qzzd,hjxsw,jqxsw){
			//返回的合计行对象
			var retdata = {};
			//合计字段数组
			var arrhjzd = hjzd.split(",");
			if(!F.common.isEmpty(hjxsw)){
				//如果设置了小数位，则按照小数位计算
				var arrhjxsw = hjxsw.split(",");
			}
			for(var row = 0 ; row < list.length; row++){
				//列表行数据
				var rowdata = list[row];
				for(var hjindex = 0 ; hjindex < arrhjzd.length; hjindex++){
					if(undefined === retdata[arrhjzd[hjindex]]){
						retdata[arrhjzd[hjindex]] = 0;
					}
					//合计字段累计值的数组对应字段赋值
					var sumlj = F.common.parseFloat(F.common.parseFloat(retdata[arrhjzd[hjindex]]) + F.common.parseFloat(rowdata[arrhjzd[hjindex]]));
					retdata[arrhjzd[hjindex]] = F.common.parseFloat(sumlj,arrhjxsw?F.common.parseFloat(arrhjxsw[hjindex]):3);
				}
			}
			if(!F.common.isEmpty(jqzd)){
				//加权字段数组
				var arrjqzd = jqzd.split(",");
				//权重字段数组
				var arrqzzd = qzzd.split(",");
				//存储  加权字段*权重  累计值的数组
				var arrjqzdsum = {};
				//权重字段累计值
				var arrqzzdsum = {};
				for(var row = 0 ; row < list.length; row++){
					//列表行数据
					var rowdata = list[row];
					for(var jqindex = 0 ; jqindex < arrjqzd.length; jqindex++){
						if(undefined === arrjqzdsum[arrjqzd[jqindex]]){
							arrjqzdsum[arrjqzd[jqindex]] = 0;
						}
						//加权字段*权重 累计值的数组对应字段赋值
						arrjqzdsum[arrjqzd[jqindex]] += F.common.parseFloat(rowdata[arrjqzd[jqindex]])*F.common.parseFloat(rowdata[arrqzzd[jqindex]]);
						
						if(F.common.parseFloat(rowdata[arrjqzd[jqindex]]) > 0){
							if(undefined === arrqzzdsum[arrjqzd[jqindex]]){
								arrqzzdsum[arrjqzd[jqindex]] = 0;
							}
							//加权字段对应的权重字段累计值
							arrqzzdsum[arrjqzd[jqindex]] += F.common.parseFloat(rowdata[arrqzzd[jqindex]]);
						}
					}
				}
				if(!F.common.isEmpty(jqxsw)){
					//如果设置了小数位，则按照小数位计算
					var arrjqxsw = jqxsw.split(",");
				}
				for(var jqsumindex = 0 ; jqsumindex < arrjqzd.length; jqsumindex++){
					if(undefined === retdata[arrjqzd[jqsumindex]]){
						retdata[arrjqzd[jqsumindex]] = 0;
					}
					var jqsum = F.common.parseFloat(arrjqzdsum[arrjqzd[jqsumindex]]);
					var qzsum = F.common.parseFloat(arrqzzdsum[arrjqzd[jqsumindex]]);
					retdata[arrjqzd[jqsumindex]] = F.common.parseFloat(jqsum/qzsum,arrjqxsw?F.common.parseFloat(arrjqxsw[jqsumindex]):2);
				}
			}
			return retdata;
		},
		
		/**
		 * list重复校验
		 * list：列表数据集(_this.controls.grid.value())
		 * attrs：需要校验的列(kd4001,ghdw4002)
		 */
		cfjyFun:function(list,attrs){
			var temp = {},has = false;
			for(var i=0;i<list.length;i++){
				var item = list[i];
				
				var key = $.map(attrs.split(","),function(ikey){
					return item[ikey]
				}).join(",");
				
				if(temp[key]){
					has = true;
					break;
				}else{
					temp[key] = true;
				}
			}
			return has;
		},
		
		/**
		 * 求数组中某个属性合计值
		 * list：列表数据集
		 * col：需要合计的列（单列）
		 */
		sum:function(list,col,xsw){
			var _arr = list;
			if(!F.common.isEmpty(col)){
				_arr = $.map(list.filter(function(item){return item[col] != null}),function(item){return item[col]});
			}
			var s = 0;
			_arr.forEach(function(val) {
				if(xsw){
					s = F.common.parseFloat(s,xsw) + F.common.parseFloat(val,xsw);
				}else{
					s = F.common.parseFloat(s) + F.common.parseFloat(val);
				}

		    }, 0);
		    return s;
		},

		
		/**
		 * 求数组中某个属性的算术平均值
		 * list：列表数据集
		 * col：需要算术平均的列
		 * len：数组长度（此参数无需传入）
		 */
		avgall:function(list,col,len){
			var _this = this;
			var len = len || list.length;
			if(len > 0){
				return _this.sum(list,col)/len;
			}else{
				return 0;
			}
		},
		
		/**
		 * 求数组中某个属性的算术平均值（剔除0或空）
		 * list：列表数据集
		 * col：需要算术平均的列
		 */
		avgoutz:function(list,col){
			var _this = this;
			var len = list.filter(function(item){return (!F.common.isEmpty(item[col]) && item[col]!=0)}).length;
			return _this.avgall(list,col,len);
		},
		
		/**
		 * 求数组中某个属性的加权平均值
		 * list：列表数据集
		 * val：需要算术平均的列
		 * qz：权重列
		 * outz：是否剔除0或空
		 */
		wavgall:function(list,val,qz,outz){
			var _this = this;
			var outz = outz || false;
			var datalist = list;
			var sumval = 0, sumqz = 0;
			for(var row = 0 ; row < datalist.length; row++){
				var item = datalist[row];
				if(outz && F.common.parseFloat(item[val]) == 0){continue;}
				sumval = F.common.parseFloat(sumval)+(F.common.parseFloat(item[val])*F.common.parseFloat(item[qz]));
				sumqz = F.common.parseFloat(sumqz)+F.common.parseFloat(item[qz]);
			};
			if(sumqz != 0){
				return sumval/sumqz;
			}else{
				return 0;
			}
		},
		
		/**
		 * 求数组中某个属性的加权平均值（剔除0或空）
		 * list：列表数据集
		 * val：需要算术平均的列
		 * qz：权重列
		 */
		wavgoutz:function(list,val,qz){
			var _this = this;
			return _this.wavgall(list,val,qz,true);
		},
		
		/**
		 * 求数组元素中某个属性的最大值
		 * dataList 元素对象数组
		 * itemName 需要计算最大值的属性名称
		 */
		maxValue:function(dataList, itemName){
			var list = dataList.filter(function(item){
				return !F.common.isEmpty(item[itemName]);
			});
			var max = null;
			if(list && list.length>0){
				max = list[0][itemName];
				for (var i = 1; i < list.length; i++) {
					if (max < list[i][itemName]) {
						max = list[i][itemName];
					}
				}
			}
			return max;
		},
		
		/**
		 * 求数组元素中某个属性的最小值
		 * dataList 元素对象数组
		 * itemName 需要计算最小值的属性名称
		 */
		minValue:function(dataList, itemName){
			var list = dataList.filter(function(item){
				return !F.common.isEmpty(item[itemName]);
			});
			var min = null;
			if(list && list.length>0){
				min = list[0][itemName];
				for (var i = 1; i < list.length; i++) {
					if (min > list[i][itemName]) {
						min = list[i][itemName];
					}
				}
			}
			return min;
		}
	}
});