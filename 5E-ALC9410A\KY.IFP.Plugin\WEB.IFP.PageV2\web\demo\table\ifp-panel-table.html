<!DOCTYPE html>
<html lang="zh-Hans">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ifp-panel-table</title>
</head>

<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button>新增</ifp-button>
            <ifp-button>修改</ifp-button>
        </ifp-toolbar>

        <ifp-panel-table 
            class="margin" style="height:50%;" 
            lxsz print>
            <ifp-table>
                <el-table-column label="列1"></el-table-column>
                <el-table-column label="列2"></el-table-column>
                <el-table-column label="列3"></el-table-column>
                <el-table-column label="列4"></el-table-column>
                <el-table-column label="列5"></el-table-column>
                <el-table-column label="列6"></el-table-column>
            </ifp-table>
        </ifp-panel-table>

        <ifp-panel-table class="flex-item margin">
            <ifp-table>
                <el-table-column label="列1"></el-table-column>
                <el-table-column label="列2"></el-table-column>
                <el-table-column label="列3"></el-table-column>
                <el-table-column label="列4"></el-table-column>
                <el-table-column label="列5"></el-table-column>
                <el-table-column label="列6"></el-table-column>
            </ifp-table>
        </ifp-panel-table>
    </ifp-page>
    <script src="/iofp/starter.js"></script>
</body>

</html>