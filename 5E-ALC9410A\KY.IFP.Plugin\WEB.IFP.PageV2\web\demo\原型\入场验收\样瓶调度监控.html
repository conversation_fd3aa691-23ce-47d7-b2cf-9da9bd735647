<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="示例页面,入场验收">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>086 样瓶调度监控</title>
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button type="primary" role="查看">查看当日取样任务</ifp-button>
        </ifp-toolbar>

        <div class="padding flex flex-row" style="justify-content: space-between;">
            <div>
                <div v-for="i in [1,2]">
                    <span class="margin-right">#{{i}}存样柜</span>
                    <ifp-legend code="cyg" :value="2"></ifp-legend>
                </div>
            </div>
            <div>
                <div v-for="i in [1,2]">
                    <span class="margin-right">#{{i}}气送系统</span>
                    <ifp-legend code="qsxt" :value="2"></ifp-legend>
                </div>
            </div>
            <div>
                <div v-for="i in [1,2]">
                    <span class="margin-right">#{{i}}机器人化验</span>
                    <ifp-legend code="jqrhy" :value="2"></ifp-legend>
                </div>
            </div>
            <div>
                <div v-for="i in [1,2]">
                    <span class="margin-right">#{{i}}在线测水</span>
                    <ifp-legend code="zxcs" :value="2"></ifp-legend>
                </div>
            </div>
        </div>

        <div class="padding flex flex-item flex-row">
            <div class="flex flex-item">
                <ifp-panel-table class="flex-item" borderless title="村样柜任务列表">
                    <ifp-table>
                        <ifp-table-column label="采样状态"></ifp-table-column>
                        <ifp-table-column label="采样编码"></ifp-table-column>
                    </ifp-table>
                </ifp-panel-table>
                <ifp-panel-table borderless title="气送任务列表" class="flex-item margin-top">
                    <ifp-table>
                        <ifp-table-column label="采样状态"></ifp-table-column>
                        <ifp-table-column label="采样编码"></ifp-table-column>
                    </ifp-table>
                </ifp-panel-table>
            </div>
            <ifp-panel borderless border flex class="margin-left" title="运行消息" style="max-width:50%">
                <div class="padding-left padding-right" v-for="item in 5">xxxxx xxxxxx xxx xxxx xxxxxx</div>
            </ifp-panel>
        </div>
    </ifp-page>
    <script src="/iofp/starter.js"></script>
</body>
</html>