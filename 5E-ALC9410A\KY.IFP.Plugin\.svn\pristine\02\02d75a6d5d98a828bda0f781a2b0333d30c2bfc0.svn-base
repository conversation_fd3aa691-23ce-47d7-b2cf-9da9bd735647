﻿using COM.IFP.PLC.SiemensS7.Protocol;
using System;
using System.Net.Sockets;


namespace COM.IFP.PLC.SiemensS7
{
    /// <summary>
    /// 通讯适配器，用于
    /// </summary>
    public class S7Adaptor
    {
        #region 属性字段
        /// <summary>
        /// 用于读写的socket连接
        /// </summary>
        //protected SocketWrapper skt = new SocketWrapper();
        TcpClient _tcpClient;
        NetworkStream _networkStream;

        /// <summary>
        /// 竞争锁
        /// </summary>
        protected object _objLocker = new object();
        /// <summary>
        /// 如果WritePLC的socket抛出异常，及时把连接状态置为false
        /// </summary>
        protected bool writeSuccess = true;

        public delegate void AdaptorConnect();
        public delegate void AdaptorClose();
        public event AdaptorConnect ONAdaptorConnect;//通信连接
        public event AdaptorClose ONAdaptorClose;//通信关闭

        /// <summary>
        /// IP地址
        /// </summary>
        protected string _ip = "************";
        /// <summary>
        /// 端口
        /// </summary>
        protected int _port = 102;
        public string IP { get { return _ip; } }
        public int PORT { get { return _port; } }


        /// <summary>
        /// CPU-槽号，各型号稍有差异，S7-1200:0x01 S7-200:0x57 S7-300:0x02(槽号必须2) S7-400:0x02(槽号一般2)
        /// </summary>
        public int _slot { get; set; } = 0x01;

        #endregion
        #region 构造
        /// <summary>
        /// 
        /// </summary>
        /// <param name="add">IP地址</param>
        /// <param name="_port">端口不填默认102</param>
        /// <param name="slot">CPU-槽号，各型号稍有差异，S7-1200:0x01 S7-200:0x57 S7-300:0x02(槽号必须2) S7-400:0x02(槽号一般2)</param>
        public S7Adaptor(string add, int port = 102, int slot = 1)
        {
            _ip = add;
            _port = port;
            _slot = slot;
        }
        #endregion
        /// <summary>
        /// 创建socket实例、配置socket、连接socket
        /// </summary>

        public virtual void Build()
        {

            //skt = new SocketWrapper();
            //skt.device = new IPEndPoint(IPAddress.Parse(_ip), _port);

            //skt.ONSocketConnect += Skt_ONSocketConnect;
            //skt.ONSocketClose += Skt_ONSocketClose;

            //skt.SocketConf();
            //skt.Connect();

            //lock (objLocker)
            //{
            //    skt.Send(S7Message.Request(Slot));
            //    skt.Receive(259);  //此处必须接收数据，否则PLC会认为客户端已经掉线
            //}
            //lock (objLocker)
            //{
            //    skt.Send(S7Message.Connect());
            //    skt.Receive(259);  //此处必须接收数据，否则PLC会认为客户端已经掉线
            //}

            lock (_objLocker)
            {
                if (_networkStream != null)
                {
                    _networkStream.Close();
                    _networkStream = null;
                }
                if (_tcpClient != null)
                {
                    _tcpClient.Close();
                    _tcpClient = null;
                }

                _tcpClient = new TcpClient();

                bool notOverTime = _tcpClient.ConnectAsync(_ip, _port).Wait(2000);
                if (notOverTime == false)
                {
                    throw new Exception($"连接{_ip}:{_port}超时2s");
                }
                _networkStream = _tcpClient.GetStream();


                byte[] send = S7Message.Request(_slot);
                byte[] receive = new byte[259];
                _networkStream.Write(send, 0, send.Length);
                _networkStream.Read(receive, 0, receive.Length);

                send = S7Message.Connect();
                _networkStream.Write(send, 0, send.Length);
                _networkStream.Read(receive, 0, receive.Length);
            }



        }
        /// <summary>
        /// 用于写PLC时创建短socket连接
        /// </summary>
        /// <returns></returns>
        //protected SocketWrapper CreateConnect()
        //{
        //    SocketWrapper tmpskt = new SocketWrapper();
        //    tmpskt.device = new IPEndPoint(IPAddress.Parse(_ip), _port);

        //    tmpskt.SocketConf();
        //    tmpskt.Connect();

        //    lock (_objLocker)
        //    {
        //        tmpskt.Send(S7Message.Request(Slot));
        //        tmpskt.Receive(259);  //此处必须接收数据，否则PLC会认为客户端已经掉线
        //    }
        //    lock (_objLocker)
        //    {
        //        tmpskt.Send(S7Message.Connect());
        //        tmpskt.Receive(259);  //此处必须接收数据，否则PLC会认为客户端已经掉线
        //    }
        //    return tmpskt;
        //}
        #region 事件
        private void Skt_ONSocketClose()
        {
            //连接已关闭
            ONAdaptorClose?.Invoke();
        }

        private void Skt_ONSocketConnect()
        {
            //连接已建立
            ONAdaptorConnect?.Invoke();
        }

        #endregion 事件

        /// <summary>
        /// 获得socket连接状态
        /// </summary>
        /// <returns>true表示连接正常</returns>
        public virtual bool GetStatus()
        {
            //bool b = false;
            //lock (_objLocker)
            //{
            //    b = skt.GetStatus();
            //}
            //return b;
            if (_tcpClient == null)
            {
                return false;
            }
            return _tcpClient.Connected;
        }
        /// <summary>
        /// 一个从PLC读数据的方法
        /// </summary>
        /// <param name="pc">读写PLC的报文都会用到，用于传寻址参数，和读的数量或写的数据。</param>
        public virtual byte[] ReadPLC(PlcS7Command pc)
        {
            int count = pc.Count;
            int area = pc.Area;
            int block = pc.Block;
            int byteOffset = pc.ByteOffset;

            byte[] pg = new byte[count];
            if (count < 250)//
            {
                byte[] data = S7Message.ReadRequest(count, area, block, byteOffset);

                byte[] receive = new byte[512];

                lock (_objLocker)
                {
                    //skt.Send(data);
                    //receive = skt.Receive(512);
                    _networkStream.Write(data, 0, data.Length);
                    _networkStream.Read(receive, 0, receive.Length);
                }

                byte[] value = S7Message.ReadResponse(receive);

                int len = value.Length;
                for (int i = 0; i < len && i < count; ++i)
                {
                    pg[i] = value[i];
                }
            }
            else//太长的分片读下位机，下面每次读128个字节
            {
                const int piece = 128;
                for (int i = 0; i < count; i += piece)
                {

                    byte[] data = S7Message.ReadRequest(piece, area, block, byteOffset + i);
                    byte[] receive = new byte[256];

                    lock (_objLocker)
                    {
                        //skt.Send(data);
                        //receive = skt.Receive(256);

                        _networkStream.Write(data, 0, data.Length);
                        _networkStream.Read(receive, 0, receive.Length);
                    }
                    byte[] value = S7Message.ReadResponse(receive);

                    int len = value.Length;//ParseData函数返回的数组的长度就是modbus报文给的数据字节数

                    for (int j = 0; j < piece && i + j < count; ++j)
                    {
                        pg[i + j] = value[j];
                    }
                }
            }
            return pg;
        }

        /// <summary>
        /// 一个从PLC读数据的方法，区别在于使用短连接
        /// </summary>
        /// <param name="pc">读写PLC的报文都会用到，用于传寻址参数，和读的数量或写的数据。</param>
        public virtual byte[] ReadPLC2(PlcS7Command pc)
        {
            int count = pc.Count;
            int area = pc.Area;
            int block = pc.Block;
            int byteOffset = pc.ByteOffset;
            byte[] pg = new byte[count];

            //using (SocketWrapper skt = CreateConnect())
            using (TcpClient tmpClient = new TcpClient(_ip, _port))
            {
                var tmpStream = tmpClient.GetStream();
                #region s7协议握手
                byte[] send0 = S7Message.Request(_slot);
                byte[] receive0 = new byte[259];
                tmpStream.Write(send0, 0, send0.Length);
                tmpStream.Read(receive0, 0, receive0.Length);

                send0 = S7Message.Connect();
                tmpStream.Write(send0, 0, send0.Length);
                tmpStream.Read(receive0, 0, receive0.Length);
                #endregion

                if (count < 250)//
                {
                    byte[] data = S7Message.ReadRequest(count, area, block, byteOffset);

                    byte[] receive = new byte[512];

                    //skt.Send(data);
                    //receive = skt.Receive(512);
                    tmpStream.Write(data, 0, data.Length);
                    tmpStream.Read(receive, 0, receive.Length);

                    byte[] value = S7Message.ReadResponse(receive);

                    int len = value.Length;
                    for (int i = 0; i < len && i < count; ++i)
                    {
                        pg[i] = value[i];
                    }
                }
                else//太长的分片读下位机，下面每次读128个字节
                {
                    const int piece = 128;
                    for (int i = 0; i < count; i += piece)
                    {

                        byte[] data = S7Message.ReadRequest(piece, area, block, byteOffset + i);
                        byte[] receive = new byte[256];

                        //skt.Send(data);
                        //receive = skt.Receive(256);

                        tmpStream.Write(data, 0, data.Length);
                        tmpStream.Read(receive, 0, receive.Length);

                        byte[] value = S7Message.ReadResponse(receive);

                        int len = value.Length;//ParseData函数返回的数组的长度就是modbus报文给的数据字节数

                        for (int j = 0; j < piece && i + j < count; ++j)
                        {
                            pg[i + j] = value[j];
                        }
                    }
                }


                #region tcp断连
                tmpStream.Close();
                tmpClient.Close();
                #endregion
            }
            return pg;
        }

        /// <summary>
        /// 一个向PLC写入数据的方法，使用短连接
        /// </summary>
        /// <param name="pc">读写PLC的报文都会用到，用于传寻址参数，和读的数量或写的数据。</param>
        public virtual void WritePLC(PlcS7Command pc)
        {
            byte[] data = pc.Data;
            int area = pc.Area;
            int block = pc.Block;
            int byteOffset = pc.ByteOffset;

            byte[] senddata = null;
            byte[] receivedata = new byte[256];
            senddata = S7Message.WriteRequest(data, area, block, byteOffset);
            //try
            //{
            //using (SocketWrapper skt = CreateConnect())
            using (TcpClient tmpClient = new TcpClient(_ip, _port))
            {
                var tmpStream = tmpClient.GetStream();
                #region s7协议握手
                byte[] send0 = S7Message.Request(_slot);
                byte[] receive0 = new byte[259];
                tmpStream.Write(send0, 0, send0.Length);
                tmpStream.Read(receive0, 0, receive0.Length);

                send0 = S7Message.Connect();
                tmpStream.Write(send0, 0, send0.Length);
                tmpStream.Read(receive0, 0, receive0.Length);
                #endregion

                tmpStream.Write(senddata, 0, senddata.Length);
                tmpStream.Read(receivedata, 0, receivedata.Length);

                #region tcp断连
                tmpStream.Close();
                tmpClient.Close();
                #endregion
            }
            //}
            //catch (Exception ex)
            //{

            //}
            if (receivedata[21] != 255)
            {
                string str = "PLC写入数据，S7协议报文异常，回复的错误码为" + receivedata[21].ToString();
                throw new Exception(str);
            }
        }

        /// <summary>
        /// 关闭连接
        /// </summary>
        public virtual void Close()
        {
            //skt.Close();
            _networkStream.Close();
            _tcpClient.Close();
        }
    }
}
