﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.Threading;

namespace KY.IFP.Runtime
{
    /// <summary>
    /// 终端信息
    /// </summary>
    public class Basics
    {
        /// <summary>
        /// 缓存标识
        /// </summary>
        public string Unique { get; init; }
        /// <summary>
        /// 窗口时间
        /// </summary>
        public TimeSpan? Period { get; init; }
        /// <summary>
        /// 监听主机
        /// </summary>
        public string Listen { get; init; }
        /// <summary>
        /// 远端结点
        /// </summary>
        public string Remote { get; init; }
        /// <summary>
        /// 访问工具
        /// </summary>
        public string Viewer { get; init; }
        /// <summary>
        /// 通道代理
        /// </summary>
        public ApiContent Agency { get; init; }

        /// <summary>
        /// 附加数据
        /// </summary>
        public object Extend { get; set; }
    }

    /// <summary>
    /// 全局缓存
    /// </summary>
    public class Caches
    {
        static readonly MemoryCache memory = new(Options.Create(new MemoryCacheOptions()));
        static readonly AsyncLocal<Basics> basics = new();

        /// <summary>
        /// 
        /// </summary>
        public static bool Inited { get => basics.Value != null; }

        /// <summary>
        /// 缓存窗口期
        /// </summary>
        public static TimeSpan? Period { get; set; }

        /// <summary>
        /// 缓存集终端
        /// </summary>
        public static Basics Basics { get => basics.Value ?? throw new Exception("缓存集尚未建立。"); }

        /// <summary>
        /// 缓存项字典
        /// </summary>
        public static ReadOnlyDictionary<string, object> Values { get => memory.TryGetValue(Basics.Unique, out Cached cached) ? new ReadOnlyDictionary<string, object>(cached.values) : null; }

        /// <summary>
        /// 检索缓存键
        /// </summary>
        /// <param name="name">缓存键名称</param>
        /// <returns>True缓存键存在，False缓存键不存在</returns>
        public static bool HasName(string name)
        {
            return (memory.Get<Cached>(Basics.Unique)?.HasName(name)).Value;
        }

        /// <summary>
        /// 设置缓存项
        /// </summary>
        /// <param name="name">缓存项名称</param>
        /// <param name="data">缓存项数值</param>
        public static void SetData(string name, object data)
        {
            memory.GetOrCreate(Basics.Unique, entry => (Basics.Period.HasValue ? entry.SetSlidingExpiration(Basics.Period.Value).SetPriority(CacheItemPriority.NeverRemove) : entry).SetValue(new Cached()).Value as Cached).SetData(name, data);
        }

        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <param name="name">缓存项名称</param>
        /// <returns>缓存项数值</returns>
        public static object GetData(string name)
        {
            return memory.Get<Cached>(Basics.Unique)?.GetData(name);
        }

        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="name">缓存项名称</param>
        /// <returns>缓存项数值</returns>
        public static T GetData<T>(string name)
        {
            return (T)GetData(name);
        }

        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <param name="name">缓存项名称</param>
        /// <param name="data">缓存项数值</param>
        /// <returns>True获取成功，False获取失败</returns>
        public static bool GetData(string name, out object data)
        {
            data = default;
            return (memory.Get<Cached>(Basics.Unique)?.GetData(name, out data)).Value;
        }

        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="name">缓存项名称</param>
        /// <param name="data">缓存项数值</param>
        /// <returns>True获取成功，False获取失败</returns>
        public static bool GetData<T>(string name, out T data)
        {
            var temp = default(object);
            if (memory.Get<Cached>(Basics.Unique)?.GetData(name, out temp) != null)
            {
                data = (T)temp;
                return true;
            }
            else
            {
                data = default;
                return false;
            }
        }

        /// <summary>
        /// 建立缓存集
        /// </summary>
        /// <param name="info">缓存集信息</param>
        public static void Build(Basics info)
        {
            if (basics.Value == null)
            {
                if (!string.IsNullOrWhiteSpace(info.Unique))
                {
                    basics.Value = info;
                }
                else
                {
                    throw new Exception("缓存集标识不能为空。");
                }
            }
            else
            {
                throw new Exception("不能重复建立缓存集。");
            }
        }

        class Cached
        {
            public ConcurrentDictionary<string, object> values = new(StringComparer.OrdinalIgnoreCase);

            public bool HasName(string name)
            {
                return values.ContainsKey(name);
            }

            public Cached SetData(string name, object data)
            {
                values.AddOrUpdate(name, data, (k, o) => data);
                return this;
            }

            public object GetData(string name)
            {
                return values.TryGetValue(name, out object data) ? data : default;
            }

            public bool GetData(string name, out object data)
            {
                return values.TryGetValue(name, out data);
            }
        }
    }
}
