﻿
using COM.IFP.Common;
using DAL.IFP.Interface;
using System;

namespace DAL.IFP.www.Fitler
{
    /// <summary>
    /// IFP单点登录，2020年9月以后 chrome等浏览器不支持iframe 携带 cookies，因此，通过菜单跨域弹出没用了，要考虑使用 Nginx 等进行反向代理，才能生效
    /// </summary>
    public class FitlerExt : IFitlerExt
    {
        private Lazy<DAL.IFP.Rights.User> user = Entity.Create<DAL.IFP.Rights.User>();

        public bool DoFilterExt()
        {
            return user.Value.IofpLogin();
        }
    }
}
