<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>板块组件</title>
</head>
<body class="flex" controller="../demo.js" option="{platform:'element'}">
    <div class="flex flex-item padding" id="app">

        <ifp-panel class="margin">
            <h1>ifp-panel 组件示例</h1>
        </ifp-panel>

        <ifp-panel border class="margin">
            <div class="padding">
                带边框 border
            </div>
        </ifp-panel>

        <ifp-panel title="标题" class="margin">
            <div class="padding">
                带标题，不带边框 title="标题"
            </div>
        </ifp-panel>

        <ifp-panel border title="标题" class="margin">
            <template v-slot:header>
                <el-button type="text" size="mini">按钮1</el-button>
            </template>

            <div class="padding">
                自定义标题栏内容
            </div>
        </ifp-panel>

        <ifp-panel border title="标题" class="margin">
            <div class="padding">
                带footer
            </div>
            <template v-slot:footer>
                <div class="padding" style="text-align:right;">
                    footer内容
                </div>
            </template>
        </ifp-panel>

        <ifp-panel flex border title="标题" class="margin" style="height:200px;">
            <div class="padding">
                定高
            </div>
            <template v-slot:footer>
                <div class="padding" style="text-align:right;">
                    footer内容
                </div>
            </template>
        </ifp-panel>

        <ifp-panel flex border :border-body="false" title="内嵌一个table，推荐使用 ifp-panel-table" 
            class="margin" 
            style="height:200px;">
            <el-table border height="100%">
                <el-table-column label="列1"></el-table-column>
                <el-table-column label="列2"></el-table-column>
                <el-table-column label="列3"></el-table-column>
                <el-table-column label="列4"></el-table-column>
                <el-table-column label="列5"></el-table-column>
            </el-table>
            <template v-slot:footer>
                <el-pagination
                    :current-page="1"
                    :page-sizes="[10,15,20,50,100]"
                    :page-size="20"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="20">
                    <detail></detail>
                </el-pagination>
            </template>
        </ifp-panel>
    </div>
    <script src="/iofp/starter.js"></script> 
</body>
</html>