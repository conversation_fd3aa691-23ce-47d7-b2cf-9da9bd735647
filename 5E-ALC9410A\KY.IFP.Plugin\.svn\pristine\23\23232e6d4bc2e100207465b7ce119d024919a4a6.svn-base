define(["iofp/api", "zutil"], function (API, zutil){
    function create(args){
        return zutil.extend(true,{
            Gid: null,		//GID
            Pgid: null,		//上级对象GID
            Bname: null,		//对象名称
            Sname: null,		//对象简称
            Ywlx: '4017',		//业务类型
            Compid: null,		//所属单位
            Addtime: null,		//新增时间
            Lasttime: null,		//修改时间
            Zfbz: 0,		//作废标志（1作废  0正常）
            Ywbm: null,		//业务编码

            PZ: null,		//皮重
            ZHAIZ: null,		//载重
            SFTSCX1099: false,		//是否特殊车型
            Length: null,		//长
            Width: null,		//宽
            Height: null,		//高
            FloorHeight: null,		//底板高度
            SFZBC1099: 10990002		//是否自备车
        },args);
    }


    var btntexts = {
        view:"查看",
        delete:"删除",
        edit:"修改",
        add:"新增"
    }

    return {
        el:"#app",
        props:{
            state: { default: "view" }, //add update delete view
            gid: { default: ""},         //传进来有gid，则会根据gid查一次覆盖 备用
            source:{
                default:function(){
                    return create();
                }
            }
        },
        computed:{
            submittext(){
                return btntexts[this.state]
            }
        },
        data(){
            return {
                editor:{
                    header:"header",
                    source: create(this.source)
                },
                rules: {
                    Bname: [
                        { required: true, message: '请输入车型名称', trigger: 'blur' }
                    ],
                    ZHAIZ: [
                        { required: true, message: '请输入载重' }
                    ],
                    PZ: [
                        { required: true, message: '请输入自重' }
                    ]
                },
                typeOptions: [],
            }
        },
        created() {
            var _this = this;
            if (_this.gid) {
                API.GetAction("API/ICS/BasicData/Supplier/GetSupplier", { Gid: _this.gid })
                    .then(data => {
                        _this.editor.source = data;
                    });//获取供应商类型
            }
        },
        watch: {
            state(val, oldVal) {  
                if (val == "add")
                    this.editor.source = create();   //默认类
                else
                    this.editor.source = create(this.source);
            },
            source(val, oldVal) {
                if (this.state == "add")
                    this.editor.source = create();   //默认类
                else
                    this.editor.source = create(val);
            }
        },
        methods:{
            create: function () {
            },
            onSubmit() {
                var _this = this;
                _this.$refs.form.validate((valid) => {
                    if (valid) {    //验证通过
                        if (this.state == "add") {
                            API.GetAction("API/ICS/BasicData/TrainModel/SubmitTrainModel", _this.editor.source)
                                .then(result => {
                                    if (result.success == true) {
                                        _this.$message.success(btntexts[this.state] + '成功');
                                        _this.$confirm("数据保存成功，是否继续新增记录？").then(() => {
                                            _this.editor.source = create(); //重新生成一组数据
                                        }).catch(() => {
                                            _this.onCancel();  //不需要新增的时候直接退出
                                        });
                                    }
                                    else {
                                        _this.$message.error(result.msg);
                                    }
                                }).catch(e => {
                                    this.$message.error(e);
                                });
                        }

                        if (this.state == "edit") {
                            API.GetAction("API/ICS/BasicData/TrainModel/SubmitTrainModel", _this.editor.source)
                                .then(result => {
                                    this.$emit("success")
                                    if (result.success == true) {
                                        _this.$message.success(btntexts[this.state] + '成功');
                                        _this.onCancel();
                                    }
                                    else {
                                        _this.$message.error(result.msg);
                                    }
                                }).catch(e => {
                                    this.$message.error(e);
                                });
                        }
                    }
                });
            },
            onCancel() {
                this.editor.source = create();   //默认类   退出前清空一下选项  因为可能退出去后下一次又点增加 此时不会触发watch里面任意一个修改
                this.$emit("cancel")
            }
        }
    }
})