<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="控件,页面,ifp-page">
    <title>页面控件</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page class="flex flex-item" id="app">

        <ifp-toolbar>
            <el-button>保存</el-button>
        </ifp-toolbar>

        <ifp-searchbar :model="search" @search="$message('查询')" @reset="$message('重置')">
            <ifp-form-item label="名称">
                <ifp-select-ywlx ywlx="1003" v-model="search.name2" placeholder="名称"></ifp-select-ywlx>
            </ifp-form-item>

            <ifp-form-item label="名称">
                <ifp-input v-model="search.name2" placeholder="名称"></ifp-input>
            </ifp-form-item>

            <el-form-item label="下拉">
                <el-select v-model="search.region" placeholder="下拉">
                    <el-option label="区域一" value="shanghai"></el-option>
                    <el-option label="区域二" value="beijing"></el-option>
                </el-select>
            </el-form-item>
        </ifp-searchbar>

        <ifp-panel-table class="flex-item margin" title="标题">
            <ifp-table ref="table5" height="100%" :data="[{mark:'表格适应容器高度'}]">
                <el-table-column label="列1" prop="mark"></el-table-column>
                <el-table-column label="列2"></el-table-column>
                <el-table-column label="列3"></el-table-column>
                <el-table-column label="列4"></el-table-column>
                <el-table-column label="列5"></el-table-column>
            </ifp-table>
        </ifp-panel-table>

    </ifp-page>
    <script src="/iofp/starter.js"></script> 
</body>
</html>