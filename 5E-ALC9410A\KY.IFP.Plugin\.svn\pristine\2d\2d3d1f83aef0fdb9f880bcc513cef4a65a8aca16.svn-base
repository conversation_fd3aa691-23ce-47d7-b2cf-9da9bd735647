# 下拉列表

## element-ui

```html

    <el-table-column
      label="姓名"
      width="180">
      <!--默认插槽插槽-->
      <template slot-scope="scope">
        <!---->
        <el-select v-model="scope.row.xxxx">
          <el-option :value="item.text" v-for="item in options">选项1</el-option>
        </el-select>

      </template>
    </el-table-column>
```

```html
  <el-select v-model="value" placeholder="请选择">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value">
    </el-option>
  </el-select>
```

```js
{
    //...
    data(){
        return {
            options:[
                {name:"value1",label:"选项1"},
                {name:"value2",label:"选项2"},
                {name:"value3",label:"选项3"},
                {name:"value4",label:"选项4"}
            ]
        }
    }
    //...
}
```