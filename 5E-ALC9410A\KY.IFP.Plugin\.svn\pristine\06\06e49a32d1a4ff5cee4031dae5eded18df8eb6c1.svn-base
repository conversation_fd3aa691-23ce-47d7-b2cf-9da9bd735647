// create 2020-08-01 / zq

define(function(){
    // 验证 JSON , 简单版，后续可替换为功能完整的第三方jsonSchema 工具
    
    var typeMaps = {
        "array":"[object Array]",
        "object":"[object Object]",
        "null":"[object Null]",
        "number":"[object Number]",
        "string":"[object String]",
        "undefined":"[object Undefined]"
    }
    
    // 获取数据类型名 [object String] => String
    var tname = function(str){return str.replace(/\[object (\S+)\]/,"$1")}

    var errors = {
        type:function(path,args){return "数据类型不匹配 "+path + " 应该是 "+ args[0] + " " +"当前是 "+args[1]},
        required:function(path,args){return "缺少必要属性" + path + " 必须包含属性 " + "["+ args[0] +"]"},
        enum:function(path,args){return "枚举值不合法 "+path + " " +"值必须在 [" + args[0] + "] 中，当前为：\""+args[1]+"\""},
        exclusiveMinimum:function(path,args){return "数值不合法 "+path + " " +"值必须大于" + args[0] + "，当前为：\""+args[1]+"\""},
        minimum:function(path,args){return "数值不合法 "+path + " " +"值必须大于等于" + args[0] + "，当前为：\""+args[1]+"\""},
        exclusiveMaxmum:function(path,args){return "数值不合法 "+path + " " +"值必须小于" + args[0] + "，当前为：\""+args[1]+"\""},
        maximum:function(path,args){return "数值不合法 "+path + " " +"值必须小于等于" + args[0] + "，当前为：\""+args[1]+"\""}
    }

    // 获取数据类型 => [object ....]
    var getType = function(d){return Object.prototype.toString.call(d)}

    // 通用校验器
    function verify(schema,data,path){
        return verifys[schema.type](schema,data,path);
    }

    // 约束
    var constraint = {
        // 类型
        type:function(schema,data,path){
            return typeMaps[schema.type]===getType(data)?"":errors.type(path,[tname(typeMaps[schema.type]),tname(getType(data))])
        },
        // 必填
        required : function (schema,data,path){
            var filed = schema.required.find(function(p){ return !data.hasOwnProperty(p)})
            return filed && errors.required(path,[filed]) || ""
        },
        // 枚举
        enum : function (schema,data,path){
            return schema.enum.indexOf(data)>-1?"":errors.enum(path,[schema.enum.join(","),data]);
        },
        // 最小值
        minimum:function(schema,data,path){
            return schema.exclusiveMinimum && schema.minimum>data && errors.exclusiveMinimum(path,[schema.minimum,data]) ||
            (schema.minimum>=data && errors.minimum(path,[schema.minimum,data])) || ""
        },
        // 最大值
        maximum:function(schema,data,path){
            return schema.exclusiveMaximum && schema.maximum>data && errors.exclusiveMaximum(path,[schema.maximum,data]) ||
            (schema.maximum>=data && errors.maximum(path,[schema.maximum,data])) || ""
        }
    }


    var verifys = {
        object:function(schema,data,path){
            return constraint.type(schema,data,path)
            || schema.required && constraint.required(schema,data,path)
            || schema.properties && (function(schema,data,path){
                for(var att in schema.properties){
                    var error = "";
                    if(!data.hasOwnProperty(att)){continue;}
                    error = verify(schema.properties[att],data[att],path+"."+att)
                    if(error){return error}
                }
            })(schema,data,path) || ""
        },
        string:function(schema,data,path){
            return constraint.type(schema,data,path) 
            || (schema.enum && constraint.enum(schema,data,path))
            || "";
        },
        number:function(schema,data,path){
            return constraint.type(schema,data,path) 
            //|| (schema.enum && constraint.enum(schema,data,path))
            || "";
        }
    }

    return function(schema,data){
        return verify(schema,data,"root")
    };
})