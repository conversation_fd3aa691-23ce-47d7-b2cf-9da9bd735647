define([
    "zutil",
    "kyadmin/utils/jsonpath",
    "util",
    'popper',
    "css!./menu/menu"
],function(zutil,jsonPath,util,Popper){
    var debug = false;

    function transform(list,option,level=0){
        return list.map(item=>({
            level:level,
            show:false,
            id:item[option.idfield],
            pid:item[option.pidfield],
            icon:item[option.icon]||option.defaultIcon,
            title:item[option.title],
            data:item,
            children:item.children&&transform(item.children,option,level+1)
        }))
    }

    // 单个子菜单
    const SubMenu = {
        props:{
            id:{default:''},
            pid:{default:''},
            title:{default:''},
            icon:{default:''},
            show:{default:false},
            children:{default:()=>[]}
        },
        template:`<div class="submenu" v-show='show'>
            <ul
                @mouseleave="$emit('leave',$props)"
                @mouseover="$emit('hover',$props)">
                <li class="submenu-item"
                    @mouseleave.self="$emit('item-leave',$event,item)"
                    @mouseover.capture.stop="$emit('item-hover',$event,item)" 
                    @click="$emit('item-click',$event,item)" 
                    v-for="item in children" 
                    :key="item.id">
                    <span>
                        <span class="dropdown-menu-left menu-item-ico" :class="item.icon" aria-hidden="true"></span>
                        {{item.title}}
                    </span>
                    <span v-if="item.children.length>0" class="dropdown-menu-right glyphicon glyphicon-menu-right"></span>\
                </li>
            </ul>
        </div>`
    }

    // 主菜单元素
    const MenuItem = {
        name:'MenuItem',
        props:{
            icon:{default:''},
            title:{default:''},
            gid:{default:''},
            active:{default:false}
        },
        template:`
        <li 
            @mouseover.capture.stop="$emit('hover',$event)" 
            @mouseleave.self="$emit('leave',$event)" 
            @click="item_click($event)" 
            class="menu-item" 
            :class="{active:active}">
            <span class="menu-item-ico" :class="icon" aria-hidden="true"></span>
            <span class="menu-item-title">{{title}}</span>
        </li>
        `,
        methods:{
            item_click(e){
                this.$emit('item-click',e)
            }
        }
    }

    // 主菜单
    const Menu = {
        components:{MenuItem,SubMenu},
        props:{
            activeid:"",
            items:{type:[Object,Array],default:function(){return []}},
            subMenuItemHeight:{default:32},
            minTop:{default:50},
            
            option:{default:function(){
                return {
                    simple:false, // 简单树 
                    idfield:"menuGid",
                    pidfield:"pid",
                    icon:"menuImg",
                    url:"menuUrl",
                    title:"menuName",
                    defaultIcon:"glyphicon glyphicon-file",
                    rootlevel:0//起始树级别
                }
            }}
        },
        data(){
            return {
                hoverItem:null,
                activeItem:null,
                submenus:[],
                setTimoutId:null,
                maxHeight:window.innerHeight
            }
        },
        created(){
            window.addEventListener('resize',this.resize)
        },
        destroyed(){
            window.removeEventListener('resize',this.resize)
        },
        computed:{
            hightItem(){
                if(this.activeid){
                    return this.hoverItem || this.list.find(item=>item.id===this.activeid) || this.activeItem;
                }
            },
            // 配置，合并了默认配置
            currentOption(){
                return zutil.extend(true,{},this.$options.props.option.default(),this.option)
            },
            list(){
                return transform(this.items,this.currentOption);
            }
        },
        template:`
        <div class="menu-wrapper">
            <ul class="menu nav nav-pills nav-stacked">
                <menu-item v-for="item in list"
                    :key="item.id"
                    :icon="item.icon"
                    :title="item.title"
                    :active="hightItem===item"


                    @item-click="submenu_item_click($event,item)"
                    @hover="menu_hover($event,item)"
                    @leave="menu_leave(item)"
                ></menu-item>
            </ul>
            <div class="submenus">
                <sub-menu ref="submenus" v-show="item.show"
                    :style="{maxHeight:maxHeight+'px'}"
                    @hover="submenu_hover"
                    @leave="submenu_leave"
                    @item-click="submenu_item_click"
                    @item-hover="submenu_item_hover"
                    @item-leave="submenu_item_leave"
                    v-for="item in submenus" 
                    :key="item.pid" 
                    v-bind="item" 
                    class="submenu"></sub-menu>
            </div>
        </div>
        `,
        methods:{
            resize(){
                this.maxHeight = window.innerHeight;
            },
            submenu_hover(item){
                this.clearHover();
            },
            submenu_leave(item){
                this.menu_leave();
            },
            submenu_item_click(event,item){
                this.$emit("menuclick",item.data);
            },
            submenu_item_hover(event,item){
                this.clearHover();
                if(item.children && item.children.length){
                    this.submenus.splice(item.level,this.submenus.length-item.level,item)
                    this.showElement(item,event.currentTarget,()=>this.$refs.submenus[item.level].$el);
                }else{
                    this.submenus.splice(item.level,this.submenus.length-item.level)
                }
            },
            // 显示子菜单
            showElement(item,target,getEle) {
                this.$set(item,'show',false);
                this.$nextTick(()=>{
                    let instance = Popper.createPopper(target,getEle(),{
                        placement:'right-start',
                    })
                    this.$set(item,'show',true);
                    this.$nextTick(()=>{
                        instance.update();
                    })
                })
            },
            submenu_item_leave(event,item){
                this.menu_leave();
            },
            menu_hover(event,item){
                this.clearHover();
                this.hoverItem=item;
                this.submenus.splice(0,this.submenus.length,item)
                this.showElement(item,event.currentTarget,()=>this.$refs.submenus[0].$el);
            },
            menu_leave(item){
                this.nextHover(()=>{
                    this.hoverItem=null;
                    this.submenus.splice(0,this.submenus.length)
                })
            },
            nextHover(fn){
                this.clearHover();
                this.setTimoutId = setTimeout(fn,1000)
            },
            clearHover(){
                if(this.setTimoutId){
                    window.clearTimeout(this.setTimoutId)
                    this.setTimoutId = null;
                }
            }
        }
    }
    return Menu;

    var submenu = {
        components:{
            //submenuitem:submenuitem,
            submenu:submenu
        },
        props:{
            // 一级子菜单定位
            position:{default:function(){return {x:"auto",y:"auto"}}},
            subMenuItemHeight:{default:20},
            minTop:{default:50},
            // 父节点数据（ 符合option，且 子节点 属性名为 children）
            menu:{default:function(){return {}}},
            show:{default:false},

            // 默认图标 （未指定 icon 且指定了 url 才会起作用）
            defaultIcon:{default:"glyphicon glyphicon-file"},

            // 父节点图标（未指定 icon 和 url 才会起作用）
            defaultIconFolder:{default:"glyphicon glyphicon-folder-open"},

            // 菜单数据配置
            option:{default:function(){
                return {
                    idfield:"menuGid",
                    pidfield:"pid",
                    icon:"menuImg",
                    url:"menuUrl",
                    title:"menuName"
                }
            }}
        },
        data:function(){
            return {
                curentLevel:0,
                submenus:[],
                menuspos:[]
            }
        },
        computed:{
            menus:function(){
                if(!this.show){return []}
                return [{level:0,data:this.menu,position:this.position}].concat(this.submenus)
            }
        },
        template:'\
        <div @mouseout="wrapper_mouseout" @mouseover="wrapper_mousemove">\
            <div ref="fixdiv" class="submenu" \
            v-for="menusitem in menus" :key="menusitem.level" \
            style="position:fixed;white-space: nowrap;" \
            :style="calculationPosition(menusitem)">\
                <ul style="overflow:auto;">\
                    <li v-for="item in menusitem.data.children" :key="item[option.idfield]" class="submenu-item"\
                        @mouseover="submenuitem_hover($event,menusitem.level,item)"\
                        @click="submenuitem_click(item)">\
                        <span class="dropdown-menu-left menu-item-ico" :class="getmenuitemicon(item)" aria-hidden="true"></span>\
                        {{item[option.title]}}\
                        <span v-if="item.children.length>0" class="dropdown-menu-right glyphicon glyphicon-menu-right"></span>\
                    </li>\
                </ul>\
            </div>\
        </div>\
        ',
        created:function(){

        },
        methods:{
            // 计算位置
            calculationPosition:function(menusitem){
                // 返回菜单绝对位置
                let style = {
                    top:menusitem.position.y+'px',
                    left:menusitem.position.x+'px'
                }

                if(menusitem.position.bottom !== ''){
                    style.bottom = menusitem.position.bottom+'px';
                }

                if(menusitem.position.scroll){
                    style.overflowY='auto';
                }
                return style;
            },

            // 返回图标
            getmenuitemicon:function(item){
                return item[this.option.icon] || 
                ((item[this.option.url]||item.children.length==0)?this.defaultIcon:this.defaultIconFolder)
            },
            submenuitem_hover:function(event,level,item){
                var pos = getSubmenuPositon(
                    event.currentTarget,
                    this.submenus.length*this.subMenuItemHeight,
                    this.minTop
                )
                if(item.children && item.children.length){
                    this.submenus[level]={
                        level:level+1,
                        data:item,
                        position:{
                            x:pos.left + event.currentTarget.offsetWidth,
                            y:pos.top,
                            bottom:pos.bottom,
                            scroll:pos.scroll
                        }
                    }
                    this.submenus = this.submenus.slice(0,level+1);
                }else{
                    this.submenus = this.submenus.slice(0,level);
                }
            },
            submenuitem_click:function(item){
                this.$emit("menuitem_click",item);
            },
            wrapper_mouseout:function(){
                this.$emit("hiding");
                debug&&console.log("hiding");
            },
            wrapper_mousemove:function(){
                this.$emit("canclehiding");
                debug&&console.log("canclehiding");
            }
        }
    }
	return {
        components:{
            submenu:submenu
        },
		template:template,
		props:{
            activeid:"",
            items:{type:[Object,Array],default:function(){return []}},
            subMenuItemHeight:{default:32},
            minTop:{default:50},
            option:{default:function(){
                return {
                    simple:false, // 简单树 
                    idfield:"menuGid",
                    pidfield:"pid",
                    icon:"menuImg",
                    url:"menuUrl",
                    title:"menuName",
                    defaultIcon:"glyphicon glyphicon-file",
                    rootlevel:0//起始树级别
                }
            }}
        },
        computed:{
            // 配置，合并了默认配置
            currentOption(){
                return zutil.extend(true,{},this.$options.props.option.default(),this.option)
            },

            // 树结构，根节点为数组
            tree(){
                // 如果是简单树
                let tree;
                let list;

                if(this.items.constructor !== Array){
                    list = [this.items];
                }else{
                    list = this.items;
                }

                if(this.currentOption.simple){
                    tree = util.getTreeData(this.items,{
                        id:this.currentOption.idfield,
                        pid:this.currentOption.pidfield
                    })
                }else{
                    tree = list;
                }

                if(this.currentOption.rootlevel>0){
                    return jsonPath.query(tree,'$[*]'+".children[*]".repeat(this.currentOption.rootlevel))
                }else{
                    return tree;
                }
            }
        },
		data: function () {
		  return {
            menustate:{},
            current_sub_menu:[],
            submenu_show:false,
            submenu_hiding:false,
            submenu_hidingid:false,

            // 子菜单定位
            subposition:{
                x:"auto",
                y:"auto"
            }
		  }
		},
		created:function(){
            //this.
        },
        methods:{
            // 一级菜单点击事件
            menuitem_click:function(item){
                this.$emit("menuclick",item);
                return;
                var url = item[this.option.url];
                if(url){
                    this.$emit("menuclick",item);
                    alert(url);
                }else{
                    alert("open "+ item[this.option.title]);
                }
            },
            menuitem_mouseover:function(event,item){
                this.current_sub_menu = item;
                var pos = getSubmenuPositon(
                    event.currentTarget,
                    this.current_sub_menu.children.length * this.subMenuItemHeight,
                    this.minTop
                );
                this.subposition.x =pos.left + event.currentTarget.offsetWidth;
                this.subposition.y =pos.top;
                this.subposition.bottom=pos.bottom;
                this.subposition.scroll=pos.scroll;

                this.submenu_show = true;
                this.submenu_hiding = false;
                if(this.submenu_hidingid){
                    window.clearTimeout(this.submenu_hidingid);
                    this.submenu_hidingid = false;
                }
            },
            submenuitem_click:function(item){

            },
            onsubmenu_hiding:function(){
                var _this = this;
                this.submenu_hiding = true;
                debug&&console.log("onsubmenu_hiding");
                this.submenu_hidingid = setTimeout(function(){
                    if(_this.submenu_hiding===true){
                        _this.submenu_show = false;
                        debug&&console.log("submenu_hiding==true");
                        _this.current_sub_menu = [];
                        _this.submenu_hiding = false;
                    }
                },500)
            },
            onsubmenu_canclehiding:function(){
                debug&&console.log("onsubmenu_canclehiding");
                this.submenu_hiding=false;
                if(this.submenu_hidingid){
                    window.clearTimeout(this.submenu_hidingid);
                    this.submenu_hidingid = false;
                }
            }
        }
    };
})