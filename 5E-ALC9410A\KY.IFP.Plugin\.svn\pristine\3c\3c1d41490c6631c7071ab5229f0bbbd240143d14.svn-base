﻿using COM.IFP.Common;
using ORM.IFP.www.DbModel.SM;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace API.IFP.www.sys
{
    public class OrgAPI
    {
        readonly Lazy<DAL.IFP.www.sys.OrgDAL> lazy = Entity.Create<DAL.IFP.www.sys.OrgDAL>();

        public List<IFP_SM_ORG> Select(JsonElement json)
        {
            List<IFP_SM_ORG> result = lazy.Value.Select();
            return result;
        }
    }
}
