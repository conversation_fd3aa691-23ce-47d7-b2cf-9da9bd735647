import {Control} from "./src/index.js"
Vue.use(ELEMENT);
new Vue({
    components:{
        zxcsypj:Control
    },
    el:'#app',
    data(){
        return {
            list: [
                { Pos: 1, Status: 1 },
                { Pos: 2, Status: 2 },
                { Pos: 3, Status: 3 },
                { Pos: 4, Status: 4 },
                { Pos: 5, Status: 5 },
                { Pos: 6, Status: 6 },
                { Pos: 7, Status: 2 },
                { Pos: 8, Status: 2 },
                { Pos: 9, Status: 2 },
                { Pos: 10, Status: 1 },
                { Pos: 11, Status: 1 },
                { Pos: 12, Status: 1 },
                { Pos: 13, Status: 1 },
                { Pos: 14, Status: 1 },
                { Pos: 15, Status: 1 },
                { Pos: 16, Status: 1 },
                { Pos: 17, Status: 1 },
                { Pos: 18, Status: 1 },
                { Pos: 19, Status: 1 },
                { Pos: 20, Status: 1 }
            ]
        }
    },
    methods:{
        getTooltip(){
            return "标题"
        },
        item_select(item){
            alert(`指定了 canselect:true 才会触发此事件 ${item.xh}`);
        },
        item_click(item){
            alert(`所有元素都可以触发此事件 ${item.xh}`);
        },
        showSelected(){
            let item = this.$refs.ypj1.getSelect();
            alert(item?.xh||'未选择')
        },
        createTooltip(item){
            if(item.state>1){
                return `指标1:2222
                指标1:2222
                指标1:2222
                指标1:2222`
            }
            return;
        }
    }
})