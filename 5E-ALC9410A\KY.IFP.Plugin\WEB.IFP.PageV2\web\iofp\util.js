

/**
 * iofp 专用工具包
 * zq/2021-07-08
 */
define(["ramda"],function(R){
    
    function Url(url){
        this._hasOrigin = R.test(/^\w+:\/\//)(url);
        this._url = this._hasOrigin?new URL(url):new URL(location.origin+url);
    }
    Url.prototype.toString = function(){
        if(!this._hasOrigin){
            return this._url.toString().replace(this._url.origin,"")
        }else{
            return this._url.toString();
        }
    }

    /**
     * 添加参数到 url
     * @param {string} url url 字符串
     * @param {object} obj 对象参数
     * @returns {string}
     * @example
     * ```
     * urlAddParams("/index.html?a=2",{b:2,c:3})
     * // => /index.html?a=2&b=2&c=2
     * ```
     */
    function urlAddParams(url,obj){
        if(isEmpty(url)){return null};
        let ourl = new Url(url);
        for(let a in obj){
            ourl._url.searchParams.append(a,obj[a]);
        }
        return ourl.toString();
    }

    /**
     * 获取所有参数，返回一个对象
     * @param {*} url 
     * @returns 
     */
    function urlGetAllParam(url){
        if(isEmpty(url)){return null};
        let ourl = new Url(url);
        return Object.fromEntries(ourl._url.searchParams.entries());
    }

    /**
     * 获取 url 中的一个参数
     * @param {*} url 
     * @param {*} name 
     * @returns 
     */
    function urlGetParam(url,name){
        if(isEmpty(url)){return null};
        let ourl = new Url(url);
        return ourl._url.searchParams.get(name);
    }
    
    /**
     * 获取 base64 加密的参数
     * @param {*} url 
     * @param {*} key 
     * @returns 
     */
    function urlGetBase64Param(url,key){
        let v = urlGetParam(url,key);
        return v && decodeBase64(v)||null;
    }

    /**
     * 设置base64参数
     * @param {*} url 
     * @param {*} key 
     * @param {*} value 
     */
    function urlAddBase64Param(url,key,value){
        return urlAddParam(url,key,encodeBase64(value))
    }

    function urlAddParam(url,key,value){
        return urlAddParams(url,{
            [key]:value
        })
    }
    
    /**
     * 转base64
     * @param {string} s 
     * @returns 
     */
    function encodeBase64(s){
        return btoa(s)
    }

    /**
     * 解base64
     * @param {string} s 
     * @returns 
     */
    function decodeBase64(s){
        return atob(s)
    }

    /**
     * get Url 中的参数 pk 并使用base64 解密 
     */
    function urlGetPageID(){
        return urlGetBase64Param(location.search,"pk")
    }

    /**
     * 获取 url 中的参数 pk ，不存在则返回 当前href;
     */
    function urlGetPageIDOrHref(){
        return urlGetPageID() || location.href;
    }


    function isEmptyString(s){
        return s === ""
    }
    function isEmpty(s){
        return s === null || typeof s === "undefined"
    }
    return {
        encodeBase64,
        decodeBase64,

        /* url 相关方法 */
        Url:Url,

        urlAddParam,
        urlAddParams,
        urlGetParam,
        urlGetAllParam,
        urlAddBase64Param,
        urlGetBase64Param,
        urlGetPageID,
        urlGetPageIDOrHref,

        isEmpty,
        isEmptyString
    }
})