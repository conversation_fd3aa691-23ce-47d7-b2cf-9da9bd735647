﻿using System;

namespace COM.IFP.PLC.SiemensS7.DataPoint
{
    /// <summary>
    /// 名字相同，存储连续的点位归为一组
    /// </summary>
    public class PointGroup
    {
        /// <summary>
        /// 组ID
        /// </summary>
        public enumGroupID GID { get; set; }
        /// <summary>
        ///字节数，读PLC存储区域用到的长度参数；
        /// </summary>
        public int Size { get; set; }
        /// <summary>
        /// 存储区域0x81(129)I点，0x82(130)Q点，0x83(131)M点，0x84(132)V点即DB块
        /// </summary>
        public string Area { get; set; } = "DB";
        /// <summary>
        /// 组的块号，只有是存储在DB才有用，其他为0
        /// </summary>
        public int Block { get; set; }

        /// <summary>
        /// 组在块内的偏移，通常每组从一个块的0号地址开始存，偏移为0
        /// </summary>
        public int ByteOffset { get; set; } = 0;
        /// <summary>
        /// 连续存储来自下位机的数据
        /// </summary>
        public byte[] Data;
        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="gID">组ID</param>
        /// <param name="area">存储区域0x81(129)I点，0x82(130)Q点，0x83(131)M点，0x84(132)V点即DB块</param>
        /// <param name="block">块号</param>
        /// <param name="byteOffset">组在块内的字节偏移</param>
        /// <param name="size">组存储数据的字节数</param>
        /// <param name="_data">字节数组</param>
        public PointGroup(enumGroupID gID, string area, int block, int byteOffset, int size, byte[] _data = null)
        {
            GID = gID;

            Area = area;

            Block = block;

            ByteOffset = byteOffset;

            Size = size;

            Data = new byte[Size];
            if (_data != null) Array.Copy(_data, Data, Size);
        }

        public void SetValue(byte[] _data)
        {
            if (_data != null) Array.Copy(_data, Data, Size);
        }
        public byte this[int index]
        {
            get
            {
                if (index < 0 || index >= Data.Length)
                {
                    throw new Exception("下标越界");
                }
                return Data[index];
            }
            set
            {
                if (index < 0 || index >= Data.Length)
                {
                    throw new Exception("下标越界");
                }
                else
                {
                    Data[index] = value;
                }
            }
        }
    }
    /// <summary>
    /// 组类型
    /// </summary>
    public enum enumGroupID
    {
        /// <summary>
        /// I点组，位置DB，块号2，外部信号转换
        /// </summary>
        I,

        /// <summary>
        /// Q点组，位置DB，块号3，输出到执行机构
        /// </summary>
        Q,

        /// <summary>
        /// Man点组，位置DB，块号1，行走伺服手动操作等。
        /// </summary>
        Man,

        /// <summary>
        /// Parm点组，位置DB，块号10，参数定义
        /// </summary>
        Parm,

        /// <summary>
        /// S点组，位置DB，块号9,流程步，具体释义见流程图

        /// </summary>
        S,

        /// <summary>
        /// ST点组，位置DB，块号5，设备手动操作状态等
        /// </summary>
        ST,

        /// <summary>
        /// AL点组，位置DB，块号4,报警变量
        /// </summary>
        AL,

        /// <summary>
        /// CM点组，位置DB，块号6,报警变量
        /// </summary>
        CM,

        /// <summary>
        /// Str点组，位置DB，块号23,读卡编码等
        /// </summary>
        Str,

        /// <summary>
        /// DCR点组，位置DB，块号43,下位机记录相应部件的运行状态值
        /// </summary>
        DCR
    }
}

