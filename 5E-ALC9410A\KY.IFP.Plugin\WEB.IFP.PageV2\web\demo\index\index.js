define([
    "vue_components/menu"
    ,"iofp/util"
    ,"pagemessage"
    ,"comp!/kyadmin/lib/vue_components/menutabs2.js"
    ,"json!menu.json"
    ,"json!/kyadmin/version.json"
    ,"css!index.css"
],function(kyaMenu,ifpUtil,PageMessage,menutabs,menus,version){
    return {
        el:"#app",
        components:{kyaMenu,menutabs},
        data(){
            return {
                menus:menus,
                tabs:[],
                selectmenuid:"",
                fullscreen:false,

                version:version,
                
                menuoption:{
                    // 菜单数据中id 和 pid 字段名
                    simple:true,//简单树
                    rootlevel:0,// 根节点界别，这里用的的第2级
                    idfield:"Gid",
                    pidfield:"pid",
                    title:"menuName",
                    //图标
                    icon:"menuImg",
                    url:"menuUrl"
                }
            }
        },
        created(){
            this.$nextTick(()=>{
                this.showMenuTab({
                    [this.menuoption.idfield]:"shouye",
                    [this.menuoption.title]:"首页",
                    [this.menuoption.url]:"/demo/index.html",
                    noClose:true
                })

                if(window.location.hash){
                    this.selectmenuid = window.location.hash.replace("#","");
                    this.showMenuTab(this.menus.find(item=>item[this.menuoption.idfield]===this.selectmenuid))
                }
            })
                
            PageMessage.register("close",(a,b,id)=>{
                this.$refs.control_tabs.deleteTab(id);
            },"*")
            PageMessage.register("add",(a,b,data)=>{
                this.$refs.control_tabs.addTab(data[_this.option.title],data[_this.option.url],true,data);
            },"*");
        },
        beforeMount(){
            
        },
        methods:{
            // 根节点菜单点击
            menuclick_handle(menuitem){
                window.location.hash = menuitem[this.menuoption.idfield]
                this.showMenuTab(menuitem);
            },
            
            showMenuTab(menuitem){
                if(menuitem && menuitem[this.menuoption.url]){
                    this.$refs.control_tabs.addTab({
                        id:menuitem[this.menuoption.idfield],
                        url:ifpUtil.urlAddParams(menuitem[this.menuoption.url],{
                            pk:ifpUtil.encodeBase64(menuitem[this.menuoption.url])
                        }),
                        title:menuitem[this.menuoption.title],
                        noClose:menuitem.noClose,
                        data:menuitem
                    },true)
                }
            },

            // 全屏
            doFullScreen:function(){
                if(this.fullscreen){
                    var exitMethod = document.exitFullscreen || //W3C
                    document.mozCancelFullScreen || //Chrome等
                    document.webkitExitFullscreen || //FireFox
                    document.webkitExitFullscreen; //IE11
                    if (exitMethod) {
                        exitMethod.call(document);
                    }
                    else if (typeof window.ActiveXObject !== "undefined") {//for Internet Explorer
                        var wscript = new ActiveXObject("WScript.Shell");
                            if (wscript !== null) {
                            wscript.SendKeys("{F11}");
                        }
                    }
                } else {
                    var  element= document.documentElement;
                    // 判断各种浏览器，找到正确的方法
                    var requestMethod = element.requestFullScreen || //W3C
                    element.webkitRequestFullScreen || //Chrome等
                    element.mozRequestFullScreen || //FireFox
                    element.msRequestFullScreen; //IE11
                    if (requestMethod) {
                      requestMethod.call(element);
                    } else if (typeof window.ActiveXObject !== "undefined") {//for Internet Explorer
                      var wscript = new ActiveXObject("WScript.Shell");
                      if (wscript !== null) {
                       wscript.SendKeys("{F11}");
                      }
                    }
                }
                this.fullscreen = !this.fullscreen;
            }
        }
    }
})