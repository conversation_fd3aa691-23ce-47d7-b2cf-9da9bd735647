define(["vuex"], function ({mapState}) {

    function treeToList(tree) {
        let list = [];
        tree.forEach(item=>{
            list.push(item);
            if(item.children){
                list.push(...treeToList(item.children))
            }
        })
        return list;
    }

    return {
        el: "#app",

        props: {
            tableid:{type:[Function,String],default:""},
            pageid:{default:""},
            showstate:{default:false}
        },
        computed:{
            rawtableid(){
                if(!this.tableid){return null;}
                if(typeof this.tableid === "function"){
                    return this.tableid();
                }
                return this.tableid;
            },
            ...mapState({
                table(state){
                    const item = state.lxsz.items.find(item=>item.id===this.pageid);
                    if(!item){
                        return null;
                    }
                    let table = item.tables.find(t=>t.id==this.rawtableid);
                    return table;
                }
            }),
            columns() {
                return this.table && this.table.originColumns || [];
            },
            allcolumns() {
                return treeToList(this.columns);
            },
            storeHideColumns(){
                return this.table && this.table.hidecolumns || [];
            }
        },
        watch: {
            showstate:{
                handler: function (newVal, oldValue) {
                    if (newVal) {
                        this.hidecolumns.splice(0,this.hidecolumns.length,...this.storeHideColumns);
                        this.$nextTick(()=>{
                            //.filter(item=>!item.childrend)
                            let allkeys = this.allcolumns.map(item=>item.id);
                            let showkeys = allkeys.filter(key=>!this.hidecolumns.includes(key))
                            this.setCheckKeys(showkeys)
                        })
                    }
                },
                immediate: true
            }
        },

        data() {
            return {
                hidecolumns:[],
                //树内容
                // treeData: [{ label: "列名", id: "C0", children: [] }],
                defaultChecked:[],
                defaultProps: {
                    children: 'children',
                    label: 'label'
                }
            }
        },
        
        methods: {
            setCheckKeys(keys,checked){
                checked = checked!==false;
                // setCheckedKeys 有bug ，所以这里使用 setChecked
                keys.forEach(key=>{
                    this.$refs.tree.setChecked(key,checked);
                })
            },

            getPageID(){
                return this.$parent.$getPageInfo().id;
            },
            //保存
            onSave() {
                

                //勾选的列（不包括半选状态）
                var a = this.$refs.tree.getCheckedKeys();
                //半选的列
                var b = this.$refs.tree.getHalfCheckedKeys();
                var columns = [...a,...b];
                var hidecolumns = this.allcolumns.filter(item=>!columns.includes(item.id)).map(item=>item.id);


                this.$store.dispatch("lxsz/setHideColumns",{
                    tableid:this.rawtableid,
                    pageid:this.pageid,
                    hidecolumns,
                    save:true
                }).then(x => {
                    this.$message.success("保存成功。");
                    this.$emit("success");
                }).catch(e => {
                    this.$message.error(e);
                });
            },

            //退出
            onExit() {
                this.$emit("cancel")
            },

            // 全选
            selectAll(){
                let allkeys = this.allcolumns.map(item=>item.id);
                this.setCheckKeys(allkeys)
            },

            // 全部取消
            selectEmpty(){
                let allkeys = this.allcolumns.map(item=>item.id);
                this.setCheckKeys(allkeys,false)
            },

            // 反选
            selectReverse(){
                let checkeds = this.$refs.tree.getCheckedKeys();
                let nocheckeds = this.allcolumns.filter(item=>!checkeds.includes(item.id)).map(item=>item.id);
                this.setCheckKeys(checkeds,false)
                this.setCheckKeys(nocheckeds,true)
            }
        }
    }
})