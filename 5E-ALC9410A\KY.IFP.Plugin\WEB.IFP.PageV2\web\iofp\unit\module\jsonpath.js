define(["kyadmin/utils/jsonpath"],(jsonpath)=>{

    var data = [
        {name:'节点1'},
        {
            name:'节点2',
            children:[
                {name:"节点2-1"}
            ]
        },
        {
            name:'节点3',
            children:[
                {name:"节点3-1"}
            ]
        },
        {name:'节点4'}
    ]

    return function(QUnit){
        QUnit.module('kyadmin/utils/jsonpath', function() {
            QUnit.test("jsonpath 使用", function(assert) {
                assert.equal(true,true,`let data = ${JSON.stringify(data,null,2)}`);
                let result = jsonpath.query(data,"$.0")[0];
                assert.equal(true,true,`let result = JsonPath(data,"$.0")[0];`);
                assert.equal(result.name,'节点1',`result.name === "节点1"`);
                result = jsonpath.query(data,"$.*.children.*")[0];
                assert.equal(true,true,`result = JsonPath(data,"$.*.children")[0];`);
                assert.equal(result.name,'节点2-1',`result.name === "节点2-1"`);
            });
            
            /*
            QUnit.test('千分位', function(assert) {
                assert.equal(converters.thousands(99999999), '99,999,999','正整数');
                assert.equal(converters.thousands(99999999.01), '99,999,999.01','正小整数');
                assert.equal(converters.thousands(-99999999), '-99,999,999','负整数');
            });
            */
        });
    }
});