# 颜色

使用[颜色变量](#颜色变量)示例

```css

.color-test{
    background-color: var(--color-danger);
}

```

## 颜色变量

* <a href="/iofp/ifp.css">/iofp/ifp.css</a> 中定义了平台通用颜色变量
* <a href="/iofp/ics/main.css">/iofp/ics/main.css</a> 中定义了ICS颜色变量

```css
--bg-body:#efefef; /* 主 */
--bg-panel:#e0e0e0; /* 板块 */
--bg-menu-item:#f3f3f3; /* 二级菜单项 */
--bg-menu-subcontainer:#0003; /* 二级菜单容器 */

--color-default:#aaa;
--color-success:green;
--color-warn:#ffaa00;
--color-danger:#f33;
--color-error:red;
```

## 控件样式

### Table 中行样式

`ifp-table` 中特殊行样式定义，参考：[ifp-talbe](pages/controls/ifp-table)
