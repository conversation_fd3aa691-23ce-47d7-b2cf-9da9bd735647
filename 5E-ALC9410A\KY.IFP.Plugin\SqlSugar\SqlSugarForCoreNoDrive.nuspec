﻿<?xml version="1.0"?>
<package >
  <metadata>
    <id>SqlSugarCoreNoDrive</id>
    <version>*********</version>
    <authors>sunkaixuan</authors>
    <owners><PERSON>a</owners>
    <licenseUrl>http://www.apache.org/licenses/LICENSE-2.0.html</licenseUrl>
    <projectUrl>https://github.com/sunkaixuan/SqlSugar</projectUrl>
    <iconUrl>https://secure.gravatar.com/avatar/a82c03402497b2e58fd65038a3699b30</iconUrl>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description> .Net Core/.net5+ SqlSugar ORM ,High-performance, lightweight https://github.com/sunkaixuan/SqlSugar</description>
    <copyright>Copyright 2016</copyright>
    <tags>SqlSugar Sql Sugar Core Asp.net core orm</tags>
    <dependencies>
      <group targetFramework=".NETStandard2.1">
        <dependency id="System.Data.Common" version="4.3.0" />
        <dependency id="Microsoft.Data.SqlClient" version="2.1.4" />
        <dependency id="Newtonsoft.Json" version="13.0.2" />
        <dependency id="System.Reflection.Emit.Lightweight" version="4.3.0" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="G:\Git\SqlSugar\Src\Asp.NetCore2\SqlSugar\bin\Debug\netstandard2.1\SqlSugar.dll" target="lib\netstandard2.1"></file>
  </files>
</package>