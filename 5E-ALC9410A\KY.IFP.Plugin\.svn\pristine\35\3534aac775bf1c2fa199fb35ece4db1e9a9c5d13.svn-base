﻿//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;
//using ColumnAttribute = LinqToDB.Mapping.ColumnAttribute;

namespace ORM.IFP.www.DbModel.SM
{
    /// <summary>
    /// 调度任务参数
    /// </summary>
    [SugarTable("IFP_SM_JOBPARAM")]
    public partial class IFP_SM_JOBPARAM
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(36)")]
        public Field<string> Gid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "PGID", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> PGid { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        [SugarColumn(ColumnName = "PARAMNAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> ParamName { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        [SugarColumn(ColumnName = "PARAMTYPE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> ParamType { get; set; }

        /// <summary>
        /// 参数值
        /// </summary>
        [SugarColumn(ColumnName = "PARAMVALUE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> ParamValue { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(ColumnName = "SORT", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "INT")]
        public Field<int> Sort { get; set; }

    }


    /* 以下为对应的Json对象
	{
		GID: null,
		PGID: null,
		PARAMNAME: null,		//参数名称
		PARAMTYPE: null,		//参数类型
		PARAMVALUE: null,		//参数值
		SORT: null		//排序号
	}
	*/

    //[LinqToDB.Mapping.Table("IFP_SM_JOBPARAM")]
    //public class IFP_SM_JOBPARAM
    //{

    //    /// <summary>
    //    /// ID一般为自增长列,自增长列在Orcl无效
    //    /// </summary>
    //    [Column("Gid"), Key, PrimaryKey]
    //    public string Gid { set; get; }

    //    /// <summary>
    //    /// PGID
    //    /// </summary>
    //    [Column("PGid")]
    //    public string PGid { set; get; }

    //    /// <summary>
    //    /// 参数名称
    //    /// </summary>
    //    [Column("ParamName")]
    //    public string ParamName { set; get; }

    //    /// <summary>
    //    /// 参数类型
    //    /// </summary>
    //    [Column("ParamType")]
    //    public string ParamType { set; get; }

    //    /// <summary>
    //    /// 参数值
    //    /// </summary>
    //    [Column("ParamValue")]
    //    public string ParamValue { set; get; }

    //    /// <summary>
    //    /// 排序号
    //    /// </summary>
    //    [Column("Sort")]
    //    public int Sort { set; get; }
    //}
}
