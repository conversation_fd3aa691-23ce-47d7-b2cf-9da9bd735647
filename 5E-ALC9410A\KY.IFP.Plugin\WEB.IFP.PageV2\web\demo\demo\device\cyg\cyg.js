define(["jquery","util","echarts","lib/bower/echarts-stat/ecStat","iofp/services/service.device.cyg"],function($,util,echarts,ecStat,cygService){
    var svg_wg = "path://M25.98076211353316,14.999999999999998L1.83697019872103e-15,30L-25.980762113533153,15.00000000000001L-25.98076211353317,-14.999999999999982L-3.2156263187166845e-14,-30L25.98076211353314,-15.000000000000037Z";
    
    var theme1 = {
        
    }

    var legendConfig = {
        yp1:"样瓶（左面）",
        yp2:"样瓶（右面）",
        wg1:"存样柜（左面）",
        wg2:"存样柜（右面）"
    };


    var graphic =  {
        id:111,
        elements:[{
            type: 'group',
            rotation: Math.PI / 4,
            bounding: 'raw',
            right: 110,
            bottom: 110,
            z: 100,
            children: [
                {
                    type: 'rect',
                    left: 'center',
                    top: 'center',
                    z: 100,
                    shape: {
                        width: 400,
                        height: 50
                    },
                    style: {
                        fill: 'rgba(0,0,0,0.3)'
                    }
                },
                {
                    type: 'text',
                    left: 'center',
                    top: 'center',
                    z: 100,
                    style: {
                        fill: 'red',
                        text: 'ECHARTS BAR CHART',
                        font: 'bold 26px Microsoft YaHei'
                    }
                }
            ]
        }]
    }

    var option = {
        title: {
            text: '',
            subtext: '',
            sublink: '',
            left: 'near',
            top: 10
        },
        graphic:graphic,
        legend: {
            type: 'scroll',
            //orient: 'vertical',
            left: 10,
            top: 20,
            //bottom: 20,
            data: [],    
            formatter: function (name) {
                return legendConfig[name];
            }
            //selected: data.selected
        },
        color: ['#eeeeee'],
        grid: [
            { name:"A",x:40,x2:40, y:40,y2:"50%", containLabel: false,borderWidth:0},
            { name:"B",x:40,x2:40, y:"50%",y2:40, containLabel: false,show:false }
        ],
        xAxis: [
            { gridIndex:0,inverse:false, type: 'value',offset:35,splitLine:{show:false} ,axisLine: {show:false},axisTick:{show:false},axisLabel:{show:false} },
            { gridIndex: 1, inverse: false, type: 'value',offset:-5,splitLine:{show:false} ,axisLine: {show:false},axisTick:{show:false},axisLabel:{show:false}}
        ],
        yAxis: [
            { gridIndex:0, type: 'value',offset:-5,splitLine:{show:false} ,axisLine: {show:false},axisTick:{show:false},axisLabel:{show:false}},
            { gridIndex:1,inverse:true, type: 'value',offset:-5,splitLine:{show:false} ,axisLine: {show:false},axisTick:{show:false},axisLabel:{show:false}}
        ],
        tooltip:{
            show:true
        },
        series: [],
        graphic: [
            {
                left:"center",
                top:"center",
                type: 'line',
                shape:{x1:0,x2:8000,y1:300,y2:300,percent:1},
                style:{fill:"#000",stroke:"#f00",lineWidth:5}
            }
        ],
        brush: {
            outOfBrush: {
                color: '#abc'
            },
            brushStyle: {
                borderWidth: 2,
                color: 'rgba(0,0,0,0.2)',
                borderColor: 'rgba(0,0,0,0.5)',
            },
            inBrush:{color:"#f00000"},
            seriesIndex: [0, 1,2,3],
            throttleType: 'debounce',
            throttleDelay: 300,
            geoIndex: "all"
        }
    };

    function addWGSi(option){
        option.series = option.series || [];
        var sIndex = option.series.length;
        ["wg1","wg2"].forEach(function(item,i){
            option.legend.data.push(item);
            option.series.push({
                name:item,
                type:"scatter",
                xAxisIndex: i,
                yAxisIndex: i,
                z:sIndex+i,
                symbol:svg_wg,
                symbolKeepAspect:false, // 保持比例
                //symbolKeepAspect:false,
                //"柜面1，2","行号","列号","编号","X","Y","样瓶类型 1，2，3 = 大中小"
                dimensions: ["柜面", "行号","列号","编号","X","Y", '样瓶类型'],
                encode: {
                    x: 4,
                    y: 5,
                    tooltip: [0,1,2,3,4,5],
                    label: 3
                },
                data:[],
                symbolSize:function(){
                    return  [window.innerWidth/60,window.innerHeight/30];
                },
                itemStyle:{
                    color:"#00000055",
                    opacity:0.3
                },
                emphasis:{
                    itemStyle:{
                        color:"#00000055",
                        opacity:0.5
                    }
                }
            });
        })
    }
    
    function addYPSi(option){
        option.series = option.series || [];
        var sIndex = option.series.length;
        ["yp1","yp2"].forEach(function(item,i){
            option.legend.data.push(item);
            option.series.push({
                name:item,
                type:"scatter",
                xAxisIndex: i,
                yAxisIndex: i,
                z:sIndex+i,
                symbol:"circle",
                //symbolKeepAspect:false,
                //"柜面1，2","行号","列号","编号","X","Y","样瓶类型 1，2，3 = 大中小"
                dimensions: ["柜面", "行号","列号","X","Y", '样瓶类型'],
                encode: {
                    x: 4,
                    y: 5,
                    tooltip: [0,1,2,3,4,5],
                    label: 3
                },
                data:[],
                symbolSize:function(){
                    return  [window.innerWidth/50-15,window.innerHeight/30-15];
                },
                itemStyle:{
                    color:"#009999",
                    opacity:0.8
                },
                emphasis:{
                    itemStyle:{
                        opacity:1
                    }
                }
            });
        })
    }


    // 选择事件
    var renderBrushed = function(params) {
        var selectedItems = [];
        var opt = this.getOption();
        params.batch[0].selected.forEach(function(mainSeries,siIndex){
            for (var i = 0; i < mainSeries.dataIndex.length; i++) {
                var rawIndex = mainSeries.dataIndex[i];
                var dataItem = opt.series[siIndex].data[rawIndex];
                selectedItems.push(dataItem);
            }
        })
        
        sels.innerHTML = "";
        for (var i = 0; i < selectedItems.length; i++) {
            var li = document.createElement("div");
            li.innerHTML = selectedItems[i][3];
            sels.append(li);
        }
    }



    function updateYPSOption(myChart,data){
        myChart.setOption({
            series:[
                {
                    name:"yp1",
                    data:data.filter(function(item){return item[0]==1})
                },{
                    name:"yp2",
                    data:data.filter(function(item){return item[0]==2})
                }
            ]
        });
    }

    function updateWGSOption (myChart,data){
        myChart.setOption({
            series:[
                {
                    name:"wg1",
                    data:data.filter(function(item){return item[0]==1})
                },{
                    name:"wg2",
                    data:data.filter(function(item){return item[0]==2})
                }
            ]
        });
    }

    function initChart(myChart){
        myChart.showLoading();
        window.addEventListener("resize",util.debounce(function(){
            myChart.resize();
        }))
        // 显示空表格
        myChart.setOption(option);
        myChart.on('brushselected', renderBrushed);
    }


    /* onload */
    
    var sels = document.getElementById("sels");
    var myChart = echarts.init(document.getElementById("echart1"));
    //更新瓶子数据
    var updateYPS = function(){
        return cygService.queryAllYP().then(function(yps){
            updateYPSOption(myChart,yps.data)
        })
    }
    //更新网格
    var updateWGS = function(){
        return cygService.queryAllWG().then(function(wgs){
            updateWGSOption(myChart,wgs.data)
        });
    }

    //addTestSi(option);
    addWGSi(option);//设置 网格
    addYPSi(option);//设置 瓶子
    initChart(myChart);// 初始化
    
    //更新网格
    updateWGS().then(function(){
        myChart.hideLoading();
        updateYPS();
        //更新瓶子数据
        setInterval(updateYPS,5000)
    })
    

    /*
    var center = [0,0];
    var pointsBG = [];
    var maxViewRadius = 30;
    var angle = Math.PI / 6;
    for (var i = 0; i < 6; i++, angle += Math.PI / 3) {
        pointsBG.push([
            center[0] + maxViewRadius * Math.cos(angle),
            center[1] + maxViewRadius * Math.sin(angle)
        ]);
    }
    console.log("M"+pointsBG.map(item => item.join(",")).join("L")+"Z");
    */
})