<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="keywords" content="工具,图标">
		<title>图标</title>
	</head>
	<body class="flex" controller="index.js" option="{platform:'element'}">
		<ifp-page class="pages-icon" id="app" style="min-height:0;">
			<ifp-toolbar close>
				<ifp-button @click="play()">{{playState?'关闭':'播放'}}</ifp-button>
				<ifp-button @click="play2()">切换视频源</ifp-button>
				<ifp-button @click="dialogShow=true">弹出</ifp-button>
			</ifp-toolbar>

			<div class="flex-item padding">

				<video-player ref="video1" v-if="playState" :options="videoOptions"></video-player>
			</div>

			<ifp-dialog :title="dialogTitle" width="600px"
				:visible.sync="dialogShow">
				<video-player :options="videoOptions"/>
			</ifp-dialog>
    	</ifp-page>
	</body>
	<script src="/iofp/starter.js"></script>
</html>
