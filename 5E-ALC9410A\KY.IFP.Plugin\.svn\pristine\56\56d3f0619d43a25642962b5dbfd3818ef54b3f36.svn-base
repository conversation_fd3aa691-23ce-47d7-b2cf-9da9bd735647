﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Mapping;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{
    /// <summary>
    /// 样品类型
    /// </summary>

    [SugarTable("IFP_BS_YWDX4024")]
    public class YWDX4024 : BaseData<YWDX4024>
    {
        /// <summary>
        /// GID
        /// </summary>
        [SugarColumn(ColumnName = "Gid", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public override Field<long> Gid { get; set; }

        /// <summary>
        /// 颗粒度
        /// </summary>
        [SugarColumn(ColumnName = "KeLiDu", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,3)")]
        public Field<decimal> KeLiDu { get; set; }

        /// <summary>
        /// 存放周期
        /// </summary>
        [SugarColumn(ColumnName = "Cfzq", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Cfzq { get; set; }

        /// <summary>
        /// 是否存放
        /// </summary>
        [SugarColumn(ColumnName = "Sfcf", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Sfcf { get; set; }

        /// <summary>
        /// 重量要求
        /// </summary>
        [SugarColumn(ColumnName = "WeightRequirement", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> WeightRequirement { get; set; }

        /// <summary>
        /// 业务场景，逗号分隔的字符串
        /// </summary>
        [SugarColumn(ColumnName = "YWCJ1072", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(256)")]
        public Field<string> Ywcj1072 { get; set; }

        /// <summary>
        /// 与之对应的编码规则
        /// </summary>
        [SugarColumn(ColumnName = "CODEID", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(36)")]
        public Field<string> CodeID { get; set; }

        /// <summary>
        /// 入炉周期
        /// </summary>
        [SugarColumn(ColumnName = "RLZQ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Rlzq { get; set; }

        /// <summary>
        /// 内控周期
        /// </summary>
        [SugarColumn(ColumnName = "NKZQ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Nkzq { get; set; }

        /// <summary>
        /// 入厂化验指标，格式：4|4|5|5|2|2|3|3|1|1|0|0   0：CHN、1：硫分、2：内水、3：挥发分、4：灰分、5：热值、6：全水
        /// </summary>
        [SugarColumn(ColumnName = "RCHYZB", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(100)")]
        public Field<string> RCHYZB { get; set; }

        /// <summary>
        /// 入炉化验指标，格式：4|4|5|5|2|2|3|3|1|1|0|0   0：CHN、1：硫分、2：内水、3：挥发分、4：灰分、5：热值、6：全水
        /// </summary>
        [SugarColumn(ColumnName = "RLHYZB", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(100)")]
        public Field<string> RLHYZB { get; set; }
    }

    //  [Table(Name = "IFP_BS_YWDX4024")]
    //  public class YWDX4024 : BaseData<YWDX4024>
    //  {

    //      /// <summary>
    //      /// GID
    //      /// </summary>
    //      [Column(Name = "Gid", IsPrimaryKey = true, DataType = DataType.Int64, DbType = "bigint")]
    //      public override Field<long> Gid { get; set; }

    //      /// <summary>
    //      /// 颗粒度
    //      /// </summary>
    //      [Column(Name = "KeLiDu", DataType = DataType.Decimal, DbType = "decimal(18,3)")]
    //      public Field<decimal> KeLiDu { get; set; }

    //      /// <summary>
    //      /// 存放周期
    //      /// </summary>
    //      [Column(Name = "Cfzq", DataType = DataType.Int32, DbType = "int")]
    //      public Field<int> Cfzq { get; set; }

    //      /// <summary>
    //      /// 是否存放
    //      /// </summary>
    //      [Column(Name = "Sfcf", DataType = DataType.Int32, DbType = "int")]
    //      public Field<int> Sfcf { get; set; }

    //      /// <summary>
    //      /// 重量要求
    //      /// </summary>
    //      [Column(Name = "WeightRequirement", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //      public Field<string> WeightRequirement { get; set; }
    //      /// <summary>
    //      /// 业务场景，逗号分隔的字符串
    //      /// </summary>
    //      [Column(Name = "YWCJ1072", DataType = DataType.NVarChar, DbType = "nvarchar(256)")]
    //      public Field<string> Ywcj1072 { get; set; }

    //      /// <summary>
    //      /// 与之对应的编码规则
    //      /// </summary>
    //      [Column(Name = "CODEID", DataType = DataType.NVarChar, DbType = "nvarchar(36)")]
    //      public Field<string> CodeID { get; set; }

    //      /// <summary>
    ///// 入炉周期
    ///// </summary>
    //[Column(Name = "RLZQ", DataType = DataType.Int32, DbType = "int")]
    //      public Field<int> Rlzq { get; set; }

    //      /// <summary>
    //      /// 内控周期
    //      /// </summary>
    //      [Column(Name = "NKZQ", DataType = DataType.Int32, DbType = "int")]
    //      public Field<int> Nkzq { get; set; }

    //      /// <summary>
    //      /// 入厂化验指标，格式：4|4|5|5|2|2|3|3|1|1|0|0   0：CHN、1：硫分、2：内水、3：挥发分、4：灰分、5：热值、6：全水
    //      /// </summary>
    //      [Column(Name = "RCHYZB", DataType = DataType.NVarChar, DbType = "nvarchar(100)")]
    //      public Field<string> RCHYZB { get; set; }

    //      /// <summary>
    //      /// 入炉化验指标，格式：4|4|5|5|2|2|3|3|1|1|0|0   0：CHN、1：硫分、2：内水、3：挥发分、4：灰分、5：热值、6：全水
    //      /// </summary>
    //      [Column(Name = "RLHYZB", DataType = DataType.NVarChar, DbType = "nvarchar(100)")]
    //      public Field<string> RLHYZB { get; set; }
    //  }


    /* 以下为对应的Json对象
	{
		Gid: null,		//GID
		Kelidu: null,		//颗粒度
		Cfzq: null,		//存放周期
		Sfcf: null,		//是否存放
		Weightrequirement: null,		//重量要求
		Ywcj1072: null,		//使用的业务场景
		Codeid: null,		//编码规则ID
		Rlzq: null,		//入炉周期
		Nkzq: null,		//内控周期
        RCHYZB: null,		//入厂化验指标，格式：4|4|5|5|2|2|3|3|1|1|0|0   0：CHN、1：硫分、2：内水、3：挥发分、4：灰分、5：热值、6：全水
		RLHYZB: null		//入炉化验指标，格式：4|4|5|5|2|2|3|3|1|1|0|0   0：CHN、1：硫分、2：内水、3：挥发分、4：灰分、5：热值、6：全水
	}
	*/
}
