define(["jquery","service"],function($,service){

    var myservice = service.createServices({
        obj:{
            url:"/servicetest/obj",
            contentType:"appliction/json"
        },
        arr:{
            url:"/servicetest/arr",
            contentType:"appliction/json"
        },
        str:{
            url:"/servicetest/str",
            contentType:"html/text"
        }
    });

    $("#btnObject").click(function(){
        myservice.obj({a:1}).then(data=>{alert(JSON.stringify(data))}).catch(ex=>alert(ex.message))
    })

    $("#btnArray").click(function(){
        myservice.arr([1,2,3,4]).then(data=>{alert(JSON.stringify(data))}).catch(ex=>alert(ex.message))
    })

    $("#btnString").click(function(){
        myservice.str("string").then(data=>{alert(JSON.stringify(data))}).catch(ex=>alert(ex.message))
    })


    $("#btnJObject").click(function(){
        $.ajax("/servicetest/obj",{
            contentType:"json",
            dataType:"json",
            data:JSON.stringify({a:1}),
            success:function(){},
            error:function(){}
        });
    })

    $("#btnJArray").click(function(){
        $.ajax("/servicetest/obj",{
            contentType:"applaction/json",
            data:JSON.stringify([1,2,3,4]),
            success:function(){},
            error:function(){}
        });
    })

    $("#btnJString").click(function(){
        $.ajax("/servicetest/obj",{
            contentType:"text",
            data:"33333",
            success:function(){},
            error:function(){}
        });
    })
})