define(["iofp/api", "platform/vue", "util", "ramda"], function (API, pVue, util, R) {

    return {
        el: "#app",
        //components: components,
        data() {
            return {
                //树节点Key
                treeKey: 'Gid',

                //是否禁用“新增”按钮
                CreateDisabled: true,

                //是否禁用“保存”按钮
                SaveDisabled: true,

                //是否禁用“修改”按钮
                UpdateDisabled: true,

                //FORM表单区域非编辑状态下要禁用
                editDisabled: true,

                //树内容
                treeData: [],

                treeNode: null,  //点击的节点
                //点击新增时机组的gid
                xzGid: null,
                defaultProps: {
                    children: 'children',
                    label: 'Bname'
                },

                //搜索框内容
                SearchName: '',

                //启用停用搜索
                SearchState: '0',

                //子窗口属性
                editor: {
                    //是否显示子窗口
                    showdetail: false,

                    //子窗口标题
                    title: '',

                    //修改数据的设备编码
                    sbbm: ''
                },

                form: {
                    Gid: null,
                    Pgid: null,
                    Bname: null,
                    Sname: null,
                    Ywlx: '4009',
                    Compid: null,
                    Addtime: null,
                    Lasttime: null,
                    Zfbz: 0,
                    Ywbm: null,
                    Beizhu: null,
                },
                rules: {
                    Bname: [
                        { required: true, message: '请输入筒仓全称', trigger: 'blur' }
                    ],
                    Sname: [
                        { required: true, message: '请输入筒仓简称', trigger: 'blur' }
                    ],
                }
            }
        },
        computed: {

        },
        created() {
            var _this = this;
            //加载树内容
            this.SelectTree();
        },
        watch: {
            SearchName: function () {
                var _this = this;
                _this.$refs.tree.filter(_this.SearchName);
            },
            SearchState: function () {
                var _this = this;
                this.SelectTree();
            },
        },
        methods: {
            //树节点点击事件
            handleNodeClick: function (data) {
                //控制工具栏按钮的可用CreateDisabled
                this.CreateDisabled = data.Pgid == -1 || data.Gid == -1;
                this.SaveDisabled = true;
                this.UpdateDisabled = data.Pgid == -1 || data.Gid == -1;

                //FORM表单禁用编辑
                this.editDisabled = true;
                this.treeNode = data;
                //如果是点击的不是煤仓，就需要从后台取数，重新赋值给FORM表达
                if (data.Pgid != null && data.Pgid != -1) {
                    //根据GID查找对象，赋值到FORM表单
                    this.Select(data.Gid);
                } else {
                    //清空表单内容
                    this.clearForm();
                }
            },

            //点“新增”按钮
            onCreate: function () {
                this.xzGid = this.treeNode.Gid;
                //清空表单内容
                this.clearForm();
                //禁用“新增”按钮
                this.CreateDisabled = true;
                //开启“保存”按钮
                this.SaveDisabled = false;
                //禁用“修改”按钮
                this.UpdateDisabled = true;
                //FORM表单启用编辑
                this.editDisabled = false;
            },
            SelectTree() {
                return API.GetAction("API/ICS/BaseData/Ywdx4004API/SelectTree", [{ zfbz: this.SearchState }]).then(x => {
                    this.treeData = util.getTreeData(x, { id: "Gid", pid: "Pgid" });
                }).catch(e => {
                    this.$message.error(e);
                });
            },
            Select(gid) {
                var _this = this;
                API.GetAction("API/ICS/BaseData/Ywdx4004API/Select", { filter: [{ Gid: gid }] }).then(x => {
                    _this.form = x[0];
                }).catch(e => {
                    this.$message.error(e);
                });
            },

            //点“保存”按钮
            onSave: function () {
                var _this = this;
                this.$refs["form"].validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                    if (this.form.Gid == null)  //是新增的，把pgid补上
                        this.form.Pgid = this.xzGid;
                    //保存FORM表单
                    API.GetAction("API/ICS/BaseData/Ywdx4004API/Submit", this.form).then(x => {
                        if (x.success) {
                            //FORM表单禁用编辑
                            this.editDisabled = true;
                            //启用“修改”按钮
                            this.UpdateDisabled = false;
                            //禁用“保存”按钮
                            this.SaveDisabled = true;
                            this.$message({
                                message: '保存成功。',
                                type: 'success'
                            });
                            this.SelectTree().then(() => {
                                setTimeout(function () {
                                    _this.$refs.tree.setCurrentKey(_this.form.Gid);
                                }, 200);
                            })
                        }
                        else {
                            this.$message({
                                message: x.msg,
                                type: 'error'
                            });
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
                });
            },

            //点“修改”按钮
            onUpdate: function () {
                //FORM表单启用编辑
                this.editDisabled = false;
                //禁用“修改”按钮
                this.UpdateDisabled = true;
                //开启“保存”按钮
                this.SaveDisabled = false;
            },

            //定位
            OnGetLocation: function () {
                var _this = this;
                _this.$refs.tree.filter(_this.SearchName);
            },

            //节点过滤函数
            filterNode(value, data) {
                if (!value) return true;
                return data.Bname.indexOf(value) !== -1;
            },

            //点“退出”按钮
            onExit: function () {
                this.$emit("cancel");
            },

            //清空表单内容
            clearForm: function () {
                this.form.Gid = null;
                this.form.Pgid = null;
                this.form.Bname = null;
                this.form.Sname = null;
                this.form.Ywlx = '4004';
                this.form.Compid = null;
                this.form.Addtime = null;
                this.form.Lasttime = null;
                this.form.Zfbz = 0;
                this.form.Ywbm = null;
            },
        }
    }
})