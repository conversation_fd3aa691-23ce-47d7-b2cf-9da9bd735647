<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item" id="app">
        <div class="toolbar">
            <el-button type="primary" icon="el-icon-print" @click="$refs.table1.print()">打印 el-table</el-button>
            <el-button type="primary" icon="el-icon-print" @click="printJSON">使用Lodop打印</el-button>
            <el-button type="primary" icon="el-icon-print" @click="checkLodop">检查Lodop</el-button>
        </div>
        
        <div class="padding flex-item">
            <ifp-table :data="viewer.source" ref="table1"
                style="width:100%;"
                row-key="id"
                height="100%"
                height="auto"
            
                highlight-current-row>
                <el-table-column prop="Gid"
                                label="标识"
                                sortable>
                </el-table-column>
                <el-table-column prop="Kdbm"
                                label="矿点编号"
                                sortable>
                </el-table-column>
                <el-table-column prop="Bname"
                                label="矿点名称"
                                sortable>
                </el-table-column>
                <el-table-column prop="Sname"
                                sortable
                                label="矿点简称">
                </el-table-column>
                <el-table-column prop="Lasttime"
                                sortable
                                :formatter="$TableFormatter.dateFormat('YYYY年')"
                                label="更新时间">
                </el-table-column>
            </ifp-table>
        </div>
    </div>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>