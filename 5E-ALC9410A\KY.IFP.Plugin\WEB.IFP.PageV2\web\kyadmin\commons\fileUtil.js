;
define([ "require"], function(require) {
	return {
		//文件预览.
		fileview:function(fjgid){
			var _this=this;
			var opt = {
					url:"/com.kysoft.service/html/sysmanage/filesys/ylfile.html",
					full:true,
					title: "预览",
					onhidden:function(){
						
					}	
				};
			// 参数
			opt.parameter = {"wjgid":fjgid};
			var win = window.parent.$.showIframe(opt);
			
		},
		//文件下载.
		filedown:function(fjgid){
			var _this=this;
			var form=$("<form>");
		    // 设置表单状态为不显示
		    form.attr("style","display:none");
		    // method属性设置请求类型为post
		    form.attr("method","post");
		    // action属性设置请求路径,
		    // 请求类型是post时,路径后面跟参数的方式不可用
		    // 可以通过表单中的input来传递参数
		    form.attr("action","/com.kysoft.service/upload/downfileByFileId.action?fileId="+fjgid);
		    $("body").append(form);// 将表单放置在web中
		    form.submit();// 表单提交
		    form.remove();
		},
		//单个文件删除.
		filedelete:function(fjgid){
			var _this=this;
			
			F.ajax({
				url:"/com.kysoft.service/upload/delfileById.action?fileId="+fjgid,
				data:{},
				success:function(result) {		
					if(result=="success"){
						$.bootoast.success("删除成功。");
					}else{
						$.bootoast.danger("删除失败。");
					}
				}
			});
			
		},

		getFileList:function(){
			var filelist = [];
			$(".filelist li .download").each(function (index,domEle){
				filelist.push(domEle.id);
			});
			return filelist;
		},

		//显示图片.
		showIMG:function(fjgid,czlx,divid){
			var _this = this;
//			F.util.showWait();// 显示等待
			var uuid = F.util.uuidFast();
			var filenum = "WU_FILE_"+uuid;
			var display;
			if(czlx=="view"||czlx=="approve"){
				display=1;
			}else{
				display=2;
			}
			var html = $("#"+divid).html();
			html = html.replace(/\s*/g,"");
			if(!html){
				$("#"+divid).html("<div class=\"wrapper\">\n" +
						"\t\t\t\t\t<!-- 上传图片 -->\n" +
						"\t\t\t\t\t<div id=\"container\" >\n" +
						"\t\t\t\t\t<div id=\"upload\" >\n" +
						"\t\t\t\t\t\t<div id=\"queueList\" class=\"queueList\">\n" +
						"\t\t\t\t\t\t\t<ul class=\"filelist \">\n" +
						"\t\t\t\t\t\t\t\t<li id=\"filePickerBlock\" class=\"filePickerBlock\" style=\"display: none;\"></li>\n" +
						"\t\t\t\t\t\t\t</ul>\n" +
						"\t\t\t\t\t\t</div>\n" +
						"\t\t\t\t\t</div>\n" +
						"\t\t\t\t\t</div>\n" +
						"\t\t\t\t</div>");
			}
			

			F.ajax({
				url:'/com.kysoft.service/upload/getImgHtml.action?display='+display,
				data: {"fileId":fjgid,"filenum":filenum},
				async:false,
				success: function (resp) {
					if(resp != null && resp != "" && resp != undefined){
						var obj = JSON.parse(resp);
						var $li = $(obj.html);

						var $filePickerBlock = $("#upload").find('.filePickerBlock');
		                $li.insertBefore($filePickerBlock);
		                var $btns = $("#"+filenum+" .file-panel");
		                $li.on('mouseenter', function () {
			                $btns.stop().animate({height: 30});
			            });
			            $li.on('mouseleave', function () {
			                $btns.stop().animate({height: 0});
			            });
			            
			            $btns.on('click', 'span', function () {
		                    var index = $(this).index(),
		                        deg;
		                    if(czlx=="view"||czlx=="approve"){
		                    	if(index==0){
			                    	if(obj.filetype=="pdf"||obj.filetype=="txt"||obj.filetype=="png"||obj.filetype=="jpg"||obj.filetype=="jpeg"||obj.filetype=="gif"||
			                    			obj.filetype=="doc"||obj.filetype=="docx"||obj.filetype=="xls"||obj.filetype=="xlsx"||
			                    			obj.filetype=="ppt"||obj.filetype=="pptx"){
			                    		// 预览.
				                    	F.common.fileUtil.fileview(this.id);
			                    	}else{
			                    		$.alert("文件类型暂不支持预览。");
			                    		return;
			                    	}
			                    }else if(index==1){
			                    	// 下载.
			                    	F.common.fileUtil.filedown(this.id);
			                    }
		                    }else{
		                    	if(index==1){
			                    	if(obj.filetype=="pdf"||obj.filetype=="txt"||obj.filetype=="png"||obj.filetype=="jpg"||obj.filetype=="jpeg"||obj.filetype=="gif"||
			                    			obj.filetype=="doc"||obj.filetype=="docx"||obj.filetype=="xls"||obj.filetype=="xlsx"||
			                    			obj.filetype=="ppt"||obj.filetype=="pptx"){
			                    		// 预览.
				                    	F.common.fileUtil.fileview(this.id);
			                    	}else{
			                    		$.alert("文件类型暂不支持预览。");
			                    		return;
			                    	}
			                    }else if(index==2){
			                    	// 下载.
			                    	F.common.fileUtil.filedown(this.id);
			                    }else{
			                    	// 删除
			                    	F.common.fileUtil.filedelete(this.id);
			                    	 var $li = $('#' + filenum);
			                         $li.off().find('.file-panel').off().end().remove();
			                    }
		                    }

		                });
					}
	            },
				complete:function(){
//					F.util.hideWait();
				}
			});
		}
	
	}
});