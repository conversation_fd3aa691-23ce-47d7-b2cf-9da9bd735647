﻿namespace COM.IFP.Common
{
    /// <summary>
    /// 统一的返回值定义，已用于SControl方法和InsertCmd方法的返回对象和外挂程序各个方法的返回值,daiabin弃用
    /// </summary>
    //public class ReturnMessage
    //{
    //    /// <summary>
    //    /// 枚举
    //    /// </summary>
    //    public EnumMsgType Type { get; set; }
    //    /// <summary>
    //    /// 返回描述
    //    /// </summary>
    //    public string Message { get; set; }
    //    /// <summary>
    //    /// 参数表
    //    /// </summary>
    //    public Dictionary<string, object> Params { get; set; } = new Dictionary<string, object>();
    //}
    ///// <summary>
    ///// 返回对象的类型
    ///// </summary>
    public enum EnumMsgType
    {
        正常 = 1,
        错误 = 2,
        抛出异常 = 3,
        执行失败 = 4,
        执行成功 = 5,
        连接失败 = 6,
        连接成功 = 7,
        未检测到样桶芯片 = 8,
        数据库中未查询到相关样桶称量信息 = 9,
        指定打印机不存在, 请重新设置打印机参数 = 10,
        本机未安装打印机 = 11
    }
}
