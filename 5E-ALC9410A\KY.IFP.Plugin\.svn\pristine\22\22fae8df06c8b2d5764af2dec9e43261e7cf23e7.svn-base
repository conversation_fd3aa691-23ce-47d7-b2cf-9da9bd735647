﻿<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <title>用户记录表</title>
        <style type="text/css">
            .SelectBG {
                color: blue;
            }
        </style>
    </head>
    <body controller="userInfo.js">
        <div form="forms/toolbar" class="layout-h">
            <a id="addBtn" control="controls/button" option="{}" log>新增</a>
            <a id="showBtn" control="controls/button" option="{}" log>查看</a>
            <a id="editBtn" control="controls/button" option="{}" log>修改</a>
            <a id="delBtn" control="controls/button" option="{}" log>删除</a>
            <a id="resetPwdBtn" control="controls/button" option="{}" log>重置密码</a>

            <a id="qxBtn" control="controls/button" option="{}" log>用户赋权</a>
            <a id="readBtn" control="controls/button" option="{}" log>绑定ID卡</a>

            <div style="float: right">
                <!--<span class="kjicon kjicon-wenhao" style="font-size:16px;color:#0075D3"></span>
            <span style="font-size: 14px; font-weight: bold;padding-right:5px;">
                帮助
            </span>-->
                <span class="dicb dicb-tuxing" style="font-size: 16px; color: #0075d3"></span>
                <span style="font-size: 14px; font-weight: bold"> 人员信息 </span>
            </div>
        </div>

        <div class="searchbar layout-h">
            <table>
                <tr>
                    <td>用户名：</td>
                    <td>
                        <input id="UsiName_LIKE" control="controls/textbox" option="{showName:'用户名',controlsUses:2}" />
                    </td>
                    <td class="pg-l-20">工号：</td>
                    <td class="pg-l-20">
                        <input id="UsiNumber_LIKE" control="controls/textbox" option="{showName:'工号',controlsUses:2}" />
                    </td>

                    <td class="seartchbar-buttons">
                        <a id="searchBtn" control="controls/button" option="{}" log>查&nbsp;&nbsp;询</a>
                        <a id="resetBtn" control="controls/button" option="{}" log>重&nbsp;&nbsp;置</a>
                    </td>
                </tr>
            </table>
        </div>
        <div class="layout-c padding">
            <table
                id="grid"
                control="controls/grid"
                option='{"autoresize":true,pager:"#pager5", cellEdit:false,sortname:"USINUMBER", sortorder : "asc",
                method:"post",
                queryParams: {
                }
            }'
            >
                <thead>
                    <tr>
                        <th>gid</th>
                        <th>用户名</th>
                        <th>所属部门</th>
                        <th>登录名</th>
                        <th>工号</th>
                        <th>性别</th>
                        <th>联系方式</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td option="{name:'Gid',width : 100,frozen: true,hidden:true, key:true}"></td>
                        <td option="{name:'UsiName',width : 200,align : 'left',editable:false}"></td>
                        <td option="{name:'DeptName',width : 200,align : 'left',editable:false}"></td>
                        <td option="{name:'UsiLoginName',width : 180,align : 'left',editable:false}"></td>
                        <td option="{name:'UsiNumber',width : 180,align : 'center',editable:false}"></td>
                        <td
                            option="{name:'UsiSex',align : 'center', width:50,
                        control:'controls/select2',controloption:{data:[{id:'0',text:'男'},{id:'1',text:'女'}]}}"
                        ></td>
                        <td option="{name:'UsiContact',width : 180,align : 'left',editable:false}"></td>
                        <td option="{name:'REMARK',width : 300,align : 'left',editable:false}"></td>
                    </tr>
                </tbody>
            </table>
            <div id="pager5"></div>
        </div>
    </body>
    <script src="/iofp/starter.js"></script>
</html>
