
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹出页子页面[element]</title>
</head>
<body controller="element.js" option="{platform:'vue',use:['ELEMENT']}">
    <div id="app">
        <el-container>
            <el-header>
                <el-row>
                    <h1>ElementUI 页面演示 / <a href="https://element.eleme.cn/#/zh-CN/component/installation">官方 API</a></h1>
                </el-row>
            </el-header>
            <el-main>
                <el-row>
                  <el-button @click="close">退出</el-button>
                    <el-button @click="btnclick">默认按钮</el-button>
                    <el-button type="primary">主要按钮</el-button>
                    <el-button type="success">成功按钮</el-button>
                    <el-button type="info">信息按钮</el-button>
                    <el-button type="warning">警告按钮</el-button>
                    <el-button type="danger">危险按钮</el-button>
                </el-row>

                <el-table
                  :data="tableData"
                  stripe
                  style="width: 100%">
                  <el-table-column
                    prop="date"
                    label="日期"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="姓名"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="address"
                    label="地址">
                  </el-table-column>
                </el-table>

                <h2>能力展示</h2>


                <h3>演示数据请求</h3>

                服务端菜单数量

                <ifp-input :value="menulist.length" readonly placeholder=""></ifp-input>
                
                

                <h3>演示嵌入页面：基于vue的页面</h3>

                <el-form label-width="auto">
                    <el-form-item label="传入子页面的数据：">
                      <ifp-input v-model="arg1"></ifp-input>
                    </el-form-item>
                </el-form>

                <subpage1 class="padding" :arg1="arg1" style="border:1px solid #aaa;"></subpage1>
            </el-main>
        </el-container>
    </div>
    <script src="/iofp/starter.js"></script> 
</body>
</html>