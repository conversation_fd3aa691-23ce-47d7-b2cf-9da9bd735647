# 模板


## 基础模板
```html
<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[标题]</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex-item" id="app">
        <!--内容-->
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>
```

## 列表页

> 实例：<a href="/demo/template/list.html">列表页</a>

### html
```html
<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[标题]</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item" id="app">
        <!--工具栏按钮-->
        <div class="toolbar">
            <ifp-button-lxsz table="table1"></ifp-button-lxsz>

            <!--工具栏按钮-->
            <ifp-button-close></ifp-button-close>
        </div>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>
```

js
```
```