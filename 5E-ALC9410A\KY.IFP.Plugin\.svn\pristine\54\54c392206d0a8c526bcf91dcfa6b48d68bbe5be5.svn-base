﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{

    /// <summary>
    /// 目前只有采样机设备有参数信息，其他设备如有参数设置，可添加字段
    /// </summary>
    [SugarTable("IFP_BS_YWDX4009_CS")]
    public partial class IFP_BS_YWDX4009_CS
    {
        /// <summary>
        /// GID
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(36)")]
        public Field<string> GID { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        [SugarColumn(ColumnName = "SBBM", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(50)")]
        public Field<string> SBBM { get; set; }

        /// <summary>
        /// 采样方式，0:全自动1:半自动2:手动
        /// </summary>
        [SugarColumn(ColumnName = "CYFS", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> CYFS { get; set; }

        /// <summary>
        /// 制样方式，0:制样口1:溜管口
        /// </summary>
        [SugarColumn(ColumnName = "ZYFS", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> ZYFS { get; set; }

        /// <summary>
        /// 在线测水，0:否1:是
        /// </summary>
        [SugarColumn(ColumnName = "ZXCS", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> ZXCS { get; set; }

        /// <summary>
        /// 在线检测，0:否1:是
        /// </summary>
        [SugarColumn(ColumnName = "ZXJC", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> ZXJC { get; set; }

        /// <summary>
        /// 是否清洗，0:否1:是
        /// </summary>
        [SugarColumn(ColumnName = "SFQX", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> SFQX { get; set; }
    }
    //[Table(Name = "IFP_BS_YWDX4009_CS")]
    //public partial class IFP_BS_YWDX4009_CS
    //{
    //	/// <summary>
    //	/// GID
    //	/// </summary>
    //	[Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.NVarChar, DbType = "nvarchar(36)")]
    //	public Field<string> GID { get; set; }

    //	/// <summary>
    //	/// 设备编码
    //	/// </summary>
    //	[Column(Name = "SBBM", DataType = DataType.NVarChar, DbType = "nvarchar(50)")]
    //	public Field<string> SBBM { get; set; }

    //	/// <summary>
    //	/// 采样方式，0:全自动1:半自动2:手动
    //	/// </summary>
    //	[Column(Name = "CYFS", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> CYFS { get; set; }

    //	/// <summary>
    //	/// 制样方式，0:制样口1:溜管口
    //	/// </summary>
    //	[Column(Name = "ZYFS", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> ZYFS { get; set; }

    //	/// <summary>
    //	/// 在线测水，0:否1:是
    //	/// </summary>
    //	[Column(Name = "ZXCS", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> ZXCS { get; set; }

    //	/// <summary>
    //	/// 在线检测，0:否1:是
    //	/// </summary>
    //	[Column(Name = "ZXJC", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> ZXJC { get; set; }

    //	/// <summary>
    //	/// 是否清洗，0:否1:是
    //	/// </summary>
    //	[Column(Name = "SFQX", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> SFQX { get; set; }

    //}


    /* 以下为对应的Json对象
	{
		GID: null,		//GID
		SBBM: null,		//设备编码
		CYFS: null,		//采样方式，0:全自动1:半自动2:手动
		ZYFS: null,		//制样方式，0:制样口1:溜管口
		ZXCS: null,		//在线测水，0:否1:是
		ZXJC: null,		//在线检测，0:否1:是
		SFQX: null		//是否清洗，0:否1:是
	}
	*/
}
