/**
 * 列显设置按钮
 * @module iofp/ifp-button-lxsz
 */
define([
  "comp!/iofp/mixins/components/ifp-dialog-lxsz/index.js",
  "vuex"
],function(lxsz,{mapState}){
    return {
        props:{
          table:{type:[String,Function],default:""},
          type:{default:""},
          text:{default:"列显设置"}
        },
        components:{ifpDialogLxsz:lxsz},
        template:`
        <el-button :type="type" icon="el-icon-setting" @click="visible=true">{{text}}<span 
        @click.stop="" style="text-align:left;width:0;display:inline-block;overflow:hidden;"><el-dialog 
        append-to-body
          class="subpage" title="表格显示列设置" @mousedown.stop
                :visible.sync="visible"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :fullscreen="false"
                width="500px">
                <ifp-dialog-lxsz @click.stop=""
                    style="height:400px;"
                    class="flex-item"
                    :tableid="table"
                    :showstate="visible"
                    :pageid="$getPageInfo().id"
                    @cancel="visible=false"
                    @sucess="visible=false"
                ></ifp-dialog-lxsz></el-dialog></span></el-button>
        `,
        data(){
          return {
            pageid:this.$getPageInfo().id,
            visible:false
          }
        },
        computed:{
          ...mapState({
              columns(state) {
                const page = this.table && state.lxsz.items.find(item => item.id == this.pageid);
                const table = page && page.tables.find(t=>t.id === this.table);
                return table && table.originColumns || [];
              }
          })
        },
        mounted(){
        }
      }
})