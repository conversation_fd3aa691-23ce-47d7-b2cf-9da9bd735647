﻿//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP.DbModel
{

    /// <summary>
	/// 基础资料类型
	/// </summary>
	[SugarTable("IFP_BS_BASEINFO_TYPE")]
    public partial class IFP_BS_BASEINFO_TYPE
    {
        /// <summary>
        /// 类型编码
        /// </summary>
        [SugarColumn(ColumnName = "YWLX", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(4)")]
        public Field<string> YWLX { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        [SugarColumn(ColumnName = "BASENAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(100)")]
        public Field<string> BaseName { get; set; }

        /// <summary>
        /// 扩展表名
        /// </summary>
        [SugarColumn(ColumnName = "TABLENAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "VARCHAR(1000)")]
        public Field<string> TableName { get; set; }

    }


    /* 以下为对应的Json对象
	{
		YWLX: null,		//类型编码
		BASENAME: null,		//类型名称
		TableName: null		//扩展表名
	}
	*/

    ////基础资料类型
    //[SugarTable("IFP_BS_BASEINFO_TYPE")]
    //public class IFP_BS_BASEINFO_TYPE
    //{
    //    /// <summary>
    //    /// 类型编码
    //    /// </summary>
    //    [SugarColumn(IsPrimaryKey = true, ColumnName = "Ywlx", ColumnDataType = "nvarchar(4)")]
    //    public string Ywlx { set; get; }

    //    /// <summary>
    //    /// 类型名称
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Basename", ColumnDataType = "nvarchar(100)")]
    //    public string Basename { set; get; }

    //    /// <summary>
    //    /// 扩展表名
    //    /// </summary>
    //    [SugarColumn(ColumnName = "TableName", ColumnDataType = "nvarchar(1000)")]
    //    public string TableName { get; set; }
    //}



    //[LinqToDB.Mapping.Table("IFP_BS_BASEINFO_TYPE")]
    //public class IFP_BS_BASEINFO_TYPE
    //{
    //    [Column("Ywlx"), Key, Required, MaxLength(4), PrimaryKey]
    //    public string Ywlx { set; get; }

    //    [Column("Basename"), Required, MaxLength(100)]
    //    public string Basename { set; get; }
    //}
}
