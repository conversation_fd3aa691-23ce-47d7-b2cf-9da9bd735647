/**
 * 功能: EXCEL上传 作者：杨士佳 创建日期：2018-05-09
 */
;define([
"require",
"controllers/base",
"jquery",
	"jclass","lib/bower/jquery-form/dist/jquery.form.min"
],
	function (require, base, $, jclass){
		return jclass(base,{
		/**
		 * 初始化.
		 */
		onLoad:function(){
			var _this = this;
			var params = this.win && this.win.parameter || {};
			var modleName = params.modleName;
			var className = params.className;
			var method = params.method;
			var modleKeys = params.modleKeys;
			var startNum = params.startNum;
			var param = params.param;
			$("#method").val(method);
			$("#modleName").val(modleName);
			$("#className").val(className);
			$("#modleKeys").val(modleKeys);
			$("#startNum").val(startNum);
			$("#param").val(param);
			
//			$('#dowLoadBtn').attr('href',params.templetUrl);
			this.bind("submitBtn","click",function(){
				_this.onSubmit();
			});
			this.bind("dowLoadBtn","click",function(){
				window.location.href = params.templetUrl; 
			});
		},
		onSubmit : function() {
			var _this = this;
			var excel_file = $("#input").val();  
	        if (excel_file == "" || excel_file.length == 0) {  
	            $.alert("请选择文件路径！");  
	            return ;  
	        } 
	        F.util.showWait();// 显示等待
			$('#form1').ajaxSubmit( {
				url: '/API/IFP/PLC/PLCPoint/UploadFiles',
				type: "POST",
				error:function(msg){
					$.alert(msg); 
				},
				success : function(msg) {	
					if(msg=="1"){
						$.alert("导入成功。"); 
						setTimeout(function(){_this.win.close()},1000);
					}else{
						$.alert(msg);  
					}
				},
				complete:function(){
					F.util.hideWait();
				}
			});
		}
	})
});

