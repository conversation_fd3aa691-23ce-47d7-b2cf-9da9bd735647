﻿<!DOCTYPE html>

<html lang="zh-CN">
<head>
    <title>菜单管理</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button :disabled="(currentMeun.menuUrl!=null&&currentMeun.menuUrl!='')" @click="onCreate" code="B1">新增</ifp-button>
            <ifp-button v-bind:disabled="disable.saveDisabled" @click="onSave" code="B1">保存</ifp-button>
            <ifp-button v-bind:disabled="disable.updateDisabled" @click="onUpdate" code="B1">修改</ifp-button>
            <ifp-button v-bind:disabled="disable.updateDisabled" @click="onDel" code="B1">删除</ifp-button>
        </ifp-toolbar>
        <div class="flex flex-row flex-item padding">
            <div class="flex" style="width:250px;">
                <div class="flex flex-row">
                    <div style="width:65%">
                        <ifp-input v-model="sreachName">
                        </ifp-input>
                    </div>
                    <div style="width:35%">
                        <el-select v-model="searchState" placeholder="请选择" style="width:100%">
                            <el-option label="启用" value="0"></el-option>
                            <el-option label="停用" value="1"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="flex-row" style=" text-align: right;">
                    <el-link v-if="treeSortChange" @click="saveTreeSort" type="primary">调整完成</el-link>
                </div>
                <div class="flex-item" style="overflow: auto; background: white; margin-top: 2px">
                    <el-tree :data="treeData"
                             :props="treeProps"
                             :default-expand-all="true"
                             :expand-on-click-node="false"
                             :filter-node-method="filterNode"
                             draggable
                             @node-click="onMenuClick"
                             @node-drop="handleDrop"
                             :allow-drag="allowDrag"
                             :allow-drop="allowDrop"
                             ref="tree">
                    </el-tree>
                </div>
            </div>
            <div class="flex-item" style="margin-left: 1rem; background: white; overflow: auto;">
                <div style="width: 1000px">
                    <el-form ref="currentMeun" label-position="right" :model="currentMeun" label-width="80px" style="padding: 1rem;" :disabled="disable.saveDisabled">
                        <el-row :gutter="0">
                            <el-col :span="8">
                                <el-form-item label="菜单名称" prop="menuName" :rules="{required:true, message:'菜单名称不能为空'}">
                                    <ifp-input v-model="currentMeun.menuName"></ifp-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="菜单简称">
                                    <ifp-input v-model="currentMeun.menuNshortName"></ifp-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="菜单图标">
                                    <ifp-input v-model="currentMeun.menuImg" :disabled="true">
                                        <el-button slot="append" @click="iconDialog.show=true;" :icon="menuIcon"></el-button>
                                    </ifp-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="0">
                            <el-col :span="8">
                                <el-form-item label="排序">
                                    <ifp-input v-model="currentMeun.sort" :disabled="true"></ifp-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="类型">
                                    <el-select v-model="currentMeun.menuType" clearable placeholder="请选择">
                                        <el-option v-for="item in menuTypes"
                                                   :key="item.value"
                                                   :label="item.label"
                                                   :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="状态">
                                    <el-radio v-model="currentMeun.Delt" :label=0>启用</el-radio>
                                    <el-radio v-model="currentMeun.Delt" :label=1>停用</el-radio>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="0">
                            <el-col :span="24">
                                <el-form-item label="菜单地址">
                                    <ifp-input v-model="currentMeun.menuUrl"></ifp-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="0">
                            <el-col :span="24">
                                <el-form-item label="备注">
                                    <ifp-input type="textarea"
                                              :rows="4"
                                              placeholder="请输入内容"
                                              v-model="currentMeun.REMARK">
                                    </ifp-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
        <el-dialog class="subpage"
                   :visible.sync="iconDialog.show"
                   title="选择图标"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :destroy-on-close="true"
                   @close="onClosed"
                   width="1000px">
            <div class="flex" style="height:600px;">
                <ifp-menu-icon @cancel="iconDialog.show=false;"
                               @save="onSelectIcon"
                               :menuimg="currentMeun.menuImg">
                </ifp-menu-icon>
            </div>
        </el-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>