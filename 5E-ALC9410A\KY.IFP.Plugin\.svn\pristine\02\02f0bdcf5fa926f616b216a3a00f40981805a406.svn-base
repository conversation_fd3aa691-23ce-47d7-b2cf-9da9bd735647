﻿using COM.IFP.Common;
using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using ORM.IFP.DbModel;
using ORM.IFP.www.DTO;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace DAL.IFP.Rights
{
    public class UserLog
    {
        //保存日志.
        public void SaveLog(JsonElement json)
        {
            IFP_SM_USERLOG logVo = json.GetValue<IFP_SM_USERLOG>();

            using (var db = DB.Create())
            {
                BaseDbHelper<IFP_SM_USERLOG> dbHelper = new BaseDbHelper<IFP_SM_USERLOG>(db);
                string ip = WebHelper.GetRemoteIp();
                logVo.Gid = Guid.NewGuid().ToString("N");
                logVo.Ip = ip;
                logVo.LogTime = System.DateTime.Now;
                logVo.UserId = UserCache.GetUserID();
                logVo.UserName = UserCache.GetUserName();
                dbHelper.saveEntity(logVo);
            }
        }
        /// <summary>
        /// 查询页面名列表
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public List<IdTextModel> GetPageNameList()
        {
            List<IdTextModel> result = new List<IdTextModel>();
            using (SqlSugarClient db = DB.Create())
            {
                BaseDbHelper<IFP_SM_USERLOG> dbHelper = new BaseDbHelper<IFP_SM_USERLOG>(db);
                //var q = from x in dbHelper.dbClient.GetTable<IFP_SM_USERLOG>() select x.PageName;
                var q = dbHelper.dbClient.Queryable<IFP_SM_USERLOG>()
                .Select(x => x.PageName)
                .ToList();
                foreach (var str in q.Distinct())  //需要筛掉重复项
                {
                    result.Add(new IdTextModel(str, str.Value));
                }
            }
            return result;
        }

        /// <summary>
        /// 查询按钮名列表
        /// </summary>
        /// <param name="obj">根据页名进行查询</param>
        /// <returns></returns>
        public List<IdTextModel> GetBtnNameList(IFP_SM_USERLOG obj)
        {
            List<IdTextModel> result = new List<IdTextModel>();
            using (SqlSugarClient db = DB.Create())
            {
                BaseDbHelper<IFP_SM_USERLOG> dbHelper = new BaseDbHelper<IFP_SM_USERLOG>(db);
                var q = dbHelper.dbClient.Queryable<IFP_SM_USERLOG>()
                .Where(x => x.PageName == obj.PageName)
                .Select(x => x.BtnName)
                .ToList();

                foreach (var str in q.Distinct())
                {
                    result.Add(new IdTextModel(str.Value, str.Value));
                }
            }
            return result;
        }
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public object GetLogList(IList<IFP_SM_USERLOG> filter, COM.IFP.Common.PageModel paging)
        {
            try
            {
                using (SqlSugarClient db = DB.Create())
                {
                    var q = db.Queryable<IFP_SM_USERLOG>().Query(filter.ToArray()).Select(x => x);
                    if (paging == null)
                    {
                        var res = q.ToList();
                        return res;
                    }
                    else
                    {
                        var res = q.Fetch(paging);
                        return res;
                    }
                }
            }
            catch (Exception e)
            {
                LoggerHelper.Error(ErrorList.E2003, "", e);
                throw;
            }


        }

    }
}
