//F.controls.textbox
define(["jclass","./base.input","jquery"],function(jclass,base,$){
	
	var defaultOption = {
	}
	
	return jclass(base,{
		name:"control-picker",
		createContainer:function(container){
			var newContainer = $('<div class="pickerwrap"></div>');
			this.replaceElement(container,newContainer);
			this.input = $("<input />");
			this.input.addClass("form-control");
			this.pickerbox = $('<div class="picker input-group"></div>')
			.appendTo(newContainer)
			.append(this.input);
			this.addon = $('<span class="input-group-addon"></span>').appendTo(this.pickerbox);
			return newContainer;
		}
	});
});