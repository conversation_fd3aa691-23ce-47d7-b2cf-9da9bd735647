define(["ELEMENT","zutil","vuex","iofp/lib/print"],function(Element,zutil,{mapState},printer){
    // 获取 table vm
    let ignore = ["index","selection"]
    function getColumnConfigs(originColumns){
        return originColumns.filter(item=>!ignore.includes(item.type)).map(item=>{
            return {
                fixed: item.fixed,
                headerAlign: item.headerAlign,
                label: item.label,
                order: item.order,
                id: item.id,//item.property||
                property: item.property,
                type: item.type,
                width: item.width,
                children:item.children&&getColumnConfigs(item.children)
            }
        })
    }

    return {
        ifpTableColumn:{
            props:{
              print:{default:true},
              printWidth:{default:"auto"}
            },
            extends:Element.TableColumn
        },
        ifpTable:{
            inject: {
                ifpTableHeight:{
                    default:null//'auto'
                }
            },
            props:{
                hideColumns:{default:()=>[]},
                height:{type:[Number,String],default(){
                    return this.ifpTableHeight;
                }},
                stripe:{type:Boolean,default:true},
                // 列显设置
                lxsz:{type:Boolean,default:true},
                border:{type:Boolean,default:true}
            },
            extends:Element.Table,
            data(){
                return {
                    // 原始列
                    rawOriginColumns:[],
                    lxszpageid:'',
                    lxsztableid:''
                }
            },
            computed:{
                ...mapState({
                    hideColumnNames(state){
                        if(this.lxszpageid && this.lxsztableid){
                            const page = state.lxsz.items.find(item => item.id == this.lxszpageid);
                            const table = page.tables.find(item => item.id == this.lxsztableid);
                            return table.hidecolumns;
                        }else{
                            return []
                        }
                    }
                })
            },
            mounted(){
                if(this.lxsz){
                    this.lxszpageid = this.$getPageInfo().id;
                    this.lxsztableid = this.getTableId();
                    // 首次 存储列配置
                    this.$set(this,"rawOriginColumns",[...this.store.states._columns])
                    // 将配置推送到 store
                    this.appendToLXSZ()
                    .then(()=>{
                        //根据隐藏列重新渲染
                        //this.reRenderByHideColumns()
                    })
                }
            },
            hiddenCols:null,// 用来存储首次渲染时默认插槽内容
            render(h){
                if(this.lxsz){
                    // 首次渲染
                    if(!this.hiddenCols){
                        this.hiddenCols = [...this.$slots.default];
                    }

                    // 从备份冲取出并过滤掉隐藏的
                    this.$slots.default = this.hiddenCols.filter(item=>!this.hideColumnNames.includes(item.child?.columnId))
                }
                return Element.Table.render.apply(this,arguments);
            },
            methods:{
                print(){
                    printer.printELTable(this);
                },
                getTableId(){
                    return this.$getOutRefid() || this.tableId
                },

                appendToLXSZ() {
                    var pageinfo = this.$getPageInfo();
                    let columns = getColumnConfigs(this.store.states.originColumns);
                    let tablerefid = this.getTableId(this);

                    return this.$store.dispatch("lxsz/addPageAndTable", { 
                        page:{
                            id:pageinfo.id,
                            url:pageinfo.url,
                            menuid:pageinfo.menuid,
                            menuurl:pageinfo.menuurl,
                            js:pageinfo.js,
                            tables:[]
                        },

                        table:{
                            id:tablerefid,
                            refid:tablerefid,
                            eltableid:this.tableId,
                            hidecolumns:this.hideColumns,
                            originColumns:columns,
                            columns:getColumnConfigs(this.store.states.columns)
                        }
                    })
                }
            }
        }
    }
})