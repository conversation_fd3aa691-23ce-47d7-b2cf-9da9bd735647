﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入厂班次信息</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    
    <ifp-page id="app">
        <ifp-toolbar close lxsz="tableBc">
            <ifp-button code="B1" @click="onAdd">新增</ifp-button>
            <ifp-button code="B2" @click="onView">查看</ifp-button>
            <ifp-button code="B3" @click="onUpdate">修改</ifp-button>
        </ifp-toolbar>
        
        <ifp-searchbar @search="onSelect" @reset="onReset">
            <ifp-form-item label="启停状态">
                <el-radio-group v-model="filter.Zfbz.Value" @change="onSelect" value ="0">
                    <el-radio :label="-1">全部</el-radio>
                    <el-radio :label="0">启用</el-radio>
                    <el-radio :label="1">停用</el-radio>
                </el-radio-group>
            </ifp-form-item>
        </ifp-searchbar>

        <ifp-panel-table class="flex-item padding">
            <ifp-table ref="tableBc"
                       :data="tableData"
                       :border="true"
                        :default-sort = "{prop: 'Bname', order: 'descending'}"
                       :highlight-current-row="true"
                       @row-click="rowClick">
                <el-table-column type="index"
                                 width="50">
                </el-table-column>
                <el-table-column prop="Bname" min-width="100" sortable
                                 label="班次名称">
                </el-table-column>
                <el-table-column prop="CYZQ" sortable
                                 label="采样周期">
                </el-table-column>
                <el-table-column prop="QB"
                                 align="center" sortable
                                 label="期别">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.QB" :ywlx="4007"  :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                    </template>
                </el-table-column>
                <el-table-column prop="Sfyxbc1099"
                                 align="center"
                                 label="是否运行班次">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.Sfyxbc1099" :ywlx="1099" :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                    </template>
                </el-table-column>

                <el-table-column prop="Kssj"
                                 label="开始时间">
                </el-table-column>
                <el-table-column prop="Jssj"
                                 label="结束时间">
                </el-table-column>

                <el-table-column prop="Zfbz"
                                 align="center" sortable
                                 label="启停状态">
                    <template slot-scope="scope">
                        <div v-if="scope.row.Zfbz==0">启用</div>
                        <div v-else>停用</div>
                    </template>
                </el-table-column>

                <el-table-column prop="Beizhu" min-width="300"
                                 label="备注">
                </el-table-column>
            </ifp-table>
        </ifp-panel-table>
        <el-dialog class="subpage" :title="editor.title"
                   :visible.sync="editor.showdetail"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   width="800px">
            <ifp-ysdw-detail @cancel="editor.showdetail=false"
                             @success="editor.showdetail=false;onSelect()"
                             :source="editor.source"
                             :czlx="editor.czlx"
                             :visible="editor.showdetail">
            </ifp-ysdw-detail>
        </el-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>