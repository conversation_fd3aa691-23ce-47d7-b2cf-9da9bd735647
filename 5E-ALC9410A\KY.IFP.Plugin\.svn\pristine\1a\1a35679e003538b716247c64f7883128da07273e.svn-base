define(["jquery"],function($){
	return {
		/*
		 * 生成tooltip内从
		 * @param { Array<Objeck> } data
		 * 
		 */
		tooltip:function(){
			var rev = [];
			for(var i=1;i<arguments.length;i++){
				if(typeof arguments[i] == "string"){
					rev.push(arguments[i]);
				}else if(typeof arguments[i] == "object"){
					for(var a in arguments[i]){
						rev.push(arguments[i][a]+"："+data[a]);
					}
				}
			}
			if(rev[0]&&rev[0].indexOf("：")==-1){
				rev[0] = "<span style='font-weight: bold;'>"+ rev[0] +"</span>"
			}
			return rev.join("<br />")
		}
	}
})
