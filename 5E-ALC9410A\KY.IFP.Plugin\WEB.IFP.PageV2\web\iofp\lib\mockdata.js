define([
    "jquery",
    "lib/bower/mockjs/mock-min",
    "mockjax"
],function($,Mock){
    function MockDataSet(exp,usecache) {
        this.cache = [];
        this.exp = exp || {
            "gid":"@guid",
            "name":"@cname()",
            "email":'@email'
        }
        this.usecache = usecache !== false;
    }
    MockDataSet.prototype.genDataItem = function () {
        if(typeof this.exp == "function"){
            return this.exp();
        }else{
            return Mock.mock(this.exp)
        }
    }
    MockDataSet.prototype.query = function (start,length) {
        var rev = [];
        for(var i = start;i<start+length;i++){
            if(!this.usecache){
                rev.push(this.genDataItem());
            }else{
                this.cache[i] = this.cache[i] || this.genDataItem()
                rev.push(this.cache[i]);
            }
        }
        return rev;
    }
    function createJQGridUrlMock(url,exp,option) {
        var records = option && option.records || Mock.mock('@integer(5,50)');
        var mds = new MockDataSet(exp);
        $.mockjax({
            url: url,
            response:function(req){
                var rows = 10,page=1;
                if(req.dataType=="json"){
                    var data = JSON.parse(req.data);
                    page = data.page;
                    rows = data.rows;
                }
                var total = Math.ceil(records/rows);
                var pagerows = total==page?records % rows:rows;

                this.responseText.page = page;
                this.responseText.total = total;
                this.responseText.rows = mds.query((page-1)*rows,pagerows)
            },
            responseText:{
                records:records,page:1,total:0,footer:null,userdata:null
            }
        });
    }
    
    var exps = {
        isdebug : /[?|&]{1,1}debug=1/
    }

    return {
        url:function(option){
            $.mockjax(option)
        },
        jqGridUrl:createJQGridUrlMock,
        guid:function() { return Mock.mock('@guid()');},
        debug:function (fn) {
            if(exps.isdebug.test(window.location.search)){
                fn()
            }
        }
    }
})