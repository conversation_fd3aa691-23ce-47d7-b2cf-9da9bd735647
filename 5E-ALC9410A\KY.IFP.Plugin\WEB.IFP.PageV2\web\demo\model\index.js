﻿; define([
    "ELEMENT",
    "util",
    "iofp/api"
], function (ELEMENT, util, API) {
        return {
            el: '#app',
            data: {
                //煤种树
                treeData: [{     
                    Gid: '-999',
                    Bname: '煤种',
                    Sname: '煤种',
                    Color: null,
                    Zfbz: 0,
                    Beizhu: null,
                    children: []
                }],
                //煤种树属性对应关系
                treeProps: {
                    children: 'children',
                    label: 'Bname'
                },
                //煤种区间信息
                MaterialQJ:[],
                //启用停用状态
                SearchState: '0',
                //定位煤种名称
                sreachMaterialName: '',
                currentIndex: -1,
                //当前选择煤种信息
                currentMaterial: {
                    Gid: '-999',
                    pGid: '',
                    ywlx:'',
                    Bname: '煤种',
                    Sname: '煤种',
                    Color: null,
                    Zfbz: 0,
                    Beizhu: null,
                },

                disable: {
                    saveDisabled: true,
                    updateDisabled:true,
                },

                ywlx1003: [],
                ywlx1004: [],
                ywlx1007: [],
            },
            methods: {
                //加载煤种树信息
                LoadTree: function (val) {
                    var _this = this;
                    API.GetAction("API/ICS/BaseData/Material/Select", { zfbz: val} ).then(x => {
                        _this.treeData = [{
                            Gid: '-999',
                            Bname: '煤种',
                            Sname: '煤种',
                            Color: null,
                            Zfbz: 0,
                            Beizhu: null,
                            children: util.getTreeData(x, { id: "Gid", pid: "Pgid" })
                        }];
                    });
                },
                //加载运算关系
                loadYwlx1003: function () {
                    var _this = this;
                    API.GetAction("/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect", { ywlx: 1003, used: 1 }).then(x => {
                        _this.ywlx1003 = x;
                    });

                    API.GetAction("/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect", { ywlx: 1004, used: 1 }).then(x => {
                        _this.ywlx1004 = x;
                    });

                    API.GetAction("/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect", { ywlx: 1007, used: 1 }).then(x => {
                        _this.ywlx1007 = x;
                    });
                },
                //新增煤种
                onCreate: function () {
                    var _this = this;
                    var pgid = _this.currentMaterial.Gid;
                    _this.currentMaterial = {
                        pGid: pgid,
                        Gid: util.uuid(),
                        Bname: null,
                        Sname: null,
                        Color: null,
                        ywlx: '4010',
                        Zfbz: 0,
                        Beizhu: null,
                    };
                    _this.MaterialQJ = [];
                    _this.disable.saveDisabled = false;
                    _this.disable.updateDisabled = true;
                },
                //保存煤种
                onSave: function () {
                    var _this = this;
                    API.GetAction("API/ICS/BaseData/Material/Submit", { ywdx: _this.currentMaterial, qj: _this.MaterialQJ }).then(() => {
                        _this.LoadTree(_this.SearchState);
                        _this.disable.updateDisabled = true;
                    });
                },
                //修改煤种
                onUpdate: function () {
                    var _this = this;
                    _this.disable.saveDisabled = false;
                    _this.disable.updateDisabled = true;
                },
                //退出
                onQuit: function () { },
                //煤种选择
                OnMaterialClick: function (data) {
                    var _this = this;
                    if (data.Gid > 0) {
                        _this.currentMaterial = data;
                        _this.disable.saveDisabled = true;
                        _this.disable.updateDisabled = false;
                        this.loadMz4010qj();
                    }
                },
                //获取煤种区间信息
                loadMz4010qj: function () {
                    var _this = this;
                    if (_this.currentMaterial.Gid != null) {
                        API.GetAction("API/ICS/BaseData/Material/SelectQj", { mz4010: _this.currentMaterial.Gid }).then(x => {
                            _this.MaterialQJ = x;
                        });
                    }
                },
                //定位
                OnGetLocation: function () {
                    var _this = this;
                    _this.$refs.tree.filter(_this.sreachMaterialName);
                },
                //新增行
                OnAddRow: function () {
                    var _this = this;
                    _this.MaterialQJ.push({
                        Gid: util.uuid(),
                        Mz4010: _this.currentMaterial.Gid,
                    });
                },
                //删除行
                onDeleteRow: function () {
                    var _this = this;
                    _this.MaterialQJ.splice(_this.currentIndex, 1);
                },
                //把每一行的索引放进row
                tableRowClassName({ row, rowIndex }) {
                           row.index = rowIndex;
                },
                //行选择事件
                tableSelect: function (row, event, column) {
                    var _this = this;
                    _this.currentIndex = row.index;
                },

                //过滤节点
                filterNode(value, data) {
                    //if (!value) {
                    //    return true
                    //}
                    //let level = node.level
                    //let _array = [] //这里使用数组存储 只是为了存储值。
                    //this.getReturnNode(node, _array, value)
                    //let result = false
                    //_array.forEach(item => {
                    //    result = result || item
                    //})
                    //return result
                    if (!value) return true;
                    return data.Bname.indexOf(value) !== -1;
                },
                getReturnNode(node, _array, value) {
                    let isPass = node.data && node.data.Bname && node.data.Bname.indexOf(value) !== -1;
                    isPass ? _array.push(isPass) : '';
                    this.index++;
                    if (!isPass && node.level != 1 && node.parent) {
                        this.getReturnNode(node.parent, _array, value);
                    }
                }
            },
            watch: {
                sreachMaterialName: function (val) {
                    var _this = this;
                    _this.$refs.tree.filter(_this.sreachMaterialName);
                },
                SearchState: function (val) {
                    var _this = this;
                    this.LoadTree(_this.SearchState);
                },
            },
            created: function () {
                var _this = this;
                _this.LoadTree(_this.SearchState);
                _this.loadYwlx1003();
            }
        }
	});