<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        html,body{margin:0;height: 100%; width:100%;overflow: hidden;}
        svg.anim rect{transition:all 0.5s ease-in;}
        svg.anim g{transition:all 0.5s ease-in;}
        svg{transition:all 0.5s;}
    </style>
</head>
<body>
    <div style="position: fixed;padding:8px;border:1px solid #ccc;background-color:#fff;">
        <button id="view1">view1</button>
        <button id="view2">view2</button>
        <button id="stop">stop</button>
        <button id="play">play</button>
    </div>
    <svg class="anim" id="svg" style="width:100%;height:100%;" viewBox="0,0,700,700"></svg>
    <script src="https://cdn.bootcdn.net/ajax/libs/d3/6.0.0-rc.3/d3.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/Mock.js/1.0.0/mock-min.js"></script>
    <script>
        var el_svg = document.getElementById("svg");
        var bindClick=function(id,fn){
            return document.getElementById(id).addEventListener("click",fn)
        }
        var intervalid = 0;
        bindClick("view1",function(){
            el_svg.setAttribute("viewBox","0 0 300 300")
        })
        bindClick("view2",function(){
            el_svg.setAttribute("viewBox","0 0 300 300")
        })
        bindClick("stop",function(){
            if(!intervalid){return;} 
            window.clearInterval(intervalid);
            intervalid = 0;
            setTimeout(function(){
                el_svg.classList.remove("anim")
            },1000)
        })
        bindClick("play",function(){
            if(intervalid){return;}
            el_svg.classList.add("anim");
            reload();
            intervalid = setInterval(reload,1000);
        })
        function gendata(){
            return Mock.mock({
                "array|20-20": [
                    {
                        "index|+1":0,
                        "x|0-600":600 ,
                        "y|0-600":600 ,
                        "w|10-100":600 ,
                        "h|10-100":600 ,
                        "f":"@color" ,
                        "s":"@color" ,
                    }
                ]
            }).array
        }

        

        function rerender(g){
            return g
            //.attr("x",d=>d.x)
            //.attr("y",d=>d.y)
            .attr("width",d=>{
                console.log(d.w);
                return d.w
            })
            .attr("height",d=>d.h)
            .attr("fill",d=>d.f)
            .attr("stroke",d=>d.s)
            //.transition().style("color","green").duration(200);
        }

        var drag = d3.drag()
        .on('start', () => {})
        .on('end', () => {})
        .on('drag', function(event,d){
            d3.select(this)
            .attr('transform', d=>"translate(" + (d.x=event.x)+ "," +(d.y=event.y) + ")")
        })

        var svg = d3.select("svg")


        function renderPosition(g){
            g.attr("transform",d=>"translate(" + d.x+ "," +d.y + ")");

            g.selectAll("rect").call(rerender)
            g.selectAll("text").call(rerender)
            return g;
        }

        function reload(){
            svg
            .selectAll("g.dataitem")
            .data(gendata())
            .call(renderPosition)
            .enter()
            .append("g")
            .classed("dataitem",true)
            .call(renderPosition)
            .call(drag)
            
            .call(function(g){
                //.attr("opacity",".5")
                g.append("rect")
                .call(rerender)
                g.append("text")
                .text(d=>d.index)
                .call(rerender)
            })
        }
        reload();
        intervalid = setInterval(reload,1000);
    </script>
</body>
</html>