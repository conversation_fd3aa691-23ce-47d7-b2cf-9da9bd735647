;define([
"controllers/base",
"jclass"],
function(base,jclass){
	return jclass(base,{
		name:"tabs",
		
		bindEvent:function(){
			var _this = this;
			
			var i = 1;
			this.bind("addBtn","click",function(){
				var idx = i++;
				_this.controls.tabsPages1.add({
					title:"test"+idx,
					content:"test"+idx
				});
			});
			this.bind("addGroupBtn","click",function(){
				var idx = i++;
				_this.controls.tabsPages1.add({
					title:"test"+idx,
					content:"test"+idx,
					group:"test合并"
				});
			});
			this.bind("addAndActiveBtn","click",function(){
				var idx = i++;
				_this.controls.tabsPages1.add({
					title:"test"+idx,
					content:"test"+idx,
					active:true
				});
			});
			this.bind("getSelectBtn","click",function(){
				var tab = _this.controls.tabsPages1.select();
				$.bootoast(tab.title);
			});
			this.bind("selectIndexBtn","click",function(){
				_this.controls.tabsPages1.selectIndex(2);
			});

			this.bind("tabsPages1","show",function(type,id,control){
				_this.log.info("显示页签开始"+id);
			})
			this.bind("tabsPages1","shown",function(type,id,control){
				_this.log.info("显示页签完成"+id);
			})
			this.bind("tabsPages1","hide",function(type,id,control){
				_this.log.info("关闭页签开始"+id);
			})
			this.bind("tabsPages1","hidden",function(type,id,control){
				_this.log.info("关闭页签完成"+id);
			})
			this.bind("hideTabBtn","click",function(type,id,control){
				_this.controls.tabsPages1.hideTab(1);
			})
			this.bind("hideTabByidBtn","click",function(type,id,control){
				_this.controls.tabsPages1.hideTab("t3");
			})
			this.bind("showTabBtn","click",function(type,id,control){
				_this.controls.tabsPages1.showTab(1);
			})
		},
		onLoad:function(){
			this.bindEvent();
		}
	})
});