﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace COM.IFP.LinqDB
{
    /// <summary>
    /// 查询条件
    /// </summary>
    public static class IWhere
    {
        public static IWhere<T> Create<T, P>(Expression<Func<T, P>> parser, IList<P> entity)
        {
            return IWhere<T>.Create(parser, entity);
        }
    }

    /// <summary>
    /// 查询条件
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class IWhere<T>
    {
        public Type Target { get; init; }
        public LambdaExpression Parser { get; init; }
        public IEnumerable Entity { get; init; }

        private IWhere() { }

        public static IWhere<T> Create<P>(Expression<Func<T, P>> parser, IList<P> entity)
        {
            return new IWhere<T>()
            {
                Target = typeof(P),
                Parser = parser,
                Entity = entity
            };
        }
    }
}
