﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace COM.IFP.Common.Secure
{
    public partial class Secure
    {
        /// <summary>
        /// AES加解密
        /// </summary>
        public class AES
        {
            private const string vector = "<EMAIL>";   //必须16位非中文字符

            /// <summary>
            /// 默认密钥字符串
            /// </summary>
            public static string Secret { get; set; } = "123456";

            /// <summary>
            /// 加密字符串，验证地址 http://tool.chacuo.net/cryptaes (CBC/pkcs7padding/128/默认密码/<EMAIL>/base64/utf8)，参考资料 https://github.com/brix/crypto-js/blob/master/docs/QuickStartGuide.wiki#AES
            /// </summary>
            /// <param name="source">待加密字符串</param>
            /// <returns>加密后字符串</returns>
            public static string Encrypt(string source)
            {
                return Encrypt(source, Secret);
            }

            /// <summary>
            /// 加密字符串，验证地址 http://tool.chacuo.net/cryptaes (CBC/pkcs7padding/128/密码参数/<EMAIL>/base64/utf8)，参考资料 https://github.com/brix/crypto-js/blob/master/docs/QuickStartGuide.wiki#AES
            /// </summary>
            /// <param name="source">待加密字符串</param>
            /// <param name="secret">密钥字符串</param>
            /// <returns>加密后字符串</returns>
            public static string Encrypt(string source, string secret)
            {
                string result = source;
                try
                {
                    using Aes aes = Aes.Create("AES");
                    var key = new byte[16];
                    var tmp = Encoding.UTF8.GetBytes(secret);
                    Array.Copy(tmp, key, Math.Min(tmp.Length, 16));
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    aes.KeySize = 128;
                    aes.Key = key;
                    aes.IV = Encoding.UTF8.GetBytes(vector);
                    ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);
                    using MemoryStream msEncrypt = new();
                    using CryptoStream csEncrypt = new(msEncrypt, encryptor, CryptoStreamMode.Write);
                    using (StreamWriter swEncrypt = new(csEncrypt))
                    {
                        swEncrypt.Write(source);
                    }
                    result = global::System.Convert.ToBase64String(msEncrypt.ToArray());
                }
                catch
                {
                    //可能会加密失败，加密失败直接返回明文还是""？
                }
                return result;
            }

            /// <summary>
            /// 解密字符串，验证地址 http://tool.chacuo.net/cryptaes (CBC/pkcs7padding/128/默认密码/<EMAIL>/base64/utf8)，参考资料 https://github.com/brix/crypto-js/blob/master/docs/QuickStartGuide.wiki#AES
            /// </summary>
            /// <param name="source">待解密字符串</param>
            /// <returns>解密后字符串</returns>
            public static string Decrypt(string source)
            {
                return Decrypt(source, Secret);
            }

            /// <summary>
            /// 解密字符串，验证地址 http://tool.chacuo.net/cryptaes (CBC/pkcs7padding/128/密码参数/<EMAIL>/base64/utf8)，参考资料 https://github.com/brix/crypto-js/blob/master/docs/QuickStartGuide.wiki#AES
            /// </summary>
            /// <param name="source">待解密字符串</param>
            /// <param name="secret">密钥字符串</param>
            /// <returns>解密后字符串</returns>
            public static string Decrypt(string source, string secret)
            {
                string result = source;
                try
                {
                    using Aes aes = Aes.Create("AES");
                    var key = new byte[16];
                    var tmp = Encoding.UTF8.GetBytes(secret);
                    Array.Copy(tmp, key, Math.Min(tmp.Length, 16));
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    aes.KeySize = 128;
                    aes.Key = key;
                    aes.IV = Encoding.UTF8.GetBytes(vector);
                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);
                    using MemoryStream msDecrypt = new(System.Convert.FromBase64String(source));
                    using CryptoStream csDecrypt = new(msDecrypt, decryptor, CryptoStreamMode.Read);
                    using StreamReader srDecrypt = new(csDecrypt);
                    result = srDecrypt.ReadToEnd();
                }
                catch
                {
                    //可能会解密失败，解密失败直接返回密文还是""？
                }
                return result;
            }
        }
    }
}
