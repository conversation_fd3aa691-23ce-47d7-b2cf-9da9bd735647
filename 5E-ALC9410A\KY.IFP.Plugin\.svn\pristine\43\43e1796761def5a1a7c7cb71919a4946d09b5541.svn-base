﻿using COM.IFP.Common;
using ORM.IFP;
using COM.IFP.SqlSugarN;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using COM.IFP.Dictionary;
using ORM.IFP.DbModel;

namespace DAL.IFP.www.BaseInfoDictionary
{
    public class BaseInfoDictionaryDAL
    {
        #region 字典类型操作
        /// <summary>
        /// 获取所有字典类型
        /// </summary>
        /// <returns></returns>
        public PageModel<IFP_BS_BASEINFO_TYPE_DICTIONARY> GetAllTypes(IList<IFP_BS_BASEINFO_TYPE_DICTIONARY> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                var result = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Where(filter.ToArray()).Fetch(paging);
                return result;
            }
        }

        /// <summary>
        /// 根据分类获取字典类型
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        public PageModel<IFP_BS_BASEINFO_TYPE_DICTIONARY> GetTypesByCategory(IList<IFP_BS_BASEINFO_TYPE_DICTIONARY> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                var result = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Where(filter.ToArray()).Fetch(paging);
                var Sql = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Where(filter.ToArray()).ToSql();
                return result;
            }
        }
        /// <summary>
        /// 获取指定字典类型
        /// </summary>
        /// <param name="json">包含ywlx参数，如{"ywlx":"1001"}</param>
        /// <returns>字典类型</returns>
        public PageModel<IFP_BS_BASEINFO_TYPE_DICTIONARY> GetTypeByCode(IList<IFP_BS_BASEINFO_TYPE_DICTIONARY> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                var result = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Where(filter.ToArray()).Fetch(paging);
                var Sql = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Where(filter.ToArray()).ToSql();
                return result;
            }
        }

        /// <summary>
        /// 只用一个查询接口
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PageModel<IFP_BS_BASEINFO_TYPE_DICTIONARY> SelectType(IList<IFP_BS_BASEINFO_TYPE_DICTIONARY> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                var result = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Where(filter.ToArray()).Fetch(paging);
                var Sql = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Where(filter.ToArray()).ToSql();
                return result;
            }
        }

        /// <summary>
        /// 保存字典类型（新增或更新）
        /// </summary>
        /// <param name="type">字典类型数据</param>
        /// <returns>操作结果</returns>
        public PFActionResult SaveType(IFP_BS_BASEINFO_TYPE_DICTIONARY type)
        {
            var result = new PFActionResult();
            using (var db = DB.Create())
            {
                try
                {
                    //判断是新增还是更新
                    bool exsits = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Any(t => t.Ywlx == type.Ywlx);

                    if (exsits)
                    {
                        // 更新操作 - 保留原有的创建信息和其他不应被覆盖的字段
                        var existingType = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().First(t => t.Ywlx == type.Ywlx);
                        if (existingType != null)
                        {
                            type.CreateDate = existingType.CreateDate;
                            type.CreateSource = existingType.CreateSource;
                            type.CreateUser = existingType.CreateUser;
                        }

                        int rows = db.Updateable(type).ExecuteCommand();
                        if (rows > 0)
                        {
                            result.success = true;
                            result.data = type;
                        }
                        else
                        {
                            result.success = false;
                            result.msg = "更新字典类型失败";
                        }
                    }
                    else
                    {
                        // 新增操作 - 设置创建信息
                        type.CreateDate = DateTime.Now;
                        // 如果没有设置创建人和来源，可以设置默认值
                        if (type.CreateUser.HasValue || string.IsNullOrEmpty(type.CreateUser.Value))
                        {
                            type.CreateUser = "系统"; // 或从当前用户上下文获取
                        }
                        if (type.CreateSource.HasValue || string.IsNullOrEmpty(type.CreateSource.Value))
                        {
                            type.CreateSource = "系统"; // 或设置默认来源
                        }

                        int rows = db.Insertable(type).ExecuteCommand();
                        if (rows > 0)
                        {
                            result.success = true;
                            result.data = type;
                        }
                        else
                        {
                            result.success = false;
                            result.msg = "创建字典类型失败";
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.success = false;
                    result.msg = ex.Message;
                    result.data = ex.StackTrace;
                }

                return result;
            }
        }

        /// <summary>
        /// 删除字典类型
        /// </summary>
        /// <param name="ywlx">业务类型编码</param>
        /// <returns>操作结果</returns>
        public PFActionResult DeleteType(string ywlx)
        {
            var result = new PFActionResult();
            using (var db = DB.Create())
            {
                try
                {
                    // 验证编码是否存在
                    var existingType = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().First(t => t.Ywlx == ywlx);
                    if (existingType == null)
                    {
                        result.success = false;
                        result.msg = $"编码为 '{ywlx}' 的字典类型不存在";
                        return result;
                    }

                    // 移除IsEditable检查，改由前端权限控制

                    // 开启事务确保类型和相关项都被删除
                    db.Ado.BeginTran();
                    try
                    {
                        // 先删除相关的字典项
                        db.Deleteable<IFP_BS_BASEINFO_DICTIONARY>().Where(i => i.Ywlx == ywlx).ExecuteCommand();

                        // 再删除字典类型
                        int rows = db.Deleteable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Where(t => t.Ywlx == ywlx).ExecuteCommand();

                        db.Ado.CommitTran();

                        if (rows > 0)
                        {
                            result.success = true;
                        }
                        else
                        {
                            result.success = false;
                            result.msg = "删除字典类型失败";
                        }
                    }
                    catch (Exception)
                    {
                        db.Ado.RollbackTran();
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    result.success = false;
                    result.msg = ex.Message;
                    result.data = ex.StackTrace;
                }
                return result;
            }
        }

        #endregion

        #region 字典项操作
        /// <summary>
        /// 基础资料字典项查询，只用一个接口
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="paging"></param>
        /// <returns></returns>
        public PageModel<IFP_BS_BASEINFO_DICTIONARY> SelectItem(IList<IFP_BS_BASEINFO_DICTIONARY> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                var wheres = filter.ToArray();
                var result = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>().Where(wheres).Order(wheres).Fetch(paging);
                var Sql = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>().Where(filter.ToArray()).Order(wheres).ToSql();
                return result;
            }
        }

        /// <summary>
        /// 保存字典项（新增或更新）
        /// </summary>
        /// <param name="item">字典项数据</param>
        /// <returns>操作结果</returns>
        public PFActionResult SaveItem(IFP_BS_BASEINFO_DICTIONARY item)
        {
            var result = new PFActionResult();
            using (var db = DB.Create())
            {
                try
                {
                    bool isNew = item.Gid == null || item.Gid.Value <= 0;

                    if (isNew)
                    {
                        // 新增操作逻辑
                        // 设置必要的字段
                        item.Addtime = DateTime.Now;
                        item.Lasttime = DateTime.Now;

                        // 验证类型是否存在
                        bool typeExists = db.Queryable<IFP_BS_BASEINFO_TYPE_DICTIONARY>().Any(t => t.Ywlx == item.Ywlx);
                        if (!typeExists)
                        {
                            result.success = false;
                            result.msg = $"编码为 '{item.Ywlx.Value}' 的字典类型不存在";
                            return result;
                        }

                        // 如果没有设置Gid，生成一个新的ID
                        if (item.Gid == null || item.Gid.Value <= 0)
                        {
                            string ywlxPrefix = item.Ywlx.Value;

                            // 获取最大ID
                            long? maxGidNullable = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>()
                                .Where(i => i.Ywlx == ywlxPrefix)
                                .Max(i => (long?)i.Gid);

                            long lastId = maxGidNullable ?? 0;

                            // 如果没有现有记录，创建第一个
                            if (lastId == 0)
                            {
                                item.Gid = long.Parse($"{ywlxPrefix}0001");
                            }
                            else
                            {
                                // 否则递增最后一个ID
                                item.Gid = lastId + 1;
                            }
                        }

                        // 如果是树结构，设置TreeCode
                        if (item.Pgid.HasValue && item.Pgid.Value > 0)
                        {
                            var parent = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>().First(i => i.Gid == item.Pgid);
                            if (parent != null)
                            {
                                // TreeCode格式：父TreeCode + 当前ID，方便排序和查询
                                item.TreeCode = string.IsNullOrEmpty(parent.TreeCode.Value)
                                    ? parent.Gid.Value.ToString()
                                    : $"{parent.TreeCode.Value}.{parent.Gid.Value}";
                            }
                        }

                        // 插入记录
                        int rows = db.Insertable(item).ExecuteCommand();
                        if (rows > 0)
                        {
                            result.success = true;
                            result.data = item;
                        }
                        else
                        {
                            result.success = false;
                            result.msg = "创建字典项失败";
                        }
                    }
                    else
                    {
                        // 更新操作逻辑

                        // 验证ID是否存在
                        var oldItem = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>().First(i => i.Gid == item.Gid);

                        if (oldItem == null)
                        {
                            result.success = false;
                            result.msg = $"ID为 {item.Gid} 的字典项不存在";
                            return result;
                        }

                        // 更新修改时间，保留原有的添加时间和其他不应被覆盖的字段
                        item.Addtime = oldItem.Addtime;
                        item.Lasttime = DateTime.Now;

                        // 如果是树结构且修改了父节点，需要更新TreeCode
                        if (item.Pgid.HasValue && oldItem.Pgid.Value != item.Pgid.Value)
                        {
                            // 验证新父节点是否存在
                            var parent = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>().First(i => i.Gid == item.Pgid);
                            if (parent == null)
                            {
                                result.success = false;
                                result.msg = $"ID为 {item.Pgid} 的父节点不存在";
                                return result;
                            }

                            // 验证父节点不能是自身或其子节点
                            DictionaryHelper dictionaryHelper = new DictionaryHelper();
                            if (dictionaryHelper.IsDescendant(parent, item.Gid.Value))
                            {
                                result.success = false;
                                result.msg = "无法将当前节点的子节点设为父节点，这会导致循环引用";
                                return result;
                            }

                            // 更新TreeCode
                            item.TreeCode = string.IsNullOrEmpty(parent.TreeCode.Value)
                                ? parent.Gid.Value.ToString()
                                : $"{parent.TreeCode.Value}.{parent.Gid.Value}";

                            // 递归更新所有子节点的TreeCode
                            dictionaryHelper.UpdateChildrenTreeCode(item.Gid.Value, oldItem.TreeCode.Value, item.TreeCode.Value);
                        }

                        // 更新记录
                        int rows = db.Updateable(item).ExecuteCommand();
                        if (rows > 0)
                        {
                            result.success = true;
                            result.data = item;
                        }
                        else
                        {
                            result.success = false;
                            result.msg = "更新字典项失败";
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.success = false;
                    result.msg = ex.Message;
                    result.data = ex.StackTrace;
                }

                return result;
            }
        }

        /// <summary>
        /// 删除字典项
        /// </summary>
        /// <param name="gid"></param>
        /// <returns>操作结果</returns>
        public PFActionResult DeleteItem(long gid)
        {
            var result = new PFActionResult();
            using (var db = DB.Create())
            {
                try
                {
                    //验证ID是否存在
                    var item = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>()
                        .First(i => i.Gid == gid);
                    if (item == null)
                    {
                        result.success = false;
                        result.msg = $"ID为{gid}的字典项不存在";
                        return result;
                    }

                    // 移除IsEditable检查，改由前端权限控制

                    //检查是否有子项
                    bool hasChildren = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>()
                        .Any(i => i.Pgid == gid);
                    if (hasChildren)
                    {
                        result.success = false;
                        result.msg = "无法删除有子项的字典项，请先删除所有子项";
                        return result;
                    }

                    //删除记录
                    int rows = db.Deleteable<IFP_BS_BASEINFO_DICTIONARY>()
                        .Where(i => i.Gid == gid)
                        .ExecuteCommand();
                    if (rows > 0)
                    {
                        result.success = true;
                    }
                    else
                    {
                        result.success = false;
                        result.msg = "删除字典失败";
                    }
                }
                catch (Exception ex)
                {
                    result.success = false;
                    result.msg = ex.Message;
                    result.data = ex.StackTrace;
                }
                return result;
            }
        }
        #endregion
    }
}