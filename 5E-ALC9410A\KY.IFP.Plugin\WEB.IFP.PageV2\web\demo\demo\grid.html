
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>dicb,grid</title>
</head>
<body controller="grid.js" style="position: static;">
    <div form="forms/toolbar" class="layout-h">
        <a id="returnBtn" control="controls/button" option="{command:'close'}">退出</a>
        <a id="selBtn" control="controls/button" option="{}">查看输入内容</a>
    </div>
    <div class="searchbar layout-h">
        <table>
            <tr>
                <td>所选行：</td>
                <td><input id="selName" control="controls/textbox" 
                option="{multiline:false,placeholder:'33333'}" /></td>
                <td class="pg-l-20">下拉-使用Service：</td>
                <td><input id="selName2" control="controls/select2" class="form-control" option="{
                    service:'iofp/services/service.demo',methodname:'getNames'
                }" /></td>
                <td  class="pg-l-20">日期：</td>
                <td><input id="rq" control="controls/datepicker" /></td>
            </tr>
            <tr>
                <td>下拉-控制器中绑定：</td>
                <td><input id="selName3" control="controls/select2" class="form-control" /></td>
                <td class="pg-l-20">下拉-手动绑定：</td>
                <td><input id="selName4" control="controls/select2" class="form-control" option="{
                    data:[{id:1,text:'22'}]
                }" /></td>
                <td  class="pg-l-20">日期：</td>
                <td><input id="rq" control="controls/datepicker" /></td>
            </tr>
            <tr>
                <td>数字：</td>
                <td><input control="controls/number" /></td>
                <td class="pg-l-20">数字：</td>
                <td><input control="controls/number" /></td>
                <td class="pg-l-20">名称：</td>
                <td><input control="controls/textbox" /></td>
            </tr>
            <tr>
                <td>选择：</td>
                <td colspan="5"><input control="controls/selector" option="{type:'mc',width:'100%'}" /></td>
                <td class="seartchbar-buttons">
                    <a id="searchBtn" control="controls/button" option="{}">查询</a>
                    <a id="resetBtn" control="controls/button" option="{}">重置</a>
                </td>
            </tr>
        </table>
    </div>
    <div class="layout-h padding" style="padding-bottom:0;">
        <div class="form-header">
            <a id="t_addBtn" control="controls/button" option="{}">新增</a>
            <a id="t_delBtn" control="controls/button" option="{}">删除</a>
            <a id="t_delCheckBtn" control="controls/button" option="{}">批量删除</a>
                <!-- 
            <a id="t_disableBtn" control="controls/button" option="{}">禁用</a>
            <a id="t_enbleBtn" control="controls/button" option="{}">启用</a>
                    -->
        </div>
    </div>
    <!--div class="layout-h padding" style="padding-top: 0;">
        <table id="grid2" control="controls/grid"
                option='{"autoresize":true,cellEdit:true
            }'>
            <thead>
                <tr>
                    <th>gid</th>
                    <th>煤质分类</th>
                    <th>占比</th>
                    <th>生产需求煤量</th>
                    <th>储备煤量</th>
                    
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td option="{name:'gid',editable:false, hidden:true,key:true}"></td>					
                    <td
                        option="{name:'mzfl4011',width : 150,align : 'left',editable:true,control:'controls/select2'}"></td>
                    <td
                        option="{name:'zhanbi',width : 150,align : 'left',editable:true,control:'controls/number',formater:'percent'}"></td>
                    <td
                        option="{name:'scxqml',width : 150,align : 'right',editable:false,control:'controls/number'}"></td>
                    <td
                        option="{name:'cbml',width : 150,align : 'right',editable:false,control:'controls/number'}"></td>
                                
                </tr>
            </tbody>
        </table>
    </div-->
    <div class="layout-c padding" style="padding-top:0;">
        <table id="grid33" control="controls/grid" option='{
            url:"data/base.json",
            cellEdit:true,
            frozen:true,
            postData : {},
            pager:"#pager1"
        }'>
            <thead>
                <tr>
                    <th>操作</th>
                    <th>关系</th>
                    <th>区间</th>
                    <th>运算符</th>
                    <th>指标</th>
                    <th>运算符</th>
                    <th>区间2</th>
                    <th>颜色</th>
                    <th>日期</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td option="{name:'gid',hidden:true,key: true,frozen: true}"></td>
                    <td option="{name:'jobName',sortable:true,editable:false,frozen: true,align:'left'}"></td>
                    <td option="{name:'name',frozen: true,editable:false,align:'left'}"></td>
                    <td option="{name:'email',frozen: true,editable:false}"></td>
                    <td option="{name:'col3',editable:false}"></td>
                    <td option="{name:'col4',editable:true,control:'controls/select2',controloption:{
                        service:'iofp/services/service.demo',methodname:'getNames'
                    }}"></td>
                    <td option="{name:'col5',editable:false}"></td>
                    <td option="{name:'col6',editable:false}"></td>
                    <td option="{name:'col7',editable:false}"></td>
                </tr>
            </tbody>
        </table>
        <div id="pager1"></div>
    </div>
    <script src="/iofp/starter.js"></script> 
</body>
</html>