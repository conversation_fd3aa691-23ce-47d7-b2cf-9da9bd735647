define(['require', 'controllers/base', 'jquery', 'jclass', 'iofp/common', 'iofp/api', 'util'], function (require, base, $, jclass, iofp, API, util) {
    return {
        el: '#app',
        props: {
            serviceCode: { type: [Number, String], require: true, default: '' },
        },
        data() {
            return {
                loading: false,
                routeTestInfo: {
                    code: this.serviceCode,
                    param: '',
                    result: '',
                }, // 测试接口信息
            };
        },
        mounted() {
            // console.log(this.serviceCode);
            this.$set(this.routeTestInfo, { code: this.serviceCode, param: '', result: '' });
        },
        methods: {
            // 测试接口
            testBtn() {
                console.log(this.serviceCode);
                let _this = this;
                _this.loading = true;
                API.GetAction('API/IFP/Client/Request/TestRoute', {
                    code: _this.routeTestInfo.code,
                    param: _this.routeTestInfo.param,
                })
                    .then((e) => {
                        _this.loading = false;
                        _this.$set(_this.routeTestInfo, 'result', e);
                    })
                    .catch(function (e) {
                        _this.loading = false;
                        _this.$message.warning(e);
                        // _this.$set(_this.routeTestInfo, 'result', e);
                    });
            },
            // 取消
            onCancel() {
                this.$set(this.routeTestInfo, { code: '', param: '', result: '' });
                this.$nextTick(() => {
                    this.$emit('cancel');
                });
            },
        },
    };
    // return jclass(base, {
    //     name: 'testRoute',

    //     bindEvent: function () {
    //         var _this = this;
    //         //确定
    //         _this.controls['testBtn'].bind('click', function () {
    //             _this.testRoute();
    //         });
    //     },

    //     testRoute: function () {
    //         var _this = this;
    //         if (F.common.isEmpty(_this.controls.code.value())) {
    //             $.bootoast.warning('接口编号不能为空');
    //             return;
    //         }
    //         if (F.common.isEmpty(_this.controls.param.value())) {
    //             $.bootoast.warning('参数不能为空');
    //             return;
    //         }
    //         F.util.showWait();
    //         F.ajax({
    //             url: '/API/IFP/Client/Request/TestRoute',
    //             data: {
    //                 code: _this.controls.code.value(),
    //                 param: _this.controls.param.value(),
    //             },
    //             success: function (resp) {
    //                 F.util.hideWait();
    //                 _this.controls.result.value(JSON.stringify(resp));
    //             },
    //             error: function (err) {
    //                 F.util.hideWait();
    //                 _this.controls.result.value(JSON.stringify(err));
    //             },
    //         });
    //     },

    //     onLoad: function () {
    //         var _this = this;
    //         var parameter = this.getDialogWindow().parameter;
    //         _this.controls.code.value(parameter.code);
    //         this.bindEvent();
    //     },
    // });
});
