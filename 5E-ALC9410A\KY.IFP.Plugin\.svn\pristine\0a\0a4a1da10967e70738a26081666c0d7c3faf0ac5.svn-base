@color-primary: #0086f1;
@color-bg-primary:#3E4D76;
@color-text:#fff;

.menu-wrapper{
    z-index: 1;
    background-color:@color-bg-primary;
    color:#fff;
    display:flex;
    flex-direction: column;
    flex-shrink: 0;
    height: 100%;
    .menu{
        flex-shrink: 0;
        height: 100%;
        .menu-item{
            +menu-item{
                margin-top: 0;
            }
            cursor: pointer;
            padding: 1.3rem 0;
            text-align: center;
            background-color: transparent;
            border: 0;
            &:hover,&.active{
                background-color:@color-primary;
            }
            .menu-item-ico {
                display: block;
                font-size: 2rem;
                padding-bottom:1rem;
            }
            .menu-item-text {
                font-size:1rem;
            }
        }
    }
    .flex{
      flex-grow: 1;
      flex-shrink: 1;
      display: flex;
      align-items: stretch;
    }
    .submenus{
        z-index: 1;
        position: absolute;left:80px;
        min-width:8rem;
        text-align: left;
        .submenu{background-color: #3E4D76;}
        .submenu-item{
            .menu-item-title{
                flex-grow: 1;
            }
            cursor: pointer;
            padding:0.6rem 0.5rem;
            align-items:center;
            display: flex;
            /*justify-content:space-around;*/
            &:hover,&.focus{
                background-color:@color-primary;
            }
        }
    }
}