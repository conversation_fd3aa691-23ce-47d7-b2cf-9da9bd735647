<!DOCTYPE html>
<html lang="zh-Hans">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础表格</title>
</head>

<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">

        <ifp-toolbar close>
            <ifp-button>新增</ifp-button>
            <ifp-button>修改</ifp-button>
        </ifp-toolbar>

        <ifp-searchbar @search="$message('search')" @reset="$message('reset')">

            <ifp-searchbar-item label="名称">
                <ifp-input v-model="search.name2" placeholder="名称"></ifp-input>
            </ifp-searchbar-item>
            
            <ifp-searchbar-item label="名称">
                <ifp-input v-model="search.name2" placeholder="名称"></ifp-input>
            </ifp-searchbar-item>

            <ifp-searchbar-item label="下拉">
                <el-select v-model="search.region" placeholder="下拉">
                    <el-option label="区域一" value="shanghai"></el-option>
                    <el-option label="区域二" value="beijing"></el-option>
                </el-select>
            </ifp-searchbar-item>

            <ifp-searchbar-item label="下拉">
                <el-select v-model="search.region" placeholder="下拉">
                    <el-option label="区域一" value="shanghai"></el-option>
                    <el-option label="区域二" value="beijing"></el-option>
                </el-select>
            </ifp-searchbar-item>
        </ifp-searchbar>

        <ifp-panel-table class="margin" style="height:50%;" :lxsz="true" :print="true">

            <ifp-table height="100%" :lxsz="true">
                <el-table-column label="列1"></el-table-column>
                <el-table-column label="列2"></el-table-column>
                <el-table-column label="列3"></el-table-column>
                <el-table-column label="列4"></el-table-column>
                <el-table-column label="列5"></el-table-column>
                <el-table-column label="列6"></el-table-column>
            </ifp-table>

            <template v-slot:pagination>
                <el-pagination 
                    :current-page="20"
                    :page-sizes="[10,15,20,50,100]"
                    :page-size="20"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="15">
                </el-pagination>
            </template>

        </ifp-panel-table>

        <ifp-panel-table class="flex-item margin">
            <ifp-table height="100%">
                <el-table-column label="列1"></el-table-column>
                <el-table-column label="列2"></el-table-column>
            </ifp-table>
        </ifp-panel-table>

    </ifp-page>
    <script src="/iofp/starter.js"></script>
</body>

</html>