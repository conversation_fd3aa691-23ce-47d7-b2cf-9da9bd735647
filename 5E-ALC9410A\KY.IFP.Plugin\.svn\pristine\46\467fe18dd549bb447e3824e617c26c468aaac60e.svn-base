﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>矿点信息</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <el-button v-if="!$btnRight.B1" type="primary" icon="el-icon-plus" @click="onCreate()" v-show="btnshow.add">新增</el-button>  <!--管控不允许新增 只能信息系统新增-->
            <el-button v-if="!$btnRight.B2" icon="el-icon-view" @click="onView">查看</el-button>
            <el-button v-if="!$btnRight.B3" icon="el-icon-edit" @click="onUpdate">修改</el-button>
            <el-button v-if="!$btnRight.B4" icon="el-icon-delete" @click="onDelete" v-show="btnshow.delete">删除</el-button>
        </ifp-toolbar>

        <ifp-searchbar @search="onSelect" reset-show @reset="reset">
            <ifp-form-item label="矿点名称" v-model="viewer.filter.kdxx[0].Bname.Value" placeholder="请输入内容"></ifp-form-item>
            <ifp-form-item label="供货单位">
                <el-select v-model="viewer.filter.ghdw[0].Gid.Value" filterable clearable placeholder="请选择">
                    <el-option v-for="item in common.ghdw"
                               :key="item.Gid"
                               :label="item.Bname"
                               :value="item.Gid">
                    </el-option>
                </el-select>
            </ifp-form-item>
            <ifp-form-item label="启停状态">
                <el-select v-model="viewer.filter.kdxx[0].Zfbz.Value" clearable placeholder="请选择">
                    <el-option label="启用" :value="0"></el-option>
                    <el-option label="停用" :value="1"></el-option>
                </el-select>
            </ifp-form-item>
        </ifp-searchbar>

        <ifp-panel-table class="flex-item padding">

            <el-table :data="viewer.source"
                      style="width:100%;"
                      row-key="kdxx.Gid"
                      :current-row-key="viewer.select?.kdxx.Gid"
                      height="100%"
                      border
                      highlight-current-row
                      :default-sort="{prop: 'Addtime', order: 'descending'}"
                      @current-change="onChange">
                <el-table-column type="index"
                                 label="序号"
                                 :index="i=>(viewer.paging.page-1)*viewer.paging.size+i+1"
                                 width="50">
                </el-table-column>
                <el-table-column prop="kdxx.Kdbm"
                                 label="矿点编码">
                </el-table-column>
                <el-table-column prop="kdxx.Bname"
                                 label="矿点名称">
                </el-table-column>
                <el-table-column prop="kdxx.Sname"
                                 label="矿点简称">
                </el-table-column>
                <el-table-column prop="ghdw.Bname"
                                 label="供货单位">
                </el-table-column>
                <el-table-column prop="fzxx.Bname"
                                 label="发站">
                </el-table-column>
                <el-table-column prop="kdxx.Zfbz"
                                 :formatter="(r,c,v,i)=>v==0?'启用':'停用'"
                                 label="启停状态">
                </el-table-column>
                <el-table-column prop="kdxx.HCYSL"
                                 label="火车运损率">
                </el-table-column>
                <el-table-column prop="kdxx.CHNHYZQ"
                                 label="CHN化验周期(天)">
                </el-table-column>
                <el-table-column prop="kdxx.Qchfhfbj"
                                 label="汽车衡发货方标记">
                </el-table-column>
                <el-table-column prop="kdxx.KDYGRZ" width="150px"
                                 header-align="center"
                                 align="right"
                                 label="矿点预估热值(Kcal/kg)">
                </el-table-column>
                <!--daiabin<el-table-column prop="kdxx.Bname"
                            label="发站">
                </el-table-column>-->
                <el-table-column prop="kdxx.Addtime" width="150px"
                                 label="创建时间">
                </el-table-column>
                <el-table-column prop="kdxx.Creator"
                                 label="创建人">
                </el-table-column>
                <el-table-column prop="kdxx.REMARK"
                                 label="备注">
                </el-table-column>
            </el-table>


            <template v-slot:pagination>
                <ifp-pagination @size-change="sizeChange"
                                @current-change="pageChange"
                                :current-page="viewer.paging.page"
                                :page-size="viewer.paging.size"
                                :total="viewer.paging.records">
                </ifp-pagination>
            </template>

        </ifp-panel-table>

        <ifp-dialog class="subpage"
                    :visible.sync="editor.dialog"
                    :title="editor.action+'-'+editor.header"
                    width="1280px">
            <ifp-kd-detail @cancel="editor.dialog=false;"
                           @submit="editor.dialog=false;onSelect()"
                           :source="editor.source"
                           :action="editor.action"></ifp-kd-detail>
        </ifp-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>