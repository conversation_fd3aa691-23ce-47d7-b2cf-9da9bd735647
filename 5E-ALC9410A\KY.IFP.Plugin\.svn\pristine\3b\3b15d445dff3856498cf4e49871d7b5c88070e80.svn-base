﻿define(["iofp/api", "platform/vue", "iofp/util"], function (API, pVue, util) {
	return {

		//在node节点中查找指定class的DOM元素，找到后存放到list这个数组中
		findDom: function (node, className, nodeName, list) {
			if (node.childNodes.length = 0) {
				return;
			}
			for (var i = 0; i < node.childNodes.length; i++) {
				var childNode = node.childNodes[i];
				if (childNode.nodeName == nodeName && childNode.className.indexOf(className) > -1) {
					list.push(childNode);
				} else {
					this.findDom(childNode, className, nodeName, list);
				}
			}
		},

		//得到表格所有显示列的prop属性数组
		getColProps: function (tableRef) {
			//找到所有带标题的表格列（排除了序号列，checkbox选择列）
			var vueComponents = tableRef.$children.filter(x => (x.$vnode.tag.endsWith("ElTableColumn") || x.$vnode.tag.endsWith("ifpTableColumn")) && !x.$options.propsData.hasOwnProperty("type"));
			var colProps = [];
			for (var i = 0; i < vueComponents.length; i++) {
				this.getCol(vueComponents[i], colProps);
			}
			return colProps;
		},

		//查找列名，找到后存入cols数组
		getCol: function (vueComponent, colProps) {
			if (vueComponent.$children.length == 0) {
				colProps.push(vueComponent.$options.propsData.prop);
				return;
			}
			var children = vueComponent.$children;
			for (var i = 0; i < children.length; i++) {
				this.getCol(children[i], colProps);
			}
		},

		//获取“表格头部”信息
		getHeads: function (tableRef) {
			//导出时要剔除序号列，checkbox选择列（序号列，checkbox全选列，都会设置属性type；）
			var beginIdx = tableRef.$children.filter(x => (x.$vnode.tag.endsWith("ElTableColumn") || x.$vnode.tag.endsWith("ifpTableColumn")) && x.$options.propsData.hasOwnProperty("type")).length;

			var dom = tableRef.$el;

			var Heads = [];

			//查找表格头部的<table>
			var tables = [];
			this.findDom(dom, "el-table__header", "TABLE", tables);
			var table = tables[0];

			//查找table中的<thead>
			var theads = [];
			this.findDom(table, "", "THEAD", theads);
			var thead = theads[0];

			//查找thead中的<tr>，一个<tr>就是表格头部的一行
			var trs = [];
			this.findDom(thead, "", "TR", trs);

			for (var i = 0; i < trs.length; i++) {
				var headRow = { Cells: [] };
				//循环每行中的列
				var ths = [];
				this.findDom(trs[i], "", "TH", ths);
				for (var j = (i == 0 ? beginIdx : 0); j < ths.length; j++) {
					//Element表格生成的特殊的，不显示的列
					if (ths[j].childNodes.length == 0) {
						continue;
					}
					var Rowspan = ths[j].attributes.rowspan.value;
					var Colspan = ths[j].attributes.colspan.value;
					var Value = ths[j].childNodes[0].childNodes.length > 0 ? ths[j].childNodes[0].childNodes[0].nodeValue : "";
					headRow.Cells.push({ Rowspan: Rowspan, Colspan: Colspan, Value: Value });
				}
				Heads.push(headRow);
			}
			return Heads;
		},

		//根据“列定义”和“表格行数据”信息，得到导出EXCEL时需要的数据结构
		getDatas: function (tableRef, colModels, rows) {
			//所有显示列的prop属性数组
			var colProps = this.getColProps(tableRef);

			var datas = [];
			for (var i = 0; i < rows.length; i++) {
				var data = { Cells: [] };
				for (var j = 0; j < colModels.length; j++) {
					if (colProps.indexOf(colModels[j].Name) >= 0) {
						var value = rows[i][colModels[j].Name];
						if (value == undefined) {
							value = null;
						}
						var col = Object.assign({}, colModels[j], { Value: value });
						data.Cells.push(col);
					}
				}
				datas.push(data);
			}
			return datas;
		},

		//导出接口，生成EXCEL文件
		export: function (workbook) {
			API.GetAction("/API/IFP/BaseInfo/ExcelExport/export", workbook).then(resp => {
				var fileName = resp["fileName"];
				if (fileName == "") {
					return;
				}
				var href = "/fileDownLoad/" + encodeURI(fileName);
				if ($("#excel_export_link_1478965").length == 0) {
					$(document.body).append("<a href=\"" + href + "\" id=\"excel_export_link_1478965\" style=\"display:none\">导出链接</a>");
				}
				$("#excel_export_link_1478965").attr("href", href);
				$("#excel_export_link_1478965")[0].click();
			}).catch(e => {

			});
		}
	}
});