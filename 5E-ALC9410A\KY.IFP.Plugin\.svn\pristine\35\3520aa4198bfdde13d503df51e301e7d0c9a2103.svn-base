<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>menu</title>
</head>
<body>
    <div class="menu-wrapper">
        <div class="flex-item" style="overflow:auto;">
            <ul @mouseout="onsubmenu_hiding" @mouseover="onsubmenu_canclehiding" class="menu nav nav-pills nav-stacked">
                <li class="menu-item" v-for="item in tree" :key="item[option.idfield]" 
                :class="{active:activeid===item[option.idfield]}"
                @mouseover="menuitem_mouseover($event,item)"
                @click="menuitem_click(item)">
                    <span class="menu-item-ico" :class="item[currentOption.icon]||currentOption.defaultIcon" aria-hidden="true"></span>
                    <span class="menu-item-title">{{item[currentOption.title]}}</span>
                </li>
            </ul>
        </div>
        <div v-if="submenu_show && current_sub_menu" class="submenus">
            <submenu :min-top="50" 
            :sub-menu-item-height="subMenuItemHeight" ref="submenu" 
            :position="subposition" :show="submenu_show" 
            @hiding="onsubmenu_hiding" 
            @canclehiding="onsubmenu_canclehiding" :menu ="current_sub_menu" 
            :option="option" @menuitem_click="menuitem_click"></submenu>
        </div>
    </div>
</body>
</html>