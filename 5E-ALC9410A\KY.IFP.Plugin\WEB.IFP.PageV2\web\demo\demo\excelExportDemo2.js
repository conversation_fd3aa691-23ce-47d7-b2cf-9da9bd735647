﻿define(["iofp/api", "platform/vue", "iofp/lib/excelExportElement"], function (API, pVue, excelExport) {
    return {
        el: "#app",

        data() {
            return {
                filter: {
                    Ysfs1001: 10010001
                },

                tableData: []
            }
        },

        created() {
            this.onSelect();
        },

        methods: {
            //查询
            onSelect: function () {
				var data = [
					{ Kd4001: 45000239, Mz4010: 45000244, Pcbm: 'QC-20200804-001', Mar: 5.23, Mad: 8.61, Aar: 18.3, Aad: 12.6, QgrarMJ: 18.252836, QgrarKcal: 4000, QnetarMJ: 17.895, QnetarKcal: 3800, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:46' },
					{ Kd4001: 45000239, Mz4010: 45000244, Pcbm: 'QC-20200804-001', Mar: 5.15, Mad: 8.33, Aar: 18.7, Aad: 13.1, QgrarMJ: 18.112, QgrarKcal: 4100, QnetarMJ: 18.216, QnetarKcal: 3900, Gyzl: 2456.38, Gyzb: 0.3514, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:35' },
					{ Kd4001: 45000239, Mz4010: 45000244, Pcbm: 'QC-20200804-002', Mar: 5.07, Mad: 8.25, Aar: 18.7, Aad: 13.6, QgrarMJ: 18.325, QgrarKcal: 4200, QnetarMJ: 18.537, QnetarKcal: 4000, Gyzl: 567.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:47' },
					{ Kd4001: 45000239, Mz4010: 45000244, Pcbm: 'QC-20200804-002', Mar: 4.99, Mad: 8.17, Aar: 17.2, Aad: 14.1, QgrarMJ: 18.538, QgrarKcal: 4300, QnetarMJ: 18.858, QnetarKcal: 4100, Gyzl: 324, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:36' },
					{ Kd4001: 45000239, Mz4010: 45000244, Pcbm: 'QC-20200804-002', Mar: 4.91, Mad: 8.09, Aar: 17.2, Aad: 14.6, QgrarMJ: 18.751, QgrarKcal: 4400, QnetarMJ: 19.179, QnetarKcal: 4200, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:48' },
					{ Kd4001: 45000239, Mz4010: 45000243, Pcbm: 'QC-20200804-002', Mar: 4.83, Mad: 8.01, Aar: 16.7, Aad: 15.1, QgrarMJ: 18.964, QgrarKcal: 4500, QnetarMJ: 19.5, QnetarKcal: 4300, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:37' },
					{ Kd4001: 45000239, Mz4010: 45000243, Pcbm: 'QC-20200804-002', Mar: 4.75, Mad: 7.93, Aar: 16.2, Aad: 15.6, QgrarMJ: 19.177, QgrarKcal: 4600, QnetarMJ: 19.821, QnetarKcal: 4400, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:49' },
					{ Kd4001: 45000239, Mz4010: 45000243, Pcbm: 'QC-20200804-003', Mar: 4.67, Mad: 7.85, Aar: 15.7, Aad: 16.1, QgrarMJ: 19.39, QgrarKcal: 4700, QnetarMJ: 20.142, QnetarKcal: 4500, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:38' },
					{ Kd4001: 45000239, Mz4010: 45000242, Pcbm: 'QC-20200804-003', Mar: 4.59, Mad: 7.77, Aar: 15.2, Aad: 16.6, QgrarMJ: 19.603, QgrarKcal: 4800, QnetarMJ: 20.463, QnetarKcal: 4600, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:50' },
					{ Kd4001: 45000239, Mz4010: 45000242, Pcbm: 'QC-20200804-004', Mar: 4.51, Mad: 7.69, Aar: 14.7, Aad: 17.1, QgrarMJ: 19.816, QgrarKcal: 4900, QnetarMJ: 20.784, QnetarKcal: 4700, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:39' },
					{ Kd4001: 45000238, Mz4010: 45000242, Pcbm: 'QC-20200804-004', Mar: 4.43, Mad: 7.61, Aar: 14.2, Aad: 17.6, QgrarMJ: 20.029, QgrarKcal: 5000, QnetarMJ: 21.105, QnetarKcal: 4800, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:51' },
					{ Kd4001: 45000238, Mz4010: 45000242, Pcbm: 'QC-20200804-005', Mar: 4.35, Mad: 7.53, Aar: 13.7, Aad: 18.1, QgrarMJ: 20.242, QgrarKcal: 5100, QnetarMJ: 21.426, QnetarKcal: 4900, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:40' },
					{ Kd4001: 45000238, Mz4010: 45000241, Pcbm: 'QC-20200804-005', Mar: 4.27, Mad: 7.45, Aar: 13.2, Aad: 18.6, QgrarMJ: 20.455, QgrarKcal: 5200, QnetarMJ: 21.747, QnetarKcal: 5000, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:52' },
					{ Kd4001: 45000238, Mz4010: 45000241, Pcbm: 'QC-20200804-006', Mar: 4.19, Mad: 7.37, Aar: 12.7, Aad: 19.1, QgrarMJ: 20.668, QgrarKcal: 5300, QnetarMJ: 22.068, QnetarKcal: 5100, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:41' },
					{ Kd4001: 45000238, Mz4010: 45000240, Pcbm: 'QC-20200804-006', Mar: 4.11, Mad: 7.29, Aar: 12.2, Aad: 19.6, QgrarMJ: 20.881, QgrarKcal: 5400, QnetarMJ: 22.389, QnetarKcal: 5200, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:53' },
					{ Kd4001: 45000238, Mz4010: 45000240, Pcbm: 'QC-20200804-007', Mar: 4.03, Mad: 7.21, Aar: 11.7, Aad: 20.1, QgrarMJ: 21.094, QgrarKcal: 5500, QnetarMJ: 22.71, QnetarKcal: 5300, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:42' },
					{ Kd4001: 45000237, Mz4010: 45000240, Pcbm: 'QC-20200804-007', Mar: 3.95, Mad: 7.13, Aar: 11.2, Aad: 20.6, QgrarMJ: 21.307, QgrarKcal: 5600, QnetarMJ: 23.031, QnetarKcal: 5400, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:54' },
					{ Kd4001: 45000237, Mz4010: 45000244, Pcbm: 'QC-20200804-008', Mar: 3.87, Mad: 7.05, Aar: 10.7, Aad: 21.1, QgrarMJ: 21.52, QgrarKcal: 5700, QnetarMJ: 23.352, QnetarKcal: 5500, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:43' },
					{ Kd4001: 45000237, Mz4010: 45000244, Pcbm: 'QC-20200804-009', Mar: 3.79, Mad: 6.97, Aar: 10.2, Aad: 21.6, QgrarMJ: 21.733, QgrarKcal: 5800, QnetarMJ: 23.673, QnetarKcal: 5600, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:30:55' },
					{ Kd4001: 45000237, Mz4010: 45000244, Pcbm: 'QC-20200804-010', Mar: 3.71, Mad: 6.89, Aar: 9.7, Aad: 22.1, QgrarMJ: 21.946, QgrarKcal: 5900, QnetarMJ: 23.994, QnetarKcal: 5700, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 1, Czr: 'admin', Czsj: '2020-08-04 15:28:44' },
					{ Kd4001: 45000236, Mz4010: 45000242, Pcbm: 'QC-20200804-010', Mar: 3.63, Mad: 6.81, Aar: 9.2, Aad: 22.6, QgrarMJ: 22.159, QgrarKcal: 6000, QnetarMJ: 24.315, QnetarKcal: 5800, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 2, Czr: 'admin', Czsj: '2020-08-04 15:30:56' },
					{ Kd4001: 45000236, Mz4010: 45000242, Pcbm: 'QC-20200804-011', Mar: 3.55, Mad: 6.73, Aar: 8.7, Aad: 23.1, QgrarMJ: 22.372, QgrarKcal: 6100, QnetarMJ: 24.636, QnetarKcal: 5900, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 2, Czr: 'admin', Czsj: '2020-08-04 15:28:45' },
					{ Kd4001: 45000236, Mz4010: 45000241, Pcbm: 'QC-20200804-012', Mar: 3.47, Mad: 6.65, Aar: 8.2, Aad: 23.6, QgrarMJ: 22.585, QgrarKcal: 6200, QnetarMJ: 24.957, QnetarKcal: 6000, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 2, Czr: 'admin', Czsj: '2020-08-04 15:30:57' },
					{ Kd4001: 45000236, Mz4010: 45000241, Pcbm: 'QC-20200804-012', Mar: 3.39, Mad: 6.57, Aar: 7.7, Aad: 24.1, QgrarMJ: 22.798, QgrarKcal: 6300, QnetarMJ: 25.278, QnetarKcal: 6100, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 2, Czr: 'admin', Czsj: '2020-08-04 15:28:46' },
					{ Kd4001: 45000235, Mz4010: 45000241, Pcbm: 'QC-20200804-013', Mar: 3.31, Mad: 6.49, Aar: 7.2, Aad: 24.6, QgrarMJ: 23.011, QgrarKcal: 6400, QnetarMJ: 25.599, QnetarKcal: 6200, Gyzl: 4589765.25, Gyzb: 0.3526, Jszt: 2, Czr: 'admin', Czsj: '2020-08-04 15:30:58' }
				];
				this.$set(this, "tableData", data);
			},

			//导出EXCEL
			onExcelExportClick: function () {
				/*
				var ExcelWorkbook = {
					FileName: "化验指标对比.xls",
					Sheets: [
						{
							SheetName: "热值对比",
							Tables: [
								{
									TableType: 1,
									Title: "当日",
									TopRows: [
										{ Cells: [{ Value: "单位：瑞光热电", Rowspan: 1, Colspan: 8 }, { Value: "业务月份：2020-08", Rowspan: 1, Colspan: 8, Align: 3 }] }
									],
									Heads: [
										{
											Cells: [
												{ Value: "矿点", Rowspan: 3, Colspan: 1 },
												{ Value: "煤种", Rowspan: 3, Colspan: 1 },
												{ Value: "批次编码", Rowspan: 3, Colspan: 1 },
												{ Value: "水份", Rowspan: 1, Colspan: 2 },
												{ Value: "灰份", Rowspan: 1, Colspan: 2 },
												{ Value: "热值", Rowspan: 1, Colspan: 4 },
												{ Value: "供应总量", Rowspan: 3, Colspan: 1 },
												{ Value: "供应占比", Rowspan: 3, Colspan: 1 },
												{ Value: "结算状态", Rowspan: 3, Colspan: 1 },
												{ Value: "操作人", Rowspan: 3, Colspan: 1 },
												{ Value: "操作时间", Rowspan: 3, Colspan: 1 },
											]
										},
										{
											Cells: [
												{ Value: "全水", Rowspan: 1, Colspan: 1 },
												{ Value: "内水", Rowspan: 1, Colspan: 1 },
												{ Value: "收到基灰份", Rowspan: 1, Colspan: 1 },
												{ Value: "空干基灰份", Rowspan: 1, Colspan: 1 },
												{ Value: "收到基高位热值", Rowspan: 1, Colspan: 2 },
												{ Value: "收到基低位热值", Rowspan: 1, Colspan: 2 }
											]
										},
										{
											Cells: [
												{ Value: "Mar(%)", Rowspan: 1, Colspan: 1 },
												{ Value: "Mad(%)", Rowspan: 1, Colspan: 1 },
												{ Value: "Aar(%)", Rowspan: 1, Colspan: 1 },
												{ Value: "Aad(%)", Rowspan: 1, Colspan: 1 },
												{ Value: "Qgr.ar(MJ/kg)", Rowspan: 1, Colspan: 1 },
												{ Value: "Qgr.ar(Kcal/kg)", Rowspan: 1, Colspan: 1 },
												{ Value: "Qnet.ar(MJ/kg)", Rowspan: 1, Colspan: 1 },
												{ Value: "Qnet.ar(Kcal/kg)", Rowspan: 1, Colspan: 1 },
											]
										}
									],
									Datas: [
										{
											Cells: [
												{ Name: "Kd4001", Value: 45000239, DataType: 1, Ywlx: 4001, Align: 1, Formater: "" },
												{ Name: "Mz4010", Value: 45000244, DataType: 1, Ywlx: 4010, Align: 1, Formater: "" },
												{ Name: "Pcbm", Value: 'QC-20200804-001', DataType: 1, Ywlx: "", Align: 1, Formater: "" },
												{ Name: "Mar", Value: 5.23, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 2},
												{ Name: "Mad", Value: 5.23, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 2},
												{ Name: "Aar", Value: 5.23, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 2},
												{ Name: "Aad", Value: 5.23, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 2},
												{ Name: "QgrarMJ", Value: 5.23, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 3},
												{ Name: "QgrarKcal", Value: 5.23, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 0},
												{ Name: "QnetarMJ", Value: 5.23, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 3},
												{ Name: "QnetarKcal", Value: 5.23, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 0},
												{ Name: "Gyzl", Value: 435446.87, DataType: 2, Ywlx: "", Align: 1, Formater: "", DecimalDigits: 2},
												{ Name: "Gyzb", Value: 0.325, DataType: 2, Ywlx: "", Align: 1, Formater: "percent", DecimalDigits: 1},
												{ Name: "Jszt", Value: 1, DataType: 1, Ywlx: "", Align: 1, Formater: "", Data: [{ id: 1, text: "未结算" }, { id: 2, text: "已结算" }]},
												{ Name: "Czr", Value: "admin", DataType: 1, Ywlx: "", Align: 1, Formater: "useridtoname" },
												{ Name: "Czsj", Value: "2020-08-04 15:30:46", DataType: 3, Ywlx: "", Align: 1, Formater: "YYYY-MM-DD" }
											]
										}
									],
									FootRows: [
										{ Cells: [{ Value: "填报人：张福成", Rowspan: 1, Colspan: 8 }, { Value: "审核人：赵文凯", Rowspan: 1, Colspan: 8, Align: 3 }] }
									],
									MergeColumnIndex: [0, 1, 2]
								},
								{
									TableType: 2,
									Datas: [
										{ Cells: [{ Value: "开元坤佳", Rowspan: 1, Colspan: 1 }, { Value: "高热值煤", Rowspan: 1, Colspan: 1 }] },
										{ Cells: [{ Value: "开元仪器", Rowspan: 1, Colspan: 1 }, { Value: "高热低硫煤", Rowspan: 1, Colspan: 1 }] }
									]
								}
							]
						}
					]
				};
				*/

				/*
				 * 指定列模型
				 * DataType：数据类型（1 字符, 2 数字, 3 时间）
				 * Align：对齐方式（1 left, 2 center, 3 right）
				 * DecimalDigits：小数位
				 * Ywlx：基础资料的类型
				 * Formater：格式转换符号(percent：百分比格式，thousands：千分符显示，useridtoname：用户登录账号转名称)
				 * Data：自定义下拉控件的选项值
				*/
				var colModels = [
					{ Name: "Kd4001", DataType: 1, Ywlx: 4001, Align: 1},
					{ Name: "Mz4010", DataType: 1, Ywlx: 4010, Align: 1},
					{ Name: "Pcbm",  DataType: 1, Align: 1},
					{ Name: "Mar", DataType: 2, Align: 3, DecimalDigits: 2 },
					{ Name: "Mad", DataType: 2, Align: 3, DecimalDigits: 2 },
					{ Name: "Aar", DataType: 2, Align: 3, DecimalDigits: 2 },
					{ Name: "Aad", DataType: 2, Align: 3, DecimalDigits: 2 },
					{ Name: "QgrarMJ", DataType: 2, Align: 3, DecimalDigits: 3 },
					{ Name: "QgrarKcal", DataType: 2, Align: 3, DecimalDigits: 0 },
					{ Name: "QnetarMJ", DataType: 2, Align: 3, DecimalDigits: 3 },
					{ Name: "QnetarKcal", DataType: 2, Align: 3, DecimalDigits: 0 },
					{ Name: "Gyzl", DataType: 2, Align: 3, DecimalDigits: 2, Formater :"thousands"},
					{ Name: "Gyzb", DataType: 2, Align: 3, Formater: "percent", DecimalDigits: 1 },
					{ Name: "Jszt", DataType: 1, Align: 2, Data: [{ id: 1, text: "未结算" }, { id: 2, text: "已结算" }] },
					{ Name: "Czr", DataType: 1, Align: 1, Formater: "useridtoname" },
					{ Name: "Czsj", DataType: 3, Align: 2, Formater: "YYYY-MM-DD" }
				];

				//表格头
				var Heads = excelExport.getHeads(this.$refs["tableLmtj"]);

				//表格数据
				var Datas = excelExport.getDatas(this.$refs["tableLmtj"], colModels, this.tableData);

				//需要合并的列索引
				var MergeColumnIndex = [0, 1, 2];

				var ExcelWorkbook = {
					FileName: "化验指标对比.xls",
					Sheets: [
						{
							SheetName: "热值对比",
							Tables: [
								{
									TableType: 1,
									Title: "当日",
									Heads: Heads,
									Datas: Datas,
									MergeColumnIndex: MergeColumnIndex
								}
							]
						}
					]
				};

				//调用通用导出方法
				excelExport.export(ExcelWorkbook);
			},

            //点“退出”按钮
            onExit: function () {
                this.$emit("cancel");
            }
        }
    }
})