define([
    "./res/qunit.js",
    "json!./unit.test.json",
    "css!./res/qunit.min.css"
],function(QUnit,list){
    QUnit.config.autostart = false;
    return function(){
        Promise.all(list.map(url=>{
            return new Promise((resolve,reject)=>requirejs([url],resolve,reject))
        })).then(list=>{
            return Promise.all(list.map(fn=>Promise.resolve(fn(QUnit))))
        }).then(function(){
            QUnit.start();
        })
    }
})