define([
    "yaml!/iofp/legend/setting.yaml",
    "platform/vue",
    "js-yaml",
    "util"
],function(setting,pVue,yaml,util){
    const colors = [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
    ];
    
    function copy(txt) {
        if(navigator?.clipboard?.writeText){
            return navigator.clipboard.writeText(txt)
        }else{
            return Promise.reject(new Error("浏览器不支持"))
        }
    }
    return {
        el:"#app",
        components:{
            ...pVue.createAsyncComponents({
                IfpMenuIcon: "comp!../tool/index.js"//对应标签ifp-menu-icon，VUE会把IfpMenuIcon 转换成 全小写， 大写字母全部变成 -小写字母
            })
        },
        data(){
            return {
                legends:setting.map(item=>({
                    value:item.default||item.states[0].code,
                    ...item,
                    default:item.default||item.states[0].code
                })),
                sellegendcode:null,
                sellegend:null,
                selstate:null,
                predefineColors:colors,
                iconDialogVisible:false,
                codeDialogVisible:false,
                code:'',

                tabname:''//
            }
        },

        mounted(){
            let t = location.hash;
            if(t){
                this.tabname=location.hash.replace(/^#/,'');
            }
        },
        computed:{
            cols(){
                return Math.max(...this.legends.map(legend=>legend.states.length));
            },
            selLegendDefaultState(){
                return this.sellegend?.states?.find(item=>item.code===this.sellegend.default)
            },
            defaultLegend(){
                return this.legends?.find(item=>item.code==="default")
            },
            defaultState(){
                return this.defaultLegend?.states?.find(item=>item.code==="default")?.states[0]
            }
        },
        methods:{
            tab_click(tabItem,event){
                this.$message.success(tabItem.label);
                location.hash="#"+tabItem.paneName;
                console.log(arguments);
            },
            updateSel(legend){
                this.sellegend = legend;
                this.sellegendcode = legend.code;
                this.selstate = legend.states.find(item=>item.code===legend.value);
            },

            addState(legend){
                let code = 0;
                let codes = legend.states.map(item=>item.code);
                if(codes.length){
                    code = Math.max(...codes)+1;
                }
                let state = {
                    icon:'el-icon-setting',
                    text:'新状态'+code,
                    ...this.defaultState,
                    code
                }
                legend.states.push(state)
                if(legend.states.length==1){
                    legend.default = state.code;
                    legend.value = state.code;
                }
                return state;
            },

            deleteState(legend,state){
                let index = legend.states.findIndex(item=>item.code===state.code)
                if(index>-1){
                    if(this.selstate===state){
                        this.selstate = null;
                    }
                    legend.states.splice(index,1);
                }
            },

            addLegend(){
                let legend = {
                    code:'newlegend1',
                    text:'新图例1',
                    states:[]
                }
                this.addState(legend);
                return legend;
            },

            onAddLegendClick(){
                let legend = this.addLegend();
                this.legends.push(legend);
                this.sellegend = legend;
                this.sellegendcode = legend.code;
                this.selstate = legend.states[0];
                this.sellegend.value = this.selstate.code;
            },
            onAddStateClick(){
                let state = this.addState(this.sellegend);
                this.sellegend.value = state.code;
                this.selstate=state;
            },
            deleteLegend(legend){
                let index = this.legends.findIndex(item=>item.code===legend.code)
                if(index>-1){
                    if(this.sellegend===legend){
                        this.sellegend = null;
                    }
                    this.legends.splice(index,1);
                }
            },
            getCode(){
                let code = yaml.dump(this.legends.map(legend=>({
                    ...legend,
                    value:undefined
                })), {
                    'styles': {
                      '!!null': 'canonical' // dump null as ~
                    },
                    'sortKeys': false        // sort object keys
                });
                return code;
            },
            copy(){
                copy(this.getCode())
                .then(()=>
                this.$notify({
                  title: '复制成功',
                  message: '已复制到剪贴板，直接粘贴覆盖到文件 iofp/components/legend/setting.yaml',
                  position: 'bottom-right'
                }))
                .catch((err)=>this.$message.error(err.message))
            }
        }
    }
})