.kyadmin-ui.kyadmin-ui-vue.ifp .pages-icon .box span{
    display:inline-block;
    font-size:20px;
    padding:5px;
    cursor: pointer;
    border:1px solid transparent;
}
.kyadmin-ui.kyadmin-ui-vue.ifp .pages-icon .box span:hover{
    border:1px solid var(--bg-primary);
    background-color:var(--bg-primary);
    color:var(--color-primary);
}
.kyadmin-ui.kyadmin-ui-vue.ifp .pages-icon .selected{
    border:1px solid red;
    background:red;
}

.kyadmin-ui.kyadmin-ui-vue.ifp .pages-icon input:not([type]){width:100px;}