# 业务下拉控件

## 业务类型

```html
<ifp-select-ywlx v-model="ywlx4002" :ywlx="4002"></ifp-select-ywlx>
```

### 属性

|属性|数据类型|默认值|说明|
|----|--------|------|----|
|showzf|bool|true|显示作废选项|
|setzf|bool|false|可选择作废选项|
|value|string、number|""|值|
|valueKey|string|"id"|value 字段名|
|labelKey|string|"text"|lable 字段名|
|disabled|bool|false|禁用|
|clearable|bool|false|显示清空按钮|
|canedit|bool|true|可编辑|
|ywlx|string、number|""|业务类型|
|url|string|见备注|业务类型|

> 备注：url 默认值取自 `iofp/starter.js` 中的 `config.require.config["configs/config.service"]._http_getYwlxCombox_`

## 角色用户

```html
<ifp-select-roleuser v-model="userid" :code="4002"></ifp-select-roleuser>
```

> 默认 url : `iofp/mixins/components/ifp-select.js` 中的 `roleurl`

### 使用其他url

实际上，方式凡是支持 `post` 参数 `{code:''}` 的接口，均可使用此控件，修改控件 url 即可

```html
<ifp-select-roleuser v-model="xxxid" :code="10000" url="/api/xxx"></ifp-select-roleuser>
```

也可以基于此控件创建一个新控件

在 `iofp/mixins/components/ifp-select.js` 添加以下代码

```js
define(function(){
    // ...

    // 添加以下代码
    // 创建一个新控件 ifp-select-xxx
    // /api/xxxxx 接口需要支持 {code:''}
    exports.ifpSelectXxx = {
        props:{
            url:{default:'/api/xxxxx'}
        },
        extends:ifpSelectRoleuser
    }
})
```
