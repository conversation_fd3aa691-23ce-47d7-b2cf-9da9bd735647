# el-table-column 格式化

配置示例

```html
<el-table-column label="2位小数" :formatter="$TableFormatter.float(2)"></el-table-column>
<el-table-column label="默认小数位数" :formatter="$TableFormatter.float"></el-table-column>
<el-table-column label="千分位" :formatter="$TableFormatter.thousands"></el-table-column>
<el-table-column label="日期" :formatter="$TableFormatter.date"></el-table-column>
<el-table-column label="日期时间" :formatter="$TableFormatter.datetime"></el-table-column>
<el-table-column label="自定义日期格式" :formatter="$TableFormatter.dateFormat('YYYY年')"></el-table-column>
```