﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ORM.IFP.DTO
{
    #region 请求数据模型

    /// <summary>
    /// 角色按钮权限请求数据
    /// </summary>
    public class RoleButtonPermissionRequest
    {
        public string RoleGid { get; set; }
        public List<PageButtonPermission> Permissions { get; set; }
    }

    /// <summary>
    /// 页面按钮权限配置
    /// </summary>
    public class PageButtonPermission
    {
        public string PageUrl { get; set; }
        public string PageName { get; set; }
        public List<string> AllowedButtons { get; set; }
        public List<string> DeniedButtons { get; set; }
    }

    /// <summary>
    /// 按钮信息
    /// </summary>
    public class ButtonInfo
    {
        public string ButtonId { get; set; }
        public string ButtonName { get; set; }
        public string ButtonGroup { get; set; }
        public string ButtonType { get; set; }
        public string Icon { get; set; }
        public string Description { get; set; }
        public int Sort { get; set; }
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// 用户按钮权限结果
    /// </summary>
    public class UserButtonPermissionResult
    {
        public string PageUrl { get; set; }
        public List<string> AllowedButtons { get; set; }
        public List<string> DeniedButtons { get; set; }
    }

    #endregion

    #region 页面按钮定义模型

    /// <summary>
    /// 页面按钮定义
    /// </summary>
    public class PageButtonDefinition
    {
        public string PageUrl { get; set; }
        public string PageName { get; set; }
        public List<ButtonDefinition> Buttons { get; set; }
    }

    /// <summary>
    /// 按钮定义
    /// </summary>
    public class ButtonDefinition
    {
        public string ButtonId { get; set; }
        public string ButtonName { get; set; }
        public string ButtonGroup { get; set; }
        public string ButtonType { get; set; }
        public string Icon { get; set; }
        public string Description { get; set; }
        public int Sort { get; set; }
        public bool IsDefault { get; set; } // 是否为默认按钮
    }

    #endregion

    #region 权限查询模型

    /// <summary>
    /// 权限查询请求
    /// </summary>
    public class PermissionQueryRequest
    {
        public string UserGid { get; set; }
        public string RoleGid { get; set; }
        public string PageUrl { get; set; }
        public List<string> ButtonIds { get; set; }
    }

    /// <summary>
    /// 权限查询结果
    /// </summary>
    public class PermissionQueryResult
    {
        public string UserGid { get; set; }
        public string PageUrl { get; set; }
        public Dictionary<string, bool> ButtonPermissions { get; set; }
        public bool HasAllPermissions { get; set; }
        public List<string> UserRoles { get; set; }
    }

    #endregion

    #region 扩展的数据传输对象

    /// <summary>
    /// 用户直接权限请求
    /// </summary>
    public class UserDirectPermissionRequest
    {
        public string UserGid { get; set; }
        public List<string> MenuPermissions { get; set; } = new List<string>();
        public List<PageButtonPermission> ButtonPermissions { get; set; } = new List<PageButtonPermission>();
        public bool ReplaceExisting { get; set; } = true;
    }

    /// <summary>
    /// 批量用户权限请求
    /// </summary>
    public class BatchUserPermissionRequest
    {
        public List<UserDirectPermissionRequest> Operations { get; set; } = new List<UserDirectPermissionRequest>();
        public bool ReplaceExisting { get; set; } = true;
    }

    /// <summary>
    /// 权限来源枚举
    /// </summary>
    public enum PermissionSourceEnum
    {
        /// <summary>
        /// 直接赋权
        /// </summary>
        Direct = 1,

        /// <summary>
        /// 角色继承
        /// </summary>
        RoleInherit = 2
    }

    #endregion
}


