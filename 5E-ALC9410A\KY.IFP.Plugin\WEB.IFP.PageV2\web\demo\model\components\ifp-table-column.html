<!DOCTYPE html>

<html lang="zh-CN">
<head>
    <title>煤种信息</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
</head>
<body controller class="flex" option="{platform:'element'}">
    <el-table-column id="app" :prop="prop" :label="label">
        <template slot-scope="scope" v-if="editable">
            <ifp-select-ywlx v-if="editType==='select-ywlx'" :ywlx="editControl.ywlx" v-model="scope.row[prop]" :placeholder="'请选择'+label">
            </ifp-select-ywlx>
            <ifp-input v-else v-model="scope.row[prop]" :placeholder="'请输入'+label">
            </ifp-input>
            
        </template>
    </el-table-column>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>