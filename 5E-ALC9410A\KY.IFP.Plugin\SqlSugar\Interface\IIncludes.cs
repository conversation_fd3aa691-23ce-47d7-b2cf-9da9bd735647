﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace SqlSugar 
{
    /// <summary>
    /// Includes
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public partial interface ISugarQueryable<T>
    {
        NavISugarQueryable<T> AsNavQueryable();
        ISugarQueryable<T> IncludesByExpression2<TReturn1, TReturn2>(Expression include1, Expression include2);
        ISugarQueryable<T> IncludesByExpression<TReturn1>(Expression include1);
        ISugarQueryable<T> IncludesByNameString(string navMemberName);
        ISugarQueryable<T> IncludesByNameString(string navMemberName,string thenNavMemberName2);
        ISugarQueryable<T> IncludesByNameString(string navMemberName, string thenNavMemberName2, string thenNavMemberName3);
        ISugarQueryable<T> IncludesByNameString(string navMemberName, string thenNavMemberName2, string thenNavMemberName3, string thenNavMemberName4);
        ISugarQueryable<T> IncludesByNameString(string navMemberName, string thenNavMemberName2, string thenNavMemberName3, string thenNavMemberName4,string thenNavMemberName5);
        ISugarQueryable<T> IncludesAllFirstLayer(params string[] ignoreProperyNameList);
        ISugarQueryable<T> IncludesAllSecondLayer<TReturn1>(Expression<Func<T, TReturn1>> expression,params string[] ignoreProperyNameList);
        ISugarQueryable<T> Includes<TReturn1>(Expression<Func<T, List<TReturn1>>> include1);
        ISugarQueryable<T> Includes<TReturn1>(Expression<Func<T, TReturn1>> include1);
        ISugarQueryable<T> Includes<TReturn1, TReturn2>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2);
        ISugarQueryable<T> Includes<TReturn1, TReturn2>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2);
        ISugarQueryable<T> Includes<TReturn1, TReturn2>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2);
        ISugarQueryable<T> Includes<TReturn1, TReturn2>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2);
        NavISugarQueryable<T> Includes<TReturn1, TReturn2, TReturn3>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3);
        NavISugarQueryable<T> Includes<TReturn1, TReturn2, TReturn3>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3);
        NavISugarQueryable<T> Includes<TReturn1, TReturn2, TReturn3>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3);
        NavISugarQueryable<T> Includes<TReturn1, TReturn2, TReturn3>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3);
        NavISugarQueryable<T> Includes<TReturn1, TReturn2, TReturn3>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3);
        NavISugarQueryable<T> Includes<TReturn1, TReturn2, TReturn3>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3);
        NavISugarQueryable<T> Includes<TReturn1, TReturn2, TReturn3>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3);
        NavISugarQueryable<T> Includes<TReturn1, TReturn2, TReturn3>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3);
      
    }

    /// <summary>
    /// Includes
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public partial interface NavISugarQueryable<T>: ISugarQueryable<T>
    {
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, TReturn3>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, List<TReturn5>>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, List<TReturn7>>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, TReturn2>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, List<TReturn1>>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, TReturn4>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, TReturn6>> include6, Expression<Func<TReturn6, TReturn7>> include7);
        NavQueryableProvider<T> Includes<TReturn1, TReturn2, TReturn3, TReturn4, TReturn5, TReturn6, TReturn7>(Expression<Func<T, TReturn1>> include1, Expression<Func<TReturn1, List<TReturn2>>> include2, Expression<Func<TReturn2, List<TReturn3>>> include3, Expression<Func<TReturn3, List<TReturn4>>> include4, Expression<Func<TReturn4, TReturn5>> include5, Expression<Func<TReturn5, List<TReturn6>>> include6, Expression<Func<TReturn6, TReturn7>> include7);

    }
}
