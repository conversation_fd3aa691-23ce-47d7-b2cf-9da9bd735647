(function(factory){
    // 加载 require,polyfill

    // kydamin 根目录
    var corepath = window.kycore && window.kycore.corepath || getBaseUrl();
    document.body.classList.add("kyadmin-ui")

    var list = []
    if(!window.Promise){
        list.push(corepath+"/lib/kjlib/babel/polyfill/polyfill.min")
    }
    if(!(window.define && window.define.amd)){
        list.push(corepath+"/lib/bower/requirejs/require.js")
    }
    
    loadScripts(list,function(){
        factory(corepath)
    },function(err){
        let result = window.confirm(err.message+",点击确定将刷新页面。");
        if(result){
            window.location.reload()
        }else{
            console.error(err);
        }
    })

    function loadScripts(urls,callback,errcallback){
        if(!urls.length){return callback()}
        var succentcount = 0;
        var timeoutid = setTimeout(function(){
            timeoutid = null;
            errcallback(new Error("静态资源请求超时"));
        },10000)
        for(var i=0;i<urls.length;i++){
            loadScript(urls[i],function(){
                succentcount++;
                if(succentcount==urls.length){
                    clearTimeout(timeoutid);
                    callback();
                }
            })
        }
    }

    function loadScript(url,callback,errcallback){
        var script=document.createElement('script');
        script.type="text/javascript";
        if(typeof(callback)!="undefined"){
            if(script.readyState){
                script.onreadystatechange=function(){
                    if(script.readyState == "loaded" || script.readyState == "complete"){
                    script.onreadystatechange=null;
                    callback();}
                }
            }else{script.onload=callback;}
        }
        script.src=url;
        document.body.appendChild(script);
    }
    function getBaseUrl(){
        let a = document.createElement("a")
        var scripts = document.getElementsByTagName("script");
        a.href=scripts[scripts.length-1].src;
        var filename = a.pathname.split("/");
        return filename.slice(0,filename.length-1).join("/");
    }
})(function(corepath){
    
    var defaultOption = {
        corepath:corepath,
        loading:true,
        platform:"default"
    }

    var kyadminBaseUrl = corepath.replace(/\/$/,"");

    // 路径地址
    define("kyadmin/paths/base",kyadminBaseUrl)
    define("kyadmin/paths/lodopexe",corepath+"/resources/CLodop_Setup_for_Win32NT.exe")

    requirejs.config({
        paths:{
            "kyadmin":kyadminBaseUrl,
            "zutil":corepath+"/utils/zutil",
            "ramda":corepath+"/lib/bower/ramda/ramda",
            "configs":corepath+"/configs",
            "domReady":corepath+"/lib/bower/domReady/domReady",
            "css":corepath+"/lib/bower/require-css/css",
            "text":corepath+"/lib/kjlib/require-plugins/text",
            "json":corepath+"/lib/kjlib/require-plugins/src/json"
        },
        waitSeconds: 15
    })

    require([
        "configs/config.amd",
        "zutil"
    ],function(amdConfig,zutil){
        var pageOption = zutil.getBodyOption();
        
        var setting = zutil.extend(true,{},defaultOption,window.kycore,pageOption,{
            require:amdConfig(corepath,window.kycore && window.kycore.require)
        });

        requirejs.config(setting.require);

        // 系统配置
        define("sysSetting",setting);
        
        let after = awaiter(); // 显示加载动画

        let flow = Promise.resolve(setting);
        if(typeof setting.before === "function"){
            flow = flow.then(setting.before)
            .then(_=>setting)
        }
        flow = flow.then(loadController)
        .then(function(){
            zutil.log("success")
        })
        .catch(function(ex){
            zutil.error("error",ex)
            return ex;
        })
        .then(function(err){
            after();
            if(typeof setting.callback === "function"){
                setting.callback(err);
            }
        });
        
        function loadController(config){
            return new Promise(function(resolve,reject){
                var controllerName = zutil.getControllerName();
                var jspath;
                if(config.platform===null || config.platform===false || config.platform==="none"){
                    if(controllerName){
                        jspath = controllerName;
                        zutil.log("controller:"+controllerName+" loaded");
                    }else{
                        zutil.log("{paltform:null,controller:null}");
                        after();
                    }
                }else{
                    var platformName = config.platform,
                        platformPath;
                    if(typeof platformName === "undefined"){
                        platformName="default";
                    }
                    if(config.require.paths["platform/"+platformName]){
                        platformPath = "platform/"+platformName;
                    }else{
                        platformPath = platformName;
                    }
                    jspath = platformPath;
                }
                requirejs(jspath?[jspath]:[],function(controller){

                    if(typeof controller === "function"){
                        controller = controller(config);
                    }
                    Promise.resolve()
                    .then(function(){
                        return controller;
                    }).then(resolve)
                    .catch(reject)
                })
            })
        }
        
        // 显示等待，返回关闭函数
        function awaiter(){
            var loadingid = null;
            if(setting.loading!== false){
                loadingid = setTimeout(function(){
                    zutil.showLoading();
                    zutil.showBody();
                    loadingid = null;
                },0);
            }
            // 加载完毕后调用
            return function(){
                zutil.showBody();
                if(loadingid){clearTimeout(loadingid);loadingid=null;}
                else{
                    zutil.hideLoading();
                }
            }
        }
    })
});