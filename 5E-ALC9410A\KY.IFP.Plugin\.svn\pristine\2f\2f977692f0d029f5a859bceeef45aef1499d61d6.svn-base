﻿//using COM.IFP.LinqDB;
//using LinqToDB.Data;
using System.Collections.Generic;
//using LinqToDB;
using COM.IFP.SqlSugarN;
using SqlSugar;
using System.Linq;
using COM.IFP.Common;
using System;
using ORM.IFP;
using DAL.IFP.Rights;
using DAL.IFP;
using Newtonsoft.Json;
using System.Reflection;
using ORM.IFP.DbModel.SM;
using NPOI.SS.Formula.Functions;
using NPOI.SS.Formula;
using NPOI.POIFS.FileSystem;
using System.Security.Policy;

namespace DAL.ICS.BasicData
{
    /// <summary>
    /// 火车车型管理
    /// </summary>
    public class Ywdx4005DAL
    {
        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public List<YWDX4005> Select(YWDX4005 filter)
        {
            using SqlSugarClient db = DB.Create();
            List<YWDX4005> result = new List<YWDX4005>();
            List<YWDX4005> bcList = lazy.Value.Select<YWDX4005>(x => x.Zfbz != new Field<int>()).ToList(); 
            List<YWDX4005_CYZQ> bc_cyzqList = db.Queryable<YWDX4005_CYZQ>().ToList();
            Dictionary<long, List<YWDX4005>> dict = new Dictionary<long, List<YWDX4005>>();  //班次Gid对班次对象列表映射  因为基础资料数据类型带继承 字段多 且Field类型 不好进行深拷贝 所以用查询多次的方法间接拷贝对象
            foreach (var item in bcList)
            {
                dict.Add(item.Gid.Value, new List<YWDX4005>());
                dict[item.Gid.Value].Add(item);
            }
            foreach(var one in bc_cyzqList)
            {
                //如果包含在被查询的编码中
                if(dict.ContainsKey(one.BC4005.Value))
                {
                    if (dict[one.BC4005.Value].Count == 0)  //说明一个班次对象不够用 重查一次 生成新的对象
                    {
                        //这里这个表达式必须重写  不能简单的用 var t = lazy.Value.Select<YWDX4005>(x => x.Zfbz != new Field<int>());
                        //然后用t,ToList() 那后台返回的对象将会是同一个  非常坑
                        bcList = lazy.Value.Select<YWDX4005>(x => x.Zfbz != new Field<int>()).ToList();
                        foreach (var item in bcList)
                            dict[item.Gid.Value].Add(item);
                    }
                    YWDX4005 obj = dict[one.BC4005.Value][0];
                    dict[one.BC4005.Value].RemoveAt(0);
                    obj.QB = one.QB4007;
                    obj.CYZQ = one.CYZQ;
                    obj.CYZQGid = one.Gid;
                    obj.Zfbz = one.Zfbz;
                    result.Add(obj);
                }
            }
            if (filter != null)   //把判断放到最后  因为班次数据反正不多 也不会影响什么性能  可以少加很多判断
                result = result.Where(x => x.Zfbz == filter.Zfbz).ToList();
            return result;
        }

        ///根据GID和期别查找对象
        public YWDX4005 GetByGIdAndQB(YWDX4005 filter)
        {
            List<YWDX4005> list = Select(null);
            YWDX4005 result = list.Where(x => x.Gid == filter.Gid && x.QB == filter.QB).FirstOrDefault();
            return result;
        }

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="entity">待保存的对象</param>
        /// <returns></returns>
        public PFActionResult Submit(YWDX4005 entity)
        {
            using SqlSugarClient db = DB.Create();
            PFActionResult result = new PFActionResult();

            //如果简称没有填值，默认和名称一样
            if (!entity.Sname.HasValue || string.IsNullOrEmpty(entity.Sname.Value))
            {
                entity.Sname = new Field<string>(entity.Bname.Value);
            }
            if (!entity.Gid.HasValue)
            {
                entity.Addtime = new Field<DateTime>(DateTime.Now);
            }
            entity.Lasttime = new Field<DateTime>(DateTime.Now);

            //如果没有Gid  就说明走的是新增  这里要判断是否存在同名的 如果存在同名 则需要用同名的Gid走修改
            if(!entity.Gid.HasValue)
                entity.Gid = db.Queryable<ORM.IFP.BaseData>().Where(x => x.Bname == entity.Bname && x.Ywlx == "4005").Select(x => x.Gid).First();

            //如果是否运行班次为否，则不保存开始时间和结束时间.
            if (entity.Sfyxbc1099.Value == 10990002)
            {
                entity.Kssj = new Field<string>();
                entity.Jssj = new Field<string>();
                entity.Begindate = new Field<int>();
                entity.Enddate = new Field<int>();
            }
            entity.Kssj = Convert.ToDateTime(entity.Kssj).ToString("HH:mm:ss");
            entity.Jssj = Convert.ToDateTime(entity.Jssj).ToString("HH:mm:ss");

            //保存实体
            try
            {
                lazy.Value.Submit(entity);
            }catch(Exception e)   //防止出问题  下面的要执行完成
            {

            }
            List<YWDX4005_CYZQ> list = db.Queryable<YWDX4005_CYZQ>().Where(x => x.Gid == entity.CYZQGid).ToList();
            foreach(var one in list)
            {
                InsertLog(one, "删除");  //插入删除日志
            }
            db.Deleteable<YWDX4005_CYZQ>().Where(x => x.Gid == entity.CYZQGid);  //先删除原子表记录
            var bcGid = db.Queryable<ORM.IFP.BaseData>().Where(x => x.Bname == entity.Bname && x.Ywlx == "4005").Select(x => x.Gid).First();   //拿到当前名称对应的班次Gid
            var q = db.Queryable<YWDX4005_CYZQ>().Where(x => x.BC4005 == bcGid && x.QB4007 == entity.QB);
            if(q.Count() == 0)  //是新增
            {
                YWDX4005_CYZQ item = new YWDX4005_CYZQ();
                item.Gid = Guid.NewGuid().ToString();
                item.BC4005 = bcGid;
                item.CYZQ = entity.CYZQ;
                item.QB4007 = entity.QB;
                item.Zfbz = entity.Zfbz;
                InsertLog(item, "新增");  //插入新增日志
                db.Insertable(item);
            }
            else{   //是修改
                YWDX4005_CYZQ item = new YWDX4005_CYZQ();
                item.Gid = Guid.NewGuid().ToString();
                item.BC4005 = bcGid;
                item.CYZQ = entity.CYZQ;
                item.QB4007 = entity.QB;
                item.Zfbz = entity.Zfbz;
                InsertLog(item, "修改");  //插入更新日志
                //db.GetTable<YWDX4005_CYZQ>().Where(x => x.BC4005 == bcGid && x.QB4007 == entity.QB)
                //    .Set(x => x.CYZQ, entity.CYZQ).Set(x => x.CYZQ, entity.CYZQ).Set(x => x.Zfbz, entity.Zfbz).Update();
                db.Updateable<YWDX4005_CYZQ>()
                  .SetColumns(x => new YWDX4005_CYZQ
                  {
                       CYZQ = entity.CYZQ,
                       Zfbz = entity.Zfbz
                  })
                  .Where(x => x.BC4005 == bcGid && x.QB4007 == entity.QB)
                  .ExecuteCommand();
            }
            //修改主表的作废标志
            //如果这个班次所有相关子表记录都为禁用的话，该批次状态也改为禁用
            var q1 = db.Queryable<YWDX4005_CYZQ>().Where(x => x.BC4005 == entity.Gid && x.Zfbz == 0).ToList();
            string qb = "";
            foreach(var one in q1)
            {
                if (qb != "")
                    qb += ',';
                qb += one.QB4007.Value.ToString();
            }
            if (q1.Count() > 0)   //子表有一个为0 主表就为0
            {
                db.Updateable<ORM.IFP.BaseData>().Where(x => x.Gid == entity.Gid).SetColumns(x => x.Zfbz, 0);
            }
            else  //全为1 主表就为1
            {
                db.Updateable<ORM.IFP.BaseData>().Where(x => x.Gid == entity.Gid).SetColumns(x => x.Zfbz, 1);
            }
            db.Updateable<YWDX4005>().Where(x => x.Gid == entity.Gid).SetColumns(x => x.QB4007, qb);  //改对应的期别
            result.success = true;
            result.msg = "保存成功。";
            result.data = entity;
            return result;
        }

        /// <summary>
        /// 修改插入日志 需要放在修改方法的前面  传入待修改的YWDX4005_CYZQ  会根据期别加班次找当前的值  然后记录日志
        /// </summary>
        /// <param name="data"></param>
        /// <param name="type">日志类型 字符串形式  删除 或者 修改 或者新增</param>
        /// <returns></returns>
        public void InsertLog(YWDX4005_CYZQ data, string type)
        {
            using (var db = DB.Create())
            {
                YWDX4005_CYZQ_LOG log = new YWDX4005_CYZQ_LOG();
                log.Gid = Guid.NewGuid().ToString();
                log.XGR = UserCache.GetUserName();
                log.XGSJ = DateTime.Now;
                log.Type = type;
                log.BC4005 = data.BC4005;
                log.QB4007 = data.QB4007;
                if (type == "删除")
                {
                    log.OldCYZQ = data.CYZQ;
                    log.OldZfbz = data.Zfbz;
                }
                else if (type == "新增")
                {
                    log.NewCYZQ = data.CYZQ;
                    log.NewZfbz = data.Zfbz;
                }
                else if (type == "修改")
                {
                    var q = db.Queryable<YWDX4005_CYZQ>().Where(x => x.BC4005 == data.BC4005 && x.QB4007 == data.QB4007).First();
                    if(q != null)
                    {
                        log.OldCYZQ = q.CYZQ;
                        log.OldZfbz = q.Zfbz;
                    }
                    log.NewCYZQ = data.CYZQ;
                    log.NewZfbz = data.Zfbz;
                }
                db.Insertable(log);
            }
        }
    }


}
