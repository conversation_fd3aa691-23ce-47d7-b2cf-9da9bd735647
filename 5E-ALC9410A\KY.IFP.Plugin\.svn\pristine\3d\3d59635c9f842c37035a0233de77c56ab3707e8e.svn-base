define(["jclass","form","util"],function(jclass,base,util){
	return jclass(base,{
		render:function(){
			this.$container.addClass("searchbar");
			var inputsbox = this.$container.children().eq(0);
			var inputs = inputsbox.children();
			inputs.wrap("<div class='searchbar-inputers-item'></div>")
			.before(function(){
				var option = $(this).attr("option");
				if(option){
					option = util.str2json(option);
					if(option.label){
						return "<label>"+ option.label +"：</label>";
					}
				}
				return null;
			})
			.wrap("<span style=''></span>")
		},
		init:function(ele,inputer,controler){
			var rev = this.base.apply(this,arguments);
			//$(ele).addClass("area-toolbar padding");
			return rev;
		}
	})
});
