<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>设备图例</title>
</head>
<body controller option="{platform:'element'}">
    <div id="app" style="display:inline-block;padding-right:8px;">
        <el-popover
          placement="top-start"
          :title="title"
          width="50"
          popper-class="ifp-popper"
          style="min-width:auto;"
          trigger="hover">
          <div slot="reference" style="font-size:14px;">
            <i :class="icon" :style="{color:color||''}"></i>  
            <span>{{text}}</span>
          </div>
          <ul>
            <li v-for="state in states" :key="state.code"  style="line-height: 24px;font-size:14px">
                <i :class="state.icon" :style="{color:state.color||''}"></i>  
                <span :style="state.code===value?'color:#409eff':''">{{state.text}}</span>
            </li>
          </ul>
        </el-popover>
    </div>
    <script src="/kyadmin/starter.js"></script>
</body>
</html>
