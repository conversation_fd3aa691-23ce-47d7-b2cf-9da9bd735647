﻿//using System.ComponentModel;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace COM.IFP.Common.Json
{
    public class JsonSerializerOptionsUtil
    {
        public static JsonConverter DefaultDateFormatJsonConverter = new DateFormatJsonConverter("yyyy-MM-dd HH:mm:ss");
        public static JsonSerializerOptions General = new JsonSerializerOptions
        {
            //Converters = { DefaultDateFormatJsonConverter },
            AllowTrailingCommas = true,
            PropertyNameCaseInsensitive = true,
            ReadCommentHandling = JsonCommentHandling.Skip,
            NumberHandling = JsonNumberHandling.AllowReadingFromString,
            //写空白字符
            WriteIndented = true,

            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All),
            Converters = { new JsonStringEnumConverter(),
                DateTimeConverter.NonNull,
                DateTimeConverter.CanNull,
                new StringConverter() },
        };

        public static JsonSerializerOptions Simple = new JsonSerializerOptions()
        {
            PropertyNameCaseInsensitive = true, //忽略大小写
            IgnoreNullValues = true, //忽略null值
            NumberHandling = JsonNumberHandling.AllowReadingFromString,
            //DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull, //与IgnoreNullValues不能同时设置
            Converters = { new JsonStringEnumConverter(),
                DateTimeConverter.NonNull,
                DateTimeConverter.CanNull,
                new StringConverter() }, //自定义转换器
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All) //支持所有字符集（不进行编码转换）
        };
        public static JsonSerializerOptions Indent = new JsonSerializerOptions()
        {
            WriteIndented = true,
            PropertyNameCaseInsensitive = true, //忽略大小写
            IgnoreNullValues = true, //忽略null值
            Converters = { new JsonStringEnumConverter(),
                DateTimeConverter.NonNull,
                DateTimeConverter.CanNull,
                new StringConverter() }, //自定义转换器
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All) //支持所有字符集（不进行编码转换）
        };
    }
}
