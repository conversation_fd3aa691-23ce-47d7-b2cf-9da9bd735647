# 设备图例

> 所有设备图例配置在 iofp/legend/setting.yaml 中

<a href="/iofp/legend/ifp-test.html">示例页面</a>

## 使用

html
```html
<ifp-legend code="cyj" :value="stateCode"></ifp-legend>
```

* code 和 value 的取值范围必须在 `iofp/legend/setting.yaml` 已存在
* code:取值范围： 设备的 code
* value:取值范围：  设备 code === zyj 的设备下 states 列表中的 code

js
```js
data(){
    return {
        stateCode:'1'
    }
}
```

## 维护图例配置

在 `iofp/legend/setting.yaml` 后追加如下内容

```yaml
# 存样柜
- code: cyj     #设备编码主键
  text: 采样机  #显示名称
  states:
    - code: '1' # 状态值 1
      text: 待采样  # 状态名
      icon: 'el-icon-save'  # 图标 class
    - code: '2'
      text: 采样中
      icon: 'el-icon-save'
```



## 配置说明



```yaml
---
# 设备状态图例配置

# 默认值
- code: default
  text: 设备
  states:
    - icon:   'el-icon-save'
      text:   '设备'
      color:   '#000'

# 采样机
- code: cyj
  text: 采样机
  states:
    - code: '1'
      icon: 'el-icon-save'
      text: 待采样
    - code: '2'
      icon: 'el-icon-save'
      text: 采样中

# 存样柜
- code: cyg
  text: 存样柜
  states:
    - code: '1'
      icon: 'el-icon-save'
      text: 待采样
    - code: '2'
      icon: 'el-icon-save'
      text: 待采样
```

## 不使用配置

单独提供： `z-legend` 组件，数据完全自定义，不使用配置

```html
<z-legend :value="stateCode" :states="states"></z-legend>
```

```js
data(){
    return {
        stateCode:'1',
        title:"采样机",
        states:[
          {code:"1",text:"待采样",icon:"el-icon-setting"},
          {code:"2",text:"采样中",icon:"el-icon-setting"}
        ]
    }
}
```