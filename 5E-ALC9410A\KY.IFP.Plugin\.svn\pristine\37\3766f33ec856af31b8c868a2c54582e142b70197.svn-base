﻿using COM.IFP.Common;
//using COM.IFP.LinqDB;
using ORM.IFP;
using System;
using System.Collections.Generic;
using System.Text.Json;
using COM.IFP.SqlSugarN;

namespace API.ICS.BaseData
{
    public class Ywdx4013API
    {
        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();

        public PageModel<YWDX4013> Select(JsonElement json)
        {
            YWDX4013 filter = json.GetValue<YWDX4013>("filter");
            //查询条件“作废标志”传入-1，表示查询全部的记录
            if (filter.Zfbz.Value == -1)
            {
                filter.Zfbz = new Field<int>() { };
            }
            PageModel<YWDX4013> page = json.GetValue<PageModel<YWDX4013>>("paging");
            lazy.Value.Select(new[] { filter }, page);
            return page;
        }

        //根据GID查找对象
        public YWDX4013 GetByGId(JsonElement json)
        {
            YWDX4013 entity = json.GetValue<ORM.IFP.YWDX4013>();
            entity.Gid = new Field<long>(entity.Gid.Value) { Match = Match.EQU };
            List<YWDX4013> list = lazy.Value.Select<YWDX4013>(new[] { entity }, null);
            return list[0];
        }

        //保存
        public PFActionResult Submit(JsonElement json)
        {
            PFActionResult result = new PFActionResult();

            //待保存的对象
            YWDX4013 entity = json.GetValue<ORM.IFP.YWDX4013>();
            //如果简称没有填值，默认和名称一样
            if (!entity.Sname.HasValue || string.IsNullOrEmpty(entity.Sname.Value))
            {
                entity.Sname = new Field<string>(entity.Bname.Value);
            }
            if (!entity.Gid.HasValue)
            {
                entity.Addtime = new Field<DateTime>(DateTime.Now);
            }
            entity.Lasttime = new Field<DateTime>(DateTime.Now);

            //保存前，判断名称是否重复
            YWDX4013 param = new YWDX4013();
            param.Ywlx = new Field<string>(entity.Ywlx.Value) { Match = Match.EQU };
            param.Bname = new Field<string>(entity.Bname.Value) { Match = Match.EQU };
            List<YWDX4013> list = lazy.Value.Select<YWDX4013>(new[] { param }, null);
            if (list.Count > 0 && (!entity.Gid.HasValue || entity.Gid.Value != list[0].Gid.Value))
            {
                result.success = false;
                result.msg = "运输单位名称不能重复。";
                return result;
            }

            //保存实体
            lazy.Value.Submit(entity);

            result.success = true;
            result.msg = "保存成功。";
            result.data = entity;

            return result;
        }

        public void Delete(JsonElement json)
        {
            var item = json.GetValue<ORM.IFP.YWDX4013>();
            lazy.Value.Delete(item);
        }
    }
}
