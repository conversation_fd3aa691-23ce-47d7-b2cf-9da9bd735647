define(function(){
    return {
        data(){
            return {
                dialogFormVisible:false,
                form:{
                    name:'',
                    password:''
                }
            }
        },
        template:`
        <el-dialog title="登录" :visible.sync="dialogFormVisible">
          <el-form :model="form" label-width="100px">
            <el-form-item label="用户名">
              <ifp-input v-model="form.name" autocomplete="off"></ifp-input>
            </el-form-item>
            <el-form-item label="密码">
                <ifp-input v-model="form.name" autocomplete="off"></ifp-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
          </div>
        </el-dialog>
        `
    }
})