﻿define(["iofp/api", "zutil", "iofp/buttonright"], function (API, zutil) {
    return {
        el: "#app",
        props: {
            action: {},  //操作类型
            source: {}
        },
        data() {
            return {
                checkAll: false,
                checkedRoles: [],
                roles: [],
                isIndeterminate: false,
                user: zutil.extend(true,
                    {
                        Gid: null,
                        CreateTime: null,
                        Delt: null,
                        UsiNumber: null,
                        UsiName: null,
                        UsiLoginName: null,
                        UsiName: null,
                        UsiPassWord: null,
                        DeptName: null,
                        DeptFullName: null,
                        UsiContact: null,
                        UsiSex: null,
                        CardNo: null
                    },
                    this.source),
                availableMenu: [],
                unavailableMenu: [],
            }
        },
        watch: {
            source: function () {
                this.$set(this, "user", zutil.extend(true,
                    {
                        Gid: null,
                        CreateTime: null,
                        Delt: null,
                        UsiNumber: null,
                        UsiName: null,
                        UsiLoginName: null,
                        UsiName: null,
                        UsiPassWord: null,
                        DeptName: null,
                        DeptFullName: null,
                        UsiContact: null,
                        UsiSex: null,
                        CardNo: null
                    },
                    this.source))
            }
        },
        created() {
            this.loadRoles();
        },
        methods: {
            handleCheckAllChange(val) {
                this.checkedRoles = val ? this.roles.map((item) => { return item.Gid }) : [];
                this.isIndeterminate = false;
            },
            handleCheckedRoleChange(value) {
                let checkedCount = value.length;
                this.checkAll = checkedCount === this.roles.length;
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.roles.length;

                let roleIds = value.join(',');
                this.loadAvailableMenu(roleIds);
            },
            loadRoles() {
                var _this = this;
                API.GetAction("API/IFP/Rights/Role/RoleList", {}).then(x => {
                    _this.roles = x;
                    _this.loadUserRoles();
                }).catch(e => {
                    this.$message.error(e);
                });
            },
            loadUserRoles() {
                var _this = this;
                API.GetAction("API/IFP/Rights/Role/UserRoleList", { Gid: _this.user.Gid }).then(x => {
                    if (x) {
                        _this.checkedRoles = x.split(",");
                        _this.handleCheckedRoleChange(_this.checkedRoles);
                    }
                }).catch(e => {
                    this.$message.error(e);
                });
            },
            onSubmit() {
                var _this = this;
                API.GetAction("API/IFP/Rights/Role/UpdateUserRole", {
                    Gid: _this.user.Gid,
                    RoleIds: _this.checkedRoles ? _this.checkedRoles.join(",") : ""
                }).then(x => {
                    if (x.success) {
                        _this.$message.success('保存成功');
                        _this.$emit("submit")
                    } else {
                        _this.$message.error("失败。" + x.data);
                    }
                }).catch(e => {
                    _this.$message.error(e);
                });
            },
            onCancel() {
                this.$emit("cancel")
            },
            loadAvailableMenu(roleIds) {
                var _this = this;
                let param = {
                    roleIDs: roleIds
                };
                API.GetAction("/API/IFP/Rights/Menu/AvailableMenu", param).then(res => {
                    if (res.success) {
                        this.availableMenu = res.data.availableMenu;
                        this.unavailableMenu = res.data.unavailableMenu;

                        //_this.updateTags(res.data.availableMenu);
                        //_this.updateTags2(res.data.unavailableMenu);
                    } else {
                        this.$message.error('加载可用菜单失败' + res.msg);

                    }
                }).catch(e => {
                    this.$message.error(e);
                });
            }
        }
    }
})