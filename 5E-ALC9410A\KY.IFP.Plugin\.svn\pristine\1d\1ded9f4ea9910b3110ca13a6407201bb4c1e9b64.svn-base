﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增加点位</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button @click="onSave">保存</ifp-button>
            <!--<ifp-button icon="kjicon kjicon-tuichu" @click="onClose">退出</ifp-button>-->
        </ifp-toolbar>
        <ifp-panel>
            <el-form :model="source"
                     ref="form"
                     label-width="110px"
                     style="padding-top:10px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="标签名称" prop="Name"
                                      :rules="[{required:true,message:'标签名称不能为空'}]">
                            <ifp-input v-model="source.Name"></ifp-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="数据类型" prop="DataType"
                                      :rules="[{required:true,message:'数据类型不能为空'}]">
                            <ifp-select v-model="source.DataType"
                                        url="/API/IFP/PLC/PLCPoint/GetDataTypeEnum"
                                        :post="{}"
                                        value-key="id" label-key="text"></ifp-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="组名" prop="GroupCode">
                            <ifp-select v-model="source.GroupCode"
                                        url="/API/IFP/PLC/PLCPoint/GetGroupNameEnum"
                                        :post="{}"
                                        value-key="id" label-key="text">

                            </ifp-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="标签块号">
                            <ifp-input v-model="source.Block"></ifp-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="字节偏移">
                            <ifp-input v-model="source.ByteOffset"></ifp-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="位偏移">
                            <ifp-input v-model="source.BitOffset"></ifp-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="是否可I/O展示">
                            <ifp-select v-model="source.IOShow">
                                <el-option v-for="item in list"
                                           :key="item.id"
                                           :label="item.text"
                                           :value="item.id">

                                </el-option>
                            </ifp-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">

                        <el-form-item label="说明">
                            <ifp-input v-model="source.Description"
                                       type="textarea"
                                       :rows="2"></ifp-input>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
        </ifp-panel>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>