<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格板块组件</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item padding" id="app">

        <h1>ifp-panel-table 组件示例</h1>

        <p>ifp-panel-table 继承自 ifp-panel</p>

        <ifp-panel-table border title="高度根据内容适应" class="margin">
            <el-table border>
                <el-table-column label="列1"></el-table-column>
                <el-table-column label="列2"></el-table-column>
                <el-table-column label="列3"></el-table-column>
                <el-table-column label="列4"></el-table-column>
                <el-table-column label="列5"></el-table-column>
            </el-table>
        </ifp-panel-table>

        <ifp-panel-table border class="margin" style="height:300px;">
            <el-table border height="100%" :data="[{mark:'表格适应容器高度'}]">
                <el-table-column label="列1" prop="mark"></el-table-column>
                <el-table-column label="列2"></el-table-column>
                <el-table-column label="列3"></el-table-column>
                <el-table-column label="列4"></el-table-column>
                <el-table-column label="列5"></el-table-column>
            </el-table>
            <template v-slot:footer>
                <el-pagination
                    :current-page="1"
                    :page-sizes="[10,15,20,50,100]"
                    :page-size="20"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="20">
                    <detail></detail>
                </el-pagination>
            </template>
        </ifp-panel-table>

        <h3>集成打印、列显</h3>

        <ifp-panel-table border :lxsz="true" :print="true" class="margin" style="height:300px;">
            <ifp-table :lxsz="true" height="100%" :data="data">
                <el-table-column label="列1" prop="mark"></el-table-column>
                <el-table-column label="ifp-panel-table" prop="col1"></el-table-column>
                <el-table-column label="ifp-table" prop="col2"></el-table-column>
                <el-table-column label="列4"></el-table-column>
                <el-table-column label="列5"></el-table-column>
            </ifp-table>
            <template v-slot:pagination>
                <el-pagination
                    :current-page="1"
                    :page-sizes="[10,15,20,50,100]"
                    :page-size="20"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="20">
                    <detail></detail>
                </el-pagination>
            </template>
        </ifp-panel-table>

        <h3>综合示例</h3>

        <div class="flex" style="height:600px;background:#fff;">
            <div class="toolbar">
                <el-button icon="el-icon-print" @click="$refs.table5.print()">打印</el-button>
            </div>
            <ifp-panel-table border class="margin flex-item">
                <ifp-table ref="table5" height="100%" :data="[{mark:'表格适应容器高度'}]">
                    <el-table-column label="列1" prop="mark"></el-table-column>
                    <el-table-column label="列2"></el-table-column>
                    <el-table-column label="列3"></el-table-column>
                    <el-table-column label="列4"></el-table-column>
                    <el-table-column label="列5"></el-table-column>
                </ifp-table>
                <template v-slot:pagination>
                    <el-pagination
                        :current-page="1"
                        :page-sizes="[10,15,20,50,100]"
                        :page-size="20"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="20">
                        <detail></detail>
                    </el-pagination>
                </template>
            </ifp-panel-table>
        </div>
    </div>
    <script src="/iofp/starter.js"></script> 
</body>
</html>