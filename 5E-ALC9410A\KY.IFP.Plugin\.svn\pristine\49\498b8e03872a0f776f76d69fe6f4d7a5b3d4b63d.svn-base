<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="示例页面,入场验收">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>077 机器人化验</title>
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button type="primary">复检</ifp-button>
            <ifp-button>查看</ifp-button>
        </ifp-toolbar>

        <div class="margin flex flex-row" style="flex-wrap: wrap;">
            <div v-for="item in jqrList" class="margin-right">
                <span>{{item.label}}</span>
                <ifp-legend :code="item.stateType" :value="item.state" style="min-width:70px;"></ifp-legend>
            </div>
        </div>

        <div class="margin flex flex-item flex-row">
            <div class="flex flex-item">
                <ifp-searchbar @search="search" @reset="reset">
                    <ifp-searchbar-item label="化验编码"></ifp-searchbar-item>
                    <ifp-searchbar-item label="存放类型"></ifp-searchbar-item>
                    <ifp-searchbar-item label="设备编码"></ifp-searchbar-item>
                    <ifp-searchbar-item label="日期">
                        <ifp-date-picker style="width:120px" type="date" placeholder="起始"></ifp-date-picker>
                        至
                        <ifp-date-picker style="width:120px" type="date" placeholder="截止"></ifp-date-picker>
                    </ifp-searchbar-item>
                    <ifp-searchbar-item>
                        <ifp-checkbox v-model="hasDone">包含已完成</ifp-checkbox>
                    </ifp-searchbar-item>
                </ifp-searchbar>

                <ifp-panel-table class="flex-item margin-top">
                    <ifp-table>
                        <ifp-table-column label="采样状态"></ifp-table-column>
                        <ifp-table-column label="采样编码"></ifp-table-column>
                    </ifp-table>
                </ifp-panel-table>
            </div>
            <div class="flex margin-left" style="width:30%;min-width:200px;">
                <ifp-panel border flex title="运行消息">
                    <div class="padding-left padding-right" v-for="item in 5">xxxxx xxxxxx xxx xxxx xxxxxx</div>
                </ifp-panel>

                <ifp-panel border flex class="flex flex-item margin-top" body-class="padding flex" title="检测明细">
                    <div class="flex flex-row">
                        <table class="form-datatable flex-item">
                            <tr>
                                <th class="title">样品编码</th>
                                <th class="content">HY2000000</th>
                            </tr>
                            <tr>
                                <th>判定结果</th>
                                <td>合格</td>
                            </tr>
                            <tr>
                                <th>判定结果</th>
                                <td>合格</td>
                            </tr>
                            <tr>
                                <th>判定结果</th>
                                <td>合格</td>
                            </tr>
                        </table>

                        <el-radio-group class="padding" v-model="radioValue" 
                        style="display:flex; flex-direction:column;justify-content: space-between;">
                            <el-radio v-for="item in radioOptions" :key="item.value" :value="item.value" :label="item.label">
                                {{item.label}}
                            </el-radio>
                        </el-radio-group>
                    </div>

                    <ifp-panel-table class="flex-item margin-top">
                        <ifp-table>
                            <ifp-table-column label="采样状态"></ifp-table-column>
                            <ifp-table-column label="采样编码"></ifp-table-column>
                        </ifp-table>
                    </ifp-panel-table>
                </ifp-panel>
            </div>
        </div>
    </ifp-page>
    <script src="/iofp/starter.js"></script>
</body>
</html>