﻿using COM.IFP.Common;
using COM.IFP.Log;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace COM.IFP.Client
{
    /// <summary>
    /// Http客户端
    /// </summary>
    public class HttpClientB
    {
        private static ConcurrentDictionary<string, System.Net.Http.HttpClient> exists;

        static HttpClientB()
        {
            //一个主机只保持一个客户端连接
            exists = new ConcurrentDictionary<string, System.Net.Http.HttpClient>();
        }

        public static string PostResponse(string url, object obj)
        {
            string msgss = PostAsync(url, obj).Result;
            return msgss;
        }

        /// <summary>
        /// 异步POST
        /// </summary>
        /// <param name="address">提交地址</param>
        /// <param name="content">提交内容</param>
        /// <param name="convert">是否转换，普通文本为true，JSON文本为false，默认视为JSON文本</param>
        /// <returns>返回对象</returns>
        public static Task<string> PostAsync(string address, string content, bool convert = false)
        {
            return PostAsync(address, new StringContent(convert ? Newtonsoft.Json.JsonConvert.SerializeObject(content) : content, Encoding.UTF8, "application/json"));
        }

        /// <summary>
        /// 异步POST
        /// </summary>
        /// <param name="address">提交地址</param>
        /// <param name="element">提交内容</param>
        /// <returns>返回对象</returns>
        public static Task<string> PostAsync(string address, JsonElement element)
        {
            return PostAsync(address, new StringContent(element.GetRawText(), Encoding.UTF8, "application/json"));
        }

        /// <summary>
        /// 异步POST
        /// </summary>
        /// <param name="address">提交地址</param>
        /// <param name="content">提交内容</param>
        /// <returns>返回对象</returns>
        public static Task<string> PostAsync(string address, object content)
        {
            return PostAsync(address, new StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(content), Encoding.UTF8, "application/json"));
        }

        /// <summary>
        /// 异步POST
        /// </summary>
        /// <param name="request">提交地址</param>
        /// <param name="content">提交内容</param>
        /// <returns>返回对象</returns>
        public static async Task<String> PostAsync(string address, StringContent content)
        {
            var target = new Uri(address);
            var hoster = target.Authority;
            var cancel = default(CancellationTokenSource);
            var result = default(HttpResponseMessage);
            if (!exists.TryGetValue(hoster, out System.Net.Http.HttpClient client))
            {
                client = new System.Net.Http.HttpClient(new HttpClientHandler() { UseCookies = true });
                client.DefaultRequestHeaders.Connection.Add("Keep-Alive");
                client.DefaultRequestHeaders.UserAgent.Add(new ProductInfoHeaderValue("IFP.HttpClient", "*******"));
                //预热
                //(cancel = new CancellationTokenSource()).CancelAfter(3000);
                //try
                //{
                //    result = await client.SendAsync(new HttpRequestMessage(HttpMethod.Head, target), cancel.Token);
                //}
                //catch (Exception ex)
                //{
                //    if (cancel.IsCancellationRequested) throw new Exception($"连接{address}超时。");
                //    if (!result.IsSuccessStatusCode) throw new Exception($"连接{address}失败，错误码{result.StatusCode}，{ex.Message}");
                //    throw new Exception($"连接{address}出错，{ex.Message}");
                //}
                exists.AddOrUpdate(hoster, client, (_, o) => { o.Dispose(); return client; });
            }
            (cancel = new CancellationTokenSource()).CancelAfter(30000);//发送超时
            try
            {
                result = await client.PostAsync(target, content, cancel.Token);
            }
            catch (Exception ex)
            {
                if (cancel.IsCancellationRequested) throw new Exception($"发送数据至{address}超时。");
                if (result?.IsSuccessStatusCode == false) throw new Exception($"发送数据至{address}失败，错误码{result.StatusCode}，{ex.Message}");
                throw new Exception($"发送数据至{address}出错，{ex.Message}");
            }
            (cancel = new CancellationTokenSource()).CancelAfter(3000);//接收超时
            try
            {
                var x = result.Content.ReadAsStringAsync().Result;
                return x;
            }
            catch (Exception ex)
            {
                if (cancel.IsCancellationRequested) throw new Exception($"等待{address}返回消息超时。");
                throw new Exception($"等待{address}返回消息出错，{ex.Message}");
            }
        }

        /// <summary>
        /// 根据ip地址请求IFP接口
        /// </summary>
        /// <param name="infCode">接口编号</param>
        /// <param name="responseIP">请求IP</param>
        /// <param name="responsePort">请求端口</param>
        /// <param name="requestUrl">请求的URL</param>
        /// <param name="msg">发送内容-无需包装</param>
        /// <returns>返回内容</returns>
        public static async Task<string> PostAndReturn(string infCode, string responseIP, string responsePort, string requestUrl, string msg)
        {
            IOFP_APIHelper iah = new IOFP_APIHelper();
            iah.InitHead(infCode, "", "");
            iah.Iofs.head.responseIP = responseIP;
            //iah.Iofs.head.responseObjId = responseObjId;
            iah.Iofs.msg = msg;
            iah.AddAuth();

            //发送中心数据至注册接口
            string requeststring = $"http://{iah.Iofs.head.responseIP}:{responsePort}{requestUrl}";

            Trace.TraceInformation("#Request#" + iah.Iofs.head.code + "#" + requeststring);

            string msgss = await PostAsync(requeststring, iah.Iofs);

            if (string.IsNullOrEmpty(msgss))
            {
                Trace.TraceError(iah.Iofs.head.responseIP + "未收到返回值");  //ErrorList.E0006
            }
            try
            {
                IOFP_Struct iofp_s = JsonConvert.DeserializeObject<IOFP_Struct>(msgss);
                if (!string.IsNullOrEmpty(iofp_s.head.errorCode))
                {
                    //LoggerHelper.Error(ErrorList.GetErrorItem(iofp_s.head.errorCode), iah.Iofs.head.responseIP + iofp_s.msg);
                    Trace.TraceError(iah.Iofs.head.responseIP + iofp_s.msg);
                    sendError2iofp(infCode, iofp_s.head.errorCode, iofp_s.msg, false);
                }
                Trace.TraceInformation("#Request Result#" + iah.Iofs.head.code);
                return iofp_s.msg;
            }
            catch (Exception ex)
            {
                sendError2iofp(infCode, "E0006", ErrorList.E0006.ToString() + iah.Iofs.head.responseIP + "参数错误" + ex.Message, false);
                //LoggerHelper.Error(ErrorList.E0006, iah.Iofs.head.responseIP + "参数错误", ex);
                Trace.TraceError(iah.Iofs.head.responseIP + "参数错误");
                return ErrorList.E0006.ToString();
            }
        }

        /// <summary>
        /// 通过已有路由请求IFP接口
        /// </summary>
        /// <param name="InfCode">接口编号</param>
        /// <param name="Msg">发送内容-无需包装</param>
        /// <returns>返回内容，多条，以适应群发</returns>
        public static List<string> PostAndReturn(string InfCode, string Msg)
        {
            return PostAndReturn(InfCode, Msg, false).Result;
        }

        public static List<string> PostAndReturn(string InfoCode, string Msg, string regCode)
        {
            return PostAndReturn(InfoCode, Msg, false, regCode).Result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="InfCode">接口编号</param>
        /// <param name="Msg"></param>
        /// <param name="test">是否调试</param>
        /// <param name="responseObjId">目标的regcode，如果为空，请求接口编号下的全部，否则只请求指定的那个</param>
        /// <returns></returns>
        public static async Task<List<string>> PostAndReturn(string InfCode, string Msg, Boolean test, string responseObjId = null)
        {
            List<string> l_return = new List<string>();
            IOFP_APIHelper iah = new IOFP_APIHelper();
            iah.InitHead(InfCode, "", responseObjId);

            JsonElement jo_RouteConfig = Config.RouteConfig; //COM.IFP.Common.Config.ReadJson("RouteConfig");

            if (jo_RouteConfig.ValueKind == JsonValueKind.Undefined)
            {
                jo_RouteConfig = COM.IFP.Common.Config.ReadJsonElement("RouteConfig");
                if (jo_RouteConfig.ValueKind == JsonValueKind.Undefined)
                {
                    return null;
                }
            }
            //执行标识.
            bool flag = false;
            foreach (var jo_tmp in jo_RouteConfig.GetProperty("requestList").EnumerateArray())
            {
                //如何代码不统一则不需要执行下一步操作
                if (InfCode != jo_tmp.GetProperty("code").GetString())
                {
                    continue;
                }

                foreach (var jo_tmp_tmp in jo_tmp.GetProperty("routeList").EnumerateArray())
                {
                    if (!string.IsNullOrEmpty(responseObjId)
                        && iah.Iofs.head.responseObjId != jo_tmp_tmp.GetProperty("responseObjId").GetString())
                    {
                        continue;
                    }
                    iah.Iofs.head.responseIP = jo_tmp_tmp.GetProperty("responseIP").GetString();
                    iah.Iofs.head.responseObjId = jo_tmp_tmp.GetProperty("responseObjId").GetString();
                    iah.Iofs.msg = Msg;
                    iah.AddAuth();

                    //发送中心数据至注册接口
                    string requeststring = $"http://{iah.Iofs.head.responseIP}:{jo_tmp_tmp.GetProperty("responsePort").GetInt32()}{jo_tmp_tmp.GetProperty("requestUrl").GetString()}";

                    Trace.TraceInformation("#Request#" + iah.Iofs.head.code + "#" + requeststring);

                    string msgss = await PostAsync(requeststring, iah.Iofs);


                    if (test)
                    {
                        l_return.Add(msgss);
                        continue;
                    }

                    if (string.IsNullOrEmpty(msgss))
                    {
                        //LoggerHelper.Error(ErrorList.E0006, iah.Iofs.head.responseIP + "未收到返回值");
                        Trace.TraceError(iah.Iofs.head.responseIP + "未收到返回值");
                        continue;
                    }

                    try
                    {
                        //至少调用过一次.
                        flag = true;
                        IOFP_Struct iofp_s = JsonConvert.DeserializeObject<IOFP_Struct>(msgss);
                        if (!string.IsNullOrEmpty(iofp_s.head.errorCode))
                        {
                            //LoggerHelper.Error(ErrorList.GetErrorItem(iofp_s.head.errorCode), iah.Iofs.head.responseIP + iofp_s.msg);
                            Trace.TraceError(iah.Iofs.head.responseIP + iofp_s.msg);
                            sendError2iofp(InfCode, iofp_s.head.errorCode, iofp_s.msg, false);
                            continue;
                        }
                        //PF.Log.LoggerHelper.Info("#Request Result#" + iah.Iofs.head.code);
                        Trace.TraceInformation("#Request Result#" + iah.Iofs.head.code);
                        l_return.Add(iofp_s.msg);
                    }
                    catch (Exception ex)
                    {
                        sendError2iofp(InfCode, "E0006", ErrorList.E0006.ToString() + iah.Iofs.head.responseIP + "参数错误" + ex.Message, false);
                        //LoggerHelper.Error(ErrorList.E0006, iah.Iofs.head.responseIP + "参数错误", ex);
                        Trace.TraceError(iah.Iofs.head.responseIP + "参数错误");
                        l_return.Add(ErrorList.E0006.ToString());
                    }
                }
            }

            if ((l_return == null || l_return.Count <= 0) && !flag)
            {
                LoggerHelper.Error(null, $"IFP接口{InfCode}未能成功调用，请检查路由配置。");
            }

            return l_return;
        }


        /// <summary>
        /// 发送接口错误到iofp
        /// </summary>
        /// <param name="InfCode">接口编号</param>
        /// <param name="errorCode"></param>
        /// <param name="errorInfo"></param>
        /// <param name="needSMS"></param>
        private static void sendError2iofp(string InfCode, string errorCode, string errorInfo, bool needSMS)
        {
            try
            {
                Thread t = new Thread(new send0108Thread(InfCode, errorCode, errorInfo, needSMS).sendMsg);
                t.Start();
            }
            catch (Exception ex)
            {
                //吃掉错误
            }


        }
    }

    class send0108Thread
    {
        string regCode;

        string InfCode;
        string errorCode;
        string errorInfo;
        bool needSMS;

        public send0108Thread(string infCode, string errorCode, string errorInfo, bool needSMS)
        {
            InfCode = infCode;
            this.errorCode = errorCode;
            this.errorInfo = errorInfo;
            this.needSMS = needSMS;
        }

        public void sendMsg()
        {
            try
            {
                if ("Setting.ErrorLog".Equals(InfCode) || string.IsNullOrEmpty(Config.RegCode))
                {
                    return;
                }

                IOFP_APIHelper iah = new IOFP_APIHelper();
                var jsonMsg = new Dictionary<string, object>();
                jsonMsg["errorGid"] = Guid.NewGuid().ToString("N");
                jsonMsg["regCode"] = Config.RegCode; //COM.IFP.Common.Config.regCode;
                jsonMsg["errorCode"] = errorCode;
                jsonMsg["errorInfo"] = $"接口{InfCode}错误：{errorInfo}"; ;
                jsonMsg["needSMS"] = needSMS ? 1 : 0;
                jsonMsg["errorTime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                HttpClientB.PostAndReturn("Setting.ErrorLog", Newtonsoft.Json.JsonConvert.SerializeObject(jsonMsg));
            }
            catch (Exception ex)
            {
                //吃掉错误
            }
        }
    }
}
