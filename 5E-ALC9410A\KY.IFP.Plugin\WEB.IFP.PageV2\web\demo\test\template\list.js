define(function(){
    return {
        el:"#app",
        data(){
            return {
                data:[],
                viewer:{
                    paging:{
                        page:1,
                        size:20,
                        records:0,
                    }
                }
            }
        },
        created(){
            this.updateTable()
        },
        methods:{
            updateTable(){
                return this.$api.getJSON("./data.json").then(d=>this.$set(this,"data",d))
            }
        }
    }
})