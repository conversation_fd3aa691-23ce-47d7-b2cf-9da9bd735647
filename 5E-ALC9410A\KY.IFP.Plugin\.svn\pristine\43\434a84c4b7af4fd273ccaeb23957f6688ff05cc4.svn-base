define(['require', 'controllers/base', 'jquery', 'jclass', 'iofp/common', 'iofp/api', 'platform/vue', 'util', 'tailwindcss'], function (require, base, $, jclass, iofp, API, pVue, util, tailwindcss) {
    // 创建异步组件列表，放在这里的好处是：主页面的加载不依赖子页面
    const components = pVue.createAsyncComponents({
        Regist2IofpConfig: './regist2IofpConfig.js', //对应标签regist2-iofp-config，VUE会把Regist2IofpConfig转换成全小写，大写字母全部变成小写字母并在中间加上横杠'-'
        RegistConfig: './registConfig.js',
        TestRoute: './testRoute.js',
    });
    return {
        el: '#app',
        components: components,
        data() {
            return {
                loading: false,
                showRegistProductDialog: false, // 注册配置弹窗
                showChangeConfigDialog: false, // 修改配置弹窗
                showTestRouteDialog: false, // 测试接口弹窗
                productInfo: {}, // 产品信息
                productTypeList: [
                    { id: '020601', text: '智能存查样柜' },
                    { id: '020301', text: '在线自动全水' },
                    { id: '020101', text: '汽车采样' },
                    { id: '020102', text: '火车采样' },
                    { id: '020103', text: '皮带采样' },
                    { id: '030201', text: '管控系统' },
                    { id: '030202', text: '区块控制' },
                ], // 产品类型
                databaseList: [
                    { id: '0', text: '无' },
                    { id: '1', text: 'oracle' },
                    { id: '2', text: 'sqlserver' },
                    { id: '3', text: 'access' },
                    { id: '4', text: 'SQLlite' },
                    { id: '5', text: '文件' },
                    { id: '6', text: '其他' },
                ], // 数据库列表
                selectRoute: {}, //选中的调用接口行
            };
        },
        created() {
            this.query();
        },

        methods: {
            //查询产品详细信息
            query() {
                API.GetAction('API/IFP/Client/Request/GetProductInfo', {})
                    .then((e) => {
                        // this.formData(resp);
                        this.productInfo = {};
                        this.productTypeList.map((item) => {
                            if (item.id == e.kind) {
                                e.productTypeName = item.text;
                            }
                        });
                        this.databaseList.map((item) => {
                            if (item.id == e.dbType) {
                                e.databaseName = item.text;
                            }
                        });
                        Object.assign(this.productInfo, e);
                    })
                    .catch(function (e) {
                        this.$message.warning(e);
                    });
                // F.ajax({
                //     url: '/API/IFP/Client/Request/GetProductInfo',
                //     data: {},
                //     success: function (resp) {
                //         _this.formData(resp);
                //     },
                // });
            },

            // 注册按钮
            regBtn() {
                let _this = this;
                let regCode = _this.productInfo['regCode']; // 注册码
                if (regCode) {
                    _this
                        .$confirm('该产品已经注册，是否重新注册?', '提示', {
                            cancelButtonClass: 'btn-custom-cancel float-right ml-2.5',
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning',
                        })
                        .then(() => {
                            _this.showRegistProductDialog = true;
                        })
                        .catch(() => {
                            //选否不执行
                            return;
                        });
                } else {
                }
            },

            // 路由请求按钮
            routeRequestBtn() {
                let _this = this;
                _this.loading = true;
                API.GetAction('API/IFP/Client/Request/ReqRoute', {})
                    .then((e) => {
                        _this.loading = false;
                        _this.$message.success(e);
                    })
                    .catch(function (e) {
                        _this.loading = false;
                        _this.$message.error(e);
                    });
                // F.util.showWait();
                // F.ajax({
                //     url: '/API/IFP/Client/Request/ReqRoute',
                //     data: {},
                //     success: function (resp) {
                //         F.util.hideWait();
                //         if (resp == '成功') {
                //             $.bootoast.success(resp);
                //             _this.query();
                //         } else {
                //             $.bootoast.danger(resp);
                //         }
                //     },
                //     error: function (err) {
                //         F.util.hideWait();
                //         $.bootoast.danger(err);
                //     },
                // });
            },
            // 修改配置按钮
            changeConfigBtn() {
                this.showChangeConfigDialog = true;
            },

            // 选中调用接口行
            selectRow(e) {
                this.selectRoute = {};
                Object.assign(this.selectRoute, e);
                // console.log(this.selectRoute);
            },
            // 调用接口测试
            testRoute() {
                if (Object.keys(this.selectRoute).length === 0) {
                    // $.bootoast.warning('请选择数据');
                    this.$message.warning('请选择数据');
                    return;
                }
                // var code = selectData.serviceCode;
                if (util.isEmpty(this.selectRoute.serviceCode)) {
                    this.$message.warning('没有接口编号');
                    return;
                }
                if (util.isEmpty(this.selectRoute.requestRouteProduct)) {
                    this.$message.warning('没有路由信息');
                    return;
                }
                this.showTestRouteDialog = true;
                // var win = $.showIframe({
                //     url: 'testRoute.html',
                //     title: '接口测试',
                //     width: 600,
                //     height: 400,
                //     parameter: {
                //         code: code,
                //     },
                //     onhide: function () {},
                // });
            },
            // submitTestRoute() {},
        },
    };
    return jclass(base, {
        name: 'regist2Iofp',

        bindEvent: function () {
            var _this = this;

            //注册
            _this.controls['regBtn'].bind('click', function () {
                _this.regBtn();
            });

            //路由请求
            _this.controls['lyqqBtn'].bind('click', function () {
                _this.lyqqFun();
            });
            //修改配置
            _this.controls['changeBtn'].bind('click', function () {
                _this.changeFun();
            });

            //路由测试
            _this.controls['testBtn'].bind('click', function () {
                _this.testRoute();
            });
        },

        //注册
        regBtn: function () {
            var _this = this;
            var regCode = _this.controls['regCode'].value();
            if (regCode) {
                $.confirm('该产品已经注册，是否重新注册?', function (result) {
                    if (result) {
                        _this.saveFun();
                    }
                });
            } else {
                _this.saveFun();
            }
        },
        //提交注册
        saveFun: function () {
            var _this = this;
            var win = $.showIframe({
                url: 'regist2IofpConfig.html',
                title: '注册产品',
                width: 600,
                height: 350,
                parameter: {
                    czlx: 'regBtn',
                    regCode: _this.controls['regCode'].value() || '',
                },
                onhide: function () {
                    _this.query();
                },
            });
        },
        //修改配置
        changeFun: function () {
            var _this = this;
            var win = $.showIframe({
                url: 'registConfig.html',
                title: '修改配置',
                full: true,
                parameter: {},
                onhide: function () {
                    _this.query();
                },
            });
        },
        //路由请求
        lyqqFun: function () {
            var _this = this;
            F.util.showWait();
            F.ajax({
                url: '/API/IFP/Client/Request/ReqRoute',
                data: {},
                success: function (resp) {
                    F.util.hideWait();
                    if (resp == '成功') {
                        $.bootoast.success(resp);
                        _this.query();
                    } else {
                        $.bootoast.danger(resp);
                    }
                },
                error: function (err) {
                    F.util.hideWait();
                    $.bootoast.danger(err);
                },
            });
        },

        //调用接口测试
        testRoute: function () {
            var _this = this;
            var selectData = _this.controls.requestList.getSelectRowData();
            if (selectData == null) {
                $.bootoast.warning('请选择数据');
                return;
            }
            var code = selectData.serviceCode;
            if (F.common.isEmpty(code)) {
                $.bootoast.warning('没有接口编号');
                return;
            }
            if (F.common.isEmpty(selectData.requestRouteProduct)) {
                $.bootoast.warning('没有路由信息');
                return;
            }
            var win = $.showIframe({
                url: 'testRoute.html',
                title: '接口测试',
                width: 600,
                height: 400,
                parameter: {
                    code: code,
                },
                onhide: function () {},
            });
        },

        //查询产品详细信息
        query: function () {
            var _this = this;
            F.ajax({
                url: '/API/IFP/Client/Request/GetProductInfo',
                data: {},
                success: function (resp) {
                    _this.formData(resp);
                },
            });
        },

        onLoad: function () {
            var _this = this;
            this.bindEvent();
            this.query();
        },
    });
});
