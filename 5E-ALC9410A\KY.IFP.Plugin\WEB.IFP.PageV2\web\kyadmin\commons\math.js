define(["mathjs"],function(math){
    return {
        
        /**
		 * 转数字（""、null、undefined转为0）.
		 */
        parseFloat:function(value,d){
        	if(arguments.length == 1){
        		d = 3;
        	}
        	if(value === "" || value === null || value === undefined){
            	return 0;
        	}else{
            	return parseFloat(math.eval(value).toFixed(d));
        	}
        }
    }
})