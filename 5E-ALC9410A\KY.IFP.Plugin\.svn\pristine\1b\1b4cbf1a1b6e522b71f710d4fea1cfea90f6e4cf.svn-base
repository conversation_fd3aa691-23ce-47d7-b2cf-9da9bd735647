﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace COM.IFP.Common
{
    public class LinuxFullScreen
    {
        public static void ToggleFullScreen()
        {
            // 查找 360 浏览器窗口（需安装 wmctrl）
            //string getWindowId = "wmctrl -l | grep '360 Browser' | awk '{print $1}'";
            //string windowId = ExecuteBashCommand(getWindowId).Trim();

            //if (!string.IsNullOrEmpty(windowId))
            //{
            //    // 激活窗口并发送 F11
            //    string commands = $@"
            //    xdotool windowactivate {windowId}
            //    xdotool key --window {windowId} F11
            //    sleep 0.5
            //    xdotool key --window {windowId} F11";

            //    ExecuteBashCommand(commands);
            //}
        }

        private static string ExecuteBashCommand(string command)
        {
            using (var process = new Process())
            {
                process.StartInfo.FileName = "/bin/bash";
                process.StartInfo.Arguments = $"-c \"{command}\"";
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.UseShellExecute = false;
                process.Start();

                string output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();
                return output;
            }
        }
    }
}