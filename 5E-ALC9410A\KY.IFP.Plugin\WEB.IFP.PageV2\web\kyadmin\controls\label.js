//F.controls.textbox
define(["jclass","./base.input","jquery"],function(jclass,base,$){
	var defaultOption = {
		value:""
	}
	
	
	return jclass(base,{
		option:{},
		render:function(){
			this.value(this.option.value);
		},
		_value:"",
		
		value:function(v){
			if(arguments.length>0){
				var formatter = this.controller && this.option.formatter && this.controller.getFormatter(this.option.formatter);
				var str = formatter&&formatter(v) || (v==null?"":v);
				this.$container.text(str);
				this._value = v;
			}else{
				return this._value;
			}
		},
		
		clear:function(){
			
		},
		
		focus:function(){
			
		},
		
		createContainer:function(container){
			var newContainer = $('<label class="form-label"></label>');
			this.replaceElement(container,newContainer);
			return newContainer;
		},
		
		createHtmlOption:function(container,option){
			var htmlOpt = {
				value:""
			}
			var ele = $(container);
			htmlOpt.value = ele.val()||ele.text();
			return htmlOpt;
		}
	});
});