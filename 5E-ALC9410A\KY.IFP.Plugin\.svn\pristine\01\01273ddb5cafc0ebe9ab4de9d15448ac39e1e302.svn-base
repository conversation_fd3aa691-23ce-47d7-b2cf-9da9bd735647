/*
* $.
* 
*/

define([
    "jquery","bootoast","dialog","controls/dialog.IFrameWindow","controls/modal.window",
    "css!bootstrap-css",
    "css!kjlib/bootstrap-theme-kykj/dist/css/bootstrap-theme-kykj",
    "css!kjlib/bootstrap-theme-kykj/dist/css/dicb",
],function($,bootoast,dialog){
    $.extend({
        alert:dialog.alert,
        show:dialog.show,
        confirm:dialog.confirm,
        showIframe:function(option){
            var IFrameWindow = require("controls/dialog.IFrameWindow");
            var iframe = new IFrameWindow(option);
            return iframe;
        },
        showWindow:function(option){
            var Modal = require("controls/modal.window");
            var mode = new Modal (option);
            mode.show();
        }
    });

    

    $.bootoast = function(msg,option){
        bootoast($.extend(true,{
            message: msg,
            position:'center-center',
            timeout:2
        },option));
    }
    $.each(["success","warning","danger"],function(i,item){
        $.bootoast[item]=function(msg){
            this(msg,{type:item});
        }
    });
    $.extend({
        alert:dialog.alert,
        show:dialog.show,
        confirm:dialog.confirm,
        showIframe:function(option){
            var IFrameWindow = require("controls/dialog.IFrameWindow");
            var iframe = new IFrameWindow(option);
            return iframe;
        },
        showWindow:function(option){
            var Modal = require("controls/modal.window");
            var mode = new Modal (option);
            mode.show();
        }
    });
    $.bootoast = function(msg,option){
        bootoast($.extend(true,{
            message: msg,
            position:'center-center',
            timeout:2
        },option));
    }
    $.each(["success","warning","danger"],function(i,item){
        $.bootoast[item]=function(msg,option){
            this(msg,$.extend({type:item},option));
        }
    });
})