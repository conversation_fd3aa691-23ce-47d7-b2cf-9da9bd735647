﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace KY.IFP.Service
{
    internal class Provider : ILoggerProvider, ILogger
    {
        public IDisposable BeginScope<TState>(TState state)
        {
            return this;
        }

        public ILogger CreateLogger(string categoryName)
        {
            return this;
        }

        public void Dispose()
        {

        }

        public bool IsEnabled(LogLevel logLevel)
        {
            return true;
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            var message = state.ToString();
            if (!message.EndsWith(". "))
            {
                message += message.EndsWith('.') ? " " : ". ";
            }
            switch (logLevel)
            {
                case LogLevel.Debug:
                case LogLevel.Information:
                case LogLevel.None:
                case LogLevel.Trace:
                    Console.WriteLine(message);
                    break;
                case LogLevel.Warning:
                    Trace.TraceWarning(message);
                    break;
                case LogLevel.Error:
                case LogLevel.Critical:
                    Trace.TraceError(message);
                    break;
            }
        }
    }
}
