define(["iofp/api",
    "platform/vue"], function (API, pVue) {

        const components = pVue.createAsyncComponents({
            ifpDetail: "./readConfigMx.js",//对应标签ifp-role-detail
        });
        return {
            el: "#app",
            components: components,
            data() {
                return {
                    zmList: [

                    ],
                    sjlxList: [

                    ],
                    ioShowList: [
                        { text: "不可展示", id: 0 },
                        { text: "可展示", id: 1 },
                    ],

                    filter: {
                        name: null,
                        groupCode: null,
                        block: null,
                    },

                    data: [
                        {}
                    ],
                    paging: {
                        size: 20,
                        page: 1,
                        records: 100,
                    },

                    loading: false,//
                    selectedRow: null,
                    //ctrlstate: 0,
                    editor: {
                        show: false,
                        title: '新增',
                        data: null,
                        czlx: "ck",
                    }
                }
            },

            methods: {
                indexMethod: function (index) {
                    let curpage = this.paging.page;   //单前页码，具体看组件取值
                    let limitpage = this.paging.size;    //每页条数，具体是组件取值
                    return (index + 1) + (curpage - 1) * limitpage;
                },

                onSelect: function () {

                    this.loading = true;
                    return API.GetAction("/API/IFP/www/Device/ReadConfig/GetReadConfigPageList",
                        { filter: [], paging: this.paging }).then(x => {
                            this.loading = false;
                            this.$set(this, "data", x.rows);
                            this.paging.records = x.records;

                            //if (x.success) {

                            //} else {
                            //    this.$message.error("查询失败");

                            //}
                        }).catch(e => {
                            this.loading = false;
                            this.$message.error("查询出错");
                            console.error(e);
                        });
                },

                checkSelect() {
                    if (this.selectedRow == null) {
                        this.$message.warning("请选择一行数据");
                        return false;
                    }
                    return true;
                },

                add() {
                    let data = {
                        Gid: null,		//GID
                        MachineCode: null,		//设备号
                        ReadCode: null,		//读卡器编码
                        IPaddr: null,		//网络地址
                        Port: null,		//端口号
                        SecNum: null,		//标签扇区
                        BlockNum: null,		//标签块号
                        Count: null,		//读写最大尝试次数
                        IPList: "127.0.0.1",		//绑定IP列表
                        Creator: null,		//创建人
                        CreateTime: null,		//创建时间
                        Remark: null,		//备注
                        Delt: null,		//删除标志
                        ExePath: null		//读卡程序地址
                    }
                    this.editor.title = "新增";
                    this.editor.data = data;
                    this.editor.czlx = "xz";
                    this.editor.show = true;
                },

                detail() {
                    if (this.checkSelect() == false) {
                        return;
                    }
                    this.editor.data = this.selectedRow;
                    this.editor.title = "查看";
                    this.editor.czlx = "ck";
                    this.editor.show = true;


                },
                modify() {
                    if (this.checkSelect() == false) {
                        return;
                    }
                    this.editor.data = this.selectedRow;
                    this.editor.title = "修改";
                    this.editor.czlx = "xg";
                    this.editor.show = true;

                },
                remove() {
                    if (this.checkSelect() == false) {
                        return;
                    }
                    this.$confirm('是否删除?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        API.GetAction("/API/IFP/www/Device/ReadConfig/DelReadConfig",
                            { gid: this.selectedRow.Gid }).then(x => {
                                if (x.success) {
                                    this.$message.success("删除成功");
                                    this.onSelect();
                                } else {
                                    this.$message.error("删除失败" + x.msg);
                                }

                            }).catch(e => {
                                this.$message.error("删除出错");
                                console.error(e);
                            });
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });
                    });
                    return
                },
                initData() {
                    //API.GetAction("/API/IFP/PLC/PLCPoint/GetGroupNameEnum", {}).then(x => {
                    //    this.zmList = x;
                    //}).catch(e => {
                    //    this.$message.error(e);
                    //    //console.error(e);
                    //});
                    //API.GetAction("/API/IFP/PLC/PLCPoint/GetDataTypeEnum", {}).then(x => {
                    //    this.sjlxList = x;
                    //}).catch(e => {
                    //    this.$message.error(e);
                    //    //console.error(e);
                    //});
                },

                sizeChange(v) {
                    this.paging.size = v;
                    this.onSelect();
                },

                pageChange(v) {
                    this.paging.page = v;
                    this.onSelect();
                },

                //重置
                onReset: function () {
                    ////this.filter.start = moment().startOf("day").format();
                    ////this.filter.end = moment().startOf("day").format();
                    //this.filter.name = null;
                    //this.filter.groupCode = null;
                    //this.filter.block = null;
                    this.onSelect();
                },
                //当前行变化事件
                rowClick: function (row) {
                    this.selectedRow = row;
                },
            },
            created() {
                this.initData();
                this.onReset();

            },
            mounted() {
                this.$on('opened', x => {
                    this.onReset();
                });
            }
        }
    })