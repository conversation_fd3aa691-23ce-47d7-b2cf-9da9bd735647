body.ifp{
    --bg-body:#efefef; /* 主 */
    --bg-panel:#e0e0e0; /* 板块 */
    --bg-menu-item:#f3f3f3; /* 二级菜单项 */
    --bg-menu-subcontainer:#0003; /* 二级菜单容器 */

    --color-default:#aaa;
    --color-success:green;
    --color-warn:#ffaa00;
    --color-danger:#f33;
    --color-error:red;
}

.color-default, .color-default > input {
    color: var(--color-default) !important;
}
.color-success, .color-success > input {
    color: var(--color-success) !important;
}
.color-warn, .color-warn > input {
    color: var(--color-warn) !important;
}
.color-danger, .color-danger > input {
    color: var(--color-danger) !important;
}
.color-error, .color-error > input {
    color: var(--color-error) !important;
}

body.ifp {
    background-color: var(--bg-body);
}

body.ifp .el-dialog__wrapper.subpage .el-dialog__body {
    padding:0;
}

/* el-form */

/* el-row */
form.el-form{
    margin:0 2px;
}

/* dialog 样式 */
.subpage.el-dialog.is-fullscreen,
.subpage .el-dialog.is-fullscreen {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
}
    .subpage.el-dialog.is-fullscreen .el-dialog__body,
    .subpage .el-dialog.is-fullscreen .el-dialog__body {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
    }

        .subpage>.el-dialog.is-fullscreen>.el-dialog__body>.flex-item
        .subpage.el-dialog.is-fullscreen>.el-dialog__body>.flex-item {
            height:0;
        }
        /* 印章标记 */
        .container-mark {
            position: relative;
            --mark-color-default: #aaa;
            --mark-color-success: green;
            --mark-color-warn: #ffaa00;
            --mark-color-error: red;
        }

.container-mark:before{
    user-select: none;
    text-align:center;
    font-size:24px;
    font-weight: bold;
    display:inline-block;
    position:absolute;
    right:1rem;
    top:1rem;
    z-index: 1;
    color:green;
    min-width:100px;
    padding:0.2rem 1rem;
    transform:rotate(30deg);
    transform-origin:0% 0%;
    border-radius:5px;
    content:"";
    border:4px solid var(--mark-color-default);
    color:var(--mark-color-default);
}

.container-mark.container-mark-huitui:before{
    content:"回退";
    color:var(--mark-color-warn);
    border-color:var(--mark-color-warn);
}
.container-mark.container-mark-yishenhe:before{
    content:"已审核";
    color:var(--mark-color-success);
    border-color:var(--mark-color-success);
}
.container-mark.container-mark-zuofei:before{
    content:"作废";
    color:var(--mark-color-default);
    border-color:var(--mark-color-default);
}

.flex:not(.flex-row) > .flex-item{
    min-height: 0;
}
.flex.flex-row > .flex-item{
    min-width: 0;
}

/*  */

.form-datatable.text-center tr th{
    text-align: center;
}

.form-datatable .el-input__inner{
    height:34px;
    line-height:34px;
}


.el-input.color-success .el-input__inner {
    color: var(--color-error) !important;
}