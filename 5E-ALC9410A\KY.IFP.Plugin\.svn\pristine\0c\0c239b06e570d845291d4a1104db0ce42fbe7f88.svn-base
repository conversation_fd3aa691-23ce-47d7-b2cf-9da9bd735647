﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户权限</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div id="app" class="flex padding">
        <el-form ref="form" label-width="100px">
            <el-form-item label="角色/权限">
                <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                <el-checkbox-group v-model="checkedRoles" @change="handleCheckedRoleChange">
                    <el-checkbox v-for="item in roles" :label="item.Gid" :key="item.Gid">{{item.RoleName}}</el-checkbox>
                </el-checkbox-group>

            </el-form-item>
            <el-form-item label="可用的菜单">
                <el-tag v-for="tag in availableMenu"
                        :key="tag.Gid"
                        type="success">
                    {{tag.MenuName}}
                </el-tag>
            </el-form-item>
            <el-form-item label="不可用的菜单">
                <el-tag v-for="tag in unavailableMenu"
                        :key="tag.Gid"
                        type="info">
                    {{tag.MenuName}}
                </el-tag>
            </el-form-item>
            <el-form-item>
                <el-button v-if="!$btnRight.B1" type="primary" @click="onSubmit">保存</el-button>
                <el-button @click="onCancel">取消</el-button>
            </el-form-item>
        </el-form>
    </div>
</body>
</html>