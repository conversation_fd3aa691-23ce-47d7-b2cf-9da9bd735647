//F.controls.textbox
define(["jclass","./selectorbase","jquery","configs/config.selector"],function(jclass,base,$,SelectorConfig){
	var defaultOption = {
		idfield:"id",
		textfield:"text",
		window:null,//指定弹出window parent/top
		dialogOption:{
			url:null,
			parameter:{}
		}
	}
	
	return jclass(base,{
		name:"control-selector",
		data:function(v){
			if(arguments.length=0){
				return this._data;
			}else{
				this._data = v;
			}
		},
		render:function(){
			this.base.apply(this,arguments);
			if(this.option.width){
				this.$container.width(this.option.width);
			}
		},
		value:function(v){
			var _this = this;
			var rev = this.base.apply(this,arguments);
			if(arguments.length>0){
				var txts = v;
				if(this.option.getTextById){
					txts = this.option.getTextById.call(this,v)
				}
				if(this.option.formater){
					if(typeof this.option.formater == "function"){
						txts = this.option.formater(v);
					}
					if(typeof this.option.formater == "string"){
						var formater = 	_this.controller
										&&_this.controller[this.option.formater]
										|| F.common.converter[this.option.formater]
						if(formater && typeof formater == "function"){
							txts = formater(v);
						}
					}
				}
				
				this.text(txts);
			}
			return this.idinput.val();
		},
		createDefaultOption:function(container,option){
			var rev = $.extend({},this.base.apply(this,arguments),defaultOption);
			if(option.type){
				$.extend(true,rev,SelectorConfig.get(option.type,option));
			}
			return rev;
		},
		focus:function(){
			//this.trigger("click");
		},
		init:function(){
			var _this = this;
			return this.base.apply(this,arguments)
			.then(function(){
			
				var opt = _this.option;
				opt.dialogOption = $.extend(true,opt.dialogOption,{
					onshow:function(){
						
						if(_this.trigger("onshow")===false){
							return false;
						}
						
						return true;
						//显示前
						_this.log.info("onshow");
					},
					
					onshown:function(){
						
						_this.trigger("onshown");
						//显示后
						_this.log.info("onshown");
					},
					
					onhide:function(){
						//关闭前 校验返回值
						_this.log.info("onhide");
						_this._data = _this.win.returnValue();

						if(_this.option.converter){
							_this._data= _this.option.converter(_this._data);
						}
						
						if(_this.trigger("onhide")===false){
							return false;
						}
						
						return true;
					},
					
					onhidden:function(){
						if(_this._data || _this._data===""|| _this._data===0){
							
							if(_this._data.constructor.toString() == Array.toString()){
								_this.value(F.util.getArrFromObjs(_this._data,_this.option.idfield).join(","));
								//_this.idinput.val(F.util.getArrFromObjs(_this._data,_this.option.idfield).join(","));
								//_this.textinput.val(F.util.getArrFromObjs(_this._data,_this.option.textfield).join(","));
							}else if(_this._data.constructor.toString() == Object.toString()){
								_this.value(_this._data[_this.option.idfield]);
								//_this.textinput.val(_this._data[_this.option.textfield]);
							}else if(typeof _this._data == "string"){
								_this.value(_this._data);
							}
							_this.trigger("change",_this._data);
						}
						_this.trigger("afterDialog");
						_this.textinput.focus();
						//关闭后
						//刷新当前页面代码
						_this.log.info("onhidden");
					}
				});
				
				_this.bind("click",function(){
					if(!opt.dialogOption.url){
						return $.bootoast.danger("未定义url。");
					}
					opt.dialogOption.parameter = $.extend(true,
						{},
						opt.dialogOption.parameter,
						{
							url:_this.option.url||opt.dialogOption.url,
							postData:_this.option.postData,
							selectValue:_this.value(),
							selectText:_this.text()
						}
					)
					_this.trigger("beforeDialog",_this,opt);
					var $win = opt.window && window[opt.window] || window;
					_this.win = $win.$.showIframe(opt.dialogOption);
				});

			})
		}
	});
});