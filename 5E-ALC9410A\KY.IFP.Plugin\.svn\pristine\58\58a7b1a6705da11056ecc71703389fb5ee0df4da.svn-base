(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "../utils/dom"], factory);
    }
})(function (require, exports) {
    "use strict";
    exports.__esModule = true;
    exports.getLog = exports.gracelog = exports.LOG_LEVELS = void 0;
    var dom_1 = require("../utils/dom");
    var LOG_LEVELS;
    (function (LOG_LEVELS) {
        LOG_LEVELS[LOG_LEVELS["DEBUG"] = 0] = "DEBUG";
        LOG_LEVELS[LOG_LEVELS["INFO"] = 1] = "INFO";
        LOG_LEVELS[LOG_LEVELS["WARN"] = 2] = "WARN";
        LOG_LEVELS[LOG_LEVELS["ERROR"] = 3] = "ERROR";
    })(LOG_LEVELS = exports.LOG_LEVELS || (exports.LOG_LEVELS = {}));
    window.LOGLEVEL = window.LOGLEVEL || 0;
    var PAGENAME = "";
    var slice = Array.prototype.slice;
    var timeAtStartLoadPage = Date.now();
    window.timeAtStartLoadPage = dom_1.topWindow.timeAtStartLoadPage || Date.now();
    if (dom_1.topWindow !== window) {
        PAGENAME = "[" + window.location.pathname + "] ";
    }
    function getGlobalLogLevel() {
        return window.LOGLEVEL || LOG_LEVELS.DEBUG;
    }
    /**
     * @class gracelog
     */
    var gracelog = /** @class */ (function () {
        function gracelog(name) {
            this.clazz = "Unknown";
            if (name) {
                this.clazz = name || "Unknown";
            }
        }
        gracelog.prototype._output = function (log, logLevel) {
            if (logLevel >= getGlobalLogLevel()) {
                var msg = getMessage(this.clazz, log);
                if (logLevel === 0) {
                    console.debug(msg);
                }
                if (logLevel === 1) {
                    console.log(msg);
                }
                if (logLevel === 2) {
                    console.warn(msg);
                }
                if (logLevel === 3) {
                    console.error(msg);
                }
            }
        };
        gracelog.prototype.debug = function (log) {
            this._output(log, LOG_LEVELS.DEBUG);
        };
        gracelog.prototype.info = function (log) {
            this._output(log, LOG_LEVELS.INFO);
        };
        gracelog.prototype.warn = function (log) {
            this._output(log, LOG_LEVELS.WARN);
        };
        gracelog.prototype.error = function (log) {
            this._output(log, LOG_LEVELS.ERROR);
        };
        return gracelog;
    }());
    exports.gracelog = gracelog;
    ;
    function getMessage(clz, log) {
        // output format exp: 123ms [System]这是一条测试信息
        var t0 = timeAtStartLoadPage;
        var dt = +new Date() - t0; //相对时间ms
        if (log instanceof Error) {
            var rev = dt + " ms [" + clz + "] ";
            if (log.stack) {
                rev += log.stack;
                return rev;
            }
            if (log.message) {
                rev += log.description;
            }
            if (log.number) {
                rev += log.number & 0xFFFF;
            }
            if (log.stack) {
                rev += "\n" + log.stack;
            }
            return rev;
        }
        else {
            return dt + " ms " + PAGENAME + "[" + clz + "] " + (log ? log : "");
        }
    }
    /**
     * 返回日志对象
     * <p>
     * 	如果传入了clazz，克隆返回日志对象，否则返回系统全局的
     * </p>
     * @param {String} clazz 信息产生的位置
     * @return {gracelog} gracelog类的实例
     */
    function getLog(clazz) {
        return new gracelog(clazz);
    }
    exports.getLog = getLog;
    /**
     * 初始化日志服务
     */
    function initLog() {
    }
});
//# sourceMappingURL=data:application/json;base64,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