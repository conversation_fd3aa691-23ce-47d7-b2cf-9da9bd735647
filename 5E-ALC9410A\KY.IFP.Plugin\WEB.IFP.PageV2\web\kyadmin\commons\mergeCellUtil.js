;
define([ "require"], function(require) {
	return {
        //公共调用方法 
        merger : function(gridName, CellName) {
            //得到显示到界面的id集合 
            var mya = $("#" + gridName + "").getDataIDs(); 
            //当前显示多少条 
            var length = mya.length; 
            for (var i = 0; i < length; i++) { 
                //从上到下获取一条信息 
                var before = $("#" + gridName + "").jqGrid('getRowData', mya[i]); 
                //定义合并行数 
                var rowSpanTaxCount = 1; 
                for (j = i + 1; j <= length; j++) { 
                    //和上边的信息对比 如果值一样就合并行数+1 然后设置rowspan 让当前单元格隐藏 
                    var end = $("#" + gridName + "").jqGrid('getRowData', mya[j]); 
                    if (before[CellName] == end[CellName]) { 
                        rowSpanTaxCount++; 
                        $("#" + gridName + "").setCell(mya[j], CellName, '', { display: 'none' }); 
                    } else { 
                        rowSpanTaxCount = 1; 
                        break; 
                    } 
                    $("#" + CellName + "" + mya[i] + "").attr("rowspan", rowSpanTaxCount); 
                } 
            } 
        },
        
        //设置单元格ID属性
        cellattr : function(gridName, CellName){
        	var colModel = $("#"+gridName).jqGrid("getGridParam").colModel;
        	for(var i=0; i<colModel.length; i++){
        		if(colModel[i].name==CellName){
        			$("#"+gridName).jqGrid("getGridParam").colModel[i].cellattr=function(rowId, tv, rawObject, cm, rdata) {
        				return "id=\'" + CellName + rowId + "\'"; 
        			};
        			break;
        		}
        	}
        },
        
        mergerList :function(gridName, CellNames){
        	for (var i = 0 ; i < CellNames.length; i++) {
        		this.merger(gridName, CellNames[i]);
        	}
        },
        
        cellattrList : function(gridName, CellNames){
        	for (var i = 0 ; i < CellNames.length; i++) {
        		this.cellattr(gridName, CellNames[i]);
        	}
        }
	}
});