<!DOCTYPE html>
<html lang="zh-Hans">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>标题</title>
        <style>
            #flex-demo-box {
                border: 3px solid #ccc;
                padding: 1rem;
                position: relative;
            }
            #flex-demo-box::before {
                position: absolute;
                left: 0;
                top: 0;
                content: '请将鼠标移动到此处';
            }
            #flex-demo-box:hover {
            }
            #flex-demo-box > div {
                margin: 1rem;
                padding: 1rem;
                user-select: none;
                border: 1px solid #ccc;
            }
            #flex-demo-box:hover::before {
                content: '点击鼠标右键';
            }
            #flex-demo-box.window-blur::before {
                content: '点击 style 中 flex 后的图标 element.style {display: flex; 图标 }';
            }
            #box2,
            #box1,
            #box3 {
                width: 100px;
                height: 100px;
                background: yellow;
                margin-left: 50px;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div id="flex-demo-box" style="display: flex">
            <div>1</div>
            <div>2</div>
            <div>3</div>
            <div>4</div>
            <div>5</div>
            <div>6</div>
            <div>7</div>
            <div>8</div>
            <div>9</div>
            <div>10</div>
        </div>

        <div style="display: flex">
            <textarea id="code"></textarea>
        </div>
        <script>
            var m = document.getElementById('flex-demo-box');
            var domCode = document.getElementById('code');

            const handleWindowBlur = function () {
                m.classList.add('window-blur');
            };
            const handleWindowFocus = function () {
                window.removeEventListener('blur', handleWindowBlur);
                window.removeEventListener('focus', handleWindowFocus);
                m.classList.remove('window-blur');
            };
            m.addEventListener('contextmenu', function () {
                m.classList.add('contextmenu');
                window.addEventListener('blur', handleWindowBlur);
                window.addEventListener('focus', handleWindowFocus);
            });
            var observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type == 'attributes' && mutation.attributeName == 'style') {
                        console.log('属性变化', mutation);
                        console.log('目标元素', mutation.target);
                        console.log('目标元素样式', mutation.target.style);

                        let style = mutation.target.style;
                        let code = {};
                        for (let i = 0; i < style.length; i++) {
                            code[style[i]] = style[style[i]];
                        }
                        domCode.value = JSON.stringify(code, null, 4);
                    }
                });
            });
            observer.observe(m, {
                attributes: true,
                attributeFilter: ['style'],
            });

            const app = new Proxy(
                {
                    data: {},
                    created() {},
                },
                {
                    get(target, property, receiver) {
                        return Reflect.get(target, property, receiver);
                    },
                    set(target, property, value, receiver) {
                        return Reflect.set(target, property, value, receiver);
                    },
                },
            );
        </script>
    </body>
</html>
