<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
    <meta http-equiv="expires" content="0">
    <title>camera</title>
</head>
<body controller class="flex" option="{platform:'element'}">
    <div id="app" class="flex flex-item" style="padding:8px; text-align:center;">
        <h3>播放器组件演示</h3>

        <ifp-camera class="flex-item" ref="camera1" :src="url" style="background-color:#fff;"></ifp-camera>

        <div style="font-size: 0;">
            <ifp-camera ref="camera2" src="http://*************:6380/" style="height:100px; display:inline-block;"></ifp-camera>
            <ifp-camera ref="camera3" src="http://*************:6380/" style="height:100px; display:inline-block;"></ifp-camera>
            <ifp-camera ref="camera4" :src="url" style="height:100px; display:inline-block;"></ifp-camera>
            <ifp-camera ref="camera5" src="http://*************:6380/" style="height:100px; display:inline-block;"></ifp-camera>
            <ifp-camera ref="camera6" src="http://*************:6380/" style="height:100px; display:inline-block;"></ifp-camera>
            <ifp-camera ref="camera7" src="http://*************:6380/" style="height:100px; display:inline-block;"></ifp-camera>
        </div>
        <div>
            使用API:
            <el-button @click="$refs.camera1.stop()">停止</el-button>
            <el-button @click="$refs.camera1.play()">播放</el-button>
        </div>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>