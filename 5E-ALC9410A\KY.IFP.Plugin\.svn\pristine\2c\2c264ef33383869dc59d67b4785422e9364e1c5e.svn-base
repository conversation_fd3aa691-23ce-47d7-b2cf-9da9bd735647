﻿using COM.IFP.SqlSugarN;
using SqlSugar;
using System;

namespace ORM.IFP.DbModel.SM
{
    /// <summary>
    /// 系统参数
    /// </summary>
    [SugarTable("IFP_SM_XTCS_LOG")]
    public partial class SM_XTCS_LOG
    {
        /// <summary>
        /// GID
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> Gid { get; set; }

        /// <summary>
        /// 参数编码
        /// </summary>
        [SugarColumn(ColumnName = "CSCODE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> CsCode { get; set; }

        /// <summary>
        /// 新参数值
        /// </summary>
        [SugarColumn(ColumnName = "NEWCSVALUE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> NewCsValue { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnName = "XGSJ", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> XGSJ { get; set; }

        /// <summary>
        /// 新数据源
        /// </summary>
        [SugarColumn(ColumnName = "NEWDATA", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(3000)")]
        public Field<string> NewData { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [SugarColumn(ColumnName = "XGR", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(100)")]
        public Field<string> XGR { get; set; }

        /// <summary>
        /// 老参数值
        /// </summary>
        [SugarColumn(ColumnName = "OLDCSVALUE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> OldCsValue { get; set; }

        /// <summary>
        /// 老数据源
        /// </summary>
        [SugarColumn(ColumnName = "OLDDATA", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(3000)")]
        public Field<string> OldData { get; set; }

    }


    /* 以下为对应的Json对象
	{
		GID: null,		//GID
		CSCODE: null,		//参数编码
		NEWCSVALUE: null,		//新参数值
		XGSJ: null,		//修改时间
		NEWDATA: null,		//新数据源
		XGR: null,		//修改人
		OLDCSVALUE: null,		//老参数值
		OLDDATA: null		//老数据源
	}
	*/
}
