define([ "require"],
function(require){
	return{
		
		/**
		 * 解析公式,返回参数数组和脚本……
		 */
		getFunObject : function(funStr){
			var funObj = {
					//参数数组
					paramArray : [],
					//脚本对象<替换脚本后的变量名，脚本字符>
					jsObj : {},
					//控件数组
					controlids : [],
					//替换脚本后的公式
					funStr : ''
			};
			funObj.funStr = funStr;
			funObj.paramArray = this.getFunParam(funStr);
			funObj.controlids = this.getFunParam(funStr);
			for(var i=0;i<funObj.paramArray.length;i++){
				funObj.jsObj[funObj.paramArray[i]]=funObj.paramArray[i];
			}
			//匹配js脚本（例如：$mxvolist.sum(ysl,mz)$）
			var matcherJs = /\$[\w\W\.\(\)\d]+?\$/g;
			var jsStr = matcherJs.exec(funStr);
			var i = 0;
			while(jsStr!=null) {
				var runObjStr = jsStr[0];
				//替换js脚本为变量
				var jsFun = jsStr[0];
				//解析js脚本，加入paramArray
				var matcherGridId = /[\w]+?\./g;
				var gridId = matcherGridId.exec(jsFun);
				funObj.controlids.push(gridId[0].replace(".",""));
				var matcherGridP = /[\(\,]?[\w]+?[\,\)]+?/g;
				var gridParam = matcherGridP.exec(jsFun);
				while(gridParam!=null){
					var gridParam = gridParam[0].replace(/[\(\,\)]/g,"");
					funObj.paramArray.push(gridId+gridParam);
					//计算公式要加引号，变成$testGrid.sum('piaozhong')$
					runObjStr = runObjStr.replace(gridParam,"'"+gridParam+"'");
					gridParam = matcherGridP.exec(jsFun);
				}
				funObj.jsObj[jsFun] = runObjStr;
				i++;
				jsStr = matcherJs.exec(funStr);
			}
			return funObj;
		},
		/**
		 * 获得公式中的参数，去掉了公式中的脚本（例如：$mxvolist.sum(ysl,mz)$）
		 */
		getFunParam : function(funStr){
			var paramArray = [];
			//替换公式中的脚本（例如：$mxvolist.sum(ysl,mz)$）
			funStr = " "+funStr.replace(/\$[\w\W\.\(\)\d]+?\$/g,"js_param");
			var matcher = new RegExp("[^a-z,^A-Z,^\\.][a-z,A-Z,_,0-9]{1,}","g");
			var str = matcher.exec(funStr);
			while(str!=null) {
				var paramName = str[0].replace(/[\+\-\*\/\s\(\[\{\>|<\=\!\&\|]/g, "");
				if(!F.common.stringUtil.isNumber(paramName)){
					if(paramName!="" &&  "js_param" != paramName) {
						paramArray.push(paramName);
					}
				}
				str = matcher.exec(funStr);
			}
			return paramArray;
		},
		
		/**
		 * 公式合法性判断
		 */
		pattern : function(funStr,paramNameList){
			//替换公式中的脚本（例如：$mxvolist.sum(ysl,mz)$）
			funStr = funStr.replace(/\$[\w\W\.\(\)\d]+?\$/g,"js_param");
			paramNameList.push("js_param");
			var error = "";
			error  = this.checkFhError(funStr);
			if(error!=""){
				return "未识别的符号："+error;
			}
			error  = this.checkParamError(funStr);
			if(error!=""){
				return "未识别的变量："+error;
			}
			error = this.checkKhError(funStr);
			if(error!="") {
				return "括号不匹配："+error;
			}
			error = this.checkSettlementParamError(funStr,paramNameList);
			if(error!="") {
				return "未识别的变量："+error;
			}
			return error;
		},
		/**
		 * 判断结算公式中的不能包含的符号和变量
		 * @param funStr 公式
		 * @param unImport 不能包含的符号和变量
		 * @return
		 */
		patternUnImportError : function(funStr,unImport){
			var error = "";
			error  = this.checkUnImportParamError(funStr,unImport);
			if(error!=""){
				return "禁用的变量："+error;
			}
			error  = this.checkUnImportFHError(funStr,unImport);
			if(error!=""){
				return "禁用的符号："+error;
			}
			return error;
		},
		/**
		 * 判断结算公式中的不能包含的变量
		 * @param funStr 公式
		 * @param unImport 不能包含的符号和变量
		 * @return
		 */
		checkUnImportParamError: function(funStr,unImport){
			var error = "";
			funStr = " "+funStr
			var matcher = new RegExp("[^a-z,^A-Z,^\\.][a-z,A-Z,_,0-9]{1,}","g");
			var str = matcher.exec(funStr);
			while(str!=null) {
				var paramName = str[0].replace(/[\+\-\*\/\s\(\[\{\>|<\=\!\&\|]/g, "");
				if(!F.common.stringUtil.isNumber(paramName)){
					if(paramName!="") {
						if(unImport.indexOf(paramName) != -1) {
							error += paramName + ",";
						}
					}
				}
				str = matcher.exec(funStr);
			}
			error = error.length>1?error.substring(0,error.length-1):error;
			return error;
		},
		/**
		 * 判断结算公式中的不能包含的符号
		 * @param funStr 公式
		 * @param unImport 不能包含的符号和变量
		 * @return
		 */
		checkUnImportFHError: function(funStr,unImport){
			var error = "";
			funStr = funStr.replace(/[\s]/g,"");//去空格
			var matcher = new RegExp("[^a-z,^A-Z,^_,^0-9]{1,}","g");
			var str = matcher.exec(funStr);
			while(str!=null) {
				var fhName = str[0];
				if(!F.common.stringUtil.isNumber(fhName)){
					if(fhName!="") {
						if(unImport.indexOf(fhName) != -1) {
							error += fhName + ",";
						}
					}
				}
				str = matcher.exec(funStr);
			}
			//单个状态解析，避免(rz+1)>0这种情况
			matcher = new RegExp("[^a-z,^A-Z,^_,^0-9]","g");
			var str = matcher.exec(funStr);
			while(str!=null) {
				var fhName = str[0];
				if(!F.common.stringUtil.isNumber(fhName)){
					if(fhName!="") {
						if(unImport.indexOf(fhName) != -1) {
							error += fhName + ",";
						}
					}
				}
				str = matcher.exec(funStr);
			}
			error = error.length>1?error.substring(0,error.length-1):error;
			return error;
		},
		/**
		 * 判断结算公式中的变量是否合法,变量是否是结算变量
		 * @param functionStr
		 * @return
		 */
		checkSettlementParamError : function(funStr,paramNameList){
			var error = "";
			funStr = " "+funStr
//			var paramNameList = ["jssl","htj","dcj","cbj","ysl","pz","jz","ys","ykd","cskd","rz","sf","hf","hff","lf","hrd","jqrz","jqsf","jqhf","jqhff","jqlf","jqhrd"];
			var matcher = new RegExp("[^a-z,^A-Z,^\\.][a-z,A-Z,_,0-9]{1,}","g");
			var str = matcher.exec(funStr);
			while(str!=null) {
				var paramName = str[0].replace(/[\+\-\*\/\s\(\[\{\>|<\=\!\&\|]/g, "");
				if(!F.common.stringUtil.isNumber(paramName)){
					if(paramName!="") {
						if(paramNameList.indexOf(paramName) == -1) {
							error += paramName + ",";
						}
					}
				}
				str = matcher.exec(funStr);
			}
			error = error.length>1?error.substring(0,error.length-1):error;
			return error;
		},
		
		/**
		 * 公式符号是否有问题
		 * @param funStr
		 * @return
		 */
		checkFhError : function(funStr){
			 funStr = funStr.replace(/[\s]/g,"");//去空格
			 var fhError = "";
			 //双符号在一起
			 var matcher = new RegExp("[\\+\\-\\*\\/\\<\\>\\=\\!]{2,}","g");
			 var str = matcher.exec(funStr);
			 while (str!=null) {
				 var m = new RegExp("(\\<|\\>|\\=|\\!)\\=","g");
				 if(!m.test(str)) {
					fhError += str==""?"":(str+",");
				 }
				 str = matcher.exec(funStr);
			 }
			 //非法符号过滤
			 matcher = new RegExp("[^a-z,^A-Z,^_,^0-9,^\\.,^\\(,^\\),^\\[,^\\],^\\{,^\\},^\\+,^\\-,^\\*,^\\/,^\\<,^\\>,^\\=,^\\&,^\\|,^\\!]","g");
			 str = matcher.exec(funStr);
			 while (str!=null) {
				fhError += str==""?"":(str+",");
				str = matcher.exec(funStr);
			 }
			 //&&和||后面不能是其他符号
			 matcher = new RegExp("(\\&\\&|\\|\\|)[\\&\\|\\>\\=\\<\\=\\*\\/\\)\\]\\}]{1,}","g");
			 str = matcher.exec(funStr);
			 while (str!=null) {
				 fhError += str==""?"":(str+",");
				 str = matcher.exec(funStr);
			}
			 //&&和||前面不能是其他符号
			 matcher = new RegExp("[\\&\\|\\>\\=\\<\\=\\*\\/\\(\\[\\{\\!]{1,}(\\&\\&|\\|\\|)","g");
			 str = matcher.exec(funStr);
			 while (str!=null) {
				 fhError += str==""?"":(str+",");
				 str = matcher.exec(funStr);
			}
			 //不能出现单个的=
			 matcher = new RegExp("[^\\>,^\\<,^\\=](\\=)[^\\>,^\\<,^\\=]","g");
			 str = matcher.exec(funStr);
			 while (str!=null) {
				str = str[0].replace(/[^\=]/g, "");
				fhError += str==""?"":(str+",");
				str = matcher.exec(funStr);
			}
			 //不能出现单个的&或|
			 matcher = new RegExp("[^\\&,^\\|](\\&|\\|)[^\\&,^\\|]","g");
			 str = matcher.exec(funStr);
			 while (str!=null) {
				str = str[0].replace(/[^\&\|]/g, "");
				fhError += str==""?"":(str+",");
				str = matcher.exec(funStr);
			}
			 //不能出现单个的&|或|&
			 matcher = new RegExp("[\\w]{1,}(\\|\\&|\\&\\|)[\\w]{1,}","g");
			 str = matcher.exec(funStr);
			 while (str!=null) {
				 str = str.replace(/[\w]/g, "");
				fhError += str==""?"":(str+",");
				str = matcher.exec(funStr);
			}
			 //结尾尾不能是以下符号
			 matcher = new RegExp("[\\+\\-\\*\\/\\>\\<\\=\\|\\&\\!]$","g");
			 str = matcher.exec(funStr);
			 while (str!=null) {
				 fhError += ("公式不能用'"+str+"'结尾"+",");	
				 str = matcher.exec(funStr);
			 }
			//开头不能是以下符号
			 matcher = new RegExp("^[\\+\\*\\/\\>\\<\\=\\&\\|\\!]","g");
			 str = matcher.exec(funStr);
			 while (str!=null) {
				 fhError += ("公式不能用'"+str+"'开头"+",");	
				 str = matcher.exec(funStr);
			 }
			 fhError = fhError.length>1?fhError.substring(0,fhError.length-1):fhError;
			 return fhError;
		},
		/**
		 * 括号是否匹配
		 * @param funStr
		 * @return
		 */
		checkKhError : function(funStr){
			var error = "";
			var dkhCount1 = this.countFroString(funStr, "\\{");
			var dkhCount2 = this.countFroString(funStr, "\\}");
			var zkhCount1 = this.countFroString(funStr, "\\[");
			var zkhCount2 = this.countFroString(funStr, "\\]");
			var xkhCount1 = this.countFroString(funStr, "\\(");
			var xkhCount2 = this.countFroString(funStr, "\\)");
			if(dkhCount1 != dkhCount2) {
				error = "多出" + Math.abs(dkhCount1-dkhCount2) + "个" + (dkhCount1>dkhCount2?"'{'":"'}'")+",";
			}
			if(zkhCount1 != zkhCount2) {
				error = "多出" + Math.abs(zkhCount1-zkhCount2) + "个" + (zkhCount1>zkhCount2?"'['":"']'")+",";
			}
			if(xkhCount1 != xkhCount2) {
				error = "多出" + Math.abs(xkhCount1-xkhCount2) + "个" + (xkhCount1>xkhCount2?"'('":"')'")+",";
			}
			if(error!="") {
				error = error.length>1?error.substring(0,error.length-1):error;
				return error;
			}
			var khList = this.jxKhtoList(funStr,'(',')');
			khList.push(this.jxKhtoList(funStr,'[',']'));
			khList.push(this.jxKhtoList(funStr,'{','}'));
			for(var i=0;i<khList.length;i++) {
				var s=khList[i];
				dkhCount1 = this.countFroString(s, "\\{");
				dkhCount2 = this.countFroString(s, "\\}");
				zkhCount1 = this.countFroString(s, "\\[");
				zkhCount2 = this.countFroString(s, "\\]");
				xkhCount1 = this.countFroString(s, "\\(");
				xkhCount2 = this.countFroString(s, "\\)");
				if(dkhCount1 != dkhCount2 || zkhCount1 != zkhCount2 || xkhCount1 != xkhCount2) {
					error += s + ",";
					break;
				}
			}
			error = error.length>1?error.substring(0,error.length-1):error;
			return error;
		},
		/**
		 * 解析成括号的数组
		 * @param funStr
		 * @param kh1 括号：(,[,{
		 * @param kh2 括号：),],}
		 * @return
		 */
		jxKhtoList : function(funStr,kh1,kh2){
			var khStrList = new Array();
			var fStr = funStr.replace(/[\s]/g,"");
			var word = new Array();
			var m = 0, n = 0;
			var count = 0;//表示第几个括号
			for (var i = 0; i < fStr.length; i++) {
				if (fStr.charAt(i) == kh1) {
					if (count == 0) {
						m = i;
					}
					count++;
				}
				if (fStr.charAt(i) == kh2) {
					count--;
					if (count == 0) {
						n = i;
						word.push(fStr.substring(m, n + 1));
					}
				}
			}
			for (var i=0;i<word.length;i++) {
				a = word[i]
				khStrList.push(a);
				a = a.substring(1,a.length-1);
				//a中是否包含括号
				if(a.indexOf(kh1)>0||a.indexOf(kh2)>0) {
					khStrList.push(this.jxKhtoList(a,kh1,kh2));
				}
			}
			return khStrList;
		},
		/**
		 * 校验变量,不能出现类似-a这种变量
		 * @param funStr
		 * @return
		 */
		checkParamError : function(funStr){
			var error = "";
			var fstr = " "+funStr.replace(/[\\s]/g,"")
			var regExp = new RegExp("[\\(\\[\\{\\s]-[a-z,A-Z]{1,}","g");
			var str=regExp.exec(fstr);
			while(str!=null){
				str[0] = str[0].replace(new RegExp("[\\(\\[\\{\\)\\]\\}\\s]","g"), "");
				error += str[0] == ""? "" : (str[0]+",");
				str=regExp.exec(fstr);
			}
			error = error.length>1?error.substring(0,error.length-1):error;
			return error;
		},
		/**
		 * 字符串中包含的某个字符数量
		 * @param srcStr 源字符
		 * @param subStr 寻找的字符
		 * @return 
		 */
		countFroString : function(srcStr,subStr){
			var count = 0;
			var regExp = new RegExp(subStr,"g");
			var str = regExp.exec(srcStr);
			while(str!=null){
				count++;
				str = regExp.exec(srcStr);
			}
			return count;
		},
		
		/**
		 * 根据公式列表获取公式中运算部分涉及的id
		 * @param gslist 公式列表
		 */
		getFunRunid:function(gslist){
			var _this = this;
			var retid = [];
			//运算部分list
			var runlist = $.map(gslist, function(item, i) { return item.split("=")[1]; });
			var allid = $.map(runlist,
				function(item, i) { 
					if(item.indexOf("$") > -1){
						//运算中存在 $ 符号，带公式运算
						retid = retid.concat(_this.getFunObject(item).controlids);
					}else{
						//运算中无 $ 符号，普通运算
						retid = retid.concat(item.match(/[A-Za-z]{1}\w+/g).map(function(item){return item;}));
					}
				}
			);
			return retid;
		},
		
		
		replaceByRegExp:function(str,replaceparam){
			Array.prototype.each = function(trans) {
				for (var i=0; i<this.length; i++)
					this[i] = trans(this[i], i, this);
				return this;
			};
			Array.prototype.map = function(trans) {
				return [].concat(this).each(trans);
			};
			RegExp.escape = function(str) {
				return new String(str).replace(/([.*+?^=!:${}()|[\]\/\\])/g, '\\$1');
			};
			var properties= function (obj) {
				var props = [];
				for (var p in obj) props.push(p);
				return props;
			}
			var regex = new RegExp(properties(replaceparam).map(RegExp.escape).join("|"), "g");
			return str.replace(regex, function($0) { return replaceparam[$0]; });
		}
		
	}
});