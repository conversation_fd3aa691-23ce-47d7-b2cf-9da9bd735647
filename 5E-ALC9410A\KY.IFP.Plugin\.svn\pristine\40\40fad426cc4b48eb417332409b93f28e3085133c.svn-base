﻿using COM.IFP.SqlSugarN;
using SqlSugar;
using System;

namespace ORM.IFP.DbModel.SM
{
    /// <summary>
    /// 数据表：系统参数模块
    /// </summary>
    [SugarTable("IFP_SM_XTCS")]
    public partial class SM_XTCS
    {
        /// <summary>
        /// GID
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> Gid { get; set; }

        /// <summary>
        /// 参数编码
        /// </summary>
        [SugarColumn(ColumnName = "CSCODE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> CsCode { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        [SugarColumn(ColumnName = "CSNAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> CsName { get; set; }

        /// <summary>
        /// 参数值
        /// </summary>
        [SugarColumn(ColumnName = "CSVALUE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> CsValue { get; set; }

        /// <summary>
        /// 参数类型,预留字段 // 1数值  2文本  3下拉框
        /// </summary>
        [SugarColumn(ColumnName = "LEIXING", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> LeiXing { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnName = "DANWEI", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DanWei { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "CREATETIME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> CreateTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(3000)")]
        public Field<string> REMARK { get; set; }

        /// <summary>
        /// 数据源
        /// </summary>
        [SugarColumn(ColumnName = "DATA", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(3000)")]
        public Field<string> Data { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(ColumnName = "PXH", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(100)")]
        public Field<string> Pxh { get; set; }

    }


    /* 以下为对应的Json对象
	{
		GID: null,		//GID
		CSCODE: null,		//参数编码
		CSNAME: null,		//参数名称
		CSVALUE: null,		//参数值
		LEIXING: null,		//参数类型,预留字段 // 1数值  2文本  3下拉框
		DANWEI: null,		//单位
		CREATETIME: null,		//创建时间
		REMARK: null,		//备注
		DATA: null,		//数据源
		PXH: null		//排序号
	}
	*/

    public partial class SM_XTCS
    {
        //    //------------------------查询条件-------------------------

        /// <summary>
        /// 该参数存在于哪些分组中
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string FenZuListStr { get; set; }

        //    public string CsName_LIKE { get; set; }

        /// <summary>
        /// 父节点id
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string pid { get; set; }
    }
}
