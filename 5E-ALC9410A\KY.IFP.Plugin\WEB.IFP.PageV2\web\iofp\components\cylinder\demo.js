define(["./cylinder.js"], function(ifpCyt){
    return {
        components:{ifpCyt},
        el:"#app",
        data(){
            return {
                height1:25,
                height2:10,
                height3:30,
                height4:80,
                height5:120,
                settings:{
                    colorOutside:"#00000011",
                    colorInside:"#aafc",
                    showOutside:true
                }
            }
        },
        mounted(){
            setInterval(() => {
                for(let i=0;i<5;i++){
                    this['height'+(i+1)] = Math.random()*150;
                }
            },200)
        }
    }
})