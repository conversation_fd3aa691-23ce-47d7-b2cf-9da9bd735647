﻿using COM.IFP.Common;
using ORM.IFP.www.DbModel.SM;
using System;
using System.Text.Json;

namespace API.IFP.www.sys
{
    public class BarCodePrintAPI
    {
        readonly Lazy<DAL.IFP.www.sys.BarCodePrintDAL> lazy = Entity.Create<DAL.IFP.www.sys.BarCodePrintDAL>();

        public IFP_SM_BARCODE_PRINT Select(JsonElement json)
        {
            IFP_SM_BARCODE_PRINT entity = json.GetValue<IFP_SM_BARCODE_PRINT>();
            return lazy.Value.Select(entity);
        }

        public IFP_SM_BARCODE_PRINT QueryDefault(JsonElement json)
        {
            IFP_SM_BARCODE_PRINT entity = json.GetValue<IFP_SM_BARCODE_PRINT>();
            return lazy.Value.QueryDefault(entity);
        }

        public void Save(JsonElement json)
        {
            IFP_SM_BARCODE_PRINT entity = json.GetValue<IFP_SM_BARCODE_PRINT>();
            lazy.Value.Save(entity);
        }
    }
}
