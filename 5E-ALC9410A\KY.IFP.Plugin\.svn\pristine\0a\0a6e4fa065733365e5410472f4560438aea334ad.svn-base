/**
*
* @license Guriddo jqGrid JS - v5.4.0 
* Copyright(c) 2008, <PERSON>, <EMAIL>
* 
* License: http://guriddo.net/?page_id=103334
*/
!function(a){"use strict";"function"==typeof define&&define.amd?define(["jquery","./grid.base"],a):a(jQuery)}(function(a){"use strict";a.jgrid.extend({groupingSetup:function(){return this.each(function(){var b,c,d,e=this,f=e.p.colModel,g=e.p.groupingView,h=a.jgrid.styleUI[e.p.styleUI||"jQueryUI"].grouping;if(null===g||"object"!=typeof g&&!a.isFunction(g))e.p.grouping=!1;else if(g.plusicon||(g.plusicon=h.icon_plus),g.minusicon||(g.minusicon=h.icon_minus),g.groupField.length){for(void 0===g.visibiltyOnNextGrouping&&(g.visibiltyOnNextGrouping=[]),g.lastvalues=[],g._locgr||(g.groups=[]),g.counters=[],b=0;b<g.groupField.length;b++)g.groupOrder[b]||(g.groupOrder[b]="asc"),g.groupText[b]||(g.groupText[b]="{0}"),"boolean"!=typeof g.groupColumnShow[b]&&(g.groupColumnShow[b]=!0),"boolean"!=typeof g.groupSummary[b]&&(g.groupSummary[b]=!1),g.groupSummaryPos[b]||(g.groupSummaryPos[b]="footer"),!0===g.groupColumnShow[b]?(g.visibiltyOnNextGrouping[b]=!0,a(e).jqGrid("showCol",g.groupField[b])):(g.visibiltyOnNextGrouping[b]=a("#"+a.jgrid.jqID(e.p.id+"_"+g.groupField[b])).is(":visible"),a(e).jqGrid("hideCol",g.groupField[b]));for(g.summary=[],g.hideFirstGroupCol&&a.isArray(g.formatDisplayField)&&!a.isFunction(g.formatDisplayField[0])&&(g.formatDisplayField[0]=function(a){return a}),c=0,d=f.length;c<d;c++)g.hideFirstGroupCol&&(f[c].hidden||g.groupField[0]!==f[c].name||(f[c].formatter=function(){return""})),f[c].summaryType&&(f[c].summaryDivider?g.summary.push({nm:f[c].name,st:f[c].summaryType,v:"",sd:f[c].summaryDivider,vd:"",sr:f[c].summaryRound,srt:f[c].summaryRoundType||"round"}):g.summary.push({nm:f[c].name,st:f[c].summaryType,v:"",sr:f[c].summaryRound,srt:f[c].summaryRoundType||"round"}))}else e.p.grouping=!1})},groupingPrepare:function(b,c){return this.each(function(){var d,e,f,g,h,i=this.p.groupingView,j=this,k=function(){a.isFunction(this.st)?this.v=this.st.call(j,this.v,this.nm,b):(this.v=a(j).jqGrid("groupingCalculations.handler",this.st,this.v,this.nm,this.sr,this.srt,b),"avg"===this.st.toLowerCase()&&this.sd&&(this.vd=a(j).jqGrid("groupingCalculations.handler",this.st,this.vd,this.sd,this.sr,this.srt,b)))},l=i.groupField.length,m=0;for(d=0;d<l;d++)e=i.groupField[d],g=i.displayField[d],f=b[e],h=null==g?null:b[g],null==h&&(h=f),void 0!==f&&(0===c?(i.groups.push({idx:d,dataIndex:e,value:f,displayValue:h,startRow:c,cnt:1,summary:[]}),i.lastvalues[d]=f,i.counters[d]={cnt:1,pos:i.groups.length-1,summary:a.extend(!0,[],i.summary)},a.each(i.counters[d].summary,k),i.groups[i.counters[d].pos].summary=i.counters[d].summary):"object"==typeof f||(a.isArray(i.isInTheSameGroup)&&a.isFunction(i.isInTheSameGroup[d])?i.isInTheSameGroup[d].call(j,i.lastvalues[d],f,d,i):i.lastvalues[d]===f)?1===m?(i.groups.push({idx:d,dataIndex:e,value:f,displayValue:h,startRow:c,cnt:1,summary:[]}),i.lastvalues[d]=f,i.counters[d]={cnt:1,pos:i.groups.length-1,summary:a.extend(!0,[],i.summary)},a.each(i.counters[d].summary,k),i.groups[i.counters[d].pos].summary=i.counters[d].summary):(i.counters[d].cnt+=1,i.groups[i.counters[d].pos].cnt=i.counters[d].cnt,a.each(i.counters[d].summary,k),i.groups[i.counters[d].pos].summary=i.counters[d].summary):(i.groups.push({idx:d,dataIndex:e,value:f,displayValue:h,startRow:c,cnt:1,summary:[]}),i.lastvalues[d]=f,m=1,i.counters[d]={cnt:1,pos:i.groups.length-1,summary:a.extend(!0,[],i.summary)},a.each(i.counters[d].summary,k),i.groups[i.counters[d].pos].summary=i.counters[d].summary))}),this},groupingToggle:function(b){return this.each(function(){var c=this,d=c.p.groupingView,e=b.split("_"),f=parseInt(e[e.length-2],10);e.splice(e.length-2,2);var g,h,i,j=e.join("_"),k=d.minusicon,l=d.plusicon,m=a("#"+a.jgrid.jqID(b)),n=m.length?m[0].nextSibling:null,o=a("#"+a.jgrid.jqID(b)+" span.tree-wrap-"+c.p.direction),p=function(b){var c=a.map(b.split(" "),function(a){if(a.substring(0,j.length+1)===j+"_")return parseInt(a.substring(j.length+1),10)});return c.length>0?c[0]:void 0},q=!1,r=!1,s=!!c.p.frozenColumns&&c.p.id+"_frozen",t=!!s&&a("#"+a.jgrid.jqID(b),"#"+a.jgrid.jqID(s)),u=t&&t.length?t[0].nextSibling:null;if(o.hasClass(k)){if(n)for(;n&&!(void 0!==(g=p(n.className))&&g<=f);)i=parseInt(a(n).attr("jqfootlevel"),10),r=!isNaN(i)&&(d.showSummaryOnHide&&i<=f),r||a(n).hide(),n=n.nextSibling,s&&(r||a(u).hide(),u=u.nextSibling);o.removeClass(k).addClass(l),q=!0}else{if(n)for(h=void 0;n;){if(g=p(n.className),void 0===h&&(h=void 0===g),r=a(n).hasClass("ui-subgrid")&&a(n).hasClass("ui-sg-collapsed"),void 0!==g){if(g<=f)break;g===f+1&&(r||(a(n).show().find(">td>span.tree-wrap-"+c.p.direction).removeClass(k).addClass(l),s&&a(u).show().find(">td>span.tree-wrap-"+c.p.direction).removeClass(k).addClass(l)))}else h&&(r||(a(n).show(),s&&a(u).show()));n=n.nextSibling,s&&(u=u.nextSibling)}o.removeClass(l).addClass(k)}a(c).triggerHandler("jqGridGroupingClickGroup",[b,q]),a.isFunction(c.p.onClickGroup)&&c.p.onClickGroup.call(c,b,q)}),!1},groupingRender:function(b,c,d,e){return this.each(function(){function f(a,b,c){var d,e=!1;if(0===b)e=c[a];else{var f=c[a].idx;if(0===f)e=c[a];else for(d=a;d>=0;d--)if(c[d].idx===f-b){e=c[d];break}}return e}function g(b,d,e,g,h){var i,j,l,m,n=f(b,d,e),o=k.p.colModel,p=n.cnt,q="",r=!1;for(j=g;j<c;j++)o[j].hidden?l="<td "+k.formatCol(j,1,"")+">&#160;</td>":!r&&h?(l=h,r=!0):l="<td "+k.formatCol(j,1,"")+">&#160;</td>",a.each(n.summary,function(){if(this.nm===o[j].name){m=o[j].summaryTpl?o[j].summaryTpl:"{0}","string"==typeof this.st&&"avg"===this.st.toLowerCase()&&(this.sd&&this.vd?this.v=this.v/this.vd:this.v&&p>0&&(this.v=this.v/p));try{this.groupCount=n.cnt,this.groupIndex=n.dataIndex,this.groupValue=n.value,i=k.formatter("",this.v,j,this)}catch(a){i=this.v}return l="<td "+k.formatCol(j,1,"")+">"+a.jgrid.template(m,i,n.cnt,n.dataIndex,n.displayValue)+"</td>",!1}}),q+=l;return q}var h,i,j,k=this,l=k.p.groupingView,m="",n="",o=l.groupCollapse?l.plusicon:l.minusicon,p=[],q=l.groupField.length,r=a.jgrid.styleUI[k.p.styleUI||"jQueryUI"].common;o=o+" tree-wrap-"+k.p.direction,a.each(k.p.colModel,function(a,b){var c;for(c=0;c<q;c++)if(l.groupField[c]===b.name){p[c]=a;break}});var s,t=0,u=a.makeArray(l.groupSummary);u.reverse(),s=k.p.multiselect?' colspan="2"':"",a.each(l.groups,function(f,v){if(l._locgr&&!(v.startRow+v.cnt>(d-1)*e&&v.startRow<d*e))return!0;t++,i=k.p.id+"ghead_"+v.idx,h=i+"_"+f,n="<span style='cursor:pointer;margin-right:8px;margin-left:5px;' class='"+r.icon_base+" "+o+"' onclick=\"jQuery('#"+a.jgrid.jqID(k.p.id)+"').jqGrid('groupingToggle','"+h+"');return false;\"></span>";try{j=a.isArray(l.formatDisplayField)&&a.isFunction(l.formatDisplayField[v.idx])?l.formatDisplayField[v.idx].call(k,v.displayValue,v.value,k.p.colModel[p[v.idx]],v.idx,l):k.formatter(h,v.displayValue,p[v.idx],v.value)}catch(a){j=v.displayValue}var w="";if(w=a.isFunction(l.groupText[v.idx])?l.groupText[v.idx].call(k,j,v.cnt,v.summary):a.jgrid.template(l.groupText[v.idx],j,v.cnt,v.summary),"string"!=typeof w&&"number"!=typeof w&&(w=j),"header"===l.groupSummaryPos[v.idx]?(m+='<tr id="'+h+'"'+(l.groupCollapse&&v.idx>0?' style="display:none;" ':" ")+'role="row" class= "'+r.content+" jqgroup ui-row-"+k.p.direction+" "+i+'">',m+=g(f,0,l.groups,""===s?0:1,'<td style="padding-left:'+12*v.idx+'px;"'+s+">"+n+w+"</td>"),m+="</tr>"):m+='<tr id="'+h+'"'+(l.groupCollapse&&v.idx>0?' style="display:none;" ':" ")+'role="row" class= "'+r.content+" jqgroup ui-row-"+k.p.direction+" "+i+'"><td style="padding-left:'+12*v.idx+'px;" colspan="'+(!1===l.groupColumnShow[v.idx]?c-1:c)+'">'+n+w+"</td></tr>",q-1===v.idx){var x,y,z=l.groups[f+1],A=0,B=v.startRow,C=void 0!==z?z.startRow:l.groups[f].startRow+l.groups[f].cnt;for(l._locgr&&(A=(d-1)*e)>v.startRow&&(B=A),x=B;x<C&&b[x-A];x++)m+=b[x-A].join("");if("header"!==l.groupSummaryPos[v.idx]){var D;if(void 0!==z){for(D=0;D<l.groupField.length&&z.dataIndex!==l.groupField[D];D++);t=l.groupField.length-D}for(y=0;y<t;y++)if(u[y]){var E="";l.groupCollapse&&!l.showSummaryOnHide&&(E=' style="display:none;"'),m+="<tr"+E+' jqfootlevel="'+(v.idx-y)+'" role="row" class="'+r.content+" jqfoot ui-row-"+k.p.direction+'">',m+=g(f,y,l.groups,0,!1),m+="</tr>"}t=D}}}),a("#"+a.jgrid.jqID(k.p.id)+" tbody:first").append(m),m=null})},groupingGroupBy:function(b,c){return this.each(function(){var d=this;"string"==typeof b&&(b=[b]);var e=d.p.groupingView;d.p.grouping=!0,e._locgr=!1,void 0===e.visibiltyOnNextGrouping&&(e.visibiltyOnNextGrouping=[]);var f;for(f=0;f<e.groupField.length;f++)!e.groupColumnShow[f]&&e.visibiltyOnNextGrouping[f]&&a(d).jqGrid("showCol",e.groupField[f]);for(f=0;f<b.length;f++)e.visibiltyOnNextGrouping[f]=a("#"+a.jgrid.jqID(d.p.id)+"_"+a.jgrid.jqID(b[f])).is(":visible");d.p.groupingView=a.extend(d.p.groupingView,c||{}),e.groupField=b,a(d).trigger("reloadGrid")})},groupingRemove:function(b){return this.each(function(){var c=this;if(void 0===b&&(b=!0),c.p.grouping=!1,!0===b){var d,e=c.p.groupingView;for(d=0;d<e.groupField.length;d++)!e.groupColumnShow[d]&&e.visibiltyOnNextGrouping[d]&&a(c).jqGrid("showCol",e.groupField);a("tr.jqgroup, tr.jqfoot","#"+a.jgrid.jqID(c.p.id)+" tbody:first").remove(),a("tr.jqgrow:hidden","#"+a.jgrid.jqID(c.p.id)+" tbody:first").show()}else a(c).trigger("reloadGrid")})},groupingCalculations:{handler:function(a,b,c,d,e,f){var g={sum:function(){return parseFloat(b||0)+parseFloat(f[c]||0)},min:function(){return""===b?parseFloat(f[c]||0):Math.min(parseFloat(b),parseFloat(f[c]||0))},max:function(){return""===b?parseFloat(f[c]||0):Math.max(parseFloat(b),parseFloat(f[c]||0))},count:function(){return""===b&&(b=0),f.hasOwnProperty(c)?b+1:0},avg:function(){return g.sum()}};if(!g[a])throw"jqGrid Grouping No such method: "+a;var h=g[a]();if(null!=d)if("fixed"===e)h=h.toFixed(d);else{var i=Math.pow(10,d);h=Math.round(h*i)/i}return h}},setGroupHeaders:function(b){return b=a.extend({useColSpanStyle:!1,groupHeaders:[]},b||{}),this.each(function(){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q=this,r=0,s=q.p.colModel,t=s.length,u=q.grid.headers,v=a("table.ui-jqgrid-htable",q.grid.hDiv),w=v.children("thead").children("tr.ui-jqgrid-labels:last").addClass("jqg-second-row-header"),x=v.children("thead"),y=v.find(".jqg-first-row-header"),z=a.jgrid.styleUI[q.p.styleUI||"jQueryUI"].base;q.p.groupHeader||(q.p.groupHeader=[]),q.p.groupHeader.push(b),void 0===y[0]?y=a("<tr>",{role:"row","aria-hidden":"true"}).addClass("jqg-first-row-header").css("height","auto"):y.empty();var A,B=function(a,b){var c,d=b.length;for(c=0;c<d;c++)if(b[c].startColumnName===a)return c;return-1};for(a(q).prepend(x),e=a("<tr>",{role:"row"}).addClass("ui-jqgrid-labels jqg-third-row-header"),c=0;c<t;c++)if(g=u[c].el,h=a(g),d=s[c],i={height:"0px",width:u[c].width+"px",display:d.hidden?"none":""},a("<th>",{role:"gridcell"}).css(i).addClass("ui-first-th-"+q.p.direction).appendTo(y),g.style.width="",(j=B(d.name,b.groupHeaders))>=0){for(k=b.groupHeaders[j],l=k.numberOfColumns,m=k.titleText,o=k.className||"",n=0,j=0;j<l&&c+j<t;j++)s[c+j].hidden||n++;f=a("<th>").attr({role:"columnheader"}).addClass(z.headerBox+" ui-th-column-header ui-th-"+q.p.direction+" "+o).html(m),n>0&&f.attr("colspan",String(n)),q.p.headertitles&&f.attr("title",f.text()),0===n&&f.hide(),h.before(f),e.append(g),r=l-1}else if(0===r)if(b.useColSpanStyle){var C=h.attr("rowspan")?parseInt(h.attr("rowspan"),10)+1:2;h.attr("rowspan",C)}else a("<th>",{role:"columnheader"}).addClass(z.headerBox+" ui-th-column-header ui-th-"+q.p.direction).css({display:d.hidden?"none":""}).insertBefore(h),e.append(g);else e.append(g),r--;p=a(q).children("thead"),p.prepend(y),e.insertAfter(w),v.append(p),b.useColSpanStyle&&(v.find("span.ui-jqgrid-resize").each(function(){var b=a(this).parent();b.is(":visible")&&(this.style.cssText="height: "+b.height()+"px !important; cursor: col-resize;")}),v.find("div.ui-jqgrid-sortable").each(function(){var b=a(this),c=b.parent();c.is(":visible")&&c.is(":has(span.ui-jqgrid-resize)")&&b.css("top",(c.height()-b.outerHeight())/2-4+"px")})),A=p.find("tr.jqg-first-row-header"),a(q).on("jqGridResizeStop.setGroupHeaders",function(a,b,c){A.find("th").eq(c)[0].style.width=b+"px"})})},destroyGroupHeader:function(b){return void 0===b&&(b=!0),this.each(function(){var c,d,e,f,g,h,i,j=this,k=j.grid,l=a("table.ui-jqgrid-htable thead",k.hDiv),m=j.p.colModel;if(k){for(a(this).off(".setGroupHeaders"),c=a("<tr>",{role:"row"}).addClass("ui-jqgrid-labels"),f=k.headers,d=0,e=f.length;d<e;d++){i=m[d].hidden?"none":"",g=a(f[d].el).width(f[d].width).css("display",i);try{g.removeAttr("rowSpan")}catch(a){g.attr("rowSpan",1)}c.append(g),h=g.children("span.ui-jqgrid-resize"),h.length>0&&(h[0].style.height=""),g.children("div")[0].style.top=""}a(l).children("tr.ui-jqgrid-labels").remove(),a(l).prepend(c),!0===b&&a(j).jqGrid("setGridParam",{groupHeader:null})}})}})});