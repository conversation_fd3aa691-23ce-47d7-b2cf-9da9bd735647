﻿using COM.IFP.ComputerInfo;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text.Json;

namespace API.IFP.BaseInfo
{
    public class CPUShow
    {
        /// <summary>
        /// CPU信息等展示
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, object[]> CPUInfoShow(JsonElement obj)
        {
            Dictionary<string, object[]> map = new Dictionary<string, object[]>();
            double cpu = PlatformInfo.CPUUtilization;
            double memory = MemoryEnvironment.MemoryRate();
            //磁盘总大小
            long diskSum = 0;
            //磁盘未用总量
            long diskFreeSum = 0;
            foreach (var i in DiskInfo.AllDiskInfo())
            {
                if (i.Key.Split('_')[0] == "diskFree")
                    diskFreeSum += i.Value;
                else
                    diskSum += i.Value;
            }
            map.Add("CPU占用率", new object[] { "CPU占用率", "100", cpu, 100 - cpu });
            map.Add("内存占用率", new object[] { "内存占用率", "100", memory, 100 - memory });
            map.Add("磁盘占用率", new object[] { "磁盘占用率", diskSum, diskSum - diskFreeSum, diskFreeSum });
            map.Add("系统架构：", new object[] { Enum.GetName(typeof(Architecture), RuntimeInformation.OSArchitecture) });
            map.Add("系统名称：", new object[] { RuntimeInformation.OSDescription });
            map.Add("进程架构：", new object[] { Enum.GetName(typeof(Architecture), RuntimeInformation.ProcessArchitecture) });
            if (Environment.Is64BitOperatingSystem)
                map.Add("是否64位操作系统：", new object[] { "是" });
            else
                map.Add("是否64位操作系统：", new object[] { "否" });
            map.Add("总内存：", new object[] { MemoryEnvironment.FormatSize(MemoryEnvironment.GetTotalPhys()) });
            map.Add("已使用：", new object[] { MemoryEnvironment.FormatSize(MemoryEnvironment.GetUsedPhys()) });
            map.Add("可使用：", new object[] { MemoryEnvironment.FormatSize(MemoryEnvironment.GetAvailPhys()) });
            //磁盘详细信息
            List<object> disk = new List<object>();
            foreach (var i in DiskInfo.AllDiskInfo())
            {
                if (i.Key.Split('_')[0] == "diskFree")
                {
                    disk.Add(i.Key.Split('_')[1]);
                    disk.Add(i.Value);
                }
                else
                    disk.Add(i.Value);
            }
            map.Add("磁盘详细信息", new object[] { disk });
            return map;
        }
    }
}
