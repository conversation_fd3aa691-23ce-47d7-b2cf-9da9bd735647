/*
 * 数据加载器
 * 通过配置 ywlx/postData/url
 */
"use strict";
define(function(){
	return {
		afterOnLoad:function(){
			this.base.apply(this,arguments);
			//公式计算
	    	this.formGsys();
	    	//设置显示最大最小值
	    	this.setShowVal();
		},
		// 表单控件公式运算
	    formGsys:function(){
	    	var _this = this;
	    	// 【公式运算】给当前界面所有公式用到的控件id，绑定afterchange事件
	    	if(!this.option.gslist) return;
	    	
	    	var gss = this.option.gslist;// （c=a+b）
	    	var zz = /[A-Za-z]{1}\w+/g;
    		var controljgs = {};
    		var controlgs = {};
    		var controls = {};
			
	    	for(var i =0;i<gss.length;i++){
	    		// 结果控件 id
	    		var jgid = gss[i].split("=")[0];
	    		// 公式 （a+b）
	    		var gsstr = gss[i].split("=")[1];
	    		// 公式中包含的控件id（["a","b"]）
	    		var ids = F.common.patternSysFunStr.getFunRunid([gss[i]]);
	    		
	    		ids.forEach(function(id){
	    			controls[id] = _this.controls[id];
	    			(function(gsstr,jgid){
		    			_this.bind(id,"afterchange",F.util.noloop(id+i,function(){
		    				var rev = _this.getGsjg(gsstr,zz,controls);
		    				if(rev === false){return;}
							_this.controls[jgid].value(rev);
							_this.controls[jgid].trigger("afterchange");
		    			}));
	    			})(gsstr,jgid);
		    	});
	    	}
	    },
	    getGsjg:function(_gs,zz,controls){
	    	var _this = this;
			var rev = 0;
			var err = "";
			if(_gs.indexOf("$") > -1){
				var replaceparam = {};
				var jsObj = F.common.patternSysFunStr.getFunObject(_gs).jsObj;
				for(var key in jsObj){
					var obj = jsObj[key];
					if(obj.indexOf("$") > -1){
						replaceparam[key] = eval("_this.controls."+obj.replace(/\$/g, ""));
					}else{
						replaceparam[key] = controls[obj].value();
					}
				}
				var release = F.common.patternSysFunStr.replaceByRegExp(_gs,replaceparam);
				rev = F.common.parseFloat(release);
			}
			else{
				var release = _gs.replace(zz, function(item){
					var v = controls[item].value();
					if(F.common.isEmpty(v)){
						err = "表单控件公式计算过程中，"+item+"为空";
					}
					return v;
				});
				if(err){
					_this.log.info(err);
					return false;
				}
				rev = F.common.parseFloat(release);
			}
			return rev;
	    },
	    
	    setShowVal:function(){
	    	var _this = this;
	    	var getShowvalByModel = function(type){
	    		return $.map(F.model.control.map,function(item){if(item[type]&&!item.gridId)return item})
	    	};
	    	var formShowmax = getShowvalByModel("showMax");
	    	var formShowmin = getShowvalByModel("showMin");
	    	for(var i =0;i<formShowmax.length;i++){
	    		if(_this.controls[formShowmax[i].id].value()>formShowmax[i].showMax){
		    		_this.controls[formShowmax[i].id].showval(formShowmax[i].showMax)
	    		}
	    	}
	    	for(var i =0;i<formShowmin.length;i++){
	    		if(_this.controls[formShowmin[i].id].value()<formShowmin[i].showMin){
		    		_this.controls[formShowmin[i].id].showval(formShowmin[i].showMin)
	    		}
	    	}
	    }
	    
	    
	};
});
