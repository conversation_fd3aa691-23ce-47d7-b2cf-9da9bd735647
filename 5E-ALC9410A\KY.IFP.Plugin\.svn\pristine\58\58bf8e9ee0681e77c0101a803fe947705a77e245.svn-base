﻿<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <title>角色管理</title>
    </head>
    <body controller="roleSetting.js">
        <div class="layout-h" style="height: 80px">
            <div form="forms/toolbar" class="layout-h">
                <div style="float: right">
                    <!--<span class="kjicon kjicon-wenhao" style="font-size:16px;color:#0075D3"></span>
                <span style="font-size: 14px; font-weight: bold;padding-right:5px;">
                    帮助
                </span>-->
                    <span class="dicb dicb-tuxing" style="font-size: 16px; color: #0075d3"></span>
                    <span style="font-size: 14px; font-weight: bold"> 角色管理 </span>
                </div>
            </div>
            <a id="editBtn" class="btn btn-default" control="controls/button" option="{}">编辑</a>
            <a id="editCloseBtn" class="btn btn-default" control="controls/button" option="{}">取消编辑</a>
            <a id="newBtn" class="btn btn-default" control="controls/button" option="{}">新增角色</a>
            <a id="deleteBtn" class="btn btn-default" control="controls/button" option="{}">删除角色</a>
            <a id="saveBtn" class="btn btn-default" control="controls/button" option="{}">保存角色列表</a>
            <a id="chooseBtn" class="btn btn-default" control="controls/button" option="{}">角色菜单管理</a>
        </div>

        <div class="layout-c">
            <table
                id="grid"
                control="controls/grid"
                option='{"autoresize":true, cellEdit:false,sortname:"RoleCode", sortorder : "asc",
    method:"post",
    queryParams: {
    }}'
            >
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>角色名</th>
                        <th>角色编码</th>
                        <th>角色说明</th>
                        <th>创建日期</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td option="{name:'Gid',width : 100,frozen: true,hidden:true, key:true}"></td>
                        <td option="{name:'RoleName',width : 150,align : 'center',editable:true}"></td>
                        <td option="{name:'RoleCode',width : 80,align : 'center',editable:true}"></td>
                        <td option="{name:'RoleDoc',width : 200,align : 'center',editable:true}"></td>
                        <td option="{name:'CreateTime',width : 150,align : 'center',editable:false}"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </body>
    <script src="/iofp/starter.js"></script>
</html>
