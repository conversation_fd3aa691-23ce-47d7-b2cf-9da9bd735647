.zxcsypj{
    display: flex;
    flex-direction: row;

    .zxcsypj-row{
        border-bottom:4px solid #ccc;
    }

    .zxcsypj-col{
        margin:3px;
    }
}

/*
* 无瓶
* 待化验
* 化验中
* 待回传
* 待复检
* 异常
*/

.zxcsypj-state-null,
.zxcsypj-state-empty,
.zxcsypj-state-dhy,
.zxcsypj-state-hyz,
.zxcsypj-state-dhc,
.zxcsypj-state-dfj,
.zxcsypj-state-error{
    line-height:1.1;
    box-sizing: border-box;
    width:48px;
    height:48px;
    display: inline-block;
    border-width:4px;
    border-style: solid;
    border-color:transparent;
    background-color:#bbb;
    border-radius: 50%;
    vertical-align: middle;
    opacity:inherit;
    text-align: center;
    position:relative;
    color:#fff;

    &::before{
        font-family: element-icons!important;
        position: absolute;
        left:50%;
        top:50%;
        font-size:36px;
        margin-left:-18px;
        margin-top:-20px;
    }
    &.canselect{
        cursor: pointer;
        border-color:#fff;
        border-style: dotted;
        border-color:#00f;
        // color:#00f;
        // background-color:#fff;

    }
    &.canselect:not(.selected){
        -webkit-animation: Tada 1s both infinite;
        -moz-animation: Tada 1s both infinite;
        -ms-animation: Tada 1s both infinite;
        animation: Tada 1s both infinite
    }
    
    &.selected,&.canselect:hover{
        border-color:#00f;
        border-style: solid;
    }
}
.zxcsypj-state-null{
    background-color:transparent;
}

.zxcsypj-state-empty{
    background-color:#ddd;
}

.zxcsypj-state-dhc::before{ content: "\e7ba"; }
.zxcsypj-state-dhy::before{ content: "";}
.zxcsypj-state-dfj::before{ content: "";}


.zxcsypj-state-hyz{
    background-color:#0f0;
}
.zxcsypj-state-error{
    background-color:#f00;
}


.zxcsypj-legend{
    margin-left:24px;

    > div + div {
        margin-top:16px;
    }
    .zxcsypj-state-empty,
    .zxcsypj-state-dhy,
    .zxcsypj-state-hyz,
    .zxcsypj-state-dhc,
    .zxcsypj-state-dfj,
    .zxcsypj-state-error{
        width:20px;
        height:20px;
        margin-right:3px;

        &::before{
            font-size:16px;
            margin-left:-8px;
            margin-top:-8px;
        }
    }
}

@keyframes Tada {
    0%,100% {
        border-color:#00ff
    }

    50% {
        border-color:#00f0
    }
}