define(function(){
    return {
        el:"#app",
        data(){
            return {
                url:"http://172.17.23.160:6380",
                mock:false
            }
        },
        mounted(){
            // 模拟断线
            setInterval(()=>{
                this.mock = !this.mock;
                if(this.mock){
                    this.url = "http://172.17.23.160:6380"
                }else{
                    this.url = "http://172.17.23.160"
                }
            },3000)
        },
        computed:{
            state(){
                return this.$refs?.camera1?.runing?"播放中":"暂停中"
            }
        }
    }
})