﻿using System;

namespace COM.IFP.Common
{
    /// <summary>
    /// 实体注入接口
    /// </summary>
    public class Entity
    {
        public interface Expect
        {
            /// <summary>
            /// 注入对象
            /// </summary>
            /// <typeparam name="T">对象类型，可以为基类或者接口</typeparam>
            /// <param name="target">目标类型，非空仅匹配目标类型</param>
            /// <returns>对象实例</returns>
            Lazy<T> Create<T>(string target = null);

            /// <summary>
            /// 注入对象
            /// </summary>
            /// <typeparam name="T">对象类型</typeparam>
            /// <param name="target">目标类型</param>
            /// <param name="create">构造参数</param>
            /// <returns></returns>
            Lazy<T> Create<T>(string target, params object[] create);
        }

        internal static Expect entity;
        public static Lazy<T> Create<T>(string target = null)
        {
            return entity.Create<T>(target);
        }
        public static Lazy<T> Create<T>(string target, params object[] create)
        {
            return entity.Create<T>(target, create);
        }
    }
}
