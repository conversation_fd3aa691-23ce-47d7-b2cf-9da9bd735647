define(["jclass","controller","jquery","mockjax"],function(jclass,base,$){

    var list = new Array(250).fill().map(function (item,i) {
        return {id:i,text:"text_"+i}
    })

    // select2 自动补全api
    $.mockjax({
        url: "/xxxx/select2",
        responseText: {
            results: [],
            pagination: {
                more: true
            }
        },
        response: function(req){
            //console.log(req.data);
            var page = req.data.page || 1;
            var qs = req.data.term || "";
            var rows = 10;
            var type = "query:append";//query
            var flist = list.filter(item=>item.text.indexOf(qs)>-1);
            this.responseText.results = flist.slice((page-1)*rows,page*rows);
            if(page*rows>=flist.length-1){
                this.responseText.pagination.more = false;
            }
        }
    });

    return jclass(base,{
        clearsel:function(){
            this.controls.sel3.value(null);
        },
        onLoad:function(){
            this.controls.sel2.bind("change",function () {
                console.log(this.value());
            })
            this.controls.sel2.focus();
            this.controls.grid33.value([{gid:1},{gid:2}])
        }
    })
})