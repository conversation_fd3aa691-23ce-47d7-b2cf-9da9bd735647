.ifp-page .toolbar + .margin {
    margin-top: 0;
}

.ifp-page .margin + .margin {
    margin-top: 0;
}

.ifp-panel-header > .ifp-button {
    padding: 0.6rem;
}

/* 控件宽度 */

/* .searbar 下宽度 */
.searchbar .el-input,
.searchbar .el-select,
.searchbar .el-date-editor.el-input,
.searchbar .el-form-item__content > .el-input,
.searchbar .el-form-item__content > .el-select,
.searchbar .el-form-item__content > .el-date-editor.el-input,
.searchbar .el-form-item .el-input,
.searchbar .el-form-item .el-select {
    width: 150px;
}

.searchbar .el-select > .el-input,
.searchbar .el-form-item .el-select > .el-input,
.el-form-item__content > .el-select,
.el-form-item__content > span > .el-select,
.el-form-item__content > .el-date-editor.el-input,
.el-form-item__content > .el-date-editor.el-input__inner {
    width: 100%;
}

.searchbar {
    padding-bottom: 0;
}

.searchbar .el-form-item {
    margin-bottom: 0 !important;
}

.searchbar .el-button + .el-button {
    margin-left: 0;
}

.ifp-layout-footer {
    text-align: right;
}

body {
    --border-color: #ddd;
}

.ifp-panel {
    background-color: #fff;
}

.ifp-panel-header {
    background-color: #f5f6f7;
    display: flex;
    align-content: center;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    padding: 6px 8px;
}

.ifp-panel-header .ifp-button {
    padding: 0.2rem 0.5rem;
}

.ifp-panel-header .ifp-button:not(:hover) {
    border: 1px solid transparent;
    background-color: transparent;
}

.ifp-panel-header-center {
    flex-grow: 1;
    display: flex;
    align-items: center;
    align-content: center;
}
/*
.ifp-panel-header-right{

}
*/
.ifp-panel-header-title {
    font-weight: bold;
    margin-right: 1rem;
    display: inline-block;
}

.ifp-panel.border .ifp-panel-header {
    border: 1px solid var(--border-color);
    border-bottom: 0;
}

.ifp-panel.border .ifp-panel-body {
    border: 1px solid var(--border-color);
}

.ifp-panel.theme-borderless {
    border: 0;
}

.ifp-panel.theme-borderless > .ifp-panel-header {
    padding: 8px 0;
    background-color: transparent;
}

.ifp-panel.theme-borderless > .ifp-panel-header > .ifp-panel-header-title::before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 1.8rem;
    width: 0.8rem;
    margin-right: 0.5rem;
    background-color: #3578ff;
    position: relative;
    top: -2px;
}

.ifp-panel.ifp-panel-table.flex-item {
    display: flex;
    flex-direction: column;
}

.ifp-panel.ifp-panel-table.flex-item > .ifp-panel-body {
    flex: 1;
    min-height: 0;
}

/*
.ifp-panel.border .ifp-panel-body.noborder{
    border-left:1px solid  var(--border-color);
    border-right:1px solid  var(--border-color);
}
*/

.ifp-panel.border .ifp-panel-footer {
    border: 1px solid var(--border-color);
    border-top: 0;
}

.ifp-page > .ifp-toolbar + .ifp-panel {
    margin-left: 8px;
    margin-right: 8px;
    margin-bottom: 8px;
}

.el-form.searchbar > .el-form-item {
    margin-bottom: 0px;
}

body.kyadmin-ui .el-form.searchbar {
    padding-bottom: 0;
    margin: 0;
}

.ifp-camera .ifp-camera-btn {
    cursor: pointer;
    color: #fffc;
}

.ifp-camera .ifp-camera-btn:hover {
    color: #fff;
}
