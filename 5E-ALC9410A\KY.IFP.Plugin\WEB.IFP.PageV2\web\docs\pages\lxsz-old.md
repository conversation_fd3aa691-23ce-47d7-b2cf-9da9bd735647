# 列显设置

## 1. HTML

### 1.1. 需要控制列显的表格，都需要添加属性：ref（值可随意命名），例如：
```html
<el-table ref="lxtable">
</el-table>
```

### 1.2 表格列都要添加属性:key和v-if指令
```html
<el-table-column  v-if="xslsz.tableLmtj['C2']">
  <!--内容-->
</el-table-column>
<el-table-column  v-if="xslsz.tableLmtj['C3']">
  <!--内容-->
</el-table-column>
```

其中:key值为随机数，v-if指令第1段固定写xslsz，第2段是表格ref属性值，第3段是从1开始的列标识，例如：C1, C2, C3，可以不连续，最大C999

### 1.3 工具栏添加“列显设置”按钮，并绑定@click事件，例如：onXslBtnClick

```html
<div id="app">
  <div class="toolbar">
    <el-button icon="el-icon-c-scale-to-original" @click="onXslBtnClick">列显设置</div>
  </div>
</div>
```

如果有多个TABLE需要设置列显，就放多个设置按钮。


### 1.4 页面下方添加显示列设置弹框界面的dialog（完全对着COPY就行，不用任何修改）

```html
<div id="app">
  <el-dialog class="subpage" tilte="列显设置" :visible="xslsz.show">
    <ifp-table-column-show 
      @cancel="xslsz.show=false" 
      :tableref="xslsz.tableref"
    >
    </ifp-table-column-show>
  </el-dialog>
</div>
```

## 2. JS

* 引入组件 `iofp/xslsz`
* data 中 添加 `xslsz:{}`
* created 调用 `initXslsz(this,["tableLmtj"])`
* methods 添加 
  * 取消返回`onXslBtnClick(){XSZ.onXslBtnClick(this,"tableLmtj")}`
  * 成功返回`onSuccess(){XSZ.onSuccess(this)}`

代码如下

```js
define(["iofp/xslsz"],function(XSZ){
  return {
    el:"#app",
    data(){
      return {
        xslsz:{}
      }
    },
    created(){
      XSZ.initXslsz(this,["tableLmtj"])
    }
  }
})
```

## 3. 添加 JSON 文件

如果HTML页面的名称是pclzpp.html，就在同目录下新建同名的JSON文件pclzpp.json,文件内容是根据表格的<el-table-column>组件来生成的，如果存在多级表头，JSON文件中的节点将是树形结构。

```json
{
  "xslsz":[
    {
      "tableref":"tableLmtj",
      "column":[
        { "label":"运输方式" ,"id":"C1"}
        { "label":"批次编号" ,"id":"C2"}
        { 
          "label":"质量信息" ,"id":"C17",
          "children":[
            { "label" : "热值", "id":"C18" },
            { "label" : "水分", "id":"C19" }
          ]
        }
      ]
    }
  ]
}
```

## 备注

* json 使用 utf-8 编码
* 示例页面
  * <a href="/pages/sys/liexian/index.html">/pages/sys/liexian/index.html</a>
  * <a href="/pages/mygl/pclzpp/index.html">/pages/mygl/pclzpp/index.html</a>