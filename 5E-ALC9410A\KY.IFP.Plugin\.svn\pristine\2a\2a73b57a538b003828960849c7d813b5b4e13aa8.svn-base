{"version": 3, "file": "amd-promise.js", "sourceRoot": "", "sources": ["../../../packages/kyadmin/src/utils/amd-promise.ts"], "names": [], "mappings": ";;;;;;;;;;;;IAAA,SAAgB,cAAc,CAAE,IAAa;QACzC,OAAO,IAAI,OAAO,CAAC,UAAS,OAAO,EAAC,MAAM;YACtC,OAAO,CAAC,IAAI,EAAC;gBAAS,iBAAgB;qBAAhB,UAAgB,EAAhB,qBAAgB,EAAhB,IAAgB;oBAAhB,4BAAgB;;gBAClC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrB,CAAC,EAAC,MAAM,CAAC,CAAA;QACb,CAAC,CAAC,CAAA;IACN,CAAC;IAND,wCAMC", "sourcesContent": ["export function requirePromise (deps:string[]):Promise<any[]>{\r\n    return new Promise(function(resolve,reject){\r\n        require(deps,function(...modules:any[]){\r\n            resolve(modules);\r\n        },reject)\r\n    })\r\n}"]}