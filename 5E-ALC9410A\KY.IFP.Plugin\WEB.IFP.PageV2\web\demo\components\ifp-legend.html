<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>设备图例-Test</title>
</head>
<body controller option="{platform:'element'}" style="display:none;">
    <div id="app" class="padding">
        <el-tabs v-model="tabname" @tab-click="tab_click" tab-position="right" class="flex-item">
          <el-tab-pane label="setting.yaml 编辑器">
            <h3>所有图例图标详情</h3>
            <table class="table" style="white-space:nowrap;">
                <thead>
                    <tr>
                        <th style="width:0px;">图例名称</th>
                        <th style="width:120px;">实例</th>
                        <th>
                            所有状态
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="legend in legends" @click="updateSel(legend)">
                        <td style="vertical-align: middle;">
                            <el-radio v-model="sellegendcode" :label="legend.code" name="sellegend">
                                {{legend.text}}：{{legend.code}}
                            </el-radio>
                        </td>
                        <td style="height:40px; vertical-align: middle">
                            <!-- 这里不能使用配置 -->
                            <!-- <ifp-legend :code="legend.code" :value="legend.value"></ifp-legend> -->
                            <z-legend :value="legend.value" :title="legend.text" :states="legend.states"></z-legend>
                        </td>
                        <td>
                            <el-radio-group v-model="legend.value" @change="updateSel(legend)">
                                <el-radio v-for="state in legend.states" :key="state.code" :label="state.code">
                                    {{state.code}}
                                    <i :class="state.icon" :style="'color:'+(state.color||'#000')"></i>
                                    {{state.text}}
                                </el-radio>
                            </el-radio-group>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div>
                <h3>编辑</h3>
                <template v-if="sellegend && selstate">
    
                    <ifp-panel title="图例" margin>
                        <template v-slot:header>
                            <ifp-button size="mini" round @click="onAddLegendClick">新增</ifp-button>
                            <ifp-button size="mini" round @click="deleteLegend(sellegend)">删除</ifp-button>
                        </template>
                        <ifp-form inline>
                            <ifp-form-item label="预览">
                                <i :class="selLegendDefaultState.icon" :style="selLegendDefaultState.color?'color:'+selLegendDefaultState.color:''"></i>
                                {{selLegendDefaultState.text}}
                            </ifp-form-item>
                            <ifp-form-item label="设备编码">
                                <ifp-input v-model="sellegend.code"></ifp-input>
                            </ifp-form-item>
                            <ifp-form-item label="设备名称">
                                <ifp-input v-model="sellegend.text"></ifp-input>
                            </ifp-form-item>
                            <ifp-form-item label="默认状态">
                                <ifp-select v-model="sellegend.default" :items="sellegend.states" value-key="code" label-key="text"></ifp-select>
                            </ifp-form-item>
                        </ifp-form>
                    </ifp-panel>
                    <ifp-panel title="图例明细" margin>
                        <template v-slot:header>
                            <ifp-button size="mini" round @click="onAddStateClick">新增</ifp-button>
                            <ifp-button size="mini" round @click="deleteState(sellegend,selstate)">删除</ifp-button>
                        </template>
                        <ifp-form inline>
                            <ifp-form-item label="预览">
                                <i :class="selstate.icon" :style="'color:'+(selstate.color||'#000')"></i>
                                {{selstate.text}}
                            </ifp-form-item>
                            <ifp-form-item label="状态编码">
                                <ifp-input-number v-model="selstate.code" :controls="false"></ifp-input-number>
                            </ifp-form-item>
                            <ifp-form-item label="状态名称">
                                <ifp-input v-model="selstate.text"></ifp-input>
                            </ifp-form-item>
                            <ifp-form-item label="状态图标">
                                <el-input placeholder="请输入内容" v-model="selstate.icon">
                                  <template slot="append">
                                      <ifp-button role="修改" @click="iconDialogVisible=true"></button-ifp>
                                  </template>
                                </el-input>
                            </ifp-form-item>
                            <ifp-form-item label="图标颜色">
                                <el-color-picker v-model="selstate.color" :predefine="predefineColors"
                                show-alpha></el-color-picker>
                            </ifp-form-item>
                        </ifp-form>
                    </ifp-panel>
                </template>
                <template v-else>
                    请先选择一个状态
                </template>
    
                <div>
                    <ifp-button @click="codeDialogVisible=true">查看代码</ifp-button>
                    <ifp-button @click="copy">复制代码到剪贴板</ifp-button>
                </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="ifp-legend 使用配置">
            <h3>使用配置 ifp-legend</h3>
            <ifp-legend code="RILS" :value="1"></ifp-legend>
            <ifp-legend code="RILS" :value="-1"></ifp-legend>
            <ifp-legend code="RILS" :value="0"></ifp-legend>
          </el-tab-pane>
          <el-tab-pane label="z-legend 不使用配置">
            <h3>不使用配置 z-legend</h3>
            <z-legend value="2" title="自定义设备" :states="[
                {code:'1',icon: 'el-icon-setting',text:'状态1'},
                {code:'2',icon: 'el-icon-setting',text:'状态2',color:'#ff0000'}
            ]"></z-legend>
          </el-tab-pane>
          <el-tab-pane label="ifp-legendbar">
            <h3>图例栏</h3>
            <ifp-panel-legend>
                <z-legend value="2" title="自定义设备" :states="[
                    {code:'1',icon: 'el-icon-setting',text:'状态1'},
                    {code:'2',icon: 'el-icon-setting',text:'状态2',color:'#ff0000'}
                ]"></z-legend>
                <ifp-legend code="RILS" :value="1"></ifp-legend>
                <ifp-legend code="RILS" :value="1"></ifp-legend>
            </ifp-panel-legend>
          </el-tab-pane>
        </el-tabs>
        <ifp-dialog class="subpage"
                   :visible.sync="iconDialogVisible"
                   :destroy-on-close="true"
                   fullscreen
                   title="选择图标"
                   width="1000px">
                   <ifp-menu-icon :class-name="selstate?.icon" @success="selstate.icon=$event"></ifp-menu-icon>
        </ifp-dialog>

        <ifp-dialog
                   :visible.sync="codeDialogVisible"
                   title="查看代码"
                   @open="code=getCode()"
                   width="1000px">
            <ifp-input type="textarea" :value="code" :rows="20"></ifp-input>
        </ifp-dialog>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>
