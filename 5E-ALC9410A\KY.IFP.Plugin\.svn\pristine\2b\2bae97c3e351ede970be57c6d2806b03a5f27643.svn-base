﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Data;
//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;

using System;
using System.Linq;

namespace ORM.IFP
{

    /// <summary>
    /// 基础数据_供应商表
    /// </summary>
    //public partial class YWDX4002 : IView<YWDX4002>
    //{
    //	/// <summary>
    //	/// 视图实现
    //	/// </summary>
    //	/// <param name="db"></param>
    //	/// <returns></returns>
    //	public IQueryable<YWDX4002> GetQuery(DataConnection db)
    //	{
    //		return db.GetQuery<YWDX4002, BaseData>((v, t) => v.Gid == t.Gid, SqlJoinType.Left);
    //	}
    //}

    [SugarTable("IFP_BS_YWDX4002")]
    public partial class YWDX4002 : BaseData<YWDX4002>
    {
        /// <summary>
        /// Gid 供应商编码
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public override Field<long> Gid { get; set; }

        /// <summary>
        /// 单位类型1005
        /// </summary>
        [SugarColumn(ColumnName = "TYPE1005", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> TYPE1005 { get; set; }

        /// <summary>
        /// 所属矿业集团
        /// </summary>
        [SugarColumn(ColumnName = "SSKYJT", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> SSKYJT { get; set; }

        /// <summary>
        /// 组织机构代码
        /// </summary>
        [SugarColumn(ColumnName = "ZZJGDM", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(64)")]
        public Field<string> ZZJGDM { get; set; }

        /// <summary>
        /// 经营许可证编号
        /// </summary>
        [SugarColumn(ColumnName = "JYXKZBH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(64)")]
        public Field<string> JYXKZBH { get; set; }

        /// <summary>
        /// 统一社会信用代码 资质
        /// </summary>
        [SugarColumn(ColumnName = "TYSHXYDM", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(64)")]
        public Field<string> TYSHXYDM { get; set; }

        /// <summary>
        /// 工商营业执照编号
        /// </summary>
        [SugarColumn(ColumnName = "GSYYZZBH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> GSYYZZBH { get; set; }

        /// <summary>
        /// 税务登记证代码
        /// </summary>
        [SugarColumn(ColumnName = "SWDJZDM", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> SWDJZDM { get; set; }

        /// <summary>
        /// 公司注册法人名称
        /// </summary>
        [SugarColumn(ColumnName = "FRDB", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(64)")]
        public Field<string> FRDB { get; set; }

        /// <summary>
        /// 公司注册资金
        /// </summary>
        [SugarColumn(ColumnName = "ZCZB", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(64)")]
        public Field<string> ZCZB { get; set; }

        /// <summary>
        /// 公司注册提起
        /// </summary>
        [SugarColumn(ColumnName = "CLRQ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "datetime")]
        public Field<DateTime> CLRQ { get; set; }

        /// <summary>
        /// 联系人名称
        /// </summary>
        [SugarColumn(ColumnName = "LXR", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(64)")]
        public Field<string> LXR { get; set; }

        /// <summary>
        /// 联系电话信息
        /// </summary>
        [SugarColumn(ColumnName = "LXDH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(32)")]
        public Field<string> LXDH { get; set; }

        /// <summary>
        /// 公司通讯地址
        /// </summary>
        [SugarColumn(ColumnName = "GSDZ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(256)")]
        public Field<string> GSDZ { get; set; }

        /// <summary>
        /// 供货商当地邮政编码
        /// </summary>
        [SugarColumn(ColumnName = "YZBM", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "varchar(32)")]
        public Field<string> YZBM { get; set; }

        /// <summary>
        /// 供货商传真地址
        /// </summary>
        [SugarColumn(ColumnName = "CHUANZHEN", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(64)")]
        public Field<string> CHUANZHEN { get; set; }

        /// <summary>
        /// 供货商所在省份
        /// </summary>
        [SugarColumn(ColumnName = "SSSF1015", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> SSSF1015 { get; set; }

        /// <summary>
        /// 银行开户许可证编号
        /// </summary>
        [SugarColumn(ColumnName = "YHKHXKZBH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(64)")]
        public Field<string> YHKHXKZBH { get; set; }

        /// <summary>
        /// 开户银行名称
        /// </summary>
        [SugarColumn(ColumnName = "KHYH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> KHYH { get; set; }

        /// <summary>
        /// 供货商对应的对公账户
        /// </summary>
        [SugarColumn(ColumnName = "YHZH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> YHZH { get; set; }

        /// <summary>
        /// 供货单位供货运输能力说明
        /// </summary>
        [SugarColumn(ColumnName = "FYNL", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(1024)")]
        public Field<string> FYNL { get; set; }

        /// <summary>
        /// 电厂与供货单位合作情况说明
        /// </summary>
        [SugarColumn(ColumnName = "HZQK", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "varchar(1024)")]
        public Field<string> HZQK { get; set; }

        /// <summary>
        /// 供货单位在国家的信誉，可信度说明
        /// </summary>
        [SugarColumn(ColumnName = "ZXQK", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(1024)")]
        public Field<string> ZXQK { get; set; }

        /// <summary>
        /// 供货单位类型
        /// </summary>
        [SugarColumn(ColumnName = "GHDWLX1047", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> GHDWLX1047 { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(ColumnName = "CREATOR", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> Creator { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(3000)")]
        public Field<string> REMARK { get; set; }
    }

    //[Table("IFP_BS_YWDX4002")]
    //public partial class YWDX4002 : BaseData<YWDX4002>
    //{
    //	/// <summary>
    //	/// Gid 供应商编码
    //	/// </summary>
    //	[Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.Int64, DbType = "bigint")]
    //	public override Field<long> Gid { get; set; }

    //	/// <summary>
    //	/// 单位类型1005
    //	/// </summary>
    //	[Column(Name = "TYPE1005", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> TYPE1005 { get; set; }

    //	/// <summary>
    //	/// 所属矿业集团
    //	/// </summary>
    //	[Column(Name = "SSKYJT", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //	public Field<string> SSKYJT { get; set; }

    //	/// <summary>
    //	/// 组织机构代码
    //	/// </summary>
    //	[Column(Name = "ZZJGDM", DataType = DataType.NVarChar, DbType = "nvarchar(64)")]
    //	public Field<string> ZZJGDM { get; set; }

    //	/// <summary>
    //	/// 经营许可证编号
    //	/// </summary>
    //	[Column(Name = "JYXKZBH", DataType = DataType.NVarChar, DbType = "nvarchar(64)")]
    //	public Field<string> JYXKZBH { get; set; }

    //	/// <summary>
    //	/// 统一社会信用代码 资质
    //	/// </summary>
    //	[Column(Name = "TYSHXYDM", DataType = DataType.NVarChar, DbType = "nvarchar(64)")]
    //	public Field<string> TYSHXYDM { get; set; }

    //	/// <summary>
    //	/// 工商营业执照编号
    //	/// </summary>
    //	[Column(Name = "GSYYZZBH", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //	public Field<string> GSYYZZBH { get; set; }

    //	/// <summary>
    //	/// 税务登记证代码
    //	/// </summary>
    //	[Column(Name = "SWDJZDM", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //	public Field<string> SWDJZDM { get; set; }

    //	/// <summary>
    //	/// 公司注册法人名称
    //	/// </summary>
    //	[Column(Name = "FRDB", DataType = DataType.NVarChar, DbType = "nvarchar(64)")]
    //	public Field<string> FRDB { get; set; }

    //	/// <summary>
    //	/// 公司注册资金
    //	/// </summary>
    //	[Column(Name = "ZCZB", DataType = DataType.NVarChar, DbType = "nvarchar(64)")]
    //	public Field<string> ZCZB { get; set; }

    //	/// <summary>
    //	/// 公司注册提起
    //	/// </summary>
    //	[Column(Name = "CLRQ", DataType = DataType.DateTime, DbType = "date")]
    //	public Field<DateTime> CLRQ { get; set; }

    //	/// <summary>
    //	/// 联系人名称
    //	/// </summary>
    //	[Column(Name = "LXR", DataType = DataType.NVarChar, DbType = "nvarchar(64)")]
    //	public Field<string> LXR { get; set; }

    //	/// <summary>
    //	/// 联系电话信息
    //	/// </summary>
    //	[Column(Name = "LXDH", DataType = DataType.NVarChar, DbType = "nvarchar(32)")]
    //	public Field<string> LXDH { get; set; }

    //	/// <summary>
    //	/// 公司通讯地址
    //	/// </summary>
    //	[Column(Name = "GSDZ", DataType = DataType.NVarChar, DbType = "nvarchar(256)")]
    //	public Field<string> GSDZ { get; set; }

    //	/// <summary>
    //	/// 供货商当地邮政编码
    //	/// </summary>
    //	[Column(Name = "YZBM", DataType = DataType.NVarChar, DbType = "varchar(32)")]
    //	public Field<string> YZBM { get; set; }

    //	/// <summary>
    //	/// 供货商传真地址
    //	/// </summary>
    //	[Column(Name = "CHUANZHEN", DataType = DataType.NVarChar, DbType = "nvarchar(64)")]
    //	public Field<string> CHUANZHEN { get; set; }

    //	/// <summary>
    //	/// 供货商所在省份
    //	/// </summary>
    //	[Column(Name = "SSSF1015", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> SSSF1015 { get; set; }

    //	/// <summary>
    //	/// 银行开户许可证编号
    //	/// </summary>
    //	[Column(Name = "YHKHXKZBH", DataType = DataType.NVarChar, DbType = "nvarchar(64)")]
    //	public Field<string> YHKHXKZBH { get; set; }

    //	/// <summary>
    //	/// 开户银行名称
    //	/// </summary>
    //	[Column(Name = "KHYH", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //	public Field<string> KHYH { get; set; }

    //	/// <summary>
    //	/// 供货商对应的对公账户
    //	/// </summary>
    //	[Column(Name = "YHZH", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //	public Field<string> YHZH { get; set; }

    //	/// <summary>
    //	/// 供货单位供货运输能力说明
    //	/// </summary>
    //	[Column(Name = "FYNL", DataType = DataType.NVarChar, DbType = "nvarchar(1024)")]
    //	public Field<string> FYNL { get; set; }

    //	/// <summary>
    //	/// 电厂与供货单位合作情况说明
    //	/// </summary>
    //	[Column(Name = "HZQK", DataType = DataType.NVarChar, DbType = "varchar(1024)")]
    //	public Field<string> HZQK { get; set; }

    //	/// <summary>
    //	/// 供货单位在国家的信誉，可信度说明
    //	/// </summary>
    //	[Column(Name = "ZXQK", DataType = DataType.NVarChar, DbType = "nvarchar(1024)")]
    //	public Field<string> ZXQK { get; set; }

    //	/// <summary>
    //	/// 供货单位类型
    //	/// </summary>
    //	[Column(Name = "GHDWLX1047", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> GHDWLX1047 { get; set; }

    //	/// <summary>
    //	/// 创建人
    //	/// </summary>
    //	[Column(Name = "CREATOR", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //	public Field<string> Creator { get; set; }

    //	/// <summary>
    //	/// 备注
    //	/// </summary>
    //	[Column(Name = "REMARK", DataType = DataType.NVarChar, DbType = "nvarchar(3000)")]
    //	public Field<string> REMARK { get; set; }

    //}
}
