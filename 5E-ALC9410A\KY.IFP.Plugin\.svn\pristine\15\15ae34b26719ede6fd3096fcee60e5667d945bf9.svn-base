﻿using KY.IFP.Runtime;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;

namespace KY.IFP.Service
{
    /// <summary>
    /// 服务组件
    /// </summary>
    public class Server
    {
        private readonly FileExtensionContentTypeProvider mime;

        /// <summary>
        /// 监听端口号
        /// </summary>
        public int Port { get; set; } = 80;
        /// <summary>
        /// 网站根目录
        /// </summary>
        public string Root { get; set; } = ".";
        ///// <summary>
        ///// 资源根目录
        ///// </summary>
        //public string Page { get; private set; }
        /// <summary>
        /// 接口根目录
        /// </summary>
        public string Rest { get; set; } = "api";
        /// <summary>
        /// 默认主页面
        /// </summary>
        public string Home { get; set; } = "web/index.html";

        /// <summary>
        /// 静态目录集，根目录path为""而非"/"
        /// </summary>
        public List<(string path, string fold)> Path { get; } = new();
        /// <summary>
        /// 文件类型值
        /// </summary>
        public IDictionary<string, string> Mime { get => mime.Mappings; }

        /// <summary>
        /// 构建服务
        /// </summary>
        public Server()
        {
            mime = new FileExtensionContentTypeProvider();
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        /// <param name="func">前置委托</param>
        public void Start(Func<HttpContext, bool> func = null)
        {
            if (IPGlobalProperties.GetIPGlobalProperties().GetActiveTcpListeners().Contains(new System.Net.IPEndPoint(System.Net.IPAddress.Any, Port)))
            {
                throw new Exception($"端口{Port}被占用。");
            }

            var builder = WebApplication.CreateBuilder();

            // 配置日志
            builder.Logging.ClearProviders();
            builder.Logging.AddProvider(new Provider());

            // 配置服务
            //builder.Services.AddControllers();    // 2024-12-11添加这行
            builder.Services.Configure<KestrelServerOptions>(sys => sys.AllowSynchronousIO = true);
            builder.Services.Configure<IISServerOptions>(sys => sys.AllowSynchronousIO = true);
            builder.WebHost.UseUrls($"http://*:{Port}");

            var app = builder.Build();

            // 添加路由中间件
            //app.UseRouting();    // 添加这行

            // API 端点配置
            //app.UseEndpoints(endpoints =>    // 添加这个块
            //{
            //    endpoints.MapControllers();
            //});

            // 配置中间件管道
            app.MapWhen(x =>
            {
                var segments = x.Request.Path.Value.Split('/', StringSplitOptions.RemoveEmptyEntries);
                var filename = segments.Last();
                var urltoken = default(string);
                if (segments[0].Equals("identity", StringComparison.OrdinalIgnoreCase) && segments[1].Length == 32)
                {
                    //当网址的第1段为identity且第2段长度为32时，初略认为第2段为identity
                    urltoken = segments[1];
                    //还原地址，移除identity和guid
                    x.Request.Path = $"/{string.Join('/', segments[2..])}";
                }
                var identity = x.Request.Cookies["identity"];  //优先从Cookie取identity
                if (string.IsNullOrWhiteSpace(identity))
                {
                    identity = x.Request.Headers["identity"].FirstOrDefault();   //其次从Header取identity
                    if (string.IsNullOrWhiteSpace(identity))
                    {
                        if (!string.IsNullOrWhiteSpace(urltoken))    //再次从UToken里取identity
                        {
                            identity = urltoken;
                        }
                        else //if (filename.Contains('.') && filename.EndsWith(".html", StringComparison.OrdinalIgnoreCase))
                        {
                            //html和接口才进行identity检测和上下文建立
                            identity = Guid.NewGuid().ToString("N");
                            //在地址前附加特征字符identity和对应的标识guid并重定向
                            x.Response.Cookies.Append("identity", identity, new CookieOptions() { 
                                HttpOnly = true ,
                                MaxAge = TimeSpan.FromDays(30)
                            });   //依然尝试写入Cookie
                            x.Response.Redirect($"/identity/{identity}{x.Request.Path}{x.Request.QueryString}");    //重定向并附加UToken
                            x.Response.StartAsync().Wait();
                            return false;
                        }
                    }
                }
                if (!string.IsNullOrWhiteSpace(identity))
                {
                    var basics = new Basics()
                    {
                        Period= new TimeSpan(days: 1, hours: 3, minutes: 0, seconds: 0),
                        Unique = identity,
                        Listen = x.Connection.LocalIpAddress + ":" + x.Connection.LocalPort,
                        Remote = x.Connection.RemoteIpAddress + ":" + x.Connection.RemotePort,
                        Viewer = x.Request.Headers["User-Agent"],
                        Agency = new Agency.Content(x)
                    };
                    basics.Extend = x;
                    Caches.Build(basics);
                }
                //执行
                return func == null ? true : func(x);
            }, app =>
            {
                app.MapWhen(x =>
                {
                    if (!x.Request.Method.Equals("GET", StringComparison.OrdinalIgnoreCase)) return false;
                    if (string.IsNullOrWhiteSpace(System.IO.Path.GetExtension(x.Request.Path))) return false;
                    if (x.Request.Headers["Upgrade"].Contains("websocket", StringComparer.OrdinalIgnoreCase)) return false;
                    var path = x.Request.Path.Value;
                    if (path == "/") x.Request.Path = new PathString(path = Home);
                    path = ";" + x.Request.Path.Value.Split('/').FirstOrDefault() + ";";
                    if ((";" + Rest.Trim(';') + ";").Contains(path, StringComparison.OrdinalIgnoreCase)) return false;
                    return true;
                }, app =>
                {
                    foreach (var (path, fold) in Path)
                    {
                        try
                        {
                            app.UseStaticFiles(new StaticFileOptions()
                            {
                                RequestPath = new PathString(path),
                                FileProvider = new PhysicalFileProvider(System.IO.Path.GetFullPath(fold)),
                                ContentTypeProvider = mime,
                                ServeUnknownFileTypes = true,
                                DefaultContentType = "application/octet-stream"
                            });
                        }
                        catch(Exception ex)
                        {

                        }
                    }
                });

                app.UseWebSockets(new WebSocketOptions() { KeepAliveInterval = TimeSpan.FromSeconds(120) });
                app.UseMiddleware<Handler>(Port, Root);
            });
            app.Run();
        }
    }
}
