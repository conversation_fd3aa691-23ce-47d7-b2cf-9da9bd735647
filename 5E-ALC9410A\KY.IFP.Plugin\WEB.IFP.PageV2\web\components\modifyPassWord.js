define(["layer", "util", "iofp/services/service.user", "iofp/common", "crypto-js/md5"], function (layer, util, userService, common, MD5){

    return function(){
        return common.getLoginUser().then(user => {

            var hasReturn = false;
            layer.open({
                type: 1,
                id:"panelModifyPassword",
                skin: 'layui-layer-lan',
                title:"修改密码",
                area: ['400px', '320px'], //宽高
                btn:["确定","取消"],
                content: '<form autocomplete="off"><div id="panelModifyPasswordContent" class="form-horizontal" style="padding:10px;overflow: hidden;">'
                    + '<div class="form-group">'
                    + '<label  class="col-sm-3 control-label">用户名</label>'
                    + '<div class="col-sm-8">'
                    + '<input id="mp_name" value="' + user.UsiName +'" readonly="readonly" type="text" style="width:100%;" class="form-control" placeholder="用户名">'
                    + '</div>'
                    + '</div>'

                    +'<div class="form-group">'
                    +'<label  class="col-sm-3 control-label">旧密码</label>'
                    + '<div class="col-sm-8">'
                    + '<input id="userId"   type="text" style="width:100%;display: none;" class="form-control"   placeholder="浏览器记住密码解决办法">'
                    + '<input id="password"   type="password" style="width:100%;display: none;" class="form-control"   placeholder="浏览器记住密码解决办法">'
                    + '<input id="mp_old"   type="password" style="width:100%;" class="form-control"  placeholder="旧密码">'
                        +'</div>'
                    +'</div>'
                    +'<div class="form-group">'
                        +'<label  class="col-sm-3 control-label">新密码</label>'
                        +'<div class="col-sm-8">'
                            +'<input id="mp_new"  type="password" style="width:100%;" class="form-control"  placeholder="新密码">'
                        +'</div>'
                    +'</div>'
                    +'<div class="form-group">'
                        +'<label  class="col-sm-3 control-label">重新输入</label>'
                        +'<div class="col-sm-8">'
                            +'<input id="mp_new2"  type="password" style="width:100%;" class="form-control" placeholder="重新输入">'
                        +'</div>'
                    +'</div>'
                +'</div></form>',
                btn1:function(index){
                    var oldp = $('#mp_old').val();
                    var newp = $('#mp_new').val();
                    var newp2 = $('#mp_new2').val();
                    var  formValidate =true;
                    var strValidate ="";
                
                    if(newp != newp2) {	
                    strValidate = "输入的密码不一致，请重新输入.";
                    formValidate = false;
                    }

                    if(oldp == newp) {							
                    formValidate = false;							
                    strValidate = "原密码和新密码不能相同,请修改.";
                    }
                    if(util.isEmpty(newp2)) {
                        formValidate = false;							
                        strValidate = "重新输入密码不能为空.";
                    }
                    if(util.isEmpty(newp)) {
                        formValidate = false;							
                        strValidate = "新密码不能为空.";
                    }
                    if(util.isEmpty(oldp)) {
                        formValidate = false;							
                        strValidate = "旧密码不能为空.";
                    }
                    if(formValidate) {
                        userService.modifyPassword({
                            userId: user.UsiLoginName,
                            passWord: MD5(oldp).toString(),
                            newPassWord: MD5(newp).toString()
                        }).then(function (data) {
                            if (data.success) {
                                layer.alert(data.data || "密码修改成完成");
                                layer.close(index);
                                //setTimeout(()=> {
                                    
                                //    hasReturn = true;
                                //    resolve(data);
                                //}, 1000);
                               
                            } else {
                                layer.alert(data.data);
                            }
                        }).catch(function(err){
                            layer.alert(err);
                        })
                    } else {
                        layer.alert(strValidate);
                    }
                    return false;
                },
                btn2:function(){
                },
                success:function(){
                },
                cancel: function(){ 
                  //右上角关闭回调
                  
                  //return false 开启该代码可禁止点击该按钮关闭
                }
            });
        })
    };
})