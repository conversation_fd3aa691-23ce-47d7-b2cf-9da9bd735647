﻿//using COM.IFP.Client;
////using COM.IFP.LinqDB;
//using COM.IFP.SqlSugarN;
//using Newtonsoft.Json.Linq;
//using ORM.IFP.www.ViewModel;
//using SqlSugar;
//using System;
//using System.Collections.Generic;
//using System.Data;
//using System.Linq;
//using System.Text.Json;

//namespace API.IFP.Client.Setting
//{
//    public class TaskWarnCmd : IReceive
//    {
//        public IOFP_Struct ReceiveRun(IOFP_Struct iofp)
//        {
//            JsonElement jo_center = JsonDocument.Parse(iofp.msg).RootElement;

//            string roles = jo_center.GetProperty("roles").GetString();

//            List<TaskWarnModel> target = new List<TaskWarnModel>();

//            //using (DataConnection db = DB.Create())
//            using (SqlSugarClient db = DB.Create())
//            {
//                List<IFP_SM_YTHDBPZ> reallist = new List<IFP_SM_YTHDBPZ>();

//                if (!string.IsNullOrEmpty(roles))
//                {
//                    /*string[] rolearr = roles.Split(",");
//                    foreach(var role in rolearr)
//                    {
//                        List<IFP_SM_YTHDBPZ> tmp = db.GetTable<IFP_SM_YTHDBPZ>().Where(x => x.Zfbz != 1&&x.Roles.Value.Contains(role)).ToList();
//                        list.AddRange(tmp);
//                    }
//                    list = list.Distinct().ToList();*/
//                    //List<IFP_SM_YTHDBPZ> list = db.GetTable<IFP_SM_YTHDBPZ>().Where(x => x.Zfbz != 1).ToList();
//                    List<IFP_SM_YTHDBPZ> list = db.Queryable<IFP_SM_YTHDBPZ>().Where(x => x.Zfbz != 1).ToList();
//                    foreach (var pz in list)
//                    {
//                        bool flag = false;
//                        string[] rolearr = roles.Split(",");
//                        foreach (var role in rolearr)
//                        {
//                            if (pz.Roles.Value.Contains(role))
//                            {
//                                flag = true;
//                                break;
//                            }
//                        }
//                        if (flag)
//                        {
//                            reallist.Add(pz);
//                        }

//                    }
//                }
//                else
//                {
//                    //List<IFP_SM_YTHDBPZ> list = db.GetTable<IFP_SM_YTHDBPZ>().Where(x => x.Zfbz != 1).ToList();
//                    List<IFP_SM_YTHDBPZ> list = db.Queryable<IFP_SM_YTHDBPZ>().Where(x => x.Zfbz != 1).ToList();
//                    foreach (var pz in list)
//                    {
//                        reallist.Add(pz);
//                    }

//                }
//                foreach (var i in reallist)
//                {
//                    #region 原linq2db代码 2024-11-26
//                    //IDataReader reder = db.ExecuteReader(i.Sql.Value).Reader;

//                    //DataTable dt = new DataTable();
//                    //dt.Load(reder);
//                    //reder.Close();
//                    //foreach (DataRow row in dt.Rows)
//                    //{
//                    //    TaskWarnModel taskmodel = new TaskWarnModel();

//                    //    string url = i.Pageurl.Value;
//                    //    string[] paramkey = string.IsNullOrEmpty(i.Paramkey.Value)?null:i.Paramkey.Value.Split(",");

//                    //    string[] values = string.IsNullOrEmpty(i.Paramkey.Value)?null:new string[paramkey.Length];
//                    //    foreach (DataColumn col in dt.Columns)
//                    //    {
//                    //        if (col.ColumnName == "ywsj")
//                    //        {
//                    //            taskmodel.ywsj = Convert.ToDateTime(row[col]);
//                    //        }
//                    //        else if (col.ColumnName == "state")
//                    //        {
//                    //            taskmodel.state = row[col].ToString();
//                    //        }
//                    //        else if (col.ColumnName == "title")
//                    //        {
//                    //            taskmodel.title = row[col].ToString();
//                    //        }
//                    //        else if (col.ColumnName == "detail")
//                    //        {
//                    //            taskmodel.detail = row[col].ToString();
//                    //        }

//                    //        if (!string.IsNullOrEmpty(i.Paramkey.Value) && i.Paramkey.Value.Contains(col.ColumnName))
//                    //        {
//                    //            int k = 0;
//                    //            foreach (var param in paramkey)
//                    //            {
//                    //                values[k] = row[col].ToString();
//                    //                k++;
//                    //            }
//                    //        }

//                    //    }

//                    //    if(paramkey != null)
//                    //    {
//                    //        for (var m = 0; m < paramkey.Length; m++)
//                    //        {
//                    //            if (m == 0)
//                    //            {
//                    //                url += $"?{paramkey[m]}={values[m]}";
//                    //            }
//                    //            else
//                    //            {
//                    //                url += $"&{paramkey[m]}={values[m]}";
//                    //            }
//                    //        }
//                    //    }


//                    //    taskmodel.url = url;

//                    //    target.Add(taskmodel);

//                    //}
//                    #endregion 
//                    using (IDataReader reader = db.Ado.GetDataReader(i.Sql.Value))
//                    {
//                        DataTable dt = new DataTable();
//                        dt.Load(reader);
//                        // 不需要显式调用 reader.Close()，using 语句会自动处理

//                        foreach (DataRow row in dt.Rows)
//                        {
//                            TaskWarnModel taskmodel = new TaskWarnModel();
//                            string url = i.Pageurl.Value;
//                            string[] paramkey = string.IsNullOrEmpty(i.Paramkey.Value) ? null : i.Paramkey.Value.Split(",");
//                            string[] values = string.IsNullOrEmpty(i.Paramkey.Value) ? null : new string[paramkey.Length];

//                            foreach (DataColumn col in dt.Columns)
//                            {
//                                if (col.ColumnName == "ywsj")
//                                {
//                                    taskmodel.ywsj = Convert.ToDateTime(row[col]);
//                                }
//                                else if (col.ColumnName == "state")
//                                {
//                                    taskmodel.state = row[col].ToString();
//                                }
//                                else if (col.ColumnName == "title")
//                                {
//                                    taskmodel.title = row[col].ToString();
//                                }
//                                else if (col.ColumnName == "detail")
//                                {
//                                    taskmodel.detail = row[col].ToString();
//                                }
//                                if (!string.IsNullOrEmpty(i.Paramkey.Value) && i.Paramkey.Value.Contains(col.ColumnName))
//                                {
//                                    int k = 0;
//                                    foreach (var param in paramkey)
//                                    {
//                                        values[k] = row[col].ToString();
//                                        k++;
//                                    }
//                                }
//                            }

//                            if (paramkey != null)
//                            {
//                                for (var m = 0; m < paramkey.Length; m++)
//                                {
//                                    if (m == 0)
//                                    {
//                                        url += $"?{paramkey[m]}={values[m]}";
//                                    }
//                                    else
//                                    {
//                                        url += $"&{paramkey[m]}={values[m]}";
//                                    }
//                                }
//                            }

//                            taskmodel.url = url;
//                            target.Add(taskmodel);
//                        }
//                    } // reader 会在这里自动关闭和释放
//                }
//                iofp.msg = "{\"todoList\":" + Newtonsoft.Json.JsonConvert.SerializeObject(target) + "}";
//                return iofp;
//            }
//        }
//    }
//}
