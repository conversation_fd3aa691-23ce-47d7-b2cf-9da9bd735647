﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Data;
//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace ORM.IFP
{
	/// <summary>
	/// 基础信息
	/// </summary>
	/// <typeparam name="T"></typeparam>
	public partial class BaseData<T> : BaseData, IView<T> where T : BaseData, new()
	{
		public virtual ISugarQueryable<T> GetQuery(SqlSugarClient db)
		{
            //var name = db.MappingSchema.GetEntityDescriptor(typeof(T)).TableName;
            var name = db.EntityMaintenance.GetEntityInfo(typeof(T)).DbTableName;
            var ywlx = name.Substring(name.Length - 4, 4);

            ////return db.GetQuery<T, BaseData>((v, t) => v.Gid == t.Gid, SqlJoinType.Right, (v, t) => new T() { Gid = Sql.AsSql(v.Gid).IsNull ? t.Gid : v.Gid }).Where(x => x.Ywlx == Sql.NoConvert(ywlx));	//以Gid排序时会报错
            //return db.GetQuery<T, BaseData>((v, t) => v.Gid == t.Gid, SqlJoinType.Right, (v, t) => new T() { Gid = t.Gid }).Where(x => x.Ywlx == Sql.NoConvert(ywlx));

            return db.Queryable<BaseData>()
                    .LeftJoin<T>((t, v) => v.Gid == t.Gid)
                    .Select((t, v) => new T { Gid = t.Gid })
                    .Where(x => x.Ywlx == ywlx);
        }
	}

    [SugarTable("IFP_BS_BASEINFO")]
    public class BaseLite
    {
        /// <summary>
        /// GID
        /// </summary>
        [SugarColumn(ColumnName = "Gid", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public virtual Field<long> Gid { get; set; }

        /// <summary>
        /// 对象名称
        /// </summary>
        [SugarColumn(ColumnName = "Bname", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public virtual Field<string> Bname { get; set; }

        /// <summary>
        /// 对象简称
        /// </summary>
        [SugarColumn(ColumnName = "Sname", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public virtual Field<string> Sname { get; set; }
    }

    public partial class BaseData
	{
		public BaseLite ToBaseLite()
		{
			//这里最好是用New表达式
			return new BaseLite()
			{
				Gid = Gid,
				Bname = Bname,
				Sname = Sname
			};
		}
	}

    /// <summary>
    /// 基础信息
    /// </summary>
    [SugarTable("IFP_BS_BASEINFO")]
    public partial class BaseData
    {
        /// <summary>
        /// GID
        /// </summary>
        [SugarColumn(ColumnName = "Gid", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public virtual Field<long> Gid { get; set; }

        /// <summary>
        /// 上级对象GID
        /// </summary>
        [SugarColumn(ColumnName = "Pgid", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Pgid { get; set; }

        /// <summary>
        /// 树结构编码，也可用于排序
        /// </summary>
        [SugarColumn(ColumnName = "TREECODE", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> TreeCode { get; set; }

        /// <summary>
        /// 对象名称
        /// </summary>
        [SugarColumn(ColumnName = "Bname", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> Bname { get; set; }

        /// <summary>
        /// 对象简称
        /// </summary>
        [SugarColumn(ColumnName = "Sname", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> Sname { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        [SugarColumn(ColumnName = "Ywlx", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(4)")]
        public Field<string> Ywlx { get; set; }

        /// <summary>
        /// 所属单位
        /// </summary>
        [SugarColumn(ColumnName = "Compid", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(8)")]
        public Field<string> Compid { get; set; }

        /// <summary>
        /// 新增时间
        /// </summary>
        [SugarColumn(ColumnName = "Addtime", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "datetime")]
        public Field<DateTime> Addtime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnName = "Lasttime", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "datetime")]
        public Field<DateTime> Lasttime { get; set; }

        /// <summary>
        /// 作废标志（1作废  0正常）
        /// </summary>
        [SugarColumn(ColumnName = "Zfbz", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Zfbz { get; set; }

        /// <summary>
        /// 业务编码
        /// </summary>
        [SugarColumn(ColumnName = "Ywbm", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(255)")]
        public Field<string> Ywbm { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "Beizhu", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(256)")]
        public Field<string> Beizhu { get; set; }
    }


    /* 以下为对应的Json对象
	{
		Gid: null,		//GID
		Pgid: null,		//上级对象GID
		Bname: null,		//对象名称
		Sname: null,		//对象简称
		Ywlx: null,		//业务类型
		Compid: null,		//所属单位
		Addtime: null,		//新增时间
		Lasttime: null,		//修改时间
		Zfbz: null,		//作废标志（1作废  0正常）
		Ywbm: null		//业务编码
	}
	*/
}
