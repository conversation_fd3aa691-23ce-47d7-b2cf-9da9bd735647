﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Mapping;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{
    /// <summary>
    /// IFP产品分类
    /// </summary>
    [SugarTable("IFP_BS_YWDX4503")]
    public class YWDX4503 : BaseData<YWDX4503>
    {
        /// <summary>
        /// GID
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Gid { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [SugarColumn(ColumnName = "SN", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(50)")]
        public Field<string> SN { get; set; }

        /// <summary>
        /// 默认地址
        /// </summary>
        [SugarColumn(ColumnName = "URL", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(1000)")]
        public Field<string> Url { get; set; }

        /// <summary>
        /// 默认端口
        /// </summary>
        [SugarColumn(ColumnName = "PORT", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(50)")]
        public Field<string> Port { get; set; }

        /// <summary>
        /// 时序数据库表
        /// </summary>
        [SugarColumn(ColumnName = "DBTABLE", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(100)")]
        public Field<string> Dbtable { get; set; }
    }

    //   [Table(Name = "IFP_BS_YWDX4503")]
    //   public class YWDX4503 : BaseData<YWDX4503>
    //   {

    //	/// <summary>
    //	/// GID
    //	/// </summary>
    //	[Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Gid { get; set; }

    //	/// <summary>
    //	/// 序列号
    //	/// </summary>
    //	[Column(Name = "SN", DataType = DataType.NVarChar, DbType = "nvarchar(50)")]
    //	public Field<string> SN { get; set; }

    //	/// <summary>
    //	/// 默认地址
    //	/// </summary>
    //	[Column(Name = "URL", DataType = DataType.NVarChar, DbType = "nvarchar(1000)")]
    //	public Field<string> Url { get; set; }

    //	/// <summary>
    //	/// 默认端口
    //	/// </summary>
    //	[Column(Name = "PORT", DataType = DataType.NVarChar, DbType = "nvarchar(50)")]
    //	public Field<string> Port { get; set; }

    //	/// <summary>
    //	/// 时序数据库表
    //	/// </summary>
    //	[Column(Name = "DBTABLE", DataType = DataType.NVarChar, DbType = "nvarchar(100)")]
    //	public Field<string> Dbtable { get; set; }
    //}


    /* 以下为对应的Json对象
	 {
		 Gid: null,		//GID
		 SN: null,		//序列号
		 Url: null,		//默认地址
		 Port: null,		//默认端口
		 Dbtable: null		//时序数据库表
	 }
	 */
}
