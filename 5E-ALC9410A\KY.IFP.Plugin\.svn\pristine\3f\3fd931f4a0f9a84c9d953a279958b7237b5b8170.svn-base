.kyadmin-theme-dark {
  background-color: #000000;
  color: #ccc;
}
.kyadmin-theme-dark .navbar-inverse .navbar-nav > li > a.warningbutton {
  color: #fff;
}
.kyadmin-theme-dark .navbar-inverse .navbar-brand {
  background-color: transparent;
  color: #fff;
}
.kyadmin-theme-dark .header-container {
  background-color: #000;
}
.kyadmin-theme-dark .footer {
  background-color: #ffffff33;
}
.kyadmin-theme-dark a {
  color: #fff;
}
.kyadmin-theme-dark a.btn-primary {
  color: #fff;
  background-color: #000000;
}
.kyadmin-theme-dark .dropdown-menu.submenubox {
  background-color: #000a;
  color: #fff;
}
.kyadmin-theme-dark .dropdown-menu.submenubox .submenuitem {
  border: 1px solid #fff;
  color: #fff;
}
.kyadmin-theme-dark .dropdown-menu.submenubox .submenuitem:hover {
  background-color: #fff;
  color: #000;
}
.kyadmin-theme-default .header-container {
  background-color: #0075D3;
}
.kyadmin-theme-default .header-container .navbar-collapse > .nav .open > a,
.kyadmin-theme-default .header-container .navbar-collapse > .nav > li.active > a {
  background-color: #fff4;
}
.kyadmin-theme-default .header-container a.navbar-brand {
  color: #fff;
}
.kyadmin-theme-default .header-container .navbar-collapse > .nav > li > a {
  color: #fff;
}
.kyadmin-theme-default .header-container .navbar-collapse > .nav > li > a.warningbutton {
  font-weight: bold;
}
.kyadmin-theme-default .header-container .navbar-collapse > .nav > li > a:focus,
.kyadmin-theme-default .header-container .navbar-collapse > .nav > li > a:hover {
  text-decoration: none;
  background-color: #fff4;
}
.kyadmin-theme-default .header-container .dropdown-menu {
  color: #000;
}
.kyadmin-theme-default .header-container .dropdown-menu.submenubox {
  background-color: var(--bg-menu-subcontainer);
}
.kyadmin-theme-default .header-container .dropdown-menu.submenubox .submenuitem {
  border: 1px solid #0075D3;
  background-color: var(--bg-menu-item);
  color: #0075D3;
}
.kyadmin-theme-default .header-container .dropdown-menu.submenubox .submenuitem:hover {
  background-color: #0075D3;
  color: #fff;
}
.vhcenter {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.footer {
  padding: 2px;
}
.footer label {
  margin-bottom: 0;
}
.dropdown-menu.submenubox {
  background-color: var(--bg-menu-subcontainer);
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 50px;
  padding: 10px;
}
.dropdown-menu.submenubox .submenuitem {
  display: inline-block;
  padding: 10px;
  width: 120px;
}
