# 工具栏 | ifp-toolbar

## 代码示例

```html
<!--显示 close[可选] 和 列显按钮[可选] -->
<ifp-toolbar close lxsz='table1'>
    <!--其他按钮-->
    <ifp-button>新增</ifp-button>
</ifp-toolbar>

<ifp-table ref="table1"></ifp-table>
```

## 列显设置

```html
<!--显示 close[可选] 和 列显按钮[可选] -->
<ifp-toolbar lxsz='table1'>
    <!--其他按钮-->
</ifp-toolbar>

<ifp-table ref="table1"></ifp-table>
```

如果页面只有一个 table 也可以这么写

```html
<!--显示 退出 列显 -->
<ifp-toolbar close lxsz>
    <!--其他按钮-->
</ifp-toolbar>

<ifp-table></ifp-table>
```

## 属性

* close 默认值 false
* lxsz 需要配置列显的 table ref，如果页面只有一个 table 也可以不设置属性
