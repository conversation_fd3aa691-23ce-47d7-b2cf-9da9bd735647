﻿<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <title>用户记录表详情</title>
    </head>
    <body controller="userMx.js">
        <div class="hidden">
            <input id="Gid" control="controls/textbox" class="hideInput" />

            <input id="Creator" control="controls/textbox" class="hideInput" />
            <input id="CreateTime" control="controls/textbox" class="hideInput" />
            <input id="Delt" control="controls/textbox" class="hideInput" />

            <input id="DpmGuid" control="controls/textbox" class="hideInput" />
            <input id="UsiTonken" control="controls/textbox" class="hideInput" />
        </div>
        <div form="forms/toolbar" class="layout-h">
            <a id="saveBtn" control="controls/button" option="{}" log>保存</a>
            <a id="readBtn" control="controls/button" option="{}" log>读卡</a>
            <a id="exitBtn" control="controls/button" option="{}">退出</a>
        </div>
        <div class="layout-c">
            <div class="bill">
                <div class="form-title">用户信息</div>
                <div class="">
                    <table class="form-datatable" style="width: 100%">
                        <tr>
                            <td style="padding-left: 20px" nowrap>工号：</td>
                            <td>
                                <input id="UsiNumber" control="controls/textbox" option="{width:'100%',showName:'工号',controlsUses:1,required:true}" />
                            </td>
                            <td style="padding-left: 20px" nowrap>姓名：</td>
                            <td>
                                <input id="UsiName" control="controls/textbox" option="{width:'100%',showName:'姓名',controlsUses:1,required:true}" />
                            </td>
                        </tr>

                        <tr>
                            <td style="padding-left: 20px" nowrap>登录名：</td>
                            <td>
                                <input id="UsiLoginName" control="controls/textbox" option="{width:'100%',showName:'登录名',controlsUses:1,required:true}" />
                            </td>
                            <td style="padding-left: 20px" nowrap>密码：</td>
                            <td>
                                <input id="UsiPassWord" control="controls/textbox.encrypt" option="{width:'100%',showName:'密码',controlsUses:1,disabled:true}" />
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-left: 20px" nowrap>部门名称：</td>
                            <td>
                                <input id="DeptName" control="controls/textbox" option="{width:'100%',showName:'部门名称',controlsUses:1}" />
                            </td>
                            <td style="padding-left: 20px" nowrap>部门全称：</td>
                            <td>
                                <input id="DeptFullName" control="controls/textbox" option="{width:'100%',showName:'部门全称',controlsUses:1}" />
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-left: 20px" nowrap>联系方式：</td>
                            <td>
                                <input id="UsiContact" control="controls/textbox" option="{width:'100%',showName:'联系方式',controlsUses:1}" />
                            </td>
                            <td style="padding-left: 20px" nowrap>性别：</td>
                            <td>
                                <input id="UsiSex" control="controls/select2" option="{showName:'性别',controlsUses:1,data:[{id:'0',text:'男'},{id:'1',text:'女'}],width:'100%'}" />
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-left: 20px" nowrap>卡号：</td>
                            <td>
                                <input id="CardNo" control="controls/textbox" option="{width:'100%',showName:'卡号',controlsUses:1}" />
                            </td>
                            <td style="padding-left: 20px" nowrap></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td style="padding-left: 20px">备注：</td>
                            <td colspan="3" style="padding-top: 8px">
                                <input id="REMARK" control="controls/textbox" option="{multiline:true,width:'100%',height:'60px',showName:'备注',controlsUses:1}" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </body>
    <script src="/iofp/starter.js"></script>
</html>
