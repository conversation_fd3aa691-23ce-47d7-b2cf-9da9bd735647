<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Control</title>
</head>
<body>
    <div class="searchbar">
        <input id="url2" control="controls/textbox" option="{value:'/api/test.action',width:200}" />
        <input id="select3" control="controls/select2" option="{Ywlx:4024,value:10020001,width:200}"/>
    </div>
    
    <!--,{gid:2,userid:2,age:23},{gid:3,userid:"10020001",age:23}-->
    
    <table id="grid33" control="controls/grid" option='{
        cellEdit:true,
        width:800,
        value:[{gid:1,userid:"40000187",age:23}],
        postData : {}
    }'>
        <thead>
            <tr>
                <th>gid</th>
                <th>姓名</th>
                <th>年龄</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td option="{name:'gid',hidden:true,key: true}"></td>
                <td option="{name:'userid',editable:true,control:'controls/select2',controloption:{ Ywlx:'4024' }}"></td>
                <td option="{name:'age',editable:false}"></td>
            </tr>
        </tbody>
    </table>
    <script src="/iofp/starter.js"></script>
</body>
</html>