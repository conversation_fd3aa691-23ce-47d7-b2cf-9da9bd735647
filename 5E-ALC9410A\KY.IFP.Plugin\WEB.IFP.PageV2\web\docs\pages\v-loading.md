# 加载中

* html,在需要显示等待的元素上，添加属性`v-loading="loading"`
* js,data 中定义 loading:false
* 请求数据前 loading = true
* 数据返回后 loading = false
* 注意：请求异常 loading = false

## 示例

### 示例1：全局

html

```html
<ifp-page v-loading="loading">
    <ifp-searchbar @search="onSearch">
        <!-- 输入控件 -->
    </ifp-searchbar>
</ifp-page>
```

js

```js
{
    data(){
        return {
            loading:false
        }
    },
    methods:{
        onSearch(){
            this.loading=true;
            getData().then(data=>{
                // ToDo: 绑定数据

                // 关闭等待
                this.loading=false;
            }).catch((ex)=>{
                // ToDo: 异常处理

                // 不要忘记异常关闭等待
                this.loading=false;
            })
        }
    }
}
```

### 示例2：局部

js 部分与示例1 相同，html 的区别在于，
将 `v-loading="loading"` 放在需要显示等待的元素上即可，这里放在 表格上了

html

```html
<ifp-page>
    <ifp-searchbar @search="onSearch">
        <!-- 输入控件 -->
    </ifp-searchbar>

    <ifp-table v-loading="loading">

    </ifp-table>
</ifp-page>
```