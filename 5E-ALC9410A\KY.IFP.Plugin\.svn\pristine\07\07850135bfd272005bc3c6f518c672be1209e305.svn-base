define(["iofp/api",
    "moment",
    "iofp/components/iofpsocket",
    "iofp/lib/excelExportElement",], function (API, moment, iofpsocket, excelExport) {


        return {
            el: "#app",
            props: {
                userid: String,
            },
            data() {
                return {
                    userData: {

                    }
                }
            },

            methods: {

                initData: function () {

                    return API.GetAction("/API/IFP/Rights/User/QueryUserById", {
                        Gid: this.userid
                    }).then(x => {
                        this.userData = x;

                    }).catch(e => {
                        this.loading = false;
                        this.$message.error("查询出错");
                        console.error(e);
                    });
                },
                readCard: function () {

                    return API.GetAction("/API/IFP/BaseInfo/ReadConfig/ClientReadConfig", {}).then(config => {
                        if (config == null) {
                            this.$message.error("本机未配置读卡器");
                            return;
                        }
                        API.GetAction("/API/IFP/BaseInfo/Reader/ReadCard", config).then(res => {
                            if (res.success) {
                                this.userData.CardNo = res.data.CardNo;
                                this.$message.success("读卡成功");
                            } else {
                                this.$message.error("读卡失败" + res.msg);
                            }

                        }).catch(e => {
                            this.loading = false;
                            this.$message.error("查询出错");
                            console.error(e);
                        });
                    }).catch(e => {
                        this.loading = false;
                        this.$message.error("查询出错");
                        console.error(e);
                    });
                },
                save: function () {
                    return API.GetAction("/API/IFP/Rights/User/UpdateUserCard", {
                        Gid: this.userid,
                        CardNo: this.userData.CardNo,
                    }).then(res => {
                        if (res.success) {
                            this.$message.success("保存成功")
                        } else {
                            this.$message.error("保存失败")
                        }
                    }).catch(e => {
                        this.loading = false;
                        this.$message.error("查询出错");
                        console.error(e);
                    });

                    var _this = this;
                    var formdata = {
                        Gid: _this.controls.userGid.value(),
                        CardNo: _this.controls.CardNo.value(),
                    };
                    F.ajax({
                        url: '/API/IFP/Rights/User/UpdateUserCard',
                        data: JSON.stringify(formdata),
                        success: function (resultObj) {
                            if (resultObj.success) {
                                window.parent.$.alert('绑定成功。');
                                if (win) {
                                    win.close();
                                }
                            } else {
                                $.alert(resultObj.data);
                            }
                        },
                    });
                },

                //报警解除事件
            },

            mounted() {
                this.$on('opened', x => {
                    this.initData()
                });
            }
        }
    })