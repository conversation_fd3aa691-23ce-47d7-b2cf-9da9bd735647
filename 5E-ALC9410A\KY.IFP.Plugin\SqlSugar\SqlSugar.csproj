<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Version>*********</Version>
    <Copyright>sun_kai_xuan</Copyright>
    <PackageProjectUrl>https://github.com/sunkaixuan/SqlSugar</PackageProjectUrl>
    <PackageLicenseUrl></PackageLicenseUrl>
    <PackageIconUrl></PackageIconUrl>
    <PackageId>SqlSugar</PackageId>
    <Description>果糖大数据科技（南通）有限公司</Description>
    <Company>果糖大数据科技（南通）有限公司</Company>
    <Authors>果糖大数据科技（南通）有限公司</Authors>
    <BaseOutputPath></BaseOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="dll\**" />
    <EmbeddedResource Remove="dll\**" />
    <None Remove="dll\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DM.DmProvider" Version="8.3.1.30495" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="8.0.1" />
    <PackageReference Include="MySqlConnector" Version="2.2.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="Npgsql" Version="5.0.7" />
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="3.21.100" />
    <PackageReference Include="Oscar.Data.SqlClient" Version="4.0.4" />
    <PackageReference Include="SqlSugarCore.Dm" Version="8.6.0" />
    <PackageReference Include="SqlSugarCore.Kdbndp" Version="8.3.715" />
    <PackageReference Include="System.Data.Common" Version="4.3.0" />
	<PackageReference Include="System.Data.OleDb" Version="6.0.0" />
    <PackageReference Include="System.Reflection.Emit.Lightweight" Version="4.3.0" />
  </ItemGroup>

</Project>
