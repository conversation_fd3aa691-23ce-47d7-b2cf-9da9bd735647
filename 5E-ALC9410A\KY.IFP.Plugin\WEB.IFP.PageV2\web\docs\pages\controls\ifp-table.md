# ifp-table

继承于：el-table

## 方法

### print

```html
<ifp-button @click="$refs.table1.print()">
<ifp-table ref="table1">
```

## 编码规范

表格控件使用

### 使用IFP控件

使用 ifp 控件可以由平台统一设置默认值

* ifp-table-column
* ifp-table

### 特殊行样式

对于表格中需要操作人员`重点关注`的记录，可以将整行置为特殊颜色来`强烈提醒`。

> 请慎用此设计

(规划)

统一规范命名

|class|含义|说明|
|-|-|-|
|el-table-row__success|成功||
|el-table-row__warn|警告||
|el-table-row__danger|危险||
|el-table-row__error|错误||

样式实现由平台统一提供（开发中）