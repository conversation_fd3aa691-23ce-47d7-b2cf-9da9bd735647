# kyadmin 页面开发规范

kyadmin 内置两种渲染器

* 一种 基于 jquery 的空间体系
* 另一种 基于 vue

必须符合以下规范

* js 必须符合 AMD 规范
* 页面必须可以能单独访问
* 控制器 js 不能强依赖 url 
* 一般情况下 js 与 html 同名，且在同一目录下，公共控制器除外
* `body` 必须指定 `controller`
* 同一项目下所有页面中只能引入一个js
* 页面独有的 css 只能以 css! 的方式导入

## 示例

### 采用默认的 jquery 控件体系

example.html

```html
<body controller="example.js">
    <div>
        <a id="btnHello" control="controls/button">Hello</a>
    </div>
    <script src="/kyadmin/starter.js"></script>
</body>
```

example.js
```
define(["controllers/base","jclass"]function(jclass,base){
    return jclass(base,{
        showHello:function(){
            alert("hello");
        },
        onLoad:function(){
            this.controls.btnHello.binc("click",function(){

            },this)
        }
    })
})
```

### 不采用UI框架,自定义控制器

注意：这种方式的控制器无需定义为模块，js 采用 require

example.html

```html
<body controller="example.js" option="{target:null}">
    <div id="app"></div>
    <script src="/kyadmin/starter.js"></script>
</body>
```

example.js
```js
require(["jquery"],function($){
    $("#app").html("hello world")
})
```

### 仅采用 vue 的页面

注意：这种方式的控制器无需定义为模块，js 采用 require

example.html

```html
<body controller="example.js" option="{target:'vue'}">
    <div id="app">
        {{msg}}
    </div>
    <script src="/kyadmin/starter.js"></script>
</body>
```

example.js
```js
require(function(){
    return {
        el:"#app",
        data:function(){
            return {
                msg:"Hello World!"
            }
        }
    }
})
```

### 仅采用 vue\Element-UI 的页面


example.html

```html
<body controller="example.js" option="{target:'vue',use:'ELEMENT'}">
    <div id="app">
        <el-input v-model="msg" />
    </div>
    <script src="/kyadmin/starter.js"></script>
</body>
```

example.js
```js
require(function(){
    return {
        el:"#app",
        data:function(){
            return {
                msg:"Hello World!"
            }
        }
    }
})
```