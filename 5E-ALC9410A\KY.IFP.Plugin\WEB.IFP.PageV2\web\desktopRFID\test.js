﻿; define([
    "ELEMENT",
    "util",
    "platform/vue",
    "iofp/api",
    "moment",
    "iofp/xslsz",
    "/desktopRFID/desktopReader.js",
    "css!/kyadmin/css/layout.css"
], function (ELEMENT, util, pVue, API, moment, XSZ, cardRead) {


    return {
        el: '#app',
        data() {
            return {
                cardNum: "123",
                loading:false
            }
        },
        methods: {
            readCard: function () {
                var _this = this;
                _this.loading = true;
                cardRead.kyReader.ReadCard(_this).then(x => {
                    _this.cardNum = x;
                    _this.loading = false;
                }).catch(e => {
                    console.log(e);
                    _this.loading = false;
                });
            },
            writeCard: function () {
                var _this = this;
                _this.loading = true;
                cardRead.kyReader.WriteCard(_this, _this.cardNum).then(x => {
                    _this.cardNum = x;
                    _this.loading = false;
                }).catch(e => {
                    console.log(e);
                    _this.loading = false;
                });
            }
        },
        watch: {
        },
        created: function () {
        }
    }
})