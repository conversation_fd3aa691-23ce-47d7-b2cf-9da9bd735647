﻿    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>EXCEL导出</title>
    </head>
    <body controller="excelExportDemo.js">
        <div form="forms/toolbar" class="layout-h">
            <a id="exportBtn" control="controls/button" option="{icon:'glyphicon-arrow-down'}">导出</a>
            <a id="returnBtn" control="controls/button" option="{command:'close'}">退出</a>
        </div>
        <div class="layout-c padding">
            <table id="rcmxgrid" control="controls/grid" option='{"autoresize":true,shrinkToFit:true,multiselect:true}'>
                <thead>
                    <tr>
                        <th option="{start:'Mar',cols:2}">水分</th>
                        <th option="{start:'Aar',cols:2}">灰分</th>
                        <th option="{start:'QgrarMJ',cols:4}">热值</th>
                    </tr>
                    <tr>
                        <th option="{start:'Mar',cols:1}">全水</th>
                        <th option="{start:'Mad',cols:1}">内水</th>
                        <th option="{start:'Aar',cols:1}">收到基灰分</th>
                        <th option="{start:'Aad',cols:1}">空干基灰分</th>
                        <th option="{start:'QgrarMJ',cols:2}">收到基高位热值</th>
                        <th option="{start:'QnetarMJ',cols:2}">收到基低位热值</th>
                    </tr>
                    <tr>
                        <th>GID</th>
                        <th>矿点</th>
                        <th>煤种</th>
                        <th>批次编码</th>
                        <th>Mar(%)</th>
                        <th>Mad(%)</th>
                        <th>Aar(%)</th>
                        <th>Aad(%)</th>
                        <th>Qgr.ar(MJ/kg)</th>
                        <th>Qgr.ar(Kcal/kg)</th>
                        <th>Qnet.ar(MJ/kg)</th>
                        <th>Qnet.ar(Kcal/kg)</th>
                        <th>供应总量</th>
                        <th>供应占比</th>
                        <th>结算状态</th>
                        <th>操作人</th>
                        <th>操作时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td option="{name:'Gid', key:true, hidden:true}"></td>
                        <td option="{name:'Kd4001', align :'left', control:'controls/select2', width:100, Ywlx:4001, merge:true}"></td>
                        <td option="{name:'Mz4010', align :'left', control:'controls/select2', width:100, Ywlx:4010, merge:true}"></td>
                        <td option="{name:'Pcbm', align :'center', control:'controls/textbox', width:120, merge:true}"></td>
                        <td option="{name:'Mar', align:'right', control:'controls/number', width:100, decimalDigits:1}"></td>
                        <td option="{name:'Mad', align:'right', control:'controls/number', width:100, decimalDigits:1}"></td>
                        <td option="{name:'Aar', align:'right', control:'controls/number', width:100}"></td>
                        <td option="{name:'Aad', align:'right', control:'controls/number', width:100}"></td>
                        <td option="{name:'QgrarMJ', align:'right', control:'controls/number', width:100, decimalDigits:3}"></td>
                        <td option="{name:'QgrarKcal', align:'right', control:'controls/number', width:100, decimalDigits:0}"></td>
                        <td option="{name:'QnetarMJ', align:'right', control:'controls/number', width:100, decimalDigits:3}"></td>
                        <td option="{name:'QnetarKcal', align:'right', control:'controls/number', width:100, decimalDigits:0}"></td>
                        <td option="{name:'Gyzl', align:'right', control:'controls/number', width:100, formater:'thousands', decimalDigits:1}"></td>
                        <td option="{name:'Gyzb', align:'right', control:'controls/number', width:100, formater:'percent', decimalDigits:1}"></td>
                        <td option="{name:'Jszt', align:'center', control:'controls/select2', width:80, data:[{id:'1',text:'已结算'},{id:'2',text:'未结算'}]}"></td>
                        <td option="{name:'Czr', align :'center', control:'controls/textbox', width:100, formater:'useridtoname'}"></td>
                        <td option="{name:'Czsj', align :'center', control:'controls/datepicker', width:180, formater:'datetimeToDate'}"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </body>
    <script src="/iofp/starter.js"></script>
</html>
