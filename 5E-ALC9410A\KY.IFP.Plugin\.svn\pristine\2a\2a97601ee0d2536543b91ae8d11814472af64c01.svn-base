﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>火车车型管理</title>
    <style>
        .el-table .stop-row {
            background: #AAAAAA !important;
        }
    </style>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <el-button v-if="!$btnRight.B1" type="primary" icon="el-icon-plus" @click="onCreate()">新增</el-button>
            <el-button v-if="!$btnRight.B1" icon="el-icon-view" @click="onView">查看</el-button>
            <el-button v-if="!$btnRight.B1" icon="el-icon-edit" @click="onUpdate">修改</el-button>
            <el-button v-if="!$btnRight.B1" icon="el-icon-delete" @click="onDelete">停用</el-button>
        </ifp-toolbar>
        <ifp-searchbar @search="updateList" @reset="onSelect">
            <ifp-searchbar-item label="车型">
                <ifp-input v-model="viewer.filter.Bname.Value" style="width:200px;"></ifp-input>
            </ifp-searchbar-item>
            <ifp-searchbar-item label="是否自备车">
                <ifp-select-ywlx v-model="viewer.filter.SFZBC1099.Value" :clearable="true" :ywlx="1099"></ifp-select-ywlx>
            </ifp-searchbar-item>
            <ifp-searchbar-item label="启停状态">
                <el-select :clearable="true" v-model="viewer.filter.Zfbz.Value">
                    <el-option v-for="item in StatusOptions"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
            </ifp-searchbar-item>
        </ifp-searchbar>
        
        <ifp-panel-table class="padding flex-item">
            <ifp-table :data="viewer.source"
                      style="width:100%;"
                      row-key="id"
                      height="100%"
                      border
                      :cell-style="cellStyle"
                      ref="table"
                      highlight-current-row
                      @current-change="onChange">
                <el-table-column type="index"
                                 label="序号"
                                 align ="center"
                                 :index="indexMethod"
                                 width="50">
                </el-table-column>
                <el-table-column prop="Bname"
                                 label="车型">
                </el-table-column>
                <el-table-column prop="ZHAIZ"
                                 align ="right"
                                 label="载重 (t)">
                </el-table-column>
                <el-table-column prop="PZ"
                                 align ="right"
                                 label="自重 (t)">
                </el-table-column>
                <el-table-column label="车内尺寸" align ="center">
                    <el-table-column prop="Length"
                                 align ="right"
                                     label="长 (mm)">
                    </el-table-column>
                    <el-table-column prop="Width"
                                 align ="right"
                                     label="宽 (mm)">
                    </el-table-column>
                    <el-table-column prop="Height"
                                 align ="right"
                                     label="高 (mm)">
                    </el-table-column>
                </el-table-column>
                <el-table-column prop="FloorHeight"
                                 align ="right"
                                 label="底板高度 (mm)">
                </el-table-column>
                <el-table-column label="是否自备车"
                                        align ="center"
                                        width="100">
                    <template slot-scope="scope">
                        <ifp-select-ywlx :canedit="false" v-model="scope.row.SFZBC1099" :ywlx="1099"></ifp-select-ywlx>
                    </template>
                </el-table-column>
                <el-table-column label="状态"
                                 align ="center"
                                    width="80">
                    <template slot-scope="scope">
                        {{scope.row.Zfbz === 0?"启用":"停用"}}
                    </template>
                </el-table-column>
            </ifp-table>
            <template v-slot:pagination>
                <el-pagination @size-change="sizeChange"
                               @current-change="pageChange"
                               :current-page="viewer.paging.Page"
                               :page-sizes="[10,15,20,50,100]"
                               :page-size="viewer.paging.Size"
                               layout="total, sizes, prev, pager, next, jumper"
                               :total="viewer.paging.Sums">
                </el-pagination>
            </template>
        </ifp-panel-table>

        <ifp-dialog class="subpage" :title="dialogTitle"
            :visible.sync="editor.dialog"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            width="950px">
            <ifp-train-model-detail
                @cancel="updateList()"
                @sucess="updateList()"
                :source="editor.source"
                :state="editor.state"
            ></ifp-train-model-detail>
        </ifp-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>