
(function (global, factory) {
    if (typeof define === 'function' && define.amd) {
        define([], factory);
    } else if (typeof module !== 'undefined' && module.exports){
        module.exports = factory();
    } else {
        global.EventEmitter = factory();
    }
})(this, function () {
    function EventEmitter(obj){
        this._events = {};
    }
    
    EventEmitter.prototype.on = function(name,callback,context){  //监听event事件，触发时调用callback函数
        var self = this;
        let callbacks = this._events[name] || [];
        var _callback = callback;
        if(context){
            _callback = function(){callback.apply(context||self,arguments)}
            _callback._source = callback;
        }
        callbacks.push(_callback)
        this._events[name] = callbacks
        return this
    }

    EventEmitter.prototype.off = function(event,callback){  //停止监听event事件
        let callbacks = this._events[event]
        this._events[event] = callbacks && callbacks.filter(fn => (fn._source?fn._source:fn) === callback?(fn=null,false):true)
        return this
    }
    EventEmitter.prototype.emit = function(){ //触发事件，并把参数传给事件的处理函数
        var self = this;
        const event = arguments[0]
        const params = [].slice.call(arguments,1)

        var arr = event.split(":");
        for(var i=arr.length;i>0;i--){
            const callbacks = this._events[arr.slice(0,i).join(":")]||[]
            callbacks.forEach(function(fn){
                fn.apply(self,params)
            })
        }

        return this
    }
    EventEmitter.prototype.once = function(event,callback,context){ //为事件注册单次监听器
        var self = this;
        let wrapFanc = function(){
            callback.apply(context||self,arguments)
            self.off(event,wrapFanc)
        }
        wrapFanc._source = callback;
        this.on(event,wrapFanc)
        return this
    }
    EventEmitter.mixin = function (obj) {
        EventEmitter.call(obj);
        for(var f in EventEmitter.prototype){
            obj[f]=EventEmitter.prototype[f];
        }
        return obj;
    }
    return EventEmitter;
})