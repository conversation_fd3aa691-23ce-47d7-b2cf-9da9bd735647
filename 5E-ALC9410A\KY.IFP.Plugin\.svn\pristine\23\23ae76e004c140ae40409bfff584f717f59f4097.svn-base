define([
    "/iofp/components/zxcsypj/dist/index.js",
    "css!/iofp/components/zxcsypj/dist/index"
],function(zxcsypj){
    return {
        components:{
            zxcsypj:zxcsypj.Control
        },
        el:'#app',
        data(){
            return {
                list:[
                    {xh:1,state:1},
                    {xh:2,state:2},
                    {xh:3,state:3},
                    {xh:4,state:4},
                    {xh:5,state:5},
                    {xh:6,state:6},
                    {xh:7,state:2},
                    {xh:8,state:2},
                    {xh:9,state:2},
                    {xh:10,state:1},
                    {xh:11,state:1},
                    {xh:12,state:1},
                    {xh:13,state:1},
                    {xh:14,state:1},
                    {xh:15,state:1},
                    {xh:16,state:1},
                    {xh:17,state:1},
                    {xh:18,state:1},
                    {xh:19,state:1},
                    {xh:20,state:1}
                ]
            }
        },
        methods:{
            createTooltip(item){
                if(item.state>1){
                    return `指标1:2222
                    指标1:2222
                    指标1:2222
                    指标1:2222`
                }
            },
            item_select(item){
                alert(item.xh);
            }
        }
    }
})