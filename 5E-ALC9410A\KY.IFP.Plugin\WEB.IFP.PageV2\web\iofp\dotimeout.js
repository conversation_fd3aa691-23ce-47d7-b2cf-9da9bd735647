/**
 * 超时无操作则退出
 * 1. 所有页面高频触发顶层 window 的 dosomething 事件
 * 2. 顶层window 的 dosomething 事件，若在 5 分钟内没有被调用，则调用 common.loginOut 退出
 */
define(["util", "iofp/common", "topwindow"], function (util, common, topwindow) {
    return function (logoutTime) {
        let isdebug = util.queryString("debug");
        isdebug = isdebug && isdebug !== "0";
        if (isdebug) {
            return;
        }

        // 顶层页面触发退出
        if (window === topwindow) {
            var fn = util.debounce(() => {
                common.loginOut();
            }, logoutTime);
            window.addEventListener("dosomething", function (event) {
                //console.log("dosomething",event.detail.pathname)
                fn();
            })
        }

        // 其他页面传递操作事件
        var event = new CustomEvent("dosomething", {
            detail: {
                window: window,
                pathname: window.location.pathname
            }
        });

        ["mouseover", "mousemove", "mousedown", "mouseup", "keyup", "keydown"].forEach(item => {
            document.addEventListener(item, function () {
                topwindow.dispatchEvent(event)
            }, true)
        })
    }
})