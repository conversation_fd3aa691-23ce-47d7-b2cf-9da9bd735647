//F.controls.textbox
define(["jquery","./select2"],function($,base){
	var defaultOption = {
	    selectywlx:null,
		idtotext:{url:'/com.kysoft.service/html/util/ywlx/idtoname.action'},
		minimumResultsForSearch :1,
		ajax:{
            url: "/com.kysoft.service/html/util/ywlx/getYwlxFilter.action",
            type: "post",
            contentType:"application/json",
            dataType: "json",
            delay: 250,
            cache:true,
            processResults: function (res, params) {
                params.page = params.page || 1;
                return {
                    results: res.rows,
                    pagination: {
                        more: params.page < res.total
                    }
                };
            },
            escapeMarkup: function (markup) { return markup; }
		}
	}

	return F.class(base,{
		name:"control-selectywlx",
        createOption:function(){
		    var _this = this;
		    var option = this.base.apply(this,arguments);
		    if(!option.selectywlx){
		        throw "selectywlx 需要配置 selectywlx 属性"
            }
            option.ajax.data = function(params){
                // 传递到后端的参数
                var p = {
                    // 搜索框内输入的内容
                    selectInput: params.term,
                    //当前页号
                    page: params.page || 1,
                    //每页显示多少条记录，默认20条
                    rows: 100,
                    //排序字段
                    sidx : "YWLXCHSSHORT",

                    ywlx:option.selectywlx,

                    //排序方式
                    sord : "ASC"
                };
                return JSON.stringify(p);
            }

		    return option;
        },
		createDefaultOption:function(){
			return $.extend(true,{},this.base.apply(this,arguments),defaultOption);
		}
	});
});