﻿using COM.IFP.Common;
//using COM.IFP.LinqDB;
//using LinqToDB.Data;
//using LinqToDB.Linq;
//using LinqToDB;
using COM.IFP.SqlSugarN;
using SqlSugar;
using ORM.IFP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace DAL.ICS.BaseData
{
    public class SampleType
    {
        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();

        /// <summary>
        /// 根据作废标志查询4024的基础信息。
        /// </summary>
        /// <param name="zfbz"></param>
        /// <returns></returns>
        public List<ORM.IFP.YWDX4024> SampleTypeList(int? zfbz)
        {
            using SqlSugarClient db = DB.Create();
            var query = db.Queryable<ORM.IFP.YWDX4024, ORM.IFP.BaseData>((x, y) => new JoinQueryInfos(
                            JoinType.Inner, x.Gid == y.Gid))
                        .Where((x, y) => y.Ywlx == "4024" && y.Zfbz == (int)zfbz)
                        .OrderBy((x, y) => y.TreeCode, OrderByType.Asc)
                        .OrderBy((x, y) => y.Gid, OrderByType.Asc)
                        .Select((x, y) => new ORM.IFP.YWDX4024
                        {
                            Gid = y.Gid,
                            Pgid = y.Pgid,
                            Bname = y.Bname,
                            Sname = y.Sname,
                            Ywlx = y.Ywlx,
                            Compid = y.Compid,
                            Addtime = y.Addtime,
                            Lasttime = y.Lasttime,
                            Zfbz = y.Zfbz,
                            Ywbm = y.Ywbm,
                            TreeCode = y.TreeCode,
                            Beizhu = y.Beizhu,
                            KeLiDu = x.KeLiDu,
                            Cfzq = x.Cfzq,
                            Rlzq = x.Rlzq,
                            Nkzq = x.Nkzq,
                            Sfcf = x.Sfcf,
                            WeightRequirement = x.WeightRequirement,
                            Ywcj1072 = x.Ywcj1072,
                            CodeID = x.CodeID,
                            RCHYZB = x.RCHYZB,
                            RLHYZB = x.RLHYZB
                        });
            return query.ToList();
        }

        /// <summary>
        /// 通过场景查询样品类型集合。
        /// </summary>
        /// <param name="ywcj"></param>
        /// <returns></returns>
        public List<ORM.IFP.YWDX4024> SampleTypeListByScene(long ywcj)
        {
            using SqlSugarClient db = DB.Create();
            string strYwcj = ywcj.ToString();
            var query = db.Queryable<ORM.IFP.YWDX4024, ORM.IFP.BaseData>((x, y) => new JoinQueryInfos(
                                JoinType.Inner, x.Gid == y.Gid))
                            .Where((x, y) => y.Ywlx == "4024" && y.Zfbz == 0)
                            .Where((x, y) => x.Ywcj1072.Value.Contains(strYwcj))  // Contains条件
                            .OrderBy((x, y) => y.TreeCode, OrderByType.Asc)
                            .OrderBy((x, y) => y.Gid, OrderByType.Asc)
                            .Select((x, y) => new ORM.IFP.YWDX4024
                            {
                                Gid = y.Gid,
                                Pgid = y.Pgid,
                                Bname = y.Bname,
                                Sname = y.Sname,
                                Ywlx = y.Ywlx,
                                Compid = y.Compid,
                                Addtime = y.Addtime,
                                Lasttime = y.Lasttime,
                                Zfbz = y.Zfbz,
                                Ywbm = y.Ywbm,
                                TreeCode = y.TreeCode,
                                KeLiDu = x.KeLiDu,
                                Cfzq = x.Cfzq,
                                Rlzq = x.Rlzq,
                                Nkzq = x.Nkzq,
                                Sfcf = x.Sfcf,
                                WeightRequirement = x.WeightRequirement,
                                Ywcj1072 = x.Ywcj1072,
                                CodeID = x.CodeID,
                                RCHYZB = x.RCHYZB,
                                RLHYZB = x.RLHYZB
                            });
            return query.ToList();
        }
        /// <summary>
        /// 新增/修改
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public PFActionResult Submit(List<ORM.IFP.YWDX4024> source)
        {
            PFActionResult res = new PFActionResult() { success = false };
            try
            {
                lazy.Value.Submit(source.ToArray());
                res.success = true;
            }
            catch (Exception e)
            {
                res.success = false;
                res.msg = e.Message;
            }
            return res;
        }
        /// <summary>
        /// 场景列表
        /// </summary>
        /// <returns></returns>
        public List<ORM.IFP.BaseData> SceneList()
        {
            using SqlSugarClient db = DB.Create();
            var query = (from x in db.Queryable<ORM.IFP.BaseData>()
                         where x.Ywlx == "1072"
                         select x);
            return query.ToList();
        }
        public PFActionResult OrderChange(List<ORM.IFP.YWDX4024> source)
        {
            PFActionResult res = new PFActionResult() { success = false };
            try
            {
                using SqlSugarClient db = DB.Create();
                foreach (var item in source)
                {
                    db.Updateable<ORM.IFP.BaseData>()
                        .SetColumns(x => x.TreeCode == item.TreeCode)
                        .Where(x => x.Gid == item.Gid)
                        .ExecuteCommand();
                }
                res.success = true;
            }
            catch (Exception e)
            {
                res.success = false;
                res.msg = e.Message;
            }
            return res;
        }

        /// <summary>
        /// 编码规则列表
        /// </summary>
        /// <returns></returns>
        public List<IFP_BS_CODE_CONFIG> CodeRuleList()
        {
            using SqlSugarClient db = DB.Create();
            var query = (from x in db.Queryable<ORM.IFP.IFP_BS_CODE_CONFIG>()
                         orderby x.Codeid
                         select x);
            return query.ToList();
        }
    }
    //public class SampleType
    //{
    //    readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();

    //    /// <summary>
    //    /// 根据作废标志查询4024的基础信息。
    //    /// </summary>
    //    /// <param name="zfbz"></param>
    //    /// <returns></returns>
    //    public List<ORM.IFP.YWDX4024> SampleTypeList(int? zfbz)
    //    {
    //        using DataConnection db = DB.Create();
    //        var query = (from x in db.GetTable<ORM.IFP.YWDX4024>()
    //                     from y in db.GetTable<ORM.IFP.BaseData>()
    //                     where x.Gid == y.Gid && y.Ywlx == "4024" && y.Zfbz == (int)zfbz
    //                     orderby y.TreeCode ascending, y.Gid ascending
    //                     select new ORM.IFP.YWDX4024()
    //                     {
    //                         Gid = y.Gid,
    //                         Pgid = y.Pgid,
    //                         Bname = y.Bname,
    //                         Sname = y.Sname,
    //                         Ywlx = y.Ywlx,
    //                         Compid = y.Compid,
    //                         Addtime = y.Addtime,
    //                         Lasttime = y.Lasttime,
    //                         Zfbz = y.Zfbz,
    //                         Ywbm = y.Ywbm,
    //                         TreeCode = y.TreeCode,
    //                         Beizhu = y.Beizhu,
    //                         KeLiDu = x.KeLiDu,
    //                         Cfzq = x.Cfzq,
    //                         Rlzq = x.Rlzq,
    //                         Nkzq = x.Nkzq,
    //                         Sfcf = x.Sfcf,
    //                         WeightRequirement = x.WeightRequirement,
    //                         Ywcj1072 = x.Ywcj1072,
    //                         CodeID=x.CodeID,
    //                         RCHYZB=x.RCHYZB,
    //                         RLHYZB=x.RLHYZB
    //                     });
    //        return query.ToList();
    //    }

    //    /// <summary>
    //    /// 通过场景查询样品类型集合。
    //    /// </summary>
    //    /// <param name="ywcj"></param>
    //    /// <returns></returns>
    //    public List<ORM.IFP.YWDX4024> SampleTypeListByScene(long ywcj)
    //    {
    //        using DataConnection db = DB.Create();
    //        string strYwcj = ywcj.ToString();
    //        var query = (from x in db.GetTable<ORM.IFP.YWDX4024>()
    //                     from y in db.GetTable<ORM.IFP.BaseData>()
    //                     where x.Gid == y.Gid && y.Ywlx == "4024" &&
    //                     //todo包含场景
    //                     y.Zfbz == 0 && x.Ywcj1072.Value.Contains(strYwcj)
    //                     orderby y.TreeCode ascending, y.Gid ascending
    //                     select new ORM.IFP.YWDX4024()
    //                     {
    //                         Gid = y.Gid,
    //                         Pgid = y.Pgid,
    //                         Bname = y.Bname,
    //                         Sname = y.Sname,
    //                         Ywlx = y.Ywlx,
    //                         Compid = y.Compid,
    //                         Addtime = y.Addtime,
    //                         Lasttime = y.Lasttime,
    //                         Zfbz = y.Zfbz,
    //                         Ywbm = y.Ywbm,
    //                         TreeCode = y.TreeCode,
    //                         KeLiDu = x.KeLiDu,
    //                         Cfzq = x.Cfzq,
    //                         Rlzq = x.Rlzq,
    //                         Nkzq = x.Nkzq,
    //                         Sfcf = x.Sfcf,
    //                         WeightRequirement = x.WeightRequirement,
    //                         Ywcj1072 = x.Ywcj1072,
    //                         CodeID=x.CodeID,
    //                         RCHYZB = x.RCHYZB,
    //                         RLHYZB = x.RLHYZB
    //                     });
    //        return query.ToList();
    //    }
    //    /// <summary>
    //    /// 新增/修改
    //    /// </summary>
    //    /// <param name="source"></param>
    //    /// <returns></returns>
    //    public PFActionResult Submit(List<ORM.IFP.YWDX4024> source)
    //    {
    //        PFActionResult res = new PFActionResult() { success = false };
    //        try
    //        {
    //            lazy.Value.Submit(source.ToArray());
    //            res.success = true;
    //        }
    //        catch (Exception e)
    //        {
    //            res.success = false;
    //            res.msg = e.Message;
    //        }
    //        return res;
    //    }
    //    /// <summary>
    //    /// 场景列表
    //    /// </summary>
    //    /// <returns></returns>
    //    public List<ORM.IFP.BaseData> SceneList()
    //    {
    //        using DataConnection db = DB.Create();
    //        var query = (from x in db.GetTable<ORM.IFP.BaseData>()
    //                     where x.Ywlx == "1072"
    //                     select x);
    //        return query.ToList();
    //    }
    //    public PFActionResult OrderChange(List<ORM.IFP.YWDX4024> source)
    //    {
    //        PFActionResult res = new PFActionResult() { success = false };
    //        try
    //        {
    //            using DataConnection db = DB.Create();
    //            foreach (var item in source)
    //            {
    //                db.GetTable<ORM.IFP.BaseData>()
    //                    .Where(x => x.Gid == item.Gid)
    //                    .Set(x => x.TreeCode.Value, item.TreeCode)
    //                    .Update();
    //            }
    //            res.success = true;
    //        }
    //        catch (Exception e)
    //        {
    //            res.success = false;
    //            res.msg = e.Message;
    //        }
    //        return res;
    //    }

    //    /// <summary>
    //    /// 编码规则列表
    //    /// </summary>
    //    /// <returns></returns>
    //    public List<IFP_BS_CODE_CONFIG> CodeRuleList()
    //    {
    //        using DataConnection db = DB.Create();
    //        var query = (from x in db.GetTable<ORM.IFP.IFP_BS_CODE_CONFIG>()
    //                     orderby x.Codeid
    //                     select x);
    //        return query.ToList();
    //    }
    //}
}
