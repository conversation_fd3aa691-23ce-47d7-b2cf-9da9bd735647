"use strict";
(function(global,util){
    var rootpath = "/";
    var corepath = window.kycore && window.kycore.corepath || util.getBaseUrl();

    if(corepath && corepath[corepath.length-1]!="/"){
        corepath+="/"
    }

    // 默认配置
    var config = {
        rootpath:rootpath,
        corepath:corepath,
        baseUrl:corepath,
        requirejspath:corepath+"require.js",
        loading:true,
        platform:"default",
        require:{
            //baseUrl:corepath,
            //urlArgs: "r=" + Date.now(),
            paths:{
                "kyadmin":corepath.replace(/\/$/,""),
                "configs":corepath+"configs",
                "amdconfig":corepath+"configs/config.amd",
                "platform-default":corepath+"platform/default",
                "domReady":corepath+"lib/bower/domReady/domReady",
                "css":corepath+"lib/bower/require-css/css",
                "text":corepath+"lib/kjlib/require-plugins/text",
                "json":corepath+"lib/kjlib/require-plugins/src/json",
                "polyfill":corepath+"lib/kjlib/babel/polyfill/polyfill.min"
            }
        }
    }

    // 启动器配置
    window.kycore = window.kycore || {};

    var option = util.getBodyOption();

    config = util.extend(true,config,window.kycore,option)


    var loadingid = null;
    if(option.loading!== false){
        loadingid = setTimeout(function(){
            util.showLoading();
            util.showBody();
            loadingid = null;
        },0);
    }
    

    // 加载完毕后调用
    var after = function(){
        util.showBody();
        if(loadingid){clearTimeout(loadingid);loadingid=null;}
        else{
            util.hideLoading();
        }
    }

    var callback = config.callback && config.callback || null;
    
    util.loadScript([config.requirejspath],function(){
        requirejs.config(config.require);

        define("zutil",util)
        define("sysSetting",config)

        var res = ["amdconfig"];
        if (!global.Promise) { res.push("polyfill"); }
        res.push('domReady!');
        
        requirejs(res,function(amdConfig){
            amdConfig=amdConfig(corepath);
            //amdConfig.baseUrl=amdConfig.baseUrl||config.require.baseUrl;
            requirejs.config(amdConfig);
            callback && callback();
            var controllerName = util.getControllerName();
            if(config.platform===null || config.platform===false || config.platform==="none"){
                if(controllerName){
                    requirejs([controllerName],function(controller){
                        util.log("controller:"+controllerName+" loaded");
                        if(typeof controller == "function"){
                            controller(config);
                        }else if(controller && typeof controller.main == "function"){
                            controller.main(config);
                        }
                        after();
                    },function(err){
                        //alert("加载控制器 [" + controllerName + "] " + "失败");
                        //util.error("加载控制器 [" + controllerName + "] " + "失败");
                        util.hideLoading()
                        throw err;
                    })
                }else{
                    util.log("{paltform:null,controller:null}");
                    after();
                }
            }else{
                var platformName = option.platform,
                    platformPath;
                if(typeof platformName === "undefined"){
                    platformName="default";
                }
                if(amdConfig.paths["platform-"+platformName]){
                    platformPath = "platform-"+platformName;
                }else{
                    platformPath = platformName;
                }
                requirejs([platformPath],function(pf){
                    if(typeof pf === "function"){
                        pf = pf(option);
                    }
                    pf.catch(function(ex){
                        console.error(ex);
                    })

                    if(pf&&pf.constructor&&pf.constructor===window.Promise){
                        pf.then(after).catch(after)
                    }else{
                        after()
                    }
                })
            }
        })
    });
})(this,(function(global){
    var md = null;
    var mdtime = 200;
    var loadingid = null;
    var class2type = {};

    //class2type[ "[object Array]" ] = array
    "Boolean Number String Function Array Date RegExp Object Error"
    .split(" ")
    .forEach(function(name) {
        class2type[ "[object " + name + "]" ] = name.toLowerCase();
    });

    var util = {
        showLoading:function(){
            if(md){return;}
            md = document.createElement("div")
            var style = {
                backgroundColor : "#ffffff",
                position : "fixed",
                zIndex:9999,
                left:0,top:0,bottom:0,right:0,
                transition : "opacity "+(mdtime/1000)+"s"
            }
            for(var a in style){
                md.style[a] = style[a];
            }
            md.innerHTML='<div style="text-align: center;margin-top: 35vh;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="48px" height="60px" viewBox="0 0 24 30" style="enable-background:new 0 0 50 50" xml:space="preserve">\
                <rect x="0" y="7.6416" width="4" height="14.7168" fill="#3578ff" opacity="0.2">\
                    <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0s" dur="0.6s" repeatCount="indefinite"></animate>\
                    <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0s" dur="0.6s" repeatCount="indefinite"></animate>\
                    <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0s" dur="0.6s" repeatCount="indefinite"></animate>\
                </rect>\
                <rect x="8" y="5.1416" width="4" height="19.7168" fill="#3578ff" opacity="0.2">\
                    <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0.15s" dur="0.6s" repeatCount="indefinite"></animate>\
                    <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0.15s" dur="0.6s" repeatCount="indefinite"></animate>\
                    <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0.15s" dur="0.6s" repeatCount="indefinite"></animate>\
                </rect>\
                <rect x="16" y="7.3584" width="4" height="15.2832" fill="#3578ff" opacity="0.2">\
                    <animate attributeName="opacity" attributeType="XML" values="0.2; 1; .2" begin="0.3s" dur="0.6s" repeatCount="indefinite"></animate>\
                    <animate attributeName="height" attributeType="XML" values="10; 20; 10" begin="0.3s" dur="0.6s" repeatCount="indefinite"></animate>\
                    <animate attributeName="y" attributeType="XML" values="10; 5; 10" begin="0.3s" dur="0.6s" repeatCount="indefinite"></animate>\
                </rect>\
            </svg>\
            <div style="color:#aaa;">加载中...</div>\
            </div>'
            document.body.appendChild(md);
        },
        showLoadingInfo:function(msg){
            if(!md){return;}
            md.children[0].children[1].innerHTML=msg;
        },
        hideLoading:function(){
            if(!md){return;}
            md.style.opacity = 0;
            if(loadingid){
                clearTimeout(loadingid);
            }
            loadingid = setTimeout(function(){
                document.body.removeChild(md);
                md = null;
                loadingid = null;
            },mdtime)
        },
        getBaseUrl:function(){
            var scripts = document.getElementsByTagName("script");
            var filename = scripts[scripts.length-1].src.split("/");
            var baseUrl = filename.slice(0,filename.length-1).join("/")+"/";
            return baseUrl;
        },
        getOption:function(target){
            return eval("("+ (target.getAttribute("option")||target.getAttribute("data-option")) +")")||{}
        },
        getBodyOption:function(){
            return util.getOption(document.body);
        },
        getDefaultControllerName:function(){
            return global.location.pathname.replace(/.html$/,".js")
        },
        getControllerName:function(container){
            var controller = (container||document.body).getAttribute("controller");
            if(controller===""){
                // 有 controller 属性，但未配置值或值===""，则返回 html 文件名
                var n = window.location.pathname.split("/").slice(-1)[0].split(".").slice(0,-1)[0]
                n = n===undefined?"index":n;
                return n+".js"
            }
            else if(controller){
                return controller.replace("{filename}",this.getDefaultControllerName())
            }else{
                return null;
            }
        },
        loadScripts:function(urls,callback,errcallback){
            var succentcount = 0;
            var timeoutid = setTimeout(function(){
                timeoutid = null;
                errcallback("超时");
            },10000)
            for(var i=0;i<urls.length;i++){
                util.loadScript(urls[i],function(){
                    succentcount++;
                    if(succentcount==urls.length){
                        clearTimeout(timeoutid);
                        callback();
                    }
                })
            }
        },
        //加载script
        loadScript:function(url,callback,errcallback){
            var script=document.createElement('script');
            script.type="text/javascript";
            if(typeof(callback)!="undefined"){
                if(script.readyState){
                    script.onreadystatechange=function(){
                        if(script.readyState == "loaded" || script.readyState == "complete"){
                        script.onreadystatechange=null;
                        callback();}
                    }
                }else{script.onload=callback;}
            }
            script.src=url;
            document.body.appendChild(script);
        },
        log:function(){
            if(global.console && global.console.log){
                global.console.log.apply(global.console,arguments);
            }
        },
        error:function(){
            if(global.console && global.console.error){
                global.console.error.apply(global.console,arguments);
            }
        },
        showBody:function(){
            if(document.body.style.display == "none"){
                var c = document.body.style;
                c.opacity = "0";
                c.display = "block";
                c.transition = "opacity 0.2s";
                setTimeout(function(){c.opacity = "1";})
            }
        },
        loadStyles:function(urls){
            for(var i=0;i<urls.length;i++){
                util.loadStyle(urls[i]);
            }
        },
        loadStyle:function(url, callback){
            var link = document.createElement('link');
            link.type = 'text/css';
            link.rel = 'stylesheet';
            if (useOnload){
                link.onload = function() {
                    link.onload = function() {};
                    // for style dimensions queries, a short delay can still be necessary
                    setTimeout(callback, 7);
                }
            }else{
                var loadInterval = setInterval(function() {
                    for (var i = 0; i < document.styleSheets.length; i++) {
                        var sheet = document.styleSheets[i];
                        if (sheet.href == link.href) {
                            clearInterval(loadInterval);
                            return callback();
                        }
                    }
                }, 10);
            }
            link.href = url;
            head.appendChild(link);
            

            var link = document.createElement('link');
            link.type= "text/css";
            link.href = url;
            link.rel ="stylesheet";
            document.getElementsByTagName('head')[0].appendChild(link);
        },
        /**
            1. $.extend的用法。 第一个参数是决定是否需要深复制。 由 true, false。 默认是浅复制
            params:
            options => 接收传递过来的arguments 的中间参数。
            name =>  没对对象的key值。
            src =>   当传递的对象key值相同到时候。要和并
            copy =>   复制的value 值。
            copyIsArray =>  判断value对象是不是数组。
            clone =>    当深度复制的时候。需要新建一个变量。不会改变原始的值。
            target =>  最开始是默认值是取传进来的第一个参数。过后往后一直跳。$.extend(a,b,c); target 为 a, 为b,为c。
            i =>  决定当前target 等于参数中的某个值
            length =>
            deep => 默认是false 是决定是否需要深复制的参数。 true 是深复制。 false 是浅复制
        */
       extend: function() {
            var options, name, src, copy, copyIsArray, clone,
                target = arguments[ 0 ] || {},
                i = 1,
                length = arguments.length,
                deep = false;
            if ( typeof target === "boolean" ) {
                deep = target;
                target = arguments[ i ] || {};
                i++;
                
            }
            if ( typeof target !== "object" && typeof target !== "function" ) {
                target = {};
            }
            if ( i === length ) {
                target = this;
                i--;  // 把i 改为1
            }
        
            for ( ; i < length; i++ ) {
                if ( ( options = arguments[ i ] ) != null ) {
                    for ( name in options ) {
                        src = target[ name ];
                        copy = options[ name ];
                        if ( target === copy ) {
                            continue;
                        }
                        if ( deep && copy && ( util.isPlainObject( copy ) ||
                            ( copyIsArray = Array.isArray( copy ) ) ) ) {
                            
                            if ( copyIsArray ) {
                                
                                copyIsArray = false;
                                clone = src && Array.isArray( src ) ? src : [];
        
                            } else {
                                
                                clone = src && util.isPlainObject( src ) ? src : {};
                            }
                            target[ name ] = util.extend( deep, clone, copy );
                        } else if ( copy !== undefined ) {
                            target[ name ] = copy;
                        }
                    }
                }
            }
        
            // Return the modified object
            return target;
        },
        type:function(obj){
            if ( obj == null ) {
                return String( obj );
            }
            return typeof obj === "object" || typeof obj === "function" ?
                class2type[ class2type.toString.call(obj) ] || "object" :typeof obj;
    
        },
        isPlainObject: function( obj ) {
            var key;
            if ( !obj || util.type( obj ) !== "object" || obj.nodeType || (null != obj && obj == obj.window) ) {
                return false;
            }
            try {
                if ( obj.constructor &&
                    !({}).hasOwnProperty.call( obj, "constructor" ) &&
                    !({}).hasOwnProperty.call( obj.constructor.prototype, "isPrototypeOf" ) ) {
                    return false;
                }
            } catch ( e ) {
                return false;
            }
            for ( key in obj ) {}
            return key === undefined || ({}).hasOwnProperty.call( obj, key );
        },

        eachObj:function(f,o){for(a in o)f(o[a],i)},
        mapObj:function(f,o){var r = {};for(a in o) r[a]=f(o[a],i);return r;},
        // 柯里化 f:函数 [l:参数长度]  默认取 f.length
        curry:function (f,l) {
            var c = this,l = l || f.length,a=[].slice.call(arguments,1);
            return function() {
                a = a.concat([].slice.call(arguments))
                return a.length < l?util.curry.apply(c, [f].concat(a)):f.apply(c, a);
            }
        },
        extendClass:function(base,sub,prototypes){
            var classbprotos = [prototypes||{},sub.prototype];
            var __ = function(){this.constructor = sub;}
            sub.prototype = base === null ? Object.create(base) : (__.prototype = base.prototype, new __());
            for(var i=0;i<classbprotos.length;i++){
                for(var a in classbprotos[i]){
                    if(!sub.hasOwnProperty(a) && classbprotos[i].hasOwnProperty(a)){
                        sub.prototype[a]=classbprotos[i][a];
                    }
                }
            }
            return sub;
        }
    }
    return util;
})(this))