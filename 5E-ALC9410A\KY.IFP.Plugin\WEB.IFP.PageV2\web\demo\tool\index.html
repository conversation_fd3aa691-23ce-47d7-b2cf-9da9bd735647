<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="keywords" content="工具,图标">
		<title>图标</title>
	</head>
	<body class="flex" controller="index.js" option="{platform:'element'}">
		<ifp-page class="pages-icon" id="app" style="min-height:0;">
			<ifp-toolbar close v-if="ifpDialog">
				<ifp-button @click="returnValue()">确定</ifp-button>
			</ifp-toolbar>
			<ifp-searchbar class="searchbar">
				<ifp-searchbar-item label="ClassName">
					<ifp-input :value="font+' '+current" readonly style="width:200px;"></ifp-input>
				</ifp-searchbar-item>
				<ifp-searchbar-item label="匹配字符：">
					<ifp-input v-model="searchText" placeholder="请输入"></ifp-input>
				</ifp-searchbar-item>
				<ifp-searchbar-item>
					<el-checkbox v-model="onlyfilter" label="仅显示匹配项" border></el-checkbox>
				</ifp-searchbar-item>
			</ifp-searchbar>

			<div style="padding-left:2rem;">
				<span :class="font + ' ' + current" 
				style="display: inline-block;vertical-align: middle;font-size:30px;" ></span>
				<el-button :icon="current.split('-')[0] + ' ' + current">查看</el-button>

				<span>{{'<'+'el-button :icon="'+current.split('-')[0]+' ' + current + '">查看<'+'/el-button>'}}</span>
			</div>

			<div class="flex-item padding">
				<div v-for="group in showlist">
					<div v-if="group.list.length">
						<h3>{{group.title || group.name}}</h3>
						<div class="box">
							<span @click="current=item.name;font=group.name" v-for="item in group.list" :class="item.type+' '+item.name + (item.selected?' selected':'')" :title="item.title"></span>
						</div>
					</div>
				</div>
			</div>
    	</ifp-page>
	</body>
	<script src="/iofp/starter.js"></script>
</html>
