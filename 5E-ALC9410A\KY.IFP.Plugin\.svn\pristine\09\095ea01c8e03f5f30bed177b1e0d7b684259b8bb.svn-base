﻿namespace COM.IFP.Common
{
    public static class Server
    {
        public interface Expect
        {
            void Start(int port = 80, string root = ".", string page = "web");
            void Close();
        }

        internal static Expect entity;
        public static void Start(int port = 80, string root = ".", string page = "web")
        {
            entity.Start(port, root, page);
        }
        public static void Close()
        {
            entity.Close();
        }
    }
}
