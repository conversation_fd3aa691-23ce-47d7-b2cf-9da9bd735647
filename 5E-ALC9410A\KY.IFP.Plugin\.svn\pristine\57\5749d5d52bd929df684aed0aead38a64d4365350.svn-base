﻿using COM.IFP.Common;
//using COM.IFP.LinqDB;
using COM.IFP.SqlSugarN;
using ORM.IFP;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace API.ICS.BaseData
{
    public class Ywdx4004API
    {
        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();

        public object Select(JsonElement jele)
        {
            var filter = jele.GetValue<IList<YWDX4004>>("filter");
            PageModel<YWDX4004> paging = null;
            var tmp = new JsonElement();
            if (jele.TryGetProperty("paging", out tmp) == true)
                paging = jele.GetValue<PageModel<YWDX4004>>("paging");

            if (paging is not null)
            {
                var res = lazy.Value.Select(filter, paging);
                return res;
            }
            else
            {
                //返回数组
                var res = lazy.Value.Select(filter);
                return res;
            }
            //YWDX4004 obj = new YWDX4004() { Bname = new Field<string>() { Order = Order.ASC } };
            //var res = lazy.Value.Select<YWDX4004>(
            //   new[] { obj }, null);
            //return res;
        }

    }
}
