﻿namespace COM.IFP.Common
{
    /// <summary>
    /// 通用的action返回实体类
    /// </summary>
    public class PFActionResult
    {
        /// <summary>
        /// 接口调用成功状态
        /// </summary>
        public bool success { set; get; }

        /// <summary>
        ///  成功或失败的错误代码,待使用
        /// </summary>
        public string code { set; get; }

        /// <summary>
        /// 成功或失败的消息
        /// </summary>
        public string msg { set; get; }
        /// <summary>
        /// 其他扩展数据
        /// </summary>
        public object data { set; get; }

        public PFActionResult()
        {
        }

        public PFActionResult(bool _success, string _msg, object _data)
        {
            this.success = _success;
            this.msg = _msg;
            this.data = _data;
        }

    }


}
