﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//using COM.IFP.LinqDB;
//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{
    /// <summary>
    /// 煤种区间
    /// </summary>
    [SugarTable("IFP_BS_MZ4010QJ")]
    public class YWDX4010_QJ
    {
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(36)")]
        public Field<string> Gid { get; set; }

        [SugarColumn(ColumnName = "HYZJ1004", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Hyzj1004 { get; set; }

        [SugarColumn(ColumnName = "LJGX1003", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Ljgx1003 { get; set; }

        [SugarColumn(ColumnName = "QJFH1", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Qjfh1 { get; set; }

        [SugarColumn(ColumnName = "QJFH2", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Qjfh2 { get; set; }

        [SugarColumn(ColumnName = "VALUE1", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,6)")]
        public Field<decimal> Value1 { get; set; }

        [SugarColumn(ColumnName = "VALUE2", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,6)")]
        public Field<decimal> Value2 { get; set; }

        [SugarColumn(ColumnName = "MZ4010", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Mz4010 { get; set; }
    }

    //[Table(Name = "IFP_BS_MZ4010QJ")]
    //public class YWDX4010_QJ
    //{
    //    [Column(Name = "GID", IsPrimaryKey = true, DbType = "nvarchar(36)")]
    //    public Field<string> Gid { get; set; }

    //    [Column(Name = "HYZJ1004", DbType = "bigint")]
    //    public Field<long> Hyzj1004 { get; set; }

    //    [Column(Name = "LJGX1003", DbType = "bigint")]
    //    public Field<long> Ljgx1003 { get; set; }

    //    [Column(Name = "QJFH1", DbType = "bigint")]
    //    public Field<long> Qjfh1 { get; set; }

    //    [Column(Name = "QJFH2", DbType = "bigint")]
    //    public Field<long> Qjfh2 { get; set; }

    //    [Column(Name = "VALUE1", DbType = "decimal(18, 6)")]
    //    public Field<decimal> Value1 { get; set; }

    //    [Column(Name = "VALUE2", DbType = "decimal(18, 6)")]
    //    public Field<decimal> Value2 { get; set; }

    //    [Column(Name = "MZ4010", DbType = "bigint")]
    //    public Field<long> Mz4010 { get; set; }

    //}
}
