﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title></title>

    <style>
    </style>
</head>
<body class="flex" controller="menuIcon.js" option="{platform:'element'}">

    <div id="app" class="pages-icon flex flex-item" >
        <div class="searchbar">
            匹配字符：
            <ifp-input v-model="searchText" placeholder="请输入"></ifp-input>
            <el-button icon="kjicon kjicon-baocun" @click="onSave">确定</el-button>
            <el-button icon="kjicon kjicon-tuichu" @click="onExit">退出</el-button>
        </div>
        <div class="flex-item padding">
            <div v-for="group in showlist">
                <div v-if="group.list.length">
                    <h3>{{group.title || group.name}}</h3>
                    <div class="box">
                        <span @click="searchText=item.name" v-for="item in group.list" :class="item.type+' '+item.name + (item.selected?' selected':'')" :title="item.title"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>
