define(["ELEMENT"],function (Element) {

  // 获取 table vm
  function getColumnConfigs(originColumns){
    return originColumns.map(item=>{
      if(item.label === "日期"){
        item.label = "日期1";
      }
      return {
        fixed: item.fixed,
        headerAlign: item.headerAlign,
        label: item.label,
        order: item.order,
        id: item.property,
        property: item.property,
        type: item.type,
        width: item.width,
        children:item.children&&getColumnConfigs(item.children)
      }
    })
  }

  const components = {
    ifpTable:{
      extends:Element.Table,
      data(){
        return {
          
        }
      },
      computed:{
        ifpcolumns(){
          return getColumnConfigs(this.store.states.originColumns);
        }
      },
      mounted(){
        console.log(getColumnConfigs(this.store.states.originColumns))

        // 下面是动态修改显示列

        /*
        if(this.store.states.originColumns.length>1){
          this.store.states.originColumns.splice(0,1);
          this.columns.splice(0,1);
        }
        */
      }
    },
    ifpButtonLx:{
      props:["table"],
      template:`
      <el-button icon="el-icon-refresh" @click="visible=true">列显设置
        <span @click.stop="">
        <el-dialog class="subpage" title="列显设置" @mousedown.stop
            :visible.sync="visible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :fullscreen="false"
            width="800px">
            <lxsz @click.stop=""
                style="height:500px;"
                class="flex-item"
                :columns="columns"
                @cancel="visible=false"
                @sucess="visible=false"
            ></lxsz>
        </el-dialog>
        </span>
      </el-button>
      `,
      data(){
        return {
          visible:false,
          columns:[]
        }
      },
      mounted(){
        setTimeout(()=>{
          this.$set(this,"columns",this.$parent.$refs[this.table].ifpcolumns)
        },1000)
      }
    }
  }

  return {
    el: "#app",
    data() {
      return {
        viewer: {
          paging: {
            size: 20,
            page: 1,
            records: 20,
          }
        },
        columns:[],
        data: Array(20).fill().map((item,i)=>({id:i+1,date:'2021-07-15'}))
      }
    },
    computed: {//计算属性
    },
    created() {
    },
    methods: {
      updateTable() {

      },
      append(data) {

      },
      remove(node, data) {

      }
    }
  };

})