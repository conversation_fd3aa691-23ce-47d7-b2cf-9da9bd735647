;define([
    "/iofp/lib/videojs/video.vue.js"
],function(videoPlayer){
        
    let src = 'https://d2zihajmogu5jn.cloudfront.net/bipbop-advanced/bipbop_16x9_variant.m3u8';

        return {
            el:"#app",
            components:{videoPlayer},
            data(){
                return {
                    dialogShow:false,
                    dialogTitle:"XXXXXXX",

                    // 播放器1属性
                    videoOptions: {
                        width:600,
                        autoplay: true,
                        controls: true,
                        language:'zh-Hans',
                        poster:'http://vjs.zencdn.net/v/oceans.png',
                        sources: [
                            {
                                src:'',
                                type: "application/x-mpegURL"
                            }
                        ],
                        liveTracker:{

                        }
                    },
                    playState:true
                }
            },
            methods:{
                play(){
                    // 修改 src
                    // this.video1Option.sources[0].src="xxxxx"

                    // 开启播放器
                    this.playState = !this.playState;
                },

                play2(){
                    this.videoOptions.sources[0].src=src;
                }
            }
        };
    });