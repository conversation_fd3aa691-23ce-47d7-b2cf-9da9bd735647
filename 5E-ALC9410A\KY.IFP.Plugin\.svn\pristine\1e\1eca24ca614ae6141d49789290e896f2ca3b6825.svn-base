# 按钮 ifp-button

继承于 el-button

## 相关资源

### 按钮默认配置

文件：```KY.IFP.Plugin\WEB.IFP.Page\web\iofp\mixins\components\index.js```

关键字```defaultButtons```


现有配置(以源码中配置为准)，开发人员可自行维护
```js
{
    "保存":{ icon:"kjicon kjicon-baocun", type:"primary" },
    "新增":{ icon:"el-icon-plus", type:"primary" },
    "查看":{ icon:"el-icon-view" },
    "修改":{ icon:"el-icon-edit" },
    "删除":{ icon:"el-icon-delete" },
    "查询":{ icon:"el-icon-refresh-search" },
    "重置":{ icon:"el-icon-refresh-left" },
    "退出":{ icon:"kjicon kjicon-tuichu" }
}
```

## 新增属性

* code 权限编码,没有权限时，不渲染
* role 使用已配置按钮默认属性

## 特性

* icon 若未配置，则根据内容生成 已配置的对应icon
* type 若未配置，则根据内容生成 已配置的对应type

## 代码示例

以下写法渲染结果相等

```html
<ifp-button>保存</ifp-button>
<el-button icon="kjicon kjicon-baocun" type="primary">保存</ifp-button>
```

以下写法渲染结果相等
```html
<ifp-button role="保存">保存XXX</ifp-button>
<el-button icon="kjicon kjicon-baocun" type="primary">保存XXX</ifp-button>
```


## 扩展组件

### 退出按钮

用于关闭当前页面，已集成至 [ifp-search](./searchbar) 控件，也可以单独使用

#### 方式1 ：使用 `ifp-button-close` 标签

用于 toolbar 工具栏，放在最后

会触发页面的 `cancel` 事件

```html
<div id="app">
    <div class="toolbar">
        <ifp-button-close />
    </div>
</div>
```

#### 方式2：this.$emit("cancel")

直接触发 `cancel` 事件，用于与其他控件集成

```html
<div id="app">
    <div class="toolbar">
        <el-button @click="$emit('cancel')">退出</el-button>
    </div>
</div>
```

#### 方式3：this.$close()

直接调用关闭方法，不会触发 `cancel` , 无法监听

```html
<div id="app">
    <div class="toolbar">
        <el-button @click="$close">退出</el-button>
    </div>
</div>
```


### 列显设置按钮

[列显设置](./lxsz)