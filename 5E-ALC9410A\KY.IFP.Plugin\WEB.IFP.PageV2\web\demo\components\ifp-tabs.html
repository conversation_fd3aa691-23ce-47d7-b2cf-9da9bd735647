<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="控件,下拉">
    <title>tabs 页签</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" class="padding">
        <ifp-tabs class="flex-item" flex :tab-position="position" v-model="activeName" @tab-click="handleClick">
          <ifp-tab-pane label="基础示例" name="first" class="flex">

            <!--示例1-->
            <ifp-tabs class="flex-item" flex v-model="activeName2" style="border:1px solid #000;padding:1rem;">
                <ifp-tab-pane label="用户管理" name="first" class="flex" style="background-color: #fff;">
                    <ifp-searchbar @search="()=>{this.$message('333')}">
                        <ifp-form-item label="姓名">
                            <ifp-input></ifp-input>
                        </ifp-form-item>
                    </ifp-searchbar>
                    <div class="flex-item padding">
                        <el-table border height="100%" :data="[{mark:'表格适应容器高度'}]">
                            <el-table-column label="列1" prop="mark"></el-table-column>
                            <el-table-column label="列2"></el-table-column>
                            <el-table-column label="列3"></el-table-column>
                            <el-table-column label="列4"></el-table-column>
                            <el-table-column label="列5"></el-table-column>
                        </el-table>
                    </div>
                </ifp-tab-pane>
                <ifp-tab-pane label="系统设置" name="second" class="flex" style="background-color: #fff;">
                    <h2>系统设置内容xxx</h2>
                    <h3>系统设置内容xxx</h3>
                    <div class="flex-item" style="background-color:#000;">
                        <!--内容会占据剩余空间-->
                    </div>
                </ifp-tab-pane>
            </ifp-tabs>

          </ifp-tab-pane>
          <ifp-tab-pane label="使用说明" name="second">
            页签位置
            <el-radio v-for="item in ['left','right','top','bottom']" :key="item" 
            v-model="position" :label="item">{{item}}</el-radio>

            <h3>关于内容自适应tabs高度</h3>
            <p>默认情况，tabs高度根据内容改变，但有的时候，我们需要将内容适应容器高度</p>
            <p>具体做法如下</p>
            <h4>
                1. 使用 ifp-tabs、ifp-tab-pane 替换 el-tabs、el-tab-pane 组件
            </h4>
            <h4>
                2. 为 ifp-tabs 设置高度
            </h4>
            <p>
                这里分两种情况：
            </p>
            <p>
                第一种：ifp-tabs 固定高度,可设置 style="height:100px;" <br>
                第二种：ifp-tabs 作为 flex-item 自适应高度,可设置 class="flex-item"，同时父元素设置 class="flex" 
            </p>
            <h4>
                3. 为 ifp-tabs 设置 flex 属性
                {{code}}
            </h4>
            <h4>
                4. 为 ifp-tab-pane 设置 class="flex"
            </h4>
            <h4>
                4. 为 ifp-tab-pane 内容设置 class="flex-item"
            </h4>
          </ifp-tab-pane>
          <ifp-tab-pane label="容器适应内容" name="third">
            
            <h1>tabs 容器适应内容 演示</h1>
            <ifp-tabs v-model="activeName2" style="border:1px solid #000;padding:1rem;">
                <ifp-tab-pane label="用户管理" name="first" class="flex" style="background-color: #fff;">
                    <h2>ifp-tabs 不使用扩展属性 flex</h2>
                    <h2>渲染效果 与 el-tabs 一致</h2>
                    <h2>二者等效</h2>
                    <div class="flex-item" style="background-color:#000;color:#fff;padding:1rem;">
                        <p>el-tabs 默认效果</p>
                        <p>el-tabs 默认效果</p>
                        <p>el-tabs 默认效果</p>
                        <p>el-tabs 默认效果</p>
                        <p>el-tabs 默认效果</p>
                        <p>el-tabs 默认效果</p>
                    </div>
                </ifp-tab-pane>
                <ifp-tab-pane label="系统设置" name="second" class="flex" style="background-color: #fff;">
                    <h2>系统设置内容xxx</h2>
                    <h3>系统设置内容xxx</h3>
                    <div class="flex-item" style="background-color:#000;color:#fff;">
                        今天是2021年<br/>
                        12月4日
                    </div>
                </ifp-tab-pane>
            </ifp-tabs>
          </ifp-tab-pane>
          <ifp-tab-pane label="内容适应容器" name="fourth">
            
            <h1>tabs 内容适应容器 演示</h1>

            <ifp-tabs flex v-model="activeName2" style="height:300px;border:1px solid #000;padding:1rem;">
                <ifp-tab-pane label="用户管理" name="first" class="flex" style="background-color: #fff;">
                    <h2>用户管理内容xxx</h2>
                    <div class="flex-item" style="background-color:#000;">
                        <!--内容会占据剩余空间-->
                    </div>
                </ifp-tab-pane>
                <ifp-tab-pane label="系统设置" name="second" class="flex" style="background-color: #fff;">
                    <h2>系统设置内容xxx</h2>
                    <h3>系统设置内容xxx</h3>
                    <div class="flex-item" style="background-color:#000;">
                        <!--内容会占据剩余空间-->
                    </div>
                </ifp-tab-pane>
            </ifp-tabs>

          </ifp-tab-pane>
        </ifp-tabs>
    </ifp-page>

    <script src="/iofp/starter.js"></script> 
</body>
</html>