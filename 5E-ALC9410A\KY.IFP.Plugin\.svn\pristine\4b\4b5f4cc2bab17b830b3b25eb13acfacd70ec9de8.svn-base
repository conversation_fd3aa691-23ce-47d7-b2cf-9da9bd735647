(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "kyadmin/utils/zutil"], factory);
    }
})(function (require, exports) {
    "use strict";
    exports.__esModule = true;
    var zutil = require("kyadmin/utils/zutil");
    var pluses = /\+/g;
    function isFunction(o) { return typeof o === "function"; }
    function encode(s) {
        return cookie.raw ? s : encodeURIComponent(s);
    }
    function decode(s) {
        return cookie.raw ? s : decodeURIComponent(s);
    }
    function stringifyCookieValue(value) {
        return encode(cookie.json ? JSON.stringify(value) : String(value));
    }
    function parseCookieValue(s) {
        if (s.indexOf('"') === 0) {
            // This is a quoted cookie as according to RFC2068, unescape...
            s = s.slice(1, -1).replace(/\\"/g, '"').replace(/\\\\/g, '\\');
        }
        try {
            // Replace server-side written pluses with spaces.
            // If we can't decode the cookie, ignore it, it's unusable.
            // If we can't parse the cookie, ignore it, it's unusable.
            s = decodeURIComponent(s.replace(pluses, ' '));
            return cookie.json ? JSON.parse(s) : s;
        }
        catch (e) { }
    }
    function read(s, converter) {
        var value = cookie.raw ? s : parseCookieValue(s);
        return isFunction(converter) ? converter(value) : value;
    }
    function getDefault() {
        return cookie.defaults;
    }
    var cookie = function (key, value, options) {
        // Write
        if (value !== undefined && !isFunction(value)) {
            options = zutil.extend({}, getDefault(), options);
            if (typeof options.expires === 'number') {
                var days = options.expires, t = options.expires = new Date();
                t.setTime(+t + days * 864e+5);
            }
            return (document.cookie = [
                encode(key), '=', stringifyCookieValue(value),
                options.expires ? '; expires=' + options.expires.toUTCString() : '',
                options.path ? '; path=' + options.path : '',
                options.domain ? '; domain=' + options.domain : '',
                options.secure ? '; secure' : ''
            ].join(''));
        }
        // Read
        var result = key ? undefined : {};
        var cookies = document.cookie ? document.cookie.split('; ') : [];
        for (var i = 0, l = cookies.length; i < l; i++) {
            var parts = cookies[i].split('=');
            var name = decode(parts.shift());
            var cookie = parts.join('=');
            if (key && key === name) {
                // If second argument (value) is a function it's a converter...
                result = read(cookie, value);
                break;
            }
            // Prevent storing a cookie that we couldn't decode.
            if (!key && (cookie = read(cookie)) !== undefined && result) {
                result[name] = cookie;
            }
        }
        return result;
    };
    cookie.defaults = {};
    cookie.removeCookie = function (key, options) {
        if (cookie(key) === undefined) {
            return false;
        }
        // Must not alter options, thus extending a fresh object...
        cookie(key, '', zutil.extend({}, options, { expires: -1 }));
        return !cookie(key);
    };
});
//# sourceMappingURL=data:application/json;base64,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