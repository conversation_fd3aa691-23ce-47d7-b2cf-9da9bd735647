define(["require","jquery","./base.input","ztree","jclass"],function(require,$,base,zTree,jclass){
	//zTree = zTree;
	/* var data = [
		{code:"0001",text:"节点1",other:"33"},
		{code:"00010001",text:"节点1-1",other:"33"},
		{code:"00010002",text:"节点1-2",other:"33"},
		{code:"00010003",text:"节点1-3",other:"33"},
		{code:"00010004",text:"节点1-4",other:"33"}
	]
	*/
	var getFontCss = function (treeId, treeNode) {
		return (!!treeNode.highlight) ? {color:"#A60000", "font-weight":"bold"} : {color:"#333", "font-weight":"normal"};
	}
	var defaultCallbacks = {
		/*
		beforeClick:[
			//点击父节点名称展开/关闭节点
			function(treeId, treeNode){
				if (treeNode.isParent) {
				    this.ztree.expandNode(treeNode);
				    return false;
				} else {
				    return true;
				}
			}
		]*/
	}
	
	var events = [
		'beforeAsync','beforeCheck','beforeClick','beforeCollapse',
		'beforeDblClick','beforeDrag','beforeDragOpen','beforeDrop',
		'beforeEditName','beforeExpand','beforeMouseDown','beforeMouseUp',
		'beforeRemove','beforeRename','beforeRightClick','onAsyncError',
		'onAsyncSuccess','onCheck','onClick','onCollapse','onDblClick','onDrag',
		'onDragMove','onDrop','onExpand','onMouseDown','onMouseUp','onNodeCreated',
		'onRemove','onRename','onRightClick'
	];
	
	
	var getCallback = function(tree,type){
		return function(){
			
		}
	}
	var defaultOption = {
		treeoption:{

			async: {
				enable: true,
				contentType:'application/json',
				//url:"data/treedata.json",
				autoParam:["id"]
//				otherParam:{"otherParam":"zTreeAsyncTest"}
			},

			data: {
				simpleData: {
					enable: true,
					idKey: "id",
					pIdKey: "pid",
					//rootPId: ""
				},
				key:{
					name:"name",
					isParent:"isParent"
				}
			},
			
			callback: {
		
			},
		    view: {
		        dblClickExpand: false,
		        showLine: false,
		        selectedMulti: false,
		        showIcon:false,
				fontCss: getFontCss
		    }
		},
		multiselect:false,
		idfield:"treecode",
		textfield:"bname",
		isparentfield:"isparent",
		rootpid:"",
		async:false,
		asyncurl:"",
		//如果配置了pid则直接取，否则通过id分割字符取
		pidfield:null,
		split:4,
		autoexpand:true
	}
	
	return jclass(base,{
		name:"control-tree",
		
		option:null,
		render:function(){
			var rev = this.base.apply(this,arguments);
			var _this = this;
			$.each(events,function(i,item){
				if(typeof _this.option.treeoption.callback[item] =="string"){
					var fn = _this.controller[_this.option.treeoption.callback[item]] ;
					if(fn){
						_this.bind(item,fn,_this.controller);
					}
				}
				
				_this.option.treeoption.callback[item] = function(){
					var args = [item];
					for(var j = 0 ; j<arguments.length;j++){
						args.push(arguments[j]);
					}
					return _this.trigger.apply(_this,args);
				}
			});
			this.$container.addClass("ztree");
			//配置了异步加载接口
			if(_this.option.treeoption.async && _this.option.treeoption.async.enable){
				_this.ztree = zTree.init(_this.$container, _this.option.treeoption);
			}else{
				this.reload();
			}
		},
		convert:function(data){
			var _this = this;
			
			//生成标准数据
			var genItem = this.option.pidfield?
			function(item){
				return {
					id:item[_this.option.idfield],
					pid:item[_this.option.pidfield],
					isParent:item[_this.option.isparentfield],
					name:item[_this.option.textfield],
					data:item
				}
			}:function(item){
				return {
					id:item[_this.option.idfield],
					pid:item[_this.option.idfield].substr(0,item[_this.option.idfield].length-_this.option.split),
					name:item[_this.option.textfield],
					isParent:item[_this.option.isparentfield],
					data:item
				}
			};
			
			return $.map(data,function(item,i){
				return genItem(item);
			});
		},
		
		destroy:function(){
			this.ztree.destroy();
			this.base();
		},

		addNodes:function(){
			var newData = [].slice.call(arguments,1,arguments.length);
			var args = [arguments[0]].concat(this.convert(newData));
			return this.ztree.addNodes.apply(this.ztree,args);
		},
		
		updateNode:function(){
			return this.ztree.updateNode.apply(this.ztree,arguments);
		},
		getArrayNodes : function(){
			if(this.ztree){
				return this.ztree.transformToArray(this.ztree.getNodes());
			}
			return null;
		},
		getCheckedNodes:function(checked){
			return this.ztree.getCheckedNodes(checked);
		},
		/**
		 * 获得展开的tree节点Id
		 */
		getOpenTreeIds : function(){
			var openIds = [];
			var nodes = this.getArrayNodes();
			if(nodes){
				for(var i=0;i<nodes.length;i++){
					if(nodes[i].open){
						openIds.push(nodes[i].id);
					}
				}
			}
			return openIds;
		},
		/**
		 * 根据Id展开tree的节点
		 */
		openTreeNodeByIds : function(nodeIds){
			for(var i=0;i<nodeIds.length;i++){
				var node = this.getNodeByParam("id",nodeIds[i]);
				if(node){
					this.ztree.expandNode(node, true, false);
				}
			}
		},
		value:function(v){
			this.my_data = v;
			if(v){
				this.reload(v);
			}else{
				return this.my_data;
			}
		},
		data:function(v){
			v&&(this.my_data = v);
			return this.my_data;
		},
		getNodeByParam : function(key, value, parentNode){
			return this.ztree.getNodeByParam(key, value, parentNode);
		},
		selectNode: function (nodeId) {
			var setting =  this.ztree.setting;
			var nodes = this.ztree.getNodes();  
			if (nodes.length>0) {  
			    var node = this.ztree.getNodeByParam("id", nodeId);  
			    if (node) {  
			    	this.ztree.selectNode(node);  
			    }  
			}  
		},
		checkNode : function(reeNode, checked, checkTypeFlag, callbackFlag){
			if(!reeNode){this.log.warn("不能选择节点："+reeNode);return;}
			this.ztree.checkNode(reeNode, checked, checkTypeFlag, callbackFlag);
		},
		reload:function(pdata){
			var _this = this;
			var rerender = function(list){
				var data = _this.convert(list);
				_this.ztree = $.fn.zTree.init(_this.$container, _this.option.treeoption,data);
				if(_this.option.autoexpand){
					_this.ztree.expandAll(true);
				}
				_this.trigger("reload");
				return list;
			}
			if(pdata && pdata.constructor == Array){
				return Promise.resolve(rerender(pdata));
			}else{
				return this.base.apply(this,arguments)
				.then(function(){
					rerender(_this.data())
				})
			}
		},
		

		createDefaultOption:function(){
			return $.extend({},defaultOption);
		},
		//更新配置
		updateOption:function(rev){
			var _this = this;
			if(!rev){
				rev = this.option;
			}
			rev.treeoption.data.simpleData.rootPId = rev.rootpid;
			if(rev.asyncurl){
				rev.treeoption.async.url=rev.asyncurl;
			}
			rev.treeoption.async.enable=rev.async;
			if(rev.multiselect){
				$.extend(true,rev,{
					treeoption:{
						check:{
							enable: true,
							chkboxType:{ "Y" : "ps", "N" : "ps" }
						}
					}
				});
			}

			if(rev.async){
				rev.treeoption.async.dataFilter = function(treeId, parentNode, responseData) {
					if (responseData) {
						return _this.convert(responseData);
					}else{
						return responseData;
					}
				};
			}
			
			return rev;
		},
		createOption:function(){
			return this.updateOption(this.base.apply(this,arguments));
		},
		bind:function(type,fn,context){
			this.base(type,function(){
				fn.apply(this,[].slice.call(arguments,1))
			})
		}
	})
});