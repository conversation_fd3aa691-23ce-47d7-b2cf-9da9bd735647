<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="示例页面,入场验收">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>070 归批机卸料</title>
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button role="设置">归批机配置</ifp-button>
            <ifp-button>卸料</ifp-button>
            <ifp-button>取桶</ifp-button>
            <ifp-button>出桶记录</ifp-button>
            <ifp-button>取桶</ifp-button>
            <ifp-button>刷新</ifp-button>
        </ifp-toolbar>

        <div class="margin">
            <span class="margin-right">归批机状态</span>
            <ifp-legend code="cyj" value="1"></ifp-legend>
        </div>

        <div class="margin flex flex-item flex-row">
            <ifp-panel-table class="flex-item" title="归批机信息" >
                <ifp-table>
                    <ifp-table-column label="采样状态"></ifp-table-column>
                    <ifp-table-column label="采样编码"></ifp-table-column>
                </ifp-table>
            </ifp-panel-table>
            <ifp-panel theme="style1" border flex class="margin-left" 
            title="运行消息" style="max-width:50%;min-width:200px;">
                <div class="padding-left padding-right" v-for="item in 5">xxxxx xxxxxx xxx xxxx xxxxxx</div>
            </ifp-panel>
        </div>

        <div class="margin flex flex-item flex-row">
            <ifp-panel-table class="flex-item" title="卸料任务列表">
                <ifp-table>
                    <ifp-table-column label="采样状态"></ifp-table-column>
                    <ifp-table-column label="采样编码"></ifp-table-column>
                </ifp-table>
            </ifp-panel-table>
            <ifp-panel-table class="margin-left" style="width:35%;" title="样桶子表">
                <ifp-table>
                    <ifp-table-column label="采样状态"></ifp-table-column>
                    <ifp-table-column label="采样编码"></ifp-table-column>
                </ifp-table>
            </ifp-panel-table>
        </div>
    </ifp-page>
    <script src="/iofp/starter.js"></script>
</body>
</html>