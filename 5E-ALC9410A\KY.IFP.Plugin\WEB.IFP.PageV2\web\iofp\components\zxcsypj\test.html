<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线测水样瓶架</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="./dist/index.css">
</head>
<body>
    <div id="app">
        <zxcsypj :items="list" 
        :tooltip="createTooltip"
        @item-click="item_click"
        @item-select="item_select"></zxcsypj>
    </div>
    <script src="https://unpkg.com/vue"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="./dist/index.js"></script>
    <script>
        Vue.use(ELEMENT);
        new Vue({
            components:{
                zxcsypj:zxcsypj.Control
            },
            el:'#app',
            data(){
                return {
                    list: [
                        { Pos: 1, Status: 1 },
                        { Pos: 2, Status: 2 },
                        { Pos: 3, Status: 3 },
                        { Pos: 4, Status: 4 },
                        { Pos: 5, Status: 5 },
                        { Pos: 6, Status: 6 },
                        { Pos: 7, Status: 2 },
                        { Pos: 8, Status: 2 },
                        { Pos: 9, Status: 2 },
                        { Pos: 10, Status: 1 },
                        { Pos: 11, Status: 1 },
                        { Pos: 12, Status: 1 },
                        { Pos: 13, Status: 1 },
                        { Pos: 14, Status: 1 },
                        { Pos: 15, Status: 1 },
                        { Pos: 16, Status: 1 },
                        { Pos: 17, Status: 1 },
                        { Pos: 18, Status: 1 },
                        { Pos: 19, Status: 1 },
                        { Pos: 20, Status: 1 }
                    ]
                }
            },
            methods:{
                getTooltip(){
                    return "标题"
                },
                item_select(item){
                    alert(`指定了 canselect:true 才会触发此事件 ${item.xh}`);
                },
                item_click(item){
                    alert(`所有元素都可以触发此事件 ${item.xh}`);
                },
                showSelected(){
                    let item = this.$refs.ypj1.getSelect();
                    alert(item?.xh||'未选择')
                },
                createTooltip(item){
                    if(item.state>1){
                        return `指标1:2222
                        指标1:2222
                        指标1:2222
                        指标1:2222`
                    }
                    return;
                }
            }
        })
    </script>
</body>
</html>