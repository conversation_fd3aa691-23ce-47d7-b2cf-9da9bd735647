define([],function(){
 
	var makeString = function(object) {
		if (object == null) return '';
		return '' + object;
	}
	
	var escapeRegExp = function(str) {
		  return makeString(str).replace(/([.*+?^=!:${}()|[\]\/\\])/g, '\\$1');
	}
	var chnNumChar = ["零","一","二","三","四","五","六","七","八","九"];
    var chnUnitSection = ["","万","亿","万亿","亿亿"];
    var chnUnitChar = ["","十","百","千"];
	
	var defaultToWhiteSpace = function(characters) {
	  if (characters == null)
	    return '\\s';
	  else if (characters.source)
	    return characters.source;
	  else
	    return '[' + escapeRegExp(characters) + ']';
	}
 
    return {
    	trim: function(str, characters) {
    		var nativeTrim = String.prototype.trim;
    		str = makeString(str);
    		if (!characters && nativeTrim) return nativeTrim.call(str);
    		characters = defaultToWhiteSpace(characters);
    		return str.replace(new RegExp('^' + characters + '+|' + characters + '+$', 'g'), '');
    	},
    	trimStart : function(str, characters) {
    		 if(characters==null||characters=="")
		    {
		        var newstr= str.replace('/^/s*/', '');
		        return newstr;
		    }
		    else
		    {
		        var rg=new RegExp("^"+characters+"*");
		        var newstr= str.replace(rg, '');
		        return newstr;
		    }
    	},
    	trimEnd : function(str, characters) {
    		  if(characters ==null||characters=="")
    		    {
    		        var newstr= str;
    		        var rg = '//s/';
    		        var i = newstr.length;
    		        while (rg.test(str.charAt(--i)));
    		        return newstr.slice(0, i + 1);
    		    }
    		    else
    		    {
    		        var newstr= str;
    		        var rg = new RegExp(characters);
    		        var i = newstr.length;
    		        while (rg.test(newstr.charAt(--i)));
    		        return newstr.slice(0, i + 1);
    		    }
    	},
    	contains: function(str, searchStr) {
            if (str == null || searchStr == null) {
                return false;
            }
            return str.indexOf(searchStr) >= 0;
        },
        containsIgnoreCase: function(str, searchStr) {
            if (str == null || searchStr == null) {
                return false;
            }
            return StringUtils.contains(str.toUpperCase(), searchStr.toUpperCase());
        },
        startsWith: function(str, starts) {
    		str = makeString(str);
    		starts = makeString(starts)
    		if(str==""||starts==""||str.length<starts.length){
    			return false;
    		}
    		return str.substring(0,starts.length)==starts;
    	},
    	startsWithIgnoreCase: function(str, starts) {
    		return StringUtils.startsWith(str.toUpperCase(), starts.toUpperCase());
    	},
    	endsWith: function(str, ends) {
    		str = makeString(str);
    		ends = makeString(ends)
    		if(str==""||ends==""||str.length<ends.length){
    			return false;
    		}
    		return str.substring(str.length-ends.length)==ends;
    	},
    	endsWithIgnoreCase: function(str, ends) {
    		return StringUtils.endsWith(str.toUpperCase(), ends.toUpperCase());
    	},
    	isEmpty: function(val) {  
    		val = $.trim(val);
            if(val == null)
                return true;
            if(val == undefined || val == 'undefined')
                return true;
            if(val == "")
                return true;
            if(val.length == 0)
                return true;
            if(!/[^(^\s*)|(\s*$)]/.test(val))
                return true;
            return false;
        },  
        
        isNotEmpty: function(val) {
        	 return !this.isEmpty(val);
        },  
        isBlank: function(str) {
        	return (/^\s*$/).test(makeString(str));
        },  
        isNotBlank: function(str) {
        	return !StringUtils.isBlank(str);
        },
        substringBetween: function(str, open, close) {
            if (str == null || open == null || close == null) {
                return null;
            }
            var start = str.indexOf(open);
            if (start != -1) {
                var end = str.indexOf(close, start + open.length);
                if (end != -1) {
                    return str.substring(start + open.length, end);
                }
            }
            return null;
        },
        substringBefore: function(str, separator) {
            if (StringUtils.isEmpty(str) || separator == null) {
                return str;
            }
            if (separator.length == 0) {
                return "";
            }
            var pos = str.indexOf(separator);
            if (pos == -1) {
                return str;
            }
            return str.substring(0, pos);
        },
        substringAfter: function(str, separator) {
            if (StringUtils.isEmpty(str)) {
                return str;
            }
            if (separator == null) {
                return "";
            }
            var pos = str.indexOf(separator);
            if (pos == -1) {
                return "";
            }
            return str.substring(pos + separator.length);
        },
        substringBeforeLast: function(str, separator) {
            if (StringUtils.isEmpty(str) || StringUtils.isEmpty(separator)) {
                return str;
            }
            var pos = str.lastIndexOf(separator);
            if (pos == -1) {
                return str;
            }
            return str.substring(0, pos);
        },
        substringAfterLast: function(str, separator) {
            if (StringUtils.isEmpty(str)) {
                return str;
            }
            if (StringUtils.isEmpty(separator)) {
                return "";
            }
            var pos = str.lastIndexOf(separator);
            if (pos == -1 || pos == (str.length - separator.length)) {
                return "";
            }
            return str.substring(pos + separator.length);
        },
        isNumber : function(str){
    		return /^-?\d+(\.\d+)?$/g.test(str);
    	},//定义整个数字全部转换的方法，需要依次对数字进行10000为单位的取余，然后分成小节，按小节计算，当每个小节的数不足1000时，则需要进行补零
    	TransformToChinese:function(num){
	        var a = this.numToChn(num);
	        num = Math.floor(num);
	         var unitPos = 0;
	         var strIns = '', chnStr = '';
	         var needZero = false;
	        
	         if(num === 0){
	               return chnNumChar[0];
	         } 
	         while(num > 0){
	               var section = num % 10000;
	               if(needZero){
	                 chnStr = chnNumChar[0] + chnStr;
	               }
	               strIns = this.sectionToChinese(section);
	               strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
	               chnStr = strIns + chnStr;
	               needZero = (section < 1000) && (section > 0);
	               num = Math.floor(num / 10000);
	               unitPos++;
	         }
	        
	        return chnStr+a;
    	},
    	//定义在每个小节的内部进行转化的方法，其他部分则与小节内部转化方法相同
    	sectionToChinese:function (section){
    	    var str = '', chnstr = '',zero= false,count=0;   //zero为是否进行补零， 第一次进行取余由于为个位数，默认不补零
    	    while(section>0){
    	         var v = section % 10;  //对数字取余10，得到的数即为个位数
    	         if(v ==0){                    //如果数字为零，则对字符串进行补零
    	             if(zero){
    	                 zero = false;        //如果遇到连续多次取余都是0，那么只需补一个零即可
    	                 chnstr = chnNumChar[v] + chnstr; 
    	             }      
    	         }else{
    	             zero = true;           //第一次取余之后，如果再次取余为零，则需要补零
    	             str = chnNumChar[v];
    	             str += chnUnitChar[count];
    	             chnstr = str + chnstr;
    	         }
    	         count++;
    	         section = Math.floor(section/10);
    	    }
    	    return chnstr;
    	},
    	//定义在每个小节的内部进行转化的方法，其他部分则与小节内部转化方法相同
    	numToChn:function (num){
    		 var index =  num.toString().indexOf(".");
    	      if(index != -1){
    	          var str = num.toString().slice(index);
    	          var a = "点";
    	              for(var i=1;i<str.length;i++){
    	                     a += chnNumChar[parseInt(str[i])];
    	               }
    	          return a ;
    	      }else{
    	          return "";
    	      }
    	},
    	
    	//将数字num补齐成n位长度（前补0）, 例如：传入6,3返回006
        lpad:function(num, n) {
            return (Array(n).join(0) + num).slice(-n);
        },
        
    	//将002005006这种排序号加1（n是每一层级的长度）
        codeIncrease:function(sort, n) {
			maxsort = Number(sort.substr(sort.length-n));
			maxsort = maxsort + 1;
			var nextsort = (Array(n).join(0) + maxsort).slice(-n);
			return sort.substr(0, sort.length-3) + nextsort;
        }
    };
 
});