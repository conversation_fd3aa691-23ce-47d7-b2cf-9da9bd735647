﻿
// 这里定义项目中配置，注意： paths key 不要和平台重复
(function(){
    
    var ifpcore = window.ifpcore || {}

    window.kycore = {
        rootpath: (ifpcore.rootpath||ifpcore.rootpath==="")?ifpcore.rootpath:"",
        use:["/iofp/vue-plugin-ifp.js",...(ifpcore.use||[])],
        states:{
            lxsz:"/iofp/states/lxsz.js",
            ywlx:"/iofp/states/ywlx.js"
        },
        vueplugins:{
            ELEMENT:{
                size:'small'
            }
        },
        require: {
            //baseUrl: "/kyadmin",
            urlArgs: "_r=" + Date.now(),
            paths: {
                "iofp": "/iofp",
                "EventEmitter": "/components/EventEmitter",
                "iofp/services": "/services",
                "mockdata": "/iofp/lib/mockdata",
                "iofp/common": "/iofp/common",
                "iofp/commands": "/iofp/commands", // 配置命令，项目目录下
                "iofp/components": "/components",
                "iofp/buttonlog": "/iofp/buttonlog",// 点击按钮出发日志
                "iofp/api": "/iofp/IFP", // 点击按钮出发日志
                "iofp/mixins": "/iofp/mixins", // mixins 功能模块目录
                "iofp/lib/video": "/iofp/lib/video/video.min",
            },
            shim: {
                "iofp/lib/video": {deps:["css!/iofp/lib/video/video-js.min"]},
                "iofp/api": {
                    exports:"window.API"
                }
            },
            config: {
                // 按钮权限
                "/iofp/vue-plugin-ifp.js":{
                    useBtnRight:true
                },
                // 平台中需要用到url，项目自定义
                "configs/config.service": {
                    _http_getYwlxCombox_: "/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect", // post:{ywlx:number|4,used:1,ids:["111","ddd"]}
                    ywlx_idtoname: "/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect",
                    _http_getYwlxCombox_usedfieldname: "Zfbz", // 停用启用标志
                    _http_getYwlxCombox_usedfieldenum: ["1", "0"], // [停用,启用]
                    _http_getYwlxCombox_idsfieldname: "Gid_IN", // ids 字段名
                    _http_getYwlxCombox_ywlxfieldname: "Ywlx" // ywlx 字段名
                },
                // 配置自定义 converter , deps 配置需要合并的模块，urls 配置自定义 url
                "commons/converter": {
                    urls: {
                        useridtoname: "/API/IFP/Rights/User/GetUserIdTextList",
                        IOfenzuidtoname: "/API/IFP/PLC/IOFenZu/FenZuComboList",
                        userid2jobid:"/API/IFP/Rights/User/GetUserIdJobIdText"
                    },
                    deps: ["iofp/converter"]
                },
                "iofp/components/iofpsocket": {
                    // host: window.location.host,

                    // 发送命令与回传命令映射表
                    commandMap: {
                        "SControl": "RControl",
                        "SConfig": "RConfig"
                    },
                    autoOpen: true // 自动连接
                },
                "iofp/buttonlog": {
                    hander: ["click"],//监听事件
                    filter: ["button,a,input[type='button']", "[log]"],//筛选器
                    url: "/API/IFP/Rights/UserLog/SaveLog"
                }
            },
            waitSeconds: 15
        },
        isDebug:false,
        callback: function () {
            
            require(["iofp/services/service.timeoutmode"], function (timeoutmodeService) {
                timeoutmodeService.getTimeoutMode(null).then(config => {
                    if (config.TimeoutMode) {
                        requirejs(["iofp/dotimeout"], function (dotimeout) {
                            dotimeout(config.LogoutTime);
                        })
                    }
                    else {
                    }
                })
            })
            

            require(['json!kyadmin/version.json'],function(d){
                console.log(
                    `%c KYAdmin@${d.version} %c IFP %c 已启动 %c`,
                    'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                    'background:#3578ff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                    'color:#060',
                    'background:transparent ; color:transparent;'
                )
            })
            
            if(typeof ifpcore.callback === "function"){
                ifpcore.callback();
            }

            // 加载样式
            requirejs(["/iofp/ifp.css"].concat(ifpcore.css||[])
            .map(item=>'css!'+kycore.rootpath+item),function(){
                // console.log("css loaded")
            })

            requirejs(["iofp/buttonlog"], btnlog => btnlog());
            
            document.body.classList.add("ifp")
        }
    }

    if (window.kycore.require.config["commons/converter"].deps) {
        window.kycore.require.shim = window.kycore.require.shim || {};
        window.kycore.require.shim["commons/converter"] = { deps: window.kycore.require.config["commons/converter"].deps }
    }

    var dt = Date.now().toString().substr(0, 8);

    function getUrlArg(){
        if (kycore.isdebug || !window.fetch) {
            return Promise.resolve(dt);
        }else{
            let option = { 
                method: "POST", 
                body: null
            };
            let segment = document.location.pathname.split('/').filter(x => x != '');
            if (segment[0] == "identity" && segment[1].length == 32) {
                option.headers = option.headers || {};
                option.headers = {
                    "identity":segment[1]
                }
            }
            return window.fetch("/API/IFP/Rights/User/ProgramStartTime", option)
            .then(function (r) {
                if (r.ok) {return r.text() } 
                else { throw r.statusText; }
            }).catch(function(ex){
                console.error("请求服务器时间出错：",ex);
                return dt;
            })
        }
    }

    getUrlArg().then(s=>{
        kycore.require.urlArgs = s;
        var s = document.createElement("script");
        s.src=window.kycore.rootpath+"/kyadmin/starter.js";
        s.addEventListener&&s.addEventListener("load",function(){
            document.body.removeChild(s)
        })
        document.body.append(s);
    });
})()