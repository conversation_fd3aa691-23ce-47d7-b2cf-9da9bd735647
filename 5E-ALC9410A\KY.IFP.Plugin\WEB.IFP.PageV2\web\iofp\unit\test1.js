define(function(){
    
    function add(a, b) {
        return a + b;
    }
    function count(n1,n2,n3,n4) {
        return n1+n2+n3+n4;
    }

    return function(QUnit){
        QUnit.module('示例', function() {
            QUnit.test('两数相加', function(assert) {
                assert.equal(add(1, 1), 2);
            });
            QUnit.test('统级', function(assert) {
                assert.equal(count(1, 1,1,1), 5);
            });
        });
    }
})