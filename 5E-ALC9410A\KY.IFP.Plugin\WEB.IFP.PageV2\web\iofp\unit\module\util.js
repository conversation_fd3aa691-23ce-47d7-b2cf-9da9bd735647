define(['util'],function(util){

    var data = [
        {id:'root',name:'根节点'},
        {id:'node1',pid:'root',name:'节点1'},
        {id:'node2',pid:'root',name:'节点2'},
        {id:'node2-1',pid:'node2',name:'节点2-1'},
        {id:'node3',pid:'root3',name:'节点3'}
    ]

    return function(QUnit){
        QUnit.module('util', function() {
            QUnit.test("util.getTreeData 简单树转标准树", function(assert) {
                assert.equal(true,true,`let data = ${JSON.stringify(data,null,2)}`);
                let result = util.getTreeData(data);
                assert.equal(result.length,1,`let result = getTreeData(data)`);
                assert.equal(result[0].children[1].children[0].name,"节点2-1",'result[0].children[1].children[0].name => "节点2-1"');
                let result2 = util.getTreeData(data,{errpidasroot:true});
                assert.equal(true,true,`let result2 = getTreeData(data,{errpidasroot:true}) // 错误节点作为根节点输出`);
                assert.equal(result2.length,2,`result2.length => 2`);
            });
            
            /*
            QUnit.test('千分位', function(assert) {
                assert.equal(converters.thousands(99999999), '99,999,999','正整数');
                assert.equal(converters.thousands(99999999.01), '99,999,999.01','正小整数');
                assert.equal(converters.thousands(-99999999), '-99,999,999','负整数');
            });
            */
        });
    }
})