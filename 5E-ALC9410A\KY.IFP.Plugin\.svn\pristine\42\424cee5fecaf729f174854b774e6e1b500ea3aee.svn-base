define(["/iofp/lib/videojs/index.js"],function(videojs){
    return {
        template:`
        <div>
            <video ref="videoPlayer" class="video-js"></video>
        </div>
        `,
        props: {
            options: {
                type: Object,
                default() {
                    return {};
                }
            }
        },
        watch:{
            options:{
                handler(newName, oldName) {
                    //切换视频源
                    if(newName.sources[0].src !== this.player.src()){
                        this.player.src(newName.sources[0])
                    }
                },
                // 深度监听
                deep: true,
                //immediate: true // 立刻调用一次
            }
        },
        created(){
            this.player = null;
        },
        methods:{
        },
        data() {
            return {
            }
        },
        mounted() {
            this.player = videojs(this.$refs.videoPlayer, this.options, function onPlayerReady() {
                console.log('onPlayerReady', this);
            })
        },
        beforeDestroy() {
            if (this.player) {
                this.player.dispose()
                this.player = null;
            }
        }
    }
})