import states from "./states.json"
import "./index.less"

// states 状态列表已固定，不再放到 data 中

export {
    states
}

export let Control = {
    props:{
        tooltip:{type:[Function,null], default:null},
        enabled:{type:Boolean, default:true},
        selectState:{type:Array,default(){
            return [5];
        }},
        rows:{type:Number, default:5},
        cols:{type:Number, default:4},
        states:{type:Array,default(){
            return states;
        }},
        items:{
            type:Array,default(){
                return []
            }
        }
    },
    template:`
    <div class="zxcsypj">
        <div class="zxcsypj-chart">
            <div v-for="rowitems in groups" class="zxcsypj-row">
                <el-tooltip :key="item.xh" v-for="item in rowitems" :disabled="item.tooltip==null">
                    <div v-if="item.tooltip!==null" slot="content" v-html="item.tooltip"></div>
                    <div
                        @click="item_click(item)"
                        class="zxcsypj-col" 
                        :class="{
                            ['zxcsypj-state-'+item.name]:true,
                            canselect:item.canselect && enabled,
                            selected:selectXH===item.xh
                        }"
                    ></div>
                </el-tooltip>
            </div>
        </div>
        <div class="zxcsypj-legend">
            <div v-for="item in states"><div :class="'zxcsypj-state-'+item.name"></div>{{item.label}}</div>
        </div>
    </div>
    `,
    computed:{
        stateMap(){
            return Object.fromEntries(this.states.map(item=>[item.code,item.name]))
        },
        groups(){
            let groups = Array(this.rows).fill().map(()=>Array(this.cols).fill().map(()=>({
                name:'null',label:'',state:null
            })));

            this.items.forEach((item)=>{
                if(!item){
                    throw `items 中元素必须是 object`
                }
                if (item.Pos === undefined){
                    throw `未指定 xh ${JSON.stringify(item)}`
                }
                
                let index = item.Pos-1;
                let row = Math.floor(index/this.cols);
                let col = index%this.cols;
                if(row >= this.rows){
                    throw `zxcsypj 数据量多于最大配置,行数 ${this.rows} 列数 ${this.cols},总数据行数 ${this.items.length}`
                }
                if (item.Status === undefined){
                    throw `未指定 state ${JSON.stringify(item)}`
                }
                if (!this.stateMap[item.Status]){
                    throw `未知 state ${JSON.stringify(item)}`
                }

                let tooltips = null;
                if(item.tooltip){
                    tooltips = item.tooltip;
                }else if(this.tooltip){
                    tooltips = this.tooltip(item);
                }

                if(tooltips){
                    tooltips = tooltips.replace(/\n/g,'<br/>');
                }

                groups[row][col] = {
                    xh:item.Pos,
                    state: item.Status,
                    canselect: this.selectState.includes(item.Status),
                    name: this.stateMap[item.Status],
                    tooltip:tooltips,
                    raw:item
                };
            })

            return groups;
        }
    },
    data(){
        return {
            selectXH:null
        }
    },
    methods:{
        item_click(item){
            if (item.canselect && this.enabled) {
                if (this.selectXH === item.xh) {
                    this.selectXH = '';
                    this.$emit('item-select', null);
                } else {
                    this.selectXH = item.xh;
                    this.$emit('item-select', item.raw);
                }
            }
            this.$emit('item-click',item.raw);
        },
        getSelect(){
            return this.items.find(item=>item.xh === this.selectXH)
        }
    }
}