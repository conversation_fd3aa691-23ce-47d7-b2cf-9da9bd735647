//F.controls.selectview 用来展示已选列表
define(["./base","jquery"],function(base,$){
	var defaultOption = {
		name:"control-selectview",
		textfield:"text",
		data:[]
	}
	
	var DataItem = function(text,data){
		this.id = F.util.genid("control-selectview-item");
		this.data = data;
		this.dom = $(['<li class="control-slelecview-item"></li>'].join("")).data("data",data);
		this.container = $('<a class="btn btn-default">'+text+'</a>').appendTo(this.dom);
		this.closebtn = $('<span class="menutitle badge">×</span>').appendTo(this.container);
	}
	DataItem.prototype.remove=function(){
		this.dom.remove();
	};
	DataItem.prototype.appendTo=function(dom){
		this.dom.appendTo(dom);
		return this;
	};
	
	return F.class(base,{
		name:"control-selectview",
		_data : null,
		
		render:function(){
			this.$container.addClass("control-slelecview");
			this.emptyItem = $("<li class='control-slelecview-empty'>请选择</li>").appendTo(this.$container);
			if(this.option.data){
				this.data(this.option.data);
			}
			this.updateEmpty();
		},
		
		add:function(data){
			var _this = this;
			var showtext = "";
			if(typeof data == "object"){
				showtext = data[this.option.textfield];
			}else{
				showtext = data.toString();
			}
			var item = new DataItem(showtext,data);
			item.appendTo(this.$container).closebtn.click(function(){
				_this.remove(item);
				_this.trigger("removeItem",item,this);
			});
			this._data.push(item);
			this.updateEmpty();
		},
		//如果可选，返回/设置可选项，否则，设置/返回 显示项
		value:function(v){
			return this.data.apply(this,arguments);
		},
		clear:function(){
			var _this = this;
			$.each(this._data,function(i,item){
				_this.remove(item);
			});
			this._data = [];
			this.updateEmpty();
		},
		updateEmpty:function(){
			this.emptyItem[this._data.length?"hide":"show"]();
		},
		remove:function(item){
			this._data = $.grep(this._data,function(itemi,i){
				return itemi.id != item.id;
			});
			item.remove();
			item = null;
			this.updateEmpty();
		},
		
		//用于展示选项
		data:function(v){
			var _this = this;
			if(v){
				this.clear();
				$.each(v,function(i,item){
					_this.add(item);
				})
			}else{
				return this._data;
			}
		},

		createDefaultOption:function(container,option){
			return $.extend({},this.base.apply(this,arguments),defaultOption);
		},
		
		init:function(){
			this.base.apply(this,arguments);
			this._data = [];
		}
	});
});