﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <title></title>
    <link type="text/css" href="css/default.css" rel="Stylesheet" />
    <script type="text/javascript" src="scripts/jquery-1.7.1.min.js"></script>
    <script type="text/javascript" src="scripts/messagecenter.js"></script>
    <script type="text/javascript">
<!--
            $(document).ready(function () {
                $axure.messageCenter.addMessageListener(messageCenter_message);
                function messageCenter_message(message, data) {
                    if(message == 'collapseFrameOnLoad') {
                        setTimeout(function() {
                            $('#maximizePanel').animate({
                                top:'-' + maxPanelHeight + 'px'
                            }, 300);
                        }, 2000);
                    }
                }
                $axure.messageCenter.postMessage('getCollapseFrameOnLoad');

                if(MOBILE_DEVICE) {
                    $('#maximizePanel').height('45px');
                }
                var maxPanelHeight = $('#maximizePanel').height();

                $('#maximizePanel').click(function () {
                    $(this).removeClass('maximizePanelOver');
                    $axure.messageCenter.postMessage('expandFrame');
                });

                if(!MOBILE_DEVICE) {
                    $('#maximizePanel').mouseenter(function() {
                        $(this).addClass('maximizePanelOver');
                    });
                    $('#maximizePanel').mouseout(function() {
                        if($(this).hasClass('maximizePanelOver')) {
                            $(this).animate({
                                top:'-' + maxPanelHeight + 'px'
                            }, 300);
                        }
                        $(this).removeClass('maximizePanelOver');
                    });
                    $('#maximizePanelOver').mouseenter(function() {
                        $('#maximizePanel').animate({
                            top:'0px'
                        }, 100);
                    });
                }
            });
        --></script>
</head>
<body style="background-color: transparent;">
    <div id="maximizePanelOver">
        <div id="maximizePanel" class="maximizePanel" title="Expand">
        </div>
    </div>
</body>
</html>