﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{
    /// <summary>
    /// 煤场固定分区
    /// </summary>
    [SugarTable("IFP_BS_MCGDFQ")]
    public class YWDX4012_GDFQ
    {
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(36)")]
        public Field<string> Gid { get; set; }

        [SugarColumn(ColumnName = "GDFQMC", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(32)")]
        public Field<string> Gdfqmc { get; set; }

        [SugarColumn(ColumnName = "LBEGIN", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Lbegin { get; set; }

        [SugarColumn(ColumnName = "LEND", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Lend { get; set; }

        [SugarColumn(ColumnName = "WBEGIN", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Wbegin { get; set; }

        [SugarColumn(ColumnName = "WEND", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Wend { get; set; }

        [SugarColumn(ColumnName = "ABEGIN", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Abegin { get; set; }

        [SugarColumn(ColumnName = "AEND", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(10,3)")]
        public Field<decimal> Aend { get; set; }

        [SugarColumn(ColumnName = "GDBL", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,10)")]
        public Field<decimal> Gdbl { get; set; }

        [SugarColumn(ColumnName = "SXH", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Sxh { get; set; }

        [SugarColumn(ColumnName = "ZFBZ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Zfbz { get; set; }

        [SugarColumn(ColumnName = "BZ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(256)")]
        public Field<string> Bz { get; set; }

        [SugarColumn(ColumnName = "CMCS4012", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Cmcs4012 { get; set; }

        [SugarColumn(ColumnName = "CML", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,3)")]
        public Field<decimal> Cml { get; set; }
    }

    //[Table(Name = "IFP_BS_MCGDFQ")]
    //public class YWDX4012_GDFQ
    //{
    //    [Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.NVarChar, DbType = "nvarchar(36)")]
    //    public Field<string> Gid { get; set; }

    //    [Column(Name = "GDFQMC", DataType = DataType.NVarChar, DbType = "nvarchar(32)")]
    //    public Field<string> Gdfqmc { get; set; }

    //    [Column(Name = "LBEGIN", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //    public Field<decimal> Lbegin { get; set; }

    //    [Column(Name = "LEND", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //    public Field<decimal> Lend { get; set; }

    //    [Column(Name = "WBEGIN", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //    public Field<decimal> Wbegin { get; set; }

    //    [Column(Name = "WEND", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //    public Field<decimal> Wend { get; set; }

    //    [Column(Name = "ABEGIN", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //    public Field<decimal> Abegin { get; set; }

    //    [Column(Name = "AEND", DataType = DataType.Decimal, DbType = "decimal(10, 3)")]
    //    public Field<decimal> Aend { get; set; }

    //    [Column(Name = "GDBL", DataType = DataType.Decimal, DbType = "decimal(18, 10)")]
    //    public Field<decimal> Gdbl { get; set; }

    //    [Column(Name = "SXH", DataType = DataType.Int64, DbType = "bigint")]
    //    public Field<long> Sxh { get; set; }

    //    [Column(Name = "ZFBZ", DataType = DataType.Int32, DbType = "int")]
    //    public Field<int> Zfbz { get; set; }

    //    [Column(Name = "BZ", DataType = DataType.NVarChar, DbType = "nvarchar(256)")]
    //    public Field<string> Bz { get; set; }

    //    [Column(Name = "CMCS4012", DataType = DataType.Int64, DbType = "bigint")]
    //    public Field<long> Cmcs4012 { get; set; }

    //    [Column(Name = "CML", DataType = DataType.Decimal, DbType = "decimal(18, 3)")]
    //    public Field<decimal> Cml { get; set; }

    //}
}
