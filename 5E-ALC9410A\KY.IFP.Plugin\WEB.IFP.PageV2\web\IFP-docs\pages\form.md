# 表单栏 | ifp-form

`ifp-form` 继承于 elementUI的 `el-form` 组件，里面具体事件和属性可参考[elementUI表单](https://element.eleme.cn/#/zh-CN/component/form)

## 示例代码

```HTML
<ifp-page id="app">
  <ifp-panel title="产品相关" border class="margin-bottom">
    <ifp-form ref="prodInfoForm" style="padding: 1rem" class="padding" label-position="left" :model="productInfo" label-width="180px">
      <ifp-form-item label="产品名称" prop="product">
          <ifp-input v-model="productInfo.product"></ifp-input>
      </ifp-form-item>
      <ifp-form-item label="产品型号" prop="pNumber">
          <ifp-input v-model="productInfo.pNumber"></ifp-input>
      </ifp-form-item>
      <ifp-form-item label="产品版本" prop="pVersion">
          <ifp-input v-model="productInfo.pVersion"></ifp-input>
      </ifp-form-item>
    </ifp-form>
  </ifp-panel>
</ifp-page>
```
