# 模块说明


## 样式模块

```js
{
    // bootstrap 自定义主题
    "css-theme-kykj":"lib/kjlib/bootstrap-theme-kykj/dist/css/bootstrap-theme-kykj",

    // 包含了控件体系的 样式
    "css-theme-kykj-admin":"lib/kjlib/bootstrap-theme-kykj/dist/css/dicb",

    // 字体 dicb
    "css-iconfont-dicb":"lib/kjlib/bootstrap-theme-kykj/iconfont/dicb2/iconfont",
    // 字体 kjicon
    "css-iconfont-kjicon":"lib/kjlib/bootstrap-theme-kykj/iconfont/kjicon/iconfont",
    // 字体 glyphions
    "css-iconfont-glyphions":"lib/kjlib/bootstrap-theme-kykj/iconfont/glyphions/css/glyphions",
}
```

### 使用

```js
// 引入 bootstrap-theme-kykj 主题样式
define(["css!css-theme-kykj","css-iconfont-dicb2"],function(){

})
```

## 字体

## 控件体系

## 控制器

## 工作流程


### starter.js

* 首先 定义一组工具函数，包括 loadscript 获取 getoption 等
* 合并 合并配置 
    * window.KYADMINCONFIG,body[option]
