﻿using FFmpeg.AutoGen;
using System.Runtime.InteropServices;

namespace COM.IFP.IPC
{
    unsafe
   public class Class1
    {
        public void Test()
        {
            //unsafe
            {
                string rtspUrl = "rtsp://username:password@ip:port/cam/realmonitor?channel=1&subtype=0";

                // 初始化网络库（如果需要）
                ffmpeg.avformat_network_init();

                // 分配 AVFormatContext
                AVFormatContext* pFormatContext = ffmpeg.avformat_alloc_context();
                if (pFormatContext == null)
                {
                    Console.WriteLine("无法分配 AVFormatContext");
                    return;
                }

                // 打开 RTSP 流
                if (ffmpeg.avformat_open_input(&pFormatContext, rtspUrl, null, null) != 0)
                {
                    Console.WriteLine("无法打开 RTSP 流");
                    return;
                }

                // 获取流信息
                if (ffmpeg.avformat_find_stream_info(pFormatContext, null) < 0)
                {
                    Console.WriteLine("无法获取流信息");
                    return;
                }

                // 查找视频流
                int videoStreamIndex = -1;
                for (int i = 0; i < pFormatContext->nb_streams; i++)
                {
                    if (pFormatContext->streams[i]->codecpar->codec_type == AVMediaType.AVMEDIA_TYPE_VIDEO)
                    {
                        videoStreamIndex = i;
                        break;
                    }
                }

                if (videoStreamIndex == -1)
                {
                    Console.WriteLine("未找到视频流");
                    return;
                }

                // 获取解码器
                AVCodecParameters* pCodecParameters = pFormatContext->streams[videoStreamIndex]->codecpar;
                AVCodec* pCodec = ffmpeg.avcodec_find_decoder(pCodecParameters->codec_id);
                if (pCodec == null)
                {
                    Console.WriteLine("未找到解码器");
                    return;
                }

                // 分配解码器上下文
                AVCodecContext* pCodecContext = ffmpeg.avcodec_alloc_context3(pCodec);
                if (pCodecContext == null)
                {
                    Console.WriteLine("无法分配解码器上下文");
                    return;
                }

                // 将流参数复制到解码器上下文
                if (ffmpeg.avcodec_parameters_to_context(pCodecContext, pCodecParameters) < 0)
                {
                    Console.WriteLine("无法复制流参数到解码器上下文");
                    return;
                }

                // 打开解码器
                if (ffmpeg.avcodec_open2(pCodecContext, pCodec, null) < 0)
                {
                    Console.WriteLine("无法打开解码器");
                    return;
                }

                // 分配 AVPacket 和 AVFrame
                AVPacket* packet = ffmpeg.av_packet_alloc();
                AVFrame* pFrame = ffmpeg.av_frame_alloc();
                if (pFrame == null)
                {
                    Console.WriteLine("无法分配 AVFrame");
                    return;
                }

                // 读取帧并解码
                while (ffmpeg.av_read_frame(pFormatContext, packet) >= 0)
                {
                    if (packet->stream_index == videoStreamIndex)
                    {
                        // 发送压缩帧到解码器
                        if (ffmpeg.avcodec_send_packet(pCodecContext, packet) == 0)
                        {
                            // 接收解码后的原始帧
                            while (ffmpeg.avcodec_receive_frame(pCodecContext, pFrame) == 0)
                            {
                                // 将解码后的帧数据保存到 byte[]
                                byte[] frameData = SaveFrameToByteArray(pFrame);
                                Console.WriteLine($"解码后的帧数据大小: {frameData.Length} 字节");

                                // 这里可以对 frameData 进行进一步处理
                            }
                        }
                    }
                    ffmpeg.av_packet_unref(packet);
                }

                // 释放资源
                ffmpeg.av_frame_free(&pFrame);

                // 释放资源
                ffmpeg.avcodec_free_context(&pCodecContext);
                ffmpeg.avformat_close_input(&pFormatContext);
                ffmpeg.avformat_network_deinit();

            }

        }
        // 将 AVFrame 保存到 byte[]
        static byte[] SaveFrameToByteArray(AVFrame* pFrame)
        {
            // 计算帧数据大小
            int width = pFrame->width;
            int height = pFrame->height;
            int dataSize = width * height * 3; // 假设是 RGB24 格式

            // 创建 byte[]
            byte[] frameData = new byte[dataSize];

            // 将 AVFrame 数据复制到 byte[]
            for (int i = 0; i < height; i++)
            {
                Marshal.Copy((IntPtr)pFrame->data[0] + i * pFrame->linesize[0], frameData, i * width * 3, width * 3);
            }

            return frameData;
        }
    }
}
