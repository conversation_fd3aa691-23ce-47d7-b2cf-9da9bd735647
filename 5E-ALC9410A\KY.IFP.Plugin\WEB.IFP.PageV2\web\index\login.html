﻿<!DOCTYPE >
<html>
    <!--html manifest="/index/login.appcache"-->
    <head>
        <link rel="shortcut icon" href="/index/img/favicon.ico" type="image/x-icon" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>登录</title>
        <style>
            * {
                font-family: Microsoft YaHei;
            }

            body.login {
                background: #3e4c73 !important;
                /*background:url(/com.kysoft.service/index/img/bk.jpg);*/
            }

            body.login .fixedcenter {
                position: fixed;
                left: 50%;
                top: 50%;
            }

            body.login .form {
                color: #fff;
                width: 400px;
                height: 300px;
                margin-left: 100px;
                margin-top: -150px;
                /*box-shadow:3px 3px 8px rgba(0,0,0,0.2);*/
                text-align: center;
            }

            body.login .forminputs {
                background: #aaa;
                background: rgba(255, 255, 255, 0.3);
                padding: 40px;
            }

            .input-group-addon {
                background: none;
            }

            body.login input[type='text'],
            body.login input[type='password'] {
                background: rgba(0, 0, 0, 0.1);
            }

            body.login .form .input-group .form-control {
                color: #fff;
                border: 0px;
                background: transparent;
                border-bottom: 1px solid #fff;
            }

            body.login .form .input-group-addon {
                color: #fff;
                border: 0px;
                background: transparent;
                font-size: 24px;
                padding-left: 0px;
            }

            body.login .login_footer {
                position: absolute;
                position: fixed;
                right: 10px;
                left: 10px;
                bottom: 10px;
                text-align: center;
                font-weight: bold;
                font-size: 15px;
                color: rgba(255, 255, 255, 0.5);
            }

            body.login .login_header {
                position: absolute;
                position: fixed;
                color: #fff;
                top: 50%;
                left: 50%;
                margin-top: -273px;
                margin-left: -487px;
                font-size: 24px;
                z-index: 2;
            }

            .drag {
                -webkit-app-region: drag;
            }

            .nodrag {
                -webkit-app-region: no-drag;
            }

            body.login input,
            body.login button,
            body.login label {
                -webkit-app-region: no-drag;
            }

            body.login #bkbox {
                margin-left: -522px;
                margin-top: -186px;
            }

            body.login .form .btn-success,
            body.login .form .btn-success.active.focus,
            body.login .form .btn-success.active:focus,
            body.login .form .btn-success.active:hover,
            body.login .form .btn-success:active.focus,
            body.login .form .btn-success:active:focus,
            body.login .form .btn-success:active:hover,
            body.login .form .btn-success.focus,
            body.login .form .btn-success:focus,
            body.login .form .btn-success.active,
            body.login .form .btn-success:active {
                background: #008dff;
                font-size: 24px;
            }

            body.login .form .input-group .form-control:focus,
            body.login .form .input-group .form-control:focus {
                background: #fffa;
                color: #000;
            }

            body.login .login_model {
                display: flex;
            }

            body.login .login_model > a:hover,
            body.login .login_model > a:visited,
            body.login .login_model > a:focus,
            body.login .login_model > a.active {
                text-decoration: none;
                background-color: rgba(255, 255, 255, 0.3);
                color: #fff;
            }

            body.login .login_model > a {
                color: #000;
                padding: 8px;
                background-color: #fffa;
                cursor: pointer;
            }

            body.login #app {
                opacity: 0;
                transition: all 1s;
            }
            body.login.loaded #app {
                opacity: 1;
            }

            body .el-input__inner {
                color: #ffffff;
            }
            .login .el-select {
                position: relative;
            }
            .login .el-select-dropdown {
                position: absolute !important;
                left: 0px !important;
                top: 40px !important;
                z-index: 10000;
                min-width: unset !important;
                width: 100% !important;
            }
        </style>
    </head>
    <body controller="/index/login.js" option="{platform:'vue',loading:false,use:['ELEMENT']}" class="drag login">
        <script>
            window.LoginConfig_DefaultTheameName = 'default_dicb';
        </script>
        <div id="app">
            <div id="bkbox" class="fixedcenter">
                <img v-if="LoginPic" :src="LoginPic" width="562px" style="padding-top: 60px; padding-left: 100px" />
            </div>
            <div class="login_header">
                <div class="media">
                    <div class="media-left">
                        <a href="#">
                            <img class="media-object" width="80px" src="/index/img/logo.png" />
                        </a>
                    </div>
                    <div class="media-body">
                        <div id="mainname">{{MainName}}</div>
                        <div id="productname" style="font-size: 19px; color: #ccc">{{Name}}</div>
                    </div>
                </div>
            </div>

            <div class="fixedcenter" style="left: 0; background: #fff; height: 2px"></div>

            <div class="container form fixedcenter">
                <!-- <form id="form1" action="/com.kysoft.service/html/sysmanage/userLogin.action" method="post"> -->
                <!--div style="font-size:24px;text-align:left;padding:20px 0;">登录</div-->
                <div class="contents">
                    <div class="login_model" style="display: flex">
                        <a href="#zh" class="nodrag" :class="{active:hash=='#zh'}" @click="hash='#zh'"><span class="glyphicon glyphicon-user"></span> 账号登录</a>
                        <a href="#sk" class="nodrag" :class="{active:hash=='#sk'}" @click="hash='#sk';duka();"><span class="dicb dicb-shuaqia" style="font-weight: bold"></span> 刷卡登录</a>
                    </div>
                    <div v-if="hash=='#zh'" class="active" style="height: 300px">
                        <div class="forminputs" style="height: 100%">
                            <div class="form-group has-feedback">
                                <div class="input-group">
                                    <span class="input-group-addon" style="padding: 12px 6px">
                                        <span class="glyphicon glyphicon-user"></span>
                                    </span>
                                    <el-select v-model="userId" placeholder="点击选择用户名" :popper-append-to-body="false" style="width: 100% !important; color: #fff !important; padding: 12px 0px">
                                        <el-option v-for="item in userlist" :key="item.UsiLoginName" :label="item.UsiName" :value="item.UsiLoginName"> </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="form-group has-feedback">
                                <div class="input-group">
                                    <span class="input-group-addon" style="padding: 12px 6px">
                                        <span class="glyphicon glyphicon-lock"></span>
                                    </span>
                                    <el-input v-model="passWord" @change="PWDFROMECOOKIE = false" @focus="select('input_pwd')" v-on:keyup.enter="login" ref="input_pwd" type="password" placeholder="请输入密码" style="padding: 12px 0px" />
                                </div>
                            </div>
                            <div class="form-group" style="padding-left: 20px; display: none">
                                <label class="checkbox nodrag" style="text-align: left"> <input v-model="remamber" type="checkbox" name="remamber" value="1" />记住密码 </label>
                            </div>
                            <div class="form-group">
                                <input @click="login" ref="btnSubmit" type="button" class="nodrag btn btn-success btn-block" value="登录" />
                            </div>
                        </div>
                    </div>
                    <div v-if="hash=='#sk'" style="height: 300px">
                        <div class="forminputs" style="height: 100%">
                            <span class="dicb dicb-shuaqia" style="font-size: 150px"></span>
                            <div style="display: flex; position: absolute; left: 20px; right: 20px; bottom: -33px">
                                <div style="flex: 1; text-align: left; line-height: 30px; padding-left: 6px; font-size: 12px">{{log_sk}}</div>
                                <div><input style="font-size: 12px; padding: 5px 10px" :disabled="!resk_enabled" type="button" class="btn btn-primary" value="重新刷卡" @click="duka" /></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- </form> -->
            </div>
            <div class="login_footer">©2020 长沙开元平方软件有限公司</div>
        </div>
        <script src="/index/identity.js"></script>
        <script src="/iofp/starter.js"></script>
        <!--script src="/kyadmin/lib/core/com.kjsoft.core.cookie.js"></script>
	<script src="/kyadmin/lib/bower/jquery/jquery.min.js"></script>
	<script src="/kyadmin/lib/bower/jquery.cookie/jquery.cookie.js"></script>
	<script src="/kyadmin/lib/bower/crypto-js/core.js"></script>
	<script src="/kyadmin/lib/bower/crypto-js/md5.js"></script>
	<script src="/kyadmin/lib/bower/layer/layer.js"></script>
	<script src="/kyadmin/lib/bower/vue/vue.min.js"></script>
	<script src="/index/login.js"></script-->
    </body>
</html>
