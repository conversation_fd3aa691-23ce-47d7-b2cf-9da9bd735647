# 搜索栏 | ifp-searchbar

`ifp-searchbar` 继承于 `ifp-form` 组件，用于搜索栏，包含查询和重置按钮，默认都不显示

## 示例代码

```html
<ifp-page id="app">
    <!--searchHandle 是自定义查询方法 -->
    <!--resetHandle 是自定义重置方法 -->
    <ifp-searchbar @search="searchHandle" @reset="resetHandle">
        
        <!-- input 只需要设置 v-model 即可 -->
        <ifp-form-item label="名称" v-model="query.name" placeholder="请输入内容"></ifp-form-item>

        <!-- ywlx 需要设置 v-model、type="ywlx"、ywlx -->
        <ifp-form-item label="类型" type="ywlx" ywlx="1003" v-model="query.type" placeholder="请选择"></ifp-form-item>

        <!-- 其他控件 -->
        <ifp-form-item label="状态">
            <el-select v-model="query.state" clearable placeholder="请选择">
            <el-option label="停用" value="1"></el-option>
            <el-option label="启用" value="2"></el-option>
            </el-select>
        </ifp-form-item>

    </ifp-searchbar>
</ifp-page>
```

组件会监听模板中的 `search` 和 `reset` 事件，来决定是否显示对应按钮

仅显示查询

```html
<ifp-searchbar @search="searchHandle">
</ifp-searchbar>
```

仅显示重置

```html
<ifp-searchbar @reset="onreset">
</ifp-searchbar>
```

## 属性

|属性|类型|默认值|说明|
|-|-|-|-|
|search|bool|false|设为`true`则强行显示查询按钮，不管有没有绑定事件|
|reset|bool|false|设为`true`则强行显示重置按钮，不管有没有绑定事件|

```html
<ifp-searchbar @search="true" @reset="true">
</ifp-searchbar>
```

## 事件

只有在模板中绑定了对应事件，对应按钮才会显示

* search
* reset

模版代码

```html
<ifp-searchbar @search="searchHandle" @reset="onreset">
</ifp-searchbar>
```

JavaScript代码

```JavaScript
{
    methods:{
        searchHandle() { },
        onreset() { },
    }
}
```

### 重置

#### 方式一：使用 vue 的双向绑定语法糖 model

html

```html
<ifp-page id="app">
    <!--searchHandle 是自定义查询方法 -->
    <ifp-searchbar @search="searchHandle" :model="query">
        <!--prop 重置 属性-->
        <ifp-form-item label="名称" prop="name" v-model="query.name" placeholder="请输入内容"></ifp-form-item>
        <ifp-form-item label="类型" prop="type" type="ywlx" ywlx="1003" v-model="query.type" placeholder="请选择"></ifp-form-item>
        <ifp-form-item label="状态" prop="state" >
            <el-select v-model="query.state" clearable placeholder="请选择">
            <el-option label="停用" value="1"></el-option>
            <el-option label="启用" value="2"></el-option>
            </el-select>
        </ifp-form-item>
        <!-- // 自定义按钮
        <template v-slot:buttons>
            <ifp-button>其他按钮1</ifp-button>
            <ifp-button>其他按钮2</ifp-button>
            <ifp-button>其他按钮3</ifp-button>
        </<template>
        -->
    </ifp-searchbar>
</ifp-page>
```

```js
{
    data(){
        return {
            query:{
                name:'',
                type:'',
                state:'1'
            }
        }
    }
}
```

#### 方式二：使用 @reset

```html

<ifp-page id="app">
    <!-- reset-show 显示 重置按钮 ，resetHandle 自定义重置函数  -->
    <ifp-searchbar @search="searchHandle" @reset="resetHandle">

    </ifp-searchbar>
</ifp-page>
```
