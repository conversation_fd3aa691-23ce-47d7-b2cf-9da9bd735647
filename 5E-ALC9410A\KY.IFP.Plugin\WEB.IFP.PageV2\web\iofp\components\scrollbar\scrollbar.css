:root {
  --mel-text-color-secondary: #ffff;
}
.mel-scrollbar {
  --mel-scrollbar-opacity: 0.5;
  --mel-scrollbar-background-color: var(--mel-text-color-secondary);
  --mel-scrollbar-hover-opacity: 0.7;
  --mel-scrollbar-hover-background-color: var(--mel-text-color-secondary);
  overflow: hidden;
  position: relative;
  height: 100%;
}
.mel-scrollbar__wrap {
  overflow: auto;
  height: 100%;
}
.mel-scrollbar__wrap--hidden-default {
  scrollbar-width: none;
}
.mel-scrollbar__wrap--hidden-default::-webkit-scrollbar {
  display: none;
}
.mel-scrollbar__thumb {
  position: relative;
  display: block;
  width: 0;
  height: 0;
  cursor: pointer;
  border-radius: inherit;
  background-color: var(--mel-scrollbar-background-color, var(--mel-text-color-secondary));
  transition: var(--mel-transition-duration) background-color;
  opacity: var(--mel-scrollbar-opacity, 0.3);
}
.mel-scrollbar__thumb:hover {
  background-color: var(--mel-scrollbar-hover-background-color, var(--mel-text-color-secondary));
  opacity: var(--mel-scrollbar-hover-opacity, 0.5);
}
.mel-scrollbar__bar {
  position: absolute;
  right: 2px;
  bottom: 2px;
  z-index: 1;
  border-radius: 4px;
}
.mel-scrollbar__bar.is-vertical {
  width: 6px;
  top: 2px;
}
.mel-scrollbar__bar.is-vertical > div {
  width: 100%;
}
.mel-scrollbar__bar.is-horizontal {
  height: 6px;
  left: 2px;
}
.mel-scrollbar__bar.is-horizontal > div {
  height: 100%;
}
.mel-scrollbar-fade-enter-active {
  transition: opacity 0.34s ease-out;
}
.mel-scrollbar-fade-leave-active {
  transition: opacity 0.12s ease-out;
}
.mel-scrollbar-fade-enter-from,
.mel-scrollbar-fade-leave-active {
  opacity: 0;
}
