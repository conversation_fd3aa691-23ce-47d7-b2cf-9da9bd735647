define(["iofp/util"],function(ifpUtil){
    return {
        el:"#app",
        data(){
            return {
                url:"/sss/sss/aa.html",
                paramName:"arg1",
                paramValue:"test"
            }
        },
        computed:{
            showurl(){
                return ifpUtil.urlAddParams(this.url,{
                    [this.paramName]:this.paramValue
                })
            }
        }
    }
})