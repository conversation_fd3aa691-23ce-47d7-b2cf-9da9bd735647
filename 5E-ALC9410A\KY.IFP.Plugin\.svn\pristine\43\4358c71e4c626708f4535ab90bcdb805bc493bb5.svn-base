# 业务控件

## 业务对象选择

### 方式1：ifp-select-ywlx

用于业务对象下拉

示例
```html
4002:
<ifp-select-ywlx v-model="ywlx4002" :ywlx="4002"></ifp-select-ywlx>
1003:
<ifp-select-ywlx v-model="ywlx1003" :ywlx="1003"></ifp-select-ywlx>
```

### 方式2：使用 el-select

配置 $option.data

```js
{
    el:"#app"
    //...

    $option:{
        data:[4002]
    },    

    //...
}
```

```html

<el-select v-model="ywlx4001" value-key="id" label-key="text">
    <el-option v-for="item in $store.getters.map.$4001" :key="item.id" :value="item.id" :label="item.text">{{item.text}}</el-option>
</el-select>

```


