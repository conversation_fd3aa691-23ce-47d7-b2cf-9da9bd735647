﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>操作记录</title>
</head>
<body controller="userLog.js">
    <div form="forms/toolbar" class="layout-h">
        <a id="exportBtn" control="controls/button" option="" log>导出</a>

        <div style="float:right;">
            <!--<span class="kjicon kjicon-wenhao" style="font-size:16px;color:#0075D3"></span>
            <span style="font-size: 14px; font-weight: bold;padding-right:5px;">
                帮助
            </span>-->
            <span class="dicb dicb-tuxing" style="font-size:16px;color:#0075D3"></span>
            <span style="font-size: 14px; font-weight: bold;">
                操作记录
            </span>
        </div>
    </div>

    <div class="searchbar layout-h">
        <table>
            <tr>
                <td class="pg-l-20">操作日期：</td>
                <td><input id="startDate" control="controls/datepicker" option="{showName:'起始日期',controlsUses:2}" /></td>
                <td class="pg-l-20">&nbsp;至：</td>
                <td><input id="endDate" control="controls/datepicker" option="{showName:'终止日期',controlsUses:2}" /></td>
                <td class="pg-l-20">人员姓名：</td>
                <td><input id="UserId" control="controls/select2" option="{showName:'页面名称', controlsUses:2}" /></td>
            </tr>
            <tr>
                <td class="pg-l-20">页面名称：</td>
                <td><input id="PageName" control="controls/select2" option="{showName:'页面名称', controlsUses:2}" /></td>
                <td class="pg-l-20">按钮名称：</td>
                <td><input id="BtnName" control="controls/select2" option="{showName:'按钮名称', controlsUses:2}" /></td>
                <td></td>
                <td class="seartchbar-buttons">
                    <a id="searchBtn" control="controls/button" class="btn-primary" option="{}" log>查询</a>
                    <a id="resetBtn" control="controls/button" option="{}" log>重置</a>
                </td>
            </tr>

        </table>
    </div>
    <div class="layout-c padding">
        <table id="grid" control="controls/grid"
               option='{"autoresize":true,pager:"#pager6", cellEdit:false,sortname:"LogTime", sortorder : "desc",
                method:"post",
                queryParams: {
                }
            }'>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>页面名称</th>
                    <th>人员工号</th>
                    <th>人员姓名</th>
                    <th>按钮名称</th>
                    <th>操作时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td option="{name:'Gid',width : 100,frozen: true,hidden:true, key:true}"></td>
                    <td option="{name:'PageName',width : 150,align : 'center',editable:false}"></td>
                    <td option="{name:'UserId',width : 200,align : 'center',editable:false,formater:'userid2jobid'}"></td>
                    <td option="{name:'UserId2',width : 200,align : 'center',editable:false,formater:'useridtoname'}"></td>
                    <td option="{name:'BtnName',width : 200,align : 'center',editable:false}"></td>
                    <td option="{name:'LogTime',width : 200,align : 'center',editable:false}"></td>
                </tr>
            </tbody>
        </table>
        <div id="pager6"></div>
    </div>

</body>
<script src="/iofp/starter.js"></script>
</html>
