define([
    "platform/vue",
    "css!kyadmin/css/layout.css",
    "css!kyadmin/css/kyadmin-ui.css"
],function(PlatformVue){
    return function(option){
        if(typeof option.use === "string"){
            option.use = [option.use]
        }else if(!option.use){
            option.use = [];
        }
        if(option.use.indexOf("ELEMENT")=="-1"){
            option.use.push("ELEMENT");
        }
        option.use.push("kyadmin-ui-vue");
        return PlatformVue(option)
    }
})