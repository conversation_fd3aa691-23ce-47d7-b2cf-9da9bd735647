define(["jquery"],function($){
	
	var defaultOption = {
		name:"选择", //选择框标题
		idfield:"id", //id字段名
		textfield:"text", //文本字段名
		url:null, //ID转名称接口地址
		postData:null, //ID转名称接口参数
		dialogOption:{ // 弹出页配置
			url:null, //页面地址
			parameter:{ //弹出页参数
				
			}
		}
	}
	
	var configs = {
		default:$.extend(true,{},defaultOption,{
			name:"通用选择"
		}),
		
		test:$.extend(true,{},defaultOption,{
			title:"煤场选择",
			idfield:'gid',
			textfield:'mdmc',
			/**
			 * @update getMcSelectList
			 */
			url: window._http_core_ + window._root_url_ +"/html/coalyard/mcgl/mcsyt/getMcSelectList.action",
			postData:null,
			dialogOption:{
				title:"煤场选择",
				full:false,
				url:window._http_core_ + window._root_url_ +"/common/mcselector/selector.html",
				parameter:{}
			},
			converter:function(data){
				return data&&data.datas||data;
			},
			formater:function(){
				
			}
		})
	}
	
	return {
		get:function(type,option){
			var config = type && configs[type] || configs["default"];
			return $.extend(true,{},config,option)
		}
	}
});