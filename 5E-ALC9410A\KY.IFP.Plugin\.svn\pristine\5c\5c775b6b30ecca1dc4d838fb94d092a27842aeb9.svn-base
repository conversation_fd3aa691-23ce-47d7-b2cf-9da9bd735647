﻿using System;

namespace COM.IFP.PLC.SiemensS7.DataPoint
{
    /// <summary>
    /// 点位信息
    /// </summary>
    public class PLCPoint : ICloneable
    {
        /// <summary>
        /// 点表名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 组
        /// </summary>
        public enumGroupID Group { set; get; }
        /// <summary>
        /// 数据类型
        /// </summary>
        public enumPLCDataType DataType { set; get; }
        /// <summary>
        /// 存储区域0x81(129)I点，0x82(130)Q点，0x83(131)M点，0x84(132)V点即DB块，暂时只支持DB区
        /// </summary>
        public string Area { get; set; } = "DB";
        /// <summary>
        /// 数据块号，I、Q、M点为0，否则为块号
        /// </summary>
        public int Block { get; set; } = 0;
        /// <summary>
        /// 字节偏移量
        /// </summary>
        public int ByteOffset { set; get; }
        /// <summary>
        /// 位偏移移量
        /// </summary>
        public int? BitOffset { get; set; }
        /// <summary>
        /// 本点位占的字节数
        /// </summary>
        public int ByteNum { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 是否为报警信号
        /// </summary>
        public int Alarm { get; set; }
        /// <summary>
        /// 是否为致命错误
        /// </summary>
        public int Fault { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public object Value { get; set; }

        /// <summary>
        /// 根据类型初始化点位值
        /// </summary>
        public void InitValue()
        {
            switch (DataType)
            {
                case enumPLCDataType.PLCbool:
                    Value = false;
                    break;
                case enumPLCDataType.PLCword:
                case enumPLCDataType.PLCdword:
                case enumPLCDataType.PLCuint:
                case enumPLCDataType.PLCint:
                case enumPLCDataType.PLCdint:
                    Value = 0;
                    break;
                case enumPLCDataType.PLCstring:
                    Value = "";
                    break;
                case enumPLCDataType.PLCreal:
                    Value = (float)0;
                    break;
                case enumPLCDataType.PLCbyte:
                    Value = (byte)0;
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 本点位所读写要操作的字节数目
        /// </summary>
        private void SetByteNum()
        {
            switch (DataType)
            {
                case enumPLCDataType.PLCbool:
                case enumPLCDataType.PLCbyte:
                    ByteNum = 1;
                    break;
                case enumPLCDataType.PLCint:
                case enumPLCDataType.PLCuint:
                case enumPLCDataType.PLCword:
                    ByteNum = 2;
                    break;
                case enumPLCDataType.PLCdint:
                case enumPLCDataType.PLCdword:
                case enumPLCDataType.PLCreal:
                    ByteNum = 4;
                    break;
                case enumPLCDataType.PLCstring:
                    ByteNum = 256;
                    break;
                default:
                    throw new Exception($"类型{DataType}无处理方法");
            }
        }

        public PLCPoint(string name, string desciption, enumPLCDataType type, string area, int block, int byteOffset, int bitOffset = 0)
        {
            Name = name;
            DataType = type;
            Area = area;
            Block = block;
            ByteOffset = byteOffset;
            BitOffset = bitOffset;
            Description = desciption;
            InitValue();
            SetByteNum();
        }

        public PLCPoint(string name, string desciption, enumGroupID group, enumPLCDataType type, string area, int block, int byteOffset, int bitOffset = 0)
        {
            Name = name;
            Group = group;
            DataType = type;
            Area = area;
            Block = block;
            ByteOffset = byteOffset;
            BitOffset = bitOffset;
            Description = desciption;
            InitValue();
            SetByteNum();
        }

        public T ToValue<T>()
        {
            return (T)Value;
        }
        /// <summary>
        /// 比较点位值和data是否相等
        /// </summary>
        /// <param name="data"></param>
        /// <returns>0表示相等</returns>
        public int CompareTo(object data)
        {
            int res = -1;
            switch (DataType)
            {
                case enumPLCDataType.PLCbool:

                    if ((bool)data == ToValue<bool>()) res = 0;
                    break;
                case enumPLCDataType.PLCword:
                    if ((uint)data == ToValue<uint>()) res = 0;
                    break;
                case enumPLCDataType.PLCdword:
                    if ((uint)data == ToValue<uint>()) res = 0;
                    break;
                case enumPLCDataType.PLCstring:

                    if ((string)data == ToValue<string>()) res = 0;
                    break;
                case enumPLCDataType.PLCuint:
                    if ((int)data == ToValue<int>()) res = 0;
                    break;
                case enumPLCDataType.PLCint:
                    if ((int)data == ToValue<int>()) res = 0;
                    break;
                case enumPLCDataType.PLCdint:
                    if ((int)data == ToValue<int>()) res = 0;
                    break;
                case enumPLCDataType.PLCreal:
                    if (Math.Abs((float)data - ToValue<float>()) <= float.Epsilon) res = 0;
                    break;
                default:
                    throw new Exception($"【{DataType} {Name}】，未设计该类型的处理方法");
            }
            return res;
        }

        object ICloneable.Clone()
        {
            return MemberwiseClone();
        }

        public PLCPoint Clone()
        {
            return (PLCPoint)MemberwiseClone();
        }
    }
    public enum enumPLCDataType
    {
        /// <summary>
        /// 1bit
        /// </summary>
        PLCbool,

        /// <summary>
        /// 2bytes
        /// </summary>
        PLCword,

        /// <summary>
        /// 4bytes
        /// </summary>
        PLCdword,

        /// <summary>
        /// 256bytes
        /// </summary>
        PLCstring,

        /// <summary>
        /// 2bytes,unsigned
        /// </summary>
        PLCuint,

        /// <summary>
        /// 2bytes,signed
        /// </summary>
        PLCint,

        /// <summary>
        /// 4bytes,signed
        /// </summary>
        PLCdint,

        /// <summary>
        /// 4bytes,single
        /// </summary>
        PLCreal,

        /// <summary>
        /// 1byte
        /// </summary>
        PLCbyte,
    }
}
