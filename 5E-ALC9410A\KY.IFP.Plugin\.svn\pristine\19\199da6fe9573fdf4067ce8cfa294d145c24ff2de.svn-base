# 编码规范

ifp 项目基于 Vue2、Element2 、RequireJS开发，因此需要遵守 Vue2、Element2、AMD 规范。

* [Vue 风格指南](https://v2.cn.vuejs.org/v2/style-guide/index.html/)

## 编码规范

* 在js代码或表达式中使用单引号 `'`
* 在html中使用双引号 `"`

如果要赋值某段 html 代码到js中，可以使用 v-html 指令

```HTML
<template>
  <div v-html="htmlContent"></div>
</template>
 
<script>
export default {
  data() {
    return {
      htmlContent: '<p>这是<b>HTML</b>片段</p>'
    };
  }
};
</script>
```

## 关于Vue2

注：以下为Vue2代码示例，非本公司业务功能代码，业务功能代码示例在后面的[IFP](/pages/page)文档中。

### 赋值

data 中对象和数组赋值请使用 `$set`，避免直接赋值，因为这样可能不会触发视图更新。

示例

```js
{
    data(){
        return {
            info:{
                name:'张三',
                gender:'男'
            }
        }
    },
    created(){
        // 不推荐
        this.info = {
            name:'小丽',
            gender:'女'
        }

        // 推荐
        this.$set(this,'info',{
            name:'小丽',
            gender:'女'
        })
    }
}
```

### props

明确定义数据类型及是否必须

```HTML
<template>
  <div>
    <p>数量：{{ count }}</p>
    <p>状态：{{ state }}</p>
  </div>
</template>

<script>
export default {
  name: 'MyCount',
  props: {
    count: {
      type: Number,   // 需传入Number类型
      required: true, // 必须
      default: 0     // 默认值
    },
    info:{
      type:[String, Number],    // 需传入字符串或数字类型
      required: false, // 非必须
      default: ''     // 默认值
    } 
  },
}
</script>
```

### data中使用props的数据

props中的数据尽量不用在data中直接使用，如果需要使用props中的数据，可以使用计算属性`computed`,或在data中初始化

**计算属性`computed`使用示例**

```HTML
<template>
  <div>
    <p>{{ reversedMessage }}</p>
  </div>
</template>
 
<script>
export default {
  props: {
    message: String
  },
  computed: {
    // 计算属性的 getter
    reversedMessage: function() {
      // 返回传入属性的反转字符串
      return this.message.split('').reverse().join('');
    }
  }
}
</script>
```

**data初始化示例**

```HTML
<template>
  <div>{{ localProp }}</div>
</template>
 
<script>
export default {
  props: {
    propA: String,
    propB: {
      type: Object,   // 需传入对象类型
      required: true, // 必须
      default: {}    // 默认值
    },
  },
  data() {
    return {
      localPropA: this.propA // 使用props初始化data
      localPropB: {...this.propB} // 使用es6的解构赋值方式初始化data
    };
  }
};
</script>
```

### watch

慎用 watch

### computed

不要编写复杂的计算属性，如果确有必要，可拆分成多个。

### 其他

代码中方法类似可以抽离出来为公共函数，通过参数控制执行；代码中注意写好注释，方便后期维护。

```Javascript
  /**
   * 操作按钮
   *
   * @param type 操作类型
   * @param optData 当前行数据，默认为空对象
   */
  operationBtn(type, optData = { row: {} }) {
      if (type === 'add') {
          //新增
          this.formTitle = '新增';
          this.formInfo = {};
      } else if (type === 'edit') {
          // 编辑
          this.formTitle = '编辑';
      } else if (type === 'check') {
          // 查看
          this.formTitle = '查看';
      } else if (type === 'download') {
          // 下载
      } else {
          // 删除
      }
  },
```
