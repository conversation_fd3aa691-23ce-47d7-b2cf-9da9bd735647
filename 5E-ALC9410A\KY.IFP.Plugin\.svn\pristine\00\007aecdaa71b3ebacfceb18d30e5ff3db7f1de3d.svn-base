﻿using COM.IFP.Client;
using COM.IFP.Common;
using COM.IFP.Log;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Text.Json;

namespace API.IFP.Client
{
    /// <summary>
    /// 统一的提供的服务进口
    /// </summary>
    public class Recvice
    {
        //业务接口map.daiabin,从配置文件拿Setting/IFPInterfaceMap.json
        //static Dictionary<string, string> YWJKMAP = new Dictionary<string, string>()
        //{
        //    {"0201","API.HY.Standard.Client.Recvice0201" },
        //    {"0202","API.HY.Standard.Client.Recvice0202" },
        //    {"0203","API.HY.Standard.Client.Recvice0203" },
        //    {"ASS.QueryStatus","API.ASS.Client.QueryStatus" },
        //    {"ASS.TakeDiscard","API.ASS.Client.TakeDiscard" },
        //    {"ASS.QueryRecord","API.ASS.Client.QueryRecord" },
        //    {"ASS.QueryBotInfo","API.ASS.Client.QueryBotInfo" },
        //    {"ASS.QueryAlarm","API.ASS.Client.QueryAlarm" },
        //    {"ASS.QueryRunning","API.ASS.Client.QueryRunning" },
        //    {"RILS.BackTaskRequest","API.HY.Standard.Client.RILS.BackTaskRequest" },
        //    {"RILS.TestTask","API.HY.Standard.Client.RILS.TestTask" },
        //};

        /// <summary>
        /// IOFP 方法接收的Post
        /// </summary>
        /// <remarks>
        /// 使用该方法，请仔细阅读IOFP的技术规范要求
        /// </remarks>
        /// <param name="apiStruct">标准的IOFP_Struct,其中Msg的内容为约定好的Msg.</param>
        /// <returns>标准的IOFP_Struct</returns>
        public IOFP_Struct Post(JsonElement json)
        {
            Lazy<API.IFP.Client.Setting.RegDown> testObj = Entity.Create<API.IFP.Client.Setting.RegDown>();

            IOFP_Struct apiStruct = JsonConvert.DeserializeObject<IOFP_Struct>(json.ToString());
            IOFP_APIHelper iah = new IOFP_APIHelper();
            iah.Iofs = apiStruct;
            // Setting.RegDown 的接口方式，不需要进行鉴权
            if (apiStruct.head.code != "Setting.RegDown")
            {
                if (!iah.CheckAuth())
                {
                    LoggerHelper.Error(ErrorList.E0004, iah.Iofs.auth + ":" + iah.GetAuth());
                    iah.ToError(ErrorList.E0004, "写入的鉴权码：" + iah.Iofs.auth);
                    iah.Iofs.head.msgType = 2;
                    iah.AddAuth();
                    return iah.Iofs;
                }
            }
            else
            {
                //Setting.RegDown被动注册时，centerIP取请求的IP
                JObject msgJson = JObject.Parse(apiStruct.msg);
                msgJson["centerIP"] = WebHelper.GetRemoteIp();
                apiStruct.msg = msgJson.ToString();
            }

            try
            {
                LoggerHelper.Info("#IOFP_API Receive#" + iah.Iofs.head.code + "#" + iah.Iofs.head.requestIP);

                //此处只能通过反射拿到实现类，存在跨包调用的情况（调用产品包的实现）
                //IReceive receiveObj = AutofacUtil.Qualifier<IReceive>($"{iah.Iofs.head.code}");
                Lazy<IReceive> receiveObj = null;
                var YWJKMAP = Config.YWJKMAP;
                if (null != YWJKMAP && YWJKMAP.ContainsKey(iah.Iofs.head.code))
                {
                    receiveObj = Entity.Create<IReceive>(YWJKMAP[iah.Iofs.head.code]);
                }
                else
                {
                    receiveObj = Entity.Create<IReceive>($"API.IFP.Client.{iah.Iofs.head.code}");
                }
                if (receiveObj.Value == null)
                {
                    throw new Exception("找不到对应的接口编号[" + iah.Iofs.head.code + "] 或未定义接口!");
                }

                iah.Iofs = receiveObj.Value.ReceiveRun(iah.Iofs);       // 调用方法，有参数，有返回值
                iah.Iofs.head.msgType = 2;
                iah.AddAuth();
                return iah.Iofs;
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E0006, iah.Iofs.auth + ":" + iah.GetAuth(), ex);
                iah.ToError(ErrorList.E0006, ex.Message);
                iah.Iofs.head.msgType = 2;
                iah.AddAuth();
                //返回错误选项
                return iah.Iofs;
            }
        }
    }
}
