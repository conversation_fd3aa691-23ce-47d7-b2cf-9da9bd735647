﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调度任务</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" class="flex flex-item">
        <ifp-toolbar close>
            <ifp-button code="B1" @click="add">新增</ifp-button>
            <ifp-button code="B2" @click="view" :disabled="viewer.select == null">查看</ifp-button>
            <ifp-button code="B3" @click="update" :disabled="viewer.select == null">修改</ifp-button>
            <ifp-button code="B4" @click="delete1" :disabled="viewer.select == null">删除</ifp-button>
            <ifp-button code="B5" class="glyphicon glyphicon-play" @click="start" :disabled="viewer.select == null || viewer.select.Status == 1">启动</ifp-button>
            <ifp-button code="B6" class="glyphicon glyphicon-pause" @click="stop" :disabled="viewer.select == null || viewer.select.Status == 0">停止</ifp-button>
            <ifp-button code="B7" class="glyphicon glyphicon-play-circle" @click="run" :disabled="viewer.select == null">立即执行</ifp-button>
            <div style="float:right;">
                <span class="dicb dicb-tuxing" style="font-size:16px;color:#0075D3"></span>
                <span style="font-size: 14px; font-weight: bold;">
                    调度任务
                </span>
            </div>
        </ifp-toolbar>
        <ifp-searchbar @search="updateList" @reset="onSelect">
            <ifp-form-item label="任务名称" label-width="80px">
                <ifp-input v-model="viewer.filter[0].JobName.Value" placeholder="支持模糊查询"></ifp-input>
            </ifp-form-item>
            <ifp-form-item label="DLL路径" label-width="80px">
                <ifp-input v-model="viewer.filter[0].DLLPath.Value" placeholder="支持模糊查询"></ifp-input>
            </ifp-form-item>
            <ifp-form-item label="创建类型" label-width="80px">
                <el-select v-model="viewer.filter[0].CreateType.Value" clearable>
                    <el-option label="初始化" :value="0"></el-option>
                    <el-option label="系统添加" :value="1"></el-option>
                </el-select>
            </ifp-form-item>
            <ifp-form-item label="任务状态" label-width="80px">
                <el-select v-model="viewer.filter[0].Status.Value" clearable>
                    <el-option label="已暂停" :value="0"></el-option>
                    <el-option label="已启动" :value="1"></el-option>
                </el-select>
            </ifp-form-item>
        </ifp-searchbar>
        <div class="flex-item flex padding">
            <ifp-panel-table class="flex-item">
                <ifp-table :data="viewer.source" ref="table1" style="width:100%;" @sort-change="sortChange"
                           highlight-current-row @current-change="onChange" :default-sort="{prop: 'CJSJ', order: 'ascending'}">
                    <ifp-table-column label="序号" type="index" :index="indexMethod" align="center"></ifp-table-column>
                    <ifp-table-column prop="Status" align="center" width="100" sortable="custom"
                                      label="任务状态">
                        <template slot-scope="scope">
                            <span v-if="scope.row.Status=='0'">已暂停</span>
                            <span v-if="scope.row.Status=='1'">已启动</span>
                        </template>
                    </ifp-table-column>
                    <ifp-table-column prop="CreateType" align="center" width="100" sortable="custom"
                                      label="创建类型">
                        <template slot-scope="scope">
                            <span v-if="scope.row.CreateType=='0'">初始化</span>
                            <span v-if="scope.row.CreateType=='1'">系统添加</span>
                        </template>
                    </ifp-table-column>
                    <ifp-table-column prop="JobName" show-overflow-tooltip label="任务名称" width="200" align="center"></ifp-table-column>
                    <ifp-table-column prop="JobDesc" show-overflow-tooltip label="任务描述" width="200" align="center"></ifp-table-column>
                    <ifp-table-column prop="DLLPath" show-overflow-tooltip label="DLL路径" align="center"></ifp-table-column>
                    <ifp-table-column prop="ClassPath" show-overflow-tooltip label="类路径" align="center"></ifp-table-column>
                    <ifp-table-column prop="MethodName" label="方法名" align="center"></ifp-table-column>
                    <ifp-table-column prop="Zxfs" align="center" width="100" sortable="custom"
                                      label="执行方式">
                        <template slot-scope="scope">
                            <span v-if="scope.row.Zxfs=='0'">定时执行</span>
                            <span v-if="scope.row.Zxfs=='1'">循环执行</span>
                        </template>
                    </ifp-table-column>
                    <ifp-table-column prop="Corn" label="CORN" align="center"></ifp-table-column>
                    <ifp-table-column prop="Interval" label="循环执行秒数" align="center"></ifp-table-column>
                    <ifp-table-column prop="Creator" label="创建人" align="center"></ifp-table-column>
                    <ifp-table-column prop="CreateTime" label="创建时间" sortable="custom" width="160" :formatter="$TableFormatter.datetime" align="center"></ifp-table-column>
                </ifp-table>
                <template v-slot:pagination>
                    <ifp-pagination @size-change="sizeChange" @current-change="pageChange" :current-page="viewer.paging.Page"
                                    :page-sizes="[10,15,20,50,100,200,500]" :page-size="viewer.paging.Size" layout="total, sizes, prev, pager, next, jumper"
                                    :total="viewer.paging.Sums"><detail></detail></ifp-pagination>
                </template>
            </ifp-panel-table>
        </div>
        <ifp-dialog :title="editor.title" :visible.sync="editor.dialog" :fullscreen="true">
            <ifp-ddrwxq-detail v-if="editor.dialog" @success="updateList();editor.dialog=false;" :source="editor.source" :action="editor.action"></ifp-ddrwxq-detail>
        </ifp-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>
