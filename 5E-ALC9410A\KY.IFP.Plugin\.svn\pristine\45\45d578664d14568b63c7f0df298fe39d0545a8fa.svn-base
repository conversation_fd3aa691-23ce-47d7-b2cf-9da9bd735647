﻿define(["iofp/api", "zutil", "platform/vue", "iofp/common", "iofp/lib/excelExportElement",], function (API, zutil, pVue, common, excelExport) {
    return {
        el: "#app",
        props: {
            source: {
                default: function () {
                    return null;
                }
            }
        },
        data() {
            return {
                activeTab: 'hour', // 控制激活的Tab
                hourSelection: 0, // 存储小时选项
                daySelection: 0, // 存储日选项
                monthSelection: 0, // 存储月选项
                weekSelection: 0, // 存储周选项
                selectedHours: [], // 存储选中的小时
                selectedDays: [], // 存储选中的日
                selectedMonths: [], // 存储选中的月
                selectedWeekDays: [], // 存储选中的星期天
                startMonth: 1, // 存储月循环的起始月
                cycleMonth: 1, // 存储月循环的周期
                startWeek: 1, // 存储周循环的起始周
                selectedDayOfWeek: 2, // 存储周循环的指定星期天
                selectedLastDayOfWeek: 2, // 存储周循环的本月最后一个
                executionTime: new Date(new Date().setHours(0, 0, 0, 0)), // 存储执行时间
                cornExpression: '', // 存储corn表达式
                xqList: ["", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"],
                next5: [],
            }
        },
        created() {
            this.create1();
        },
        watch: {
            hourSelection: 'getCorn',
            daySelection: 'getCorn',
            monthSelection: 'getCorn',
            weekSelection: 'getCorn',
            selectedHours: {
                handler: 'getCorn',
                deep: true, // 深度监听数组
            },
            selectedDays: {
                handler: 'getCorn',
                deep: true, // 深度监听数组
            },
            selectedMonths: {
                handler: 'getCorn',
                deep: true, // 深度监听数组
            },
            selectedWeekDays: {
                handler: 'getCorn',
                deep: true, // 深度监听数组
            },
            startMonth: 'getCorn',
            cycleMonth: 'getCorn',
            startWeek: 'getCorn',
            selectedDayOfWeek: 'getCorn',
            selectedLastDayOfWeek: 'getCorn',
        },
        methods: {
            //必须处理这个焦点的问题 不然切换页签就会报错 虽然不影响功能
            handleTabClick(tab) {
                this.activeTab = tab.name;
                // 控制焦点
                this.$nextTick(() => {
                    const firstInput = this.$el.querySelector('input');
                    if (firstInput) firstInput.focus();
                });
            },
            getCorn() {
                if (this.executionTime == null) {
                    this.$message.error("请先选择执行时间再验证");
                    return;
                }
                let second = this.executionTime.getSeconds().toString();
                let minute = this.executionTime.getMinutes().toString();
                let hour = "*";
                let day = "*";
                let month = "*";
                let Week = "?";

                if (this.weekSelection == 1)  //如果指定了周  那日就变成不指定
                    this.daySelection = 3;

                if (this.weekSelection == 0 && this.daySelection == 3)  //俩都不指定 则日变成每日
                    this.daySelection = 0;

                // 按指定小时
                if (this.hourSelection == 1 && this.selectedHours.length > 0) {
                    hour = this.selectedHours.join(',');
                }

                // 对于日字段，如果选择了“按指定日”
                if (this.daySelection == 1 && this.selectedDays.length > 0) {
                    day = this.selectedDays.join(',');
                }
                //指定日但没选或者不指定是?
                if (this.daySelection == 3 || (this.daySelection == 1 && this.selectedDays.length == 0)) {
                    day = "?";
                }
                //如果选择了“按本月最后一天”就是L
                if (this.daySelection == 2) {
                    day = "L";
                }

                // 对于月字段，如果选择了“按指定月”
                if (this.monthSelection == 1 && this.selectedMonths.length > 0) {
                    month = this.selectedMonths.join(',');
                }
                //指定月但没选走 ?
                if (this.monthSelection == 1 && this.selectedMonths.length == 0) {
                    month = "?";
                }
                //如果选择按月循环
                if (this.monthSelection == 2) {
                    month = this.startMonth + "/" + this.cycleMonth;
                }

                // 对于星期字段，如果选择了“按指定”
                if (this.weekSelection == 1 && this.selectedWeekDays.length > 0) {
                    Week = this.selectedWeekDays.join(',');
                }
                // 对于星期字段，如果选择了“按指定”但没选具体值 走?
                if (this.weekSelection == 1 && this.selectedWeekDays.length == 0) {
                    Week = "?";
                }
                //如果选择按周循环
                if (this.weekSelection == 2) {
                    Week = this.startWeek + "#" + this.selectedDayOfWeek;
                }
                //如果选择按本月最后一个星期几
                if (this.weekSelection == 3) {
                    Week = this.selectedLastDayOfWeek + "L";
                }
                this.cornExpression = `${second} ${minute} ${hour} ${day} ${month} ${Week}`;
            },
            getNext5() {
                var _this = this;
                var obj = {
                    "CronExpression": this.cornExpression,
                    "numTimes": 5
                }
                API.GetAction("API/IFP/Job/Job/GetTaskeFireTime", obj).then(data => {
                    _this.next5 = data;
                }).catch(e => {
                    this.$message.error(e);
                });
            },
            sumbit() {
                var _this = this;
                this.$emit("success", this.cornExpression);
            },
            create1() {
                if (this.source == null || this.source == "") {
                    this.executionTime = new Date(new Date().setHours(0, 0, 0, 0));
                    this.getCorn();
                }
                else {
                    this.cornExpression = this.source;
                    this.getNext5();
                }
            },
            validateCron() {
                // 验证Cron表达式的方法
                this.getNext5();
            },
            confirm() {
                // 确认操作的方法
            },
            onCancel() {
                this.$emit("cancel");
            },
        }
    }
})