define("IOFP-Websocket",[
    "jquery",
    "iofp/components/kywebsocket",
    "EventEmitter",
    "lib/kjlib/jsonschema/verify",
    "iofp/commands",
    "zutil"
],function($,KYWebSocket,EventEmitter,verifySchame,commands,zutil){
    // 类继承
	var extendClass = function(base,sub,prototypes){
		var classbprotos = [prototypes||{},sub.prototype];
		var __ = function(){this.constructor = sub;}
		sub.prototype = base === null ? Object.create(base) : (__.prototype = base.prototype, new __());
		for(var i=0;i<classbprotos.length;i++){
			for(var a in classbprotos[i]){
				if(!sub.hasOwnProperty(a) && classbprotos[i].hasOwnProperty(a)){
					sub.prototype[a]=classbprotos[i][a];
				}
			}
		}
		return sub;
    }

    // 基于 websocket 扩展的
    var IOFPWebSocket = extendClass(
        KYWebSocket, function IOFPWebSocket(host,option) {
            this.sendindex = 1;
            KYWebSocket.call(this, host,null,zutil.extend({
                timeoutInterval:3000, //连接超过3秒，则重新连接
                autoOpen:false
            },option));
            this.commandMap = option.commandMap;
            this.commandEvents = new EventEmitter();
            
            this.on("message",function(event){
                var data = JSON.parse(event);
                var eventName = data.H;
                if(data.I){
                    eventName+=(":")+data.I;
                    this.triggerMsg(data.I,data);// 直接触发I事件
                }
                this.triggerMsg(eventName,data);
            })
        },{
            sendCommand : function(name,data,i,callback){
                if(typeof i == "function"){
                    this.sendindex++;
                    callback = i;
                    i = Date.now().toString() + this.sendindex;
                }
                var schema = commands[name];
                if(!schema){alert("命令 ["+name+"] 未定义");return}
                var error = verifySchame(schema,data);
                if(error){ alert(error);  return; }
                var postData = { H:name, C:JSON.stringify(data) };
                if(arguments.length>2){postData.I=i};
                if (callback) {
                    return this.send(JSON.stringify(postData));
                    /*
                    var eventName = name;
                    if(this.commandMap && this.commandMap[name]){
                        eventName=this.commandMap[name];
                        eventName += (i?(":" + i):"");
                    }else if(i){
                        eventName=i;
                    }
                    if (callback) {
                        this.onceCmdMsg(eventName, callback)
                    }
                    */
                }
                return this.send(JSON.stringify(postData));
            },
            onCmdMsg : function(name,callback){
                if(typeof callback !== "function"){throw "callback 必须为 Function"}
                
                this.commandEvents.off(name,callback);
                this.commandEvents.on(name,callback);
            },
            triggerMsg:function(name,data){
                this.commandEvents.emit(name,data);
            },
            offCmdMsg : function(name,callback){
                this.commandEvents.off(name,callback);
            },
            onceCmdMsg : function(name,callback){
                this.commandEvents.off(name,callback);
                this.commandEvents.once(name,callback);
            }
        }
    );

    return IOFPWebSocket;
})


define([
    "sysSetting",
    "module",
    "IOFP-Websocket",
    "topwindow",
    "log",
    "zutil",
    "iofp/components/getsocketurl"
],function(option,module,IOFPWebSocket,TopWindow,Log,zutil,getsocketurl){
    if(TopWindow.__IOFPWS){
        // 如果时子页面，则在页面销毁时要移除在父页面绑定的事件
        $(window).on("beforeunload",function(){
            console.log("beforeunload");
            [TopWindow.__IOFPWS.commandEvents,TopWindow.__IOFPWS].forEach(function(obj){
                for(var key in obj._events){
                    var list = obj._events[key];
                    for(var i=0;i<list.length;i++){
                        if(list[i].window === window){
                            list[i].window = null;
                        }
                    }
                    obj._events[key] = list.filter(item=>item.window!==null);
                }
            })
        })

        var mypws = Object.create(TopWindow.__IOFPWS);
        mypws.onCmdMsg=function(name,callback){
            callback.window = window;
            TopWindow.__IOFPWS.onCmdMsg(name,callback)
        }
        return mypws;
    }

    var wsopt = zutil.extend(true,{},module.config(),option.websocket);
    let iofpws = TopWindow.__IOFPWS = new IOFPWebSocket(getsocketurl(),wsopt);
    
    var log = Log.getLog("socket");
    //iofpws.on("log",function(msg){
    //    log.debug(msg);
    //})
    
    return iofpws;
})