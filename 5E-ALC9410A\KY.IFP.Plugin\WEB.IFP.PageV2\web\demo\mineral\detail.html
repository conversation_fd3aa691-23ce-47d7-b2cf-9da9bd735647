<!DOCTYPE html>
  <html lang="zh-Hans">
  <head>
      <meta charset="UTF-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>矿点详情信息</title>
  </head>
  <body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">

        <ifp-toolbar>
            <template v-if="state&&state!=='view'">
                <ifp-button v-if="state&&state!=='view'" type="primary" @click="onSubmit">保存</ifp-button>
                <ifp-button-close></ifp-button-close>
            </template>
            <ifp-button v-else @click="onCancel">确定</ifp-button>
        </ifp-toolbar>

        <ifp-panel>
            <ifp-form ref="form" :model="editor.source" :inline="false"
                label-width="80px" class="padding">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="矿点编码" prop="Kdbm" :rules="{required:true, message:'矿点编码不能为空'}">
                            <ifp-input v-model="editor.source.Kdbm" placeholder="建议使用矿点简称拼音首字母大写"></ifp-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="矿点名称" prop="Bname" :rules="{required:true, message:'矿点名称不能为空'}">
                            <ifp-input v-model="editor.source.Bname"></ifp-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="矿点简称" prop="Sname">
                            <ifp-input v-model="editor.source.Sname"></ifp-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="矿点简称" prop="Sname">
                            <ifp-input v-model="editor.source.Sname"></ifp-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="矿点简称" prop="Sname">
                            <ifp-input v-model="editor.source.Sname"></ifp-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="矿点简称" prop="Sname">
                            <ifp-input v-model="editor.source.Sname"></ifp-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="矿点简称" prop="Sname">
                            <ifp-input v-model="editor.source.Sname"></ifp-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="矿点简称" prop="Sname">
                            <ifp-input v-model="editor.source.Sname"></ifp-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </ifp-form>
        </ifp-panel>

        <ifp-dialog class="subpage" title="测试" :visible.sync="dialog"
             width="800px">
            <demo-mineral-detail style="height:500px;" class="flex-item" @cancel="dialog=false"
                @sucess="dialog=false"></demo-mineral-detail>
        </ifp-dialog>
    </ifp-page>
      <script src="/iofp/ics/starter.js"></script>
  </body>
  </html>
  