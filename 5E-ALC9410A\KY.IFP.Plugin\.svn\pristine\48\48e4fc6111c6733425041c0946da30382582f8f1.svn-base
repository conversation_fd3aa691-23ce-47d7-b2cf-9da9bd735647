﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace API.IFP.SignalR
{
    /// <summary>
    /// SignalR 组件注册表 - 用于管理所有SignalR组件
    /// </summary>
    public static class SignalRComponentRegistry
    {
        private static IServer2Client _provider = new EmptyMessageProvider();
        private static IClient2Server? _handler = null;

        /// <summary>
        /// 注册消息提供者(服务器到客户端)
        /// </summary>
        public static void RegisterProvider(IServer2Client provider)
        {
            _provider = provider ?? new EmptyMessageProvider();
        }

        /// <summary>
        /// 获取注册的消息提供者
        /// </summary>
        public static IServer2Client GetProvider()
        {
            return _provider;
        }

        /// <summary>
        /// 注册消息处理器(客户端到服务器)
        /// </summary>
        public static void RegisterHandler(IClient2Server handler)
        {
            _handler = handler ?? null;
        }

        /// <summary>
        /// 获取注册的消息处理器
        /// </summary>
        public static IClient2Server GetHandler()
        {
            return _handler;
        }
    }
}
