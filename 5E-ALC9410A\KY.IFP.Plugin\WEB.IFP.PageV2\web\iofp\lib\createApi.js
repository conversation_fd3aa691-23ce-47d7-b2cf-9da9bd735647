define(["iofp/vue-plugins/ifp-api"],(api)=>{

    function createArgs(option,args){
        if(typeof option.prop === "function"){
            return Promise.resolve(option.prop.apply(this,args)).then(p=>[p]);
        }else if(typeof option.prop === "object"){
            return Promise.resolve([...args,option.prop])
        }else{
            return Promise.resolve(args)
        }
    }

    function query(option,args){
        if(option.usemock && !option.mock){
            throw new Error("usemock === true 时，必须要定义 mock")
        }
        if(!option.url && !option.mock){
            throw new Error("url 与 mock 至少定义一个")
        }

        let usemock = option.usemock || !option.url

        if(usemock){
            if(typeof option.mock==="function"){
                return Promise.resolve(option.mock(...args))
            } else{
                return Promise.resolve(option.mock)
            }
        }

        return Promise.resolve(api.post(option.url,args[0]))
    }

    function createApi(option){
        return function(...args){
            return createArgs(option,args)
            .then(args=>query(option,args))
            .then(d=>{
                if(option.then){
                    return Promise.resolve(option.then(d))
                }else{
                    return d;
                }
            })
            .catch(err=>{
                console.error(err)
                throw err;
            })
        }
    }
    function createApis(option){
        let rev = {};
        for(var a in option){
            rev[a]=createApi(option)
        }
        return rev;
    }

    return {
        createApi,
        createApis
    }
})