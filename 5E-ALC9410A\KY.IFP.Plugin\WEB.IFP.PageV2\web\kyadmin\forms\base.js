define(["jclass","renderer","log"],function(jclass,renderer,log){
	var defaultOption = {
		
	}
	
	return jclass(Object,{
		name:"form",
		container:null,//容器
		controller:null,//控制器
		id:null,
		autoRender:true,
		log:null,
		autoRenderControls:true,
		controls:null,
		option:null,
		renderControls:function(callback){
			renderer.controls(this.controller,this,this.container,callback);
		},
		render:function(){
			
		},
		
		createOption:function(container,option){
			return $.extend(true,{},defaultOption,option);
		},
		events:{},
		bind:function(type,fn,context){
			this.events[type] = this.events[type] || [];
			if(context&&fn.bind){
				fn = fn.bind(context);
			}
			this.events[type].push(fn);
		},
		trigger:function(type){
			if(!this.events[type]){
				return;
			}
			var arg = [].slice.call(arguments,1);
			arg.push(this);
			var events = this.events[type];
			var rev;
			if(events){
				for(var i =0;i<events.length;i++){
					rev = events[i].apply(this,[type].concat(arg));
				}
			}
			if(rev){
				return rev;
			}
		},
		afterReader:function(){
			
		},
		init:function(container,option,controller,callback){
			this.controls = {};
			this.option = this.createOption(container,option);
			this.log = log.getLog(this.name || "form");
			var _this = this;
			this.container = container;
			this.$container = $(container);
			this.controller = controller;
			this.id = this.$container.attr("id");
			if(!this.id){
				this.$container.attr("id",this.id = F.util.genid(this.name));
			}
			controller&&(controller.forms[this.id] = this);
			this.$container.attr("isrendered",true);
			$.extend(true,this.option,option);
			
			var ps = [];
			if(this.autoRender){
				ps.push(F.util.promise(this.render()));
			}
			
			if(this.autoRenderControls){
				ps.push(F.util.promise(this.renderControls()));
			}
			return Promise.all(ps).then(function(){
				F.util.promise(_this.afterReader())
				.then(callback);
			})
		}
	});
});
