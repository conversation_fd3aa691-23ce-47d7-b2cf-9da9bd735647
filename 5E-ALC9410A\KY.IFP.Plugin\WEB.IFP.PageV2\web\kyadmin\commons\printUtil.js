;
define([ "require"], function(require) {
	
	var getPrintStyle = function(){
		return new Promise(function(resolve1,reject){
			$.get(F.sysConfig.get("printCssPath"),function(css){
				resolve1(css);
			});
		});
	};
	
	return {
		getPrintControl:function(){
			return F.common.LodopFuncs.loadLodop()["catch"](function(err){
				console.error(err);
			});
		},

		printTable : function(url,data,table){
			var _this = this;
			return this.getPrinter()
			.then(function(datas){
				var LODOP = datas.LODOP;
				var css = datas.CSS;
			    LODOP.PRINT_INIT("分页打印表格");
			    LODOP.SET_PRINT_PAGESIZE(2,"","","");
			    _this.createTable(url,data,table,LODOP,css);
		        LODOP.PREVIEW();
			})
		},

		getPrinter:function(){
			var _this = this;
			return Promise.all([
				getPrintStyle(),
				_this.getPrintControl()
			]).then(function(datas){
				return {
					LODOP:datas[1],
					CSS:datas[0]
				}
			});
		},
		
		printHtml:function(){
			
		},
		
		printBill:function(id,cssid){
			var _this = this;
			return this.getPrinter()
			.then(function(datas){
				var LODOP = datas.LODOP;
				var css = datas.CSS;
			    LODOP.PRINT_INIT("打印单据");
			    var strStyleCSS = "<style>" + css + "\n" + $("#"+cssid).html() + "</style>" ;
				var form1Str = strStyleCSS + "<body>"+ $("#"+id).html()+"</body>";
//				LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED",1);//横向时的正向显示
			    LODOP.ADD_PRINT_HTM(40, 40, "90%", "100%", form1Str);
		        LODOP.PREVIEW();
		        F.util.hideWait();
			});
		},
		
		createTable:function(url,data,table,LODOP,css){
			var sidx = table.$container.getGridParam("sortname");
			var sord = table.$container.getGridParam("sortorder");
			delete data[table.id];
			data.page = 1,
			data.rows = 10000,
			data.sidx = sidx==null?"":sidx,
			data.sord = sord==null?"desc":sord
			var resp = F.common.postSyn(url,data);
			var	resultObj = eval('(' + resp + ')');	
			var colModel = table.option.colModel;
			var colNames = table.option.colNames;
			var data = resultObj.rows;
			var json = {};
			var columns = [];
			var rows = [];
			json.total=data.length;
			json.columns=columns;
			json.rows=rows;
			
			columns.push({
				name:"rn",
				title:"序号",
				align:"center",
				width:40
			});
			
			json.columns = columns = columns.concat(colModel.filter(function(data,i){
				data.colname = colNames[i]
				return !data.hidden
			}).map(function(data,i){
				var cline={
					name:data.name,
					title:data.colname,
					width:data.width,
					align:data.align||"left"
				};
				if(data.control=="controls/select2"||data.control=="controls/select"){
					cline.ywlx=data.ywlx;
					if(!cline.ywlx&&data.controloption){
						var arr = data.controloption.data;
						var map = new F.common.Map();
						if(arr&&arr.length>0){
							for(var j=0;j<arr.length;j++){
								map.put(arr[j].id,arr[j].text);
							}
							cline.idmcmap=map;
						}
					}else{
						var map = F.common.ywUtil.getYwlxMap(data.ywlx);
						cline.idmcmap=map;
					}
				}
				return cline;
			}));
			
			for(var j=0;j<data.length;j++){
				var rline={};
				for(var k=0;k<columns.length;k++){
					var cname = columns[k].name;
					var idmcmap = columns[k].idmcmap;
					if(cname=="rn"){
						rline[cname]=j+1;
					}else{
						if(idmcmap){
							rline[cname]=columns[k].idmcmap.get(data[j][cname]+"");
						}else{
							rline[cname]=data[j][cname];
						}
					}
				}
				rows.push(rline);
			}
			
			var rowNum=!table.option.rowNum?20:table.option.rowNum;
			var tablenum=Math.ceil(data.length/rowNum);
            var cellCount = json.columns.length;
            
			for(var k=0;k<tablenum;k++){
	            var cellCount = json.columns.length;
				var htmlstr = ["<table class=\"form-datatable\">"]
				htmlstr.push("<thead><tr>");
				htmlstr.push(json.columns.map(function(data,i){
					var w = 0;
					var align = "left";
					if(data.width){w=data.width;}
					if(data.align){align=data.align;}
					w=w?w+"px":"";
					return "<th style=\"width:"+ w +";text-align:"+align+";\">" +data.title + "</th>"
				}).join(""));
				htmlstr.push("</tr></thead>");
				htmlstr.push("<tbody>");
				htmlstr.push(json.rows.slice(k*rowNum,k*rowNum+rowNum).map(function(data){
					return "<tr>"+json.columns.map(function(item,i){
						var val = data[item.name];
						var align = "left";
						if(item.align){align=item.align;}
	        			return "<td style=\"text-align:"+align+";\">"+(val===null?"":val)+"</td>"
	        		}).join("")+"<tr>";
				}).join(""));
				htmlstr.push("</tbody>");
				htmlstr.push("</table>");
				LODOP.ADD_PRINT_HTM(40, 40, "90%", "100%", "<style>" + css + "</style>" + "<body>"+ htmlstr.join("")+"</body>");
				LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED",1);//横向时的正向显示
				if(k!=tablenum-1){
					LODOP.NEWPAGE();
				}
			}
		},
		
		/**
		 * barList 条码列表[{code:'40000005684000000372',title:'碘化钾AR500ml'},{code:'40000005684000000373',title:'碘化钾AR800ml'}]
		 * preview 是否先预览
		 * group   多个码是否打印在一张A4纸上
		 * title   条码上方是否显示标题
		 */
		printCode:function(barList, preview, group, title){
			var _this = this;
			F.ajax({
				url:"/com.kysoft.service/html/barcodeprint/getBarcodePrint.action",
				data:{pathname:window.location.pathname},
				success:function(resp){
					var data = JSON.parse(resp);
					var width = data.pagewidth;		//单个条码打印区域宽度
					var height = data.pageheight;	//单个条码打印区域高度
					var top = data.toppadding;		//“条码顶端”与“打印区域顶部”的距离
					var left = data.leftpadding;	//“条码左边”与“打印区域左边”的距离
					var barwidth = data.barwidth;	//条码宽度
					var barheight = data.barheight;	//条码高度
					var formater = data.formater;	//条码格式，128A，128B，128C，128Auto，QRCode等
					//如果打印在一张A4纸上，要根据A4纸张的大小，及打印区域width和height的值，计算一张A4纸上打印几行几列
					if(group){
						width = width*1.5;
						height = height*1.5;
						left = left+barwidth/2;
					}
					var widthA4 = 210;	//A4纸宽度
					var heightA4 = 297;	//A4纸高度
					var padding = 5;	//A4纸边距
					var row = Math.floor((heightA4-padding*2)/height);
					var col = Math.floor((widthA4-padding*2)/width);
					F.common.LodopFuncs.loadLodop().then(function(LODOP){
						LODOP.PRINT_INIT("条码打印");
						if(group){
							LODOP.SET_PRINT_PAGESIZE(1,widthA4+"mm",heightA4+"mm");
						}else{
							LODOP.SET_PRINT_PAGESIZE(1,width+"mm",height+"mm");
						}
						var pages = -1;
						for(var i=0; i<barList.length; i++){
							if(group){
								if(i%(row*col)==0){
									LODOP.NewPage();
									pages+=1;
								}
								if(title){
									var barLeft = _this.getBarLeft(barList[i].title, width);
									LODOP.ADD_PRINT_HTM(Math.floor((i-pages*row*col)/col)*height+padding+2+"mm",
														(i%col)*width+padding+barLeft+"mm",
														width+"mm","8mm",barList[i].title);
								}
								LODOP.ADD_PRINT_BARCODE(Math.floor((i-pages*row*col)/col)*height+padding+top+"mm",
														(i%col)*width+padding+left+"mm",
														barwidth+"mm",barheight+"mm",formater,barList[i].code);
							}else{
								LODOP.NewPage();
								if(title){
									var barLeft = _this.getBarLeft(barList[i].title, width);
									LODOP.ADD_PRINT_HTM("2mm",barLeft+"mm",width+"mm","8mm",barList[i].title);
								}
								LODOP.ADD_PRINT_BARCODE(top+"mm",left+"mm",barwidth+"mm",barheight+"mm",formater,barList[i].code);
							}
						}
						if(preview){
							LODOP.PREVIEW();
						}else{
							LODOP.PRINT();
						}
					});
				}
			})
		},
		
		/**
		 * 根据条码内容和条码纸宽度，确定二维码标题左边距
		 */
        getBarLeft:function(code, width) {
        	//条码长度（汉字算2个，字母数字算1个）
            var len = 0;
            for (var i = 0; i < code.length; i++) {
                var a = code.charAt(i);
                if (a.match(/[^\x00-\xff]/ig) != null) {
                    len += 2;
                }else {
                    len += 1;
                }
            }
            //每个条码字符的宽度（mm）
            var cwidth = 2;
            var left = (width - cwidth*len)/2;
            return left<2 ? 2:left;
        }
	}
});


/**
	var barList = [];
	barList.push({code:"400001234000768",title:"1-氨基-2萘酚-4磺酸500ml"});
	barList.push({code:"400001244000769",title:"1.10-菲罗啉500ml"});
	barList.push({code:"400001254000770",title:"30%过氧化氢500ml"});
	barList.push({code:"400001264000771",title:"36%乙酸500ml"});
	barList.push({code:"400001274000772",title:"5-磺基水杨酸500ml"});
	barList.push({code:"400001284000773",title:"95%乙醇500ml"});
	barList.push({code:"400001294000774",title:"L-半胱氨酸500ml"});
	barList.push({code:"400001304000775",title:"L-半胱氨酸盐酸盐500ml"});
	barList.push({code:"400001314000776",title:"三乙醇胺500ml"});
	barList.push({code:"400001324000777",title:"三氯化铁500ml"});
	barList.push({code:"400001334000778",title:"三氯甲烷500ml"});
	barList.push({code:"400001344000779",title:"丙三醇500ml"});
	barList.push({code:"400001354000780",title:"丙酮500ml"});
	barList.push({code:"400001364000781",title:"中性红500ml"});
	barList.push({code:"400001374000782",title:"乙二胺500ml"});
	barList.push({code:"400001384000783",title:"乙二胺四乙酸二钠盐500ml"});
	barList.push({code:"400001394000784",title:"乙二胺四乙酸二钠镁500ml"});
	barList.push({code:"400001404000785",title:"乙二醇双四乙酸500ml"});
	barList.push({code:"400001414000786",title:"乙酸500ml"});
	barList.push({code:"400001424000787",title:"乙酸钠500ml"});
	barList.push({code:"400001434000788",title:"乙酸铵500ml"});
	barList.push({code:"400001444000789",title:"乙醇胺500ml"});
	barList.push({code:"400001454000790",title:"乙醚500ml"});
	barList.push({code:"400001464000791",title:"乙醛（40%）500ml"});
	barList.push({code:"400001474000792",title:"二异丙胺500ml"});
	barList.push({code:"400001484000793",title:"二苯胺-4-硫磺钠500ml"});
	barList.push({code:"400001494000794",title:"亚硝酸钠500ml"});
	barList.push({code:"400001504000795",title:"亚硫酸氢钠500ml"});
	barList.push({code:"400001514000796",title:"亚硫酸钠500ml"});
	barList.push({code:"400001524000797",title:"偏钒酸铵500ml"});
	barList.push({code:"400001534000798",title:"偶氮氯膦500ml"});
	barList.push({code:"400001544000799",title:"六次甲基四胺500ml"});
	barList.push({code:"400001554000800",title:"变色硅胶500ml"});
	barList.push({code:"400001564000801",title:"可溶性淀粉500ml"});
	barList.push({code:"400001574000802",title:"四氯化碳500ml"});
	barList.push({code:"400001584000803",title:"四硼酸钠500ml"});
	barList.push({code:"400001594000804",title:"安替福尼500ml"});
	barList.push({code:"400001604000805",title:"对二甲基苯甲酸500ml"});
	barList.push({code:"400001614000806",title:"对氨基二乙基苯胺硫酸盐500ml"});
	barList.push({code:"400001624000807",title:"对氨基苯磺酸500ml"});
	barList.push({code:"400001634000808",title:"对硝基苯酚500ml"});
	barList.push({code:"400001644000809",title:"异丙醇500ml"});
	barList.push({code:"400001654000810",title:"异戊醇500ml"});
	barList.push({code:"400001664000811",title:"抗坏血酸500ml"});
	barList.push({code:"400001674000812",title:"无水乙酸钠500ml"});
	barList.push({code:"400001684000813",title:"无水乙醇500ml"});
	barList.push({code:"400001694000814",title:"无水亚硫酸钠500ml"});
	barList.push({code:"400001704000815",title:"无水氯化钙500ml"});
	barList.push({code:"400001714000816",title:"无水氯化铝500ml"});
	barList.push({code:"400001724000817",title:"无水硫代硫酸钠500ml"});
	barList.push({code:"400001734000818",title:"无水碳酸钠500ml"});
	barList.push({code:"400001744000819",title:"无水磷酸二氢钾500ml"});
	barList.push({code:"400001754000820",title:"无水磷酸氢二钠500ml"});
	barList.push({code:"400001764000821",title:"柠檬酸500ml"});
	barList.push({code:"400001774000822",title:"柠檬酸三钠500ml"});
	barList.push({code:"400001784000823",title:"次氯酸钠500ml"});
	barList.push({code:"400001794000824",title:"正丁醇500ml"});
	barList.push({code:"400001804000825",title:"氟化钾500ml"});
	barList.push({code:"400001814000826",title:"氢氟酸500ml"});
	barList.push({code:"400001824000827",title:"氢氧化钠500ml"});
	barList.push({code:"400001834000828",title:"氢氧化钾500ml"});
	barList.push({code:"400001844000829",title:"氨基乙酸500ml"});
	barList.push({code:"400001854000830",title:"氨水500ml"});
	barList.push({code:"400001864000831",title:"氨磺酸500ml"});
	barList.push({code:"400001874000832",title:"氯化亚锡500ml"});
	barList.push({code:"400001884000833",title:"氯化钠500ml"});
	barList.push({code:"400001894000834",title:"氯化钾500ml"});
	barList.push({code:"400001904000835",title:"氯化铅500ml"});
	barList.push({code:"400001914000836",title:"氯化铵500ml"});
	barList.push({code:"400001924000837",title:"水杨酸钠500ml"});
	barList.push({code:"400001934000838",title:"溴甲酚紫500ml"});
	barList.push({code:"400001944000839",title:"溴甲酚绿500ml"});
	barList.push({code:"400001954000840",title:"甲基橙500ml"});
	barList.push({code:"400001964000841",title:"甲基红500ml"});
	barList.push({code:"400001974000842",title:"甲酚红500ml"});
	barList.push({code:"400001984000843",title:"甲醇500ml"});
	barList.push({code:"400001994000844",title:"盐酸500ml"});
	barList.push({code:"400002004000845",title:"盐酸羟胺500ml"});
	barList.push({code:"400002014000846",title:"盐酸邻联甲苯胺500ml"});
	barList.push({code:"400002024000847",title:"石油醚500ml"});
	barList.push({code:"400002034000848",title:"硝酸500ml"});
	barList.push({code:"400002044000849",title:"硝酸银500ml"});
	barList.push({code:"400002054000850",title:"硫代乙酰胺500ml"});
	barList.push({code:"400002064000851",title:"硫代硫酸钠500ml"});
	barList.push({code:"400002074000852",title:"硫氰酸钠500ml"});
	barList.push({code:"400002084000853",title:"硫酸500ml"});
	barList.push({code:"400002094000854",title:"硫酸亚铁铵500ml"});
	barList.push({code:"400002104000855",title:"硫酸汞500ml"});
	barList.push({code:"400002114000856",title:"硫酸联胺500ml"});
	barList.push({code:"400002124000857",title:"硫酸铜500ml"});
	barList.push({code:"400002134000858",title:"硫酸铝钾500ml"});
	barList.push({code:"400002144000859",title:"硫酸银500ml"});
	barList.push({code:"400002154000860",title:"硼酸500ml"});
	barList.push({code:"400002164000861",title:"碘500ml"});
	barList.push({code:"400002174000862",title:"碘化钾500ml"});
	barList.push({code:"400002184000863",title:"碘酸钾500ml"});
	barList.push({code:"400002194000864",title:"碳酸钙500ml"});
	barList.push({code:"400002204000865",title:"磷酸三钠500ml"});
	barList.push({code:"400002214000866",title:"磷酸二氢钠500ml"});
	barList.push({code:"400002224000867",title:"磷酸二氢钾500ml"});
	barList.push({code:"400002234000868",title:"磷酸氢二钠500ml"});
	barList.push({code:"400002244000869",title:"磷酸氢二铵500ml"});
	barList.push({code:"400002254000870",title:"磺基水杨酸钠500ml"});
	barList.push({code:"400002264000871",title:"结晶氯化铝500ml"});
	barList.push({code:"400002274000872",title:"肼硫酸盐500ml"});
	barList.push({code:"400002284000873",title:"苯并三氮唑500ml"});
	barList.push({code:"400002294000874",title:"苯甲醛500ml"});
	barList.push({code:"400002304000875",title:"苯酚红500ml"});
	barList.push({code:"400002314000876",title:"草酸500ml"});
	barList.push({code:"400002324000877",title:"草酸.2H2O500ml"});
	barList.push({code:"400002334000878",title:"草酸钠500ml"});
	barList.push({code:"400002344000879",title:"药品名称500ml"});
	barList.push({code:"400002354000880",title:"萘酚绿B500ml"});
	barList.push({code:"400002364000881",title:"蔗糖500ml"});
	barList.push({code:"400002374000882",title:"过硫酸钠500ml"});
	barList.push({code:"400002384000883",title:"过硫酸钾500ml"});
	barList.push({code:"400002394000884",title:"过硫酸铵500ml"});
	barList.push({code:"400002404000885",title:"邻苯二甲酸氢钾500ml"});
	barList.push({code:"400002414000886",title:"邻菲罗啉盐酸盐500ml"});
	barList.push({code:"400002424000887",title:"酒石酸500ml"});
	barList.push({code:"400002434000888",title:"酒石酸钾钠500ml"});
	barList.push({code:"400002444000889",title:"酒石酸锑钾500ml"});
	barList.push({code:"400002454000890",title:"酚酞500ml"});
	barList.push({code:"400002464000891",title:"酸性铬蓝K500ml"});
	barList.push({code:"400002474000892",title:"重铬酸钾500ml"});
	barList.push({code:"400002484000893",title:"钙羧酸500ml"});
	barList.push({code:"400002494000894",title:"钼酸钠500ml"});
	barList.push({code:"400002504000895",title:"钼酸铵500ml"});
	barList.push({code:"400002514000896",title:"铬天青500ml"});
	barList.push({code:"400002524000897",title:"铬酸钡500ml"});
	barList.push({code:"400002534000898",title:"铬酸钾500ml"});
	barList.push({code:"400002544000899",title:"铬黑T500ml"});
	barList.push({code:"400002554000900",title:"锌粒500ml"});
	barList.push({code:"400002564000901",title:"锌试剂500ml"});
	barList.push({code:"400002574000902",title:"间硝基酚500ml"});
	barList.push({code:"400002584000903",title:"高氯酸500ml"});
	barList.push({code:"400002594000904",title:"高氯酸钡.三水500ml"});
	barList.push({code:"400002604000905",title:"高锰酸钾500ml"});
	barList.push({code:"400002614000906",title:"麝香草酚蓝500ml"});
	barList.push({code:"400002624000907",title:"麝香草酚蓝800ml"});
*/