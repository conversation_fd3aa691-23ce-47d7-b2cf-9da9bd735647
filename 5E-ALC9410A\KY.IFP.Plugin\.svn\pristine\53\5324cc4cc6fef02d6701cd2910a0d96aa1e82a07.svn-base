# IFP 页面开发规范

ifp 是基于 kyadmin ，修改了默认参数 , 采用 ifp 项目自定义启动器 `/iofp/starter.js`

ics 基于 ifp 的启动器，修改了部分参数，采用 启动器 `/iofp/ics/starter.js`


```
<body controller option="{target:'element'}">
    <div id="app">
        <el-input v-model="msg" />
    </div>
    <script src="/iofp/starter.js"></script>
</body>
```

IFP 页面采用 vue 组件化开发，`页面即组件`

必须符合以下规范

* js 必须符合 AMD 规范
* 页面必须可以能单独访问
* 控制器 js 不能强依赖 url
* 同一项目下所有页面中只能引入一个js
* 页面独有的 css 只能以 css! 的方式导入

* js 与 html 同名，且在同一目录下
* `body` 节点下必须且只能存在一个 `div`
* 一般情况 `body` 无需配置 `controller`，除非 js 与 html 的文件名不同

## 示例

example.html

```html
<body>
    <div>{{msg}}</div>
    <script src="/ifp/starter.js"></script>
</body>
```

example.js
```
define(function(){
    return {
        el:"#app",
        data:function(){
            return {
                msg:"Hello World!"
            }
        }
    }
})
```