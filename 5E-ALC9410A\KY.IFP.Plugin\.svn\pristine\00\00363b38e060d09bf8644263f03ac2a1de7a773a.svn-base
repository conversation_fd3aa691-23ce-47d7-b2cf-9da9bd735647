<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单据状态标识</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div id="app" class="flex flex-item" id="app">
        <div class="toolbar">
            <el-button icon="el-icon-refresh">刷新</el-button>
        </div>
        <div class="flex flex-row flex-item container-mark" :class="'container-mark-'+mark1">
            <el-select v-model="mark1" placeholder="">
              <el-option value="huitui" label="回退"></el-option>
              <el-option value="yishenhe" label="已审核"></el-option>
              <el-option value="zuofei" label="作废"></el-option>
            </el-select>
        </div>
    </div>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>