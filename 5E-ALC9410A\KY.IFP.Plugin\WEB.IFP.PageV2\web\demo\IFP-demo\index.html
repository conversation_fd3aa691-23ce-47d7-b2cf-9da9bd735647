﻿<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <title>前端调试Demo</title>
        <style></style>
    </head>
    <body class="flex test-demo" controller option="{platform:'element'}">
        <ifp-page class="flex flex-item" id="app" v-loading="loading">
            <!-- <ifp-toolbar>
                <ifp-button icon="dicb dicb-queding" @click="regBtn">注册</ifp-button>
                <ifp-button icon="kjicon kjicon-gengxin" @click="routeRequestBtn">路由请求</ifp-button>
                <ifp-button icon="dicb dicb-queding" @click="changeConfigBtn">修改配置</ifp-button>
            </ifp-toolbar> -->
            <ifp-searchbar ref="searchForm" :model="searchForm" @search="search" @reset="onReset">
                <ifp-form-item label="范围查询">
                    <ifp-date-picker v-model="searchForm.testDate" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></ifp-date-picker>
                </ifp-form-item>
                <ifp-form-item label="单个日期可选">
                  <ifp-date-picker v-model="searchForm.startTime" type="date" placeholder="开始日期" :picker-options="pickerStartOptions"></ifp-date-picker>
                  至
                  <ifp-date-picker v-model="searchForm.endTime" type="date" placeholder="结束日期" :picker-options="pickerEndOptions"></ifp-date-picker>
              </ifp-form-item>
                <ifp-form-item label="查询名称">
                    <ifp-input v-model="searchForm.testName" placeholder="请输入查询名称"></ifp-input>
                </ifp-form-item>
                <ifp-form-item label="查询编码">
                    <ifp-input v-model="searchForm.testCode" placeholder="请输入查询编码"></ifp-input>
                </ifp-form-item>
                <ifp-form-item label="查询状态">
                    <ifp-select v-model="searchForm.testStatus" placeholder="请选择状态">
                        <el-option v-for="item in testStatusList" :key="item.id" :label="item.text" :value="item.id"></el-option>
                    </ifp-select>
                </ifp-form-item>
                <ifp-form-item>
                    <ifp-button @click="operationBtn('add')">新增</ifp-button>
                    <ifp-button @click="batchDownload">批量下载</ifp-button>
                </ifp-form-item>
            </ifp-searchbar>
            <!-- 注册产品 -->
            <ifp-dialog class="registProduct" :title="formTitle" v-if="showFormDialog" :visible.sync="showFormDialog" :close-on-click-modal="false" :close-on-press-escape="false" width="800px">
                <ifp-form ref="formInfo" style="padding: 1rem" class="padding" :rules="rules" label-position="right" :model="formInfo" label-width="90px" :disabled="disableForm">
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <ifp-form-item label="名称：" prop="name">
                                <ifp-input v-model="formInfo.name"></ifp-input>
                            </ifp-form-item>
                        </el-col>
                        <el-col :span="12">
                            <ifp-form-item label="城市：" prop="city">
                                <ifp-input v-model="formInfo.city"></ifp-input>
                            </ifp-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10"
                        ><el-col :span="12">
                            <ifp-form-item label="邮编：" prop="zip">
                                <ifp-input v-model="formInfo.zip"></ifp-input>
                            </ifp-form-item>
                        </el-col>
                        <el-col :span="12">
                            <ifp-form-item label="邮箱：" prop="email">
                                <ifp-input v-model="formInfo.email"></ifp-input>
                            </ifp-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <ifp-form-item label="出生日期：" prop="birthday">
                                <ifp-date-picker v-model="formInfo.birthday" type="date" placeholder="选择日期"></ifp-date-picker>
                            </ifp-form-item>
                        </el-col>
                        <el-col :span="12">
                            <ifp-form-item label="照片：" prop="photo">
                                <ifp-input v-model="formInfo.photo"></ifp-input>
                            </ifp-form-item>
                        </el-col>
                    </el-row>
                </ifp-form>
            </ifp-dialog>

            <ifp-panel-table title="测试表格" class="margin-bottom flex-item" style="margin: 10px 8px 0 8px; min-height: 300px">
                <el-table ref="testTable" :data="testTableData" style="width: 100%; overflow-y: auto" row-key="Gid" border :key="Math.random()" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" label="" width="50"> </el-table-column>
                    <el-table-column v-for="(i,idx) in testTableHead" :prop="i.value" :label="i.name" :key="idx" align="center">
                        <template slot-scope="scope">
                            <img v-if="i.value==='img'" :src="scope.row[i.value]" class="w-full" />
                            <div v-else>{{scope.row[i.value]}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column width="350" fixed="right" label="操作" align="center">
                        <template slot-scope="row">
                            <ifp-button size="mini" @click="operationBtn('check',row)">查看</ifp-button>
                            <el-button size="mini" @click="operationBtn('edit',row)">编辑</el-button>
                            <el-button size="mini" @click="operationBtn('download',row)">下载</el-button>
                            <el-button type="danger" size="mini" @click="operationBtn('delete',row)">删除</el-button>
                            <!-- <span v-show="record.row.status<2" class="opBtn" v-if="$authority['safetyDeathDetail_audit']" @click="reviewDetail(record)">不可点击</span> -->
                            <!-- <span v-show="record.row.status>2&&record.row.status<=3" v-if="$authority['safetyDeathDetail_edit']" class="opBtn" @click="approve(record)">审批</span> -->
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    @size-change="sizeChange"
                    @current-change="pageChange"
                    :current-page="baseSetting.pageNum"
                    :page-sizes="[10,15,20,50,100]"
                    :page-size="baseSetting.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="baseSetting.total"
                >
                </el-pagination>
            </ifp-panel-table>

            <!-- 注册产品 -->
            <!-- <ifp-dialog
                class="registProduct"
                title="注册产品"
                v-if="showFormDialog"
                :visible.sync="showFormDialog"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                width="600px"
            >
                <regist2-iofp-config @cancel="showFormDialog=false;"> </regist2-iofp-config>
            </ifp-dialog> -->
            <!-- 修改配置 -->
            <!-- <ifp-dialog
                class="changeConfig"
                title="修改配置"
                v-if="showChangeConfigDialog"
                :visible.sync="showChangeConfigDialog"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :fullscreen="true"
            >
                <regist-config @cancel="showChangeConfigDialog=false;"> </regist-config>
            </ifp-dialog> -->
            <!-- 接口测试 -->
            <!-- <ifp-dialog class="testRoute" title="接口测试" v-if="showTestRouteDialog" :visible.sync="showTestRouteDialog" :close-on-click-modal="false" :close-on-press-escape="false" width="700px">
                <test-route @cancel="showTestRouteDialog=false;" :service-code="selectRoute.serviceCode"> </test-route>
            </ifp-dialog> -->
        </ifp-page>
    </body>
    <!-- <script src="/iofp/starter.js"></script> -->
    <script src="/iofp/ics/starter.js"></script>
</html>
