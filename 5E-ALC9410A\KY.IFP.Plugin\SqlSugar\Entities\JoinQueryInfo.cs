﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

namespace SqlSugar
{
 
    public class JoinQueryInfo
    {
        public JoinType JoinType { get; set; }
        public string TableName { get; set; }
        public string ShortName { get; set; }
        public int JoinIndex { get; set; }
        public string JoinWhere { get; set; }
        public Type EntityType { get;  set; }
    }
    public class JoinQueryInfos
    {
        private JoinQueryInfos() { }
        public JoinQueryInfos(JoinType joinType, bool whereExpress)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3, JoinType joinType4, bool whereExpress4)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3, JoinType joinType4, bool whereExpress4, JoinType joinType5, bool whereExpress5)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3, JoinType joinType4, bool whereExpress4, JoinType joinType5, bool whereExpress5, JoinType joinType6, bool whereExpress6)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3, JoinType joinType4, bool whereExpress4, JoinType joinType5, bool whereExpress5, JoinType joinType6, bool whereExpress6, JoinType joinType7, bool whereExpress7)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3, JoinType joinType4, bool whereExpress4, JoinType joinType5, bool whereExpress5, JoinType joinType6, bool whereExpress6, JoinType joinType7, bool whereExpress7, JoinType joinType8, bool whereExpress8)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3, JoinType joinType4, bool whereExpress4, JoinType joinType5, bool whereExpress5, JoinType joinType6, bool whereExpress6, JoinType joinType7, bool whereExpress7, JoinType joinType8, bool whereExpress8, JoinType joinType9, bool whereExpress9)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3, JoinType joinType4, bool whereExpress4, JoinType joinType5, bool whereExpress5, JoinType joinType6, bool whereExpress6, JoinType joinType7, bool whereExpress7, JoinType joinType8, bool whereExpress8, JoinType joinType9, bool whereExpress9, JoinType joinType10, bool whereExpress10)
        {

        }
        public JoinQueryInfos(JoinType joinType, bool whereExpress, JoinType joinType2, bool whereExpress2, JoinType joinType3, bool whereExpress3, JoinType joinType4, bool whereExpress4, JoinType joinType5, bool whereExpress5, JoinType joinType6, bool whereExpress6, JoinType joinType7, bool whereExpress7, JoinType joinType8, bool whereExpress8, JoinType joinType9, bool whereExpress9, JoinType joinType10, bool whereExpress10, JoinType joinType11, bool whereExpress11)
        {

        }
    }
}
