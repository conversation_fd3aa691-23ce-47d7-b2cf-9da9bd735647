﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
namespace SqlSugar
{
    internal class ExpressionConst
    {
        public const string Const = "Const";
        public const string FormatSymbol = "{0}";
        public const string RightParenthesis = ")";
        public const string LeftParenthesis = "(";
        public const string MethodConst = "MethodConst";
        public const string SqlFuncFullName = "SqlSugar.SqlFunc";
        public const string BinaryFormatString = " ( {0} {1} {2} ) ";
        public const string ExpressionReplace = "46450BDC-77B7-4025-B2A6-3F048CA85AD0";
    }
}
