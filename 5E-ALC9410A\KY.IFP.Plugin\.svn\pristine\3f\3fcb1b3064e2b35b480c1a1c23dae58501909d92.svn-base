define([
    "iofp/services/service.alarm"
    , "vue"
    , "jquery"
    , "util"
    , "iofp/common"
    , "iofp/services/service.user"
    , "iofp/services/service.menu"
    , "json!kyadmin/version.json"
    , "iofp/components/iofpsocket"
    , "crypto-js/md5"
    , "iofp/services/service.KYRegistration"
    , "jquery.ky.dialog"
    /* 样式 */
    , "css!lib/bower/bootstrap/css/bootstrap.min"
    , "css!lib/kjlib/bootstrap-theme-kykj/iconfont/dicb2/iconfont"
    , "css!lib/kjlib/bootstrap-theme-kykj/iconfont/kjicon/iconfont"
], function (alarmService, Vue, $, util, common, userService, menuService, versionInfo, iofpsocket, MD5, kyRegistrationService) {

    // 所有数据必须转换为页面定义格式，再次定义
    var query = {
        loginUser: function () {
            return common.getLoginUser()
                .then(function (user) {
                    if (!user) { throw "未登录" }
                    return {
                        name: user.UsiName,
                        depName: user.DeptFullName || user.DeptName,
                        userId: user.UsiLoginName,
                        roleIds: user.RoleIds
                    }
                })
        },
        menus: function () {
            return menuService.getMenusByCurrentUser()
                .then(function (data) {
                    return (data || []).map(function (item) {
                        return {
                            Gid: item.Gid,
                            Pid: item.Pid,
                            MenuName: item.MenuName,
                            MenuImg: item.MenuImg,
                            MenuUrl: item.MenuUrl,
                            data: item
                        }
                    })
                });
        },
        iofpsettings: function () {
            return menuService.getProductSetting()
                .then(function (data) {
                    return data;
                });
        },
        getIndexAlarmList: function () {
            return alarmService.getIndexAlarmList({})
                .then(function (data) {
                    return data;
                });
        },
        //可用的和不可用的菜单
        loadAvailableMenu: function (roleids) {
            let param = {
                roleIDs: roleids
            };
            return menuService.loadAvailableMenu(param);
        },
        getRegistrationInfo: function () {
            return kyRegistrationService.getRegistrationInfo();
        },
        setRegistration: function (data) {
            return kyRegistrationService.setRegistration(data);
        }
    }
    // 弹出页
    var open = {
        // 修改密码
        modifyPassword: function () {
            return util.require("iofp/components/modifyPassWord")
                .then(function (modifyPassWord) {
                    return modifyPassWord()
                }).then(function (data) {
                    //if(data.error){
                    //    return {
                    //        error:data.error,
                    //        message:data.message
                    //    }
                    //}else{
                    //    return {
                    //        error:null,
                    //        message:data.message
                    //    }
                    //}
                });
        }
    }

    // 简单树转标准树
    var getTreeData = function (data, option) {
        var idField = option.idfield;
        var pidField = option.pidfield;
        var searchnode = function (id) {
            return data.filter(function (item) {
                return item[idField] == id;
            });
        }

        var errnodes = $.grep(data, function (item) {
            return item[pidField] && searchnode(item[pidField]).length == 0;
        });
        if (errnodes.length) {
            log.error("存在无效菜单：pid 无效\n" + JSON.stringify(errnodes, null, 2));
        }
        //console&&console.log.error(errnodes);

        // 无pid 或 pid 无效，认为是根节点
        var root = $.grep(data, function (item) {
            return !item[pidField];
        });

        var subnodes = function (node, nodedata, module) {
            var salf = arguments.callee;
            var children = $.grep(nodedata, function (item) {
                return item[pidField] == node[idField]
            });
            children = $.map(children, function (item) {
                item.children = salf(item, nodedata, module || item)
                item.module = module;
                return item;
            });
            return children;
        }

        return $.map(root, function (item) {
            item.children = subnodes(item, data)
            return item;
        })
    }

    var getLevel2Tree = function (datas) {
        var roots = datas && datas[0] && datas[0].children || [];
        var getsubs = function (parent, list) {
            list.forEach(function (sub) {
                parent.allchildren.push(sub);
                if (sub.children) {
                    getsubs(parent, sub.children)
                }
            })
        }
        roots.forEach(function (item) {
            item.allchildren = item.allchildren || [];
            getsubs(item, item.children || []);
        })
        return roots;
    }

    var defaultData = {
        title: "全自动智能存查样系统",
        deviceName: "5E-ASS1650",
        authKey: "",
        fullscreen: false,
        showmessagebox: false,
        showuserinfo: false,
        themename: "default",
        themes: {
            dark: {
                name: "kyadmin-theme-dark",
                navbar: "navbar-inverse"
            },
            default: {
                name: "kyadmin-theme-default",
                navbar: "navbar-parimary"
            }
        },
        headdata: {
            collapse: true // 显示
        },
        user: {
            name: "***",
            depName: "xxx"
        },
        mainpage: "",
        menulist: [],
        version: {
            number: "0.0.1"
        },
        plcState: "未连接", // PLC 连接状态
        ctrlstate: 0, //WEBSOCKET控制权限
        isConnect: 0,
        menuoption: {
            idfield: "Gid",
            pidfield: "Pid",
            title: "MenuName",
            //图标
            icon: "MenuImg",
            url: "MenuUrl",
            // 默认图标 （未指定 icon 且指定了 url 才会起作用）
            defaultIcon: "glyphicon glyphicon-file",

            // 父节点图标（未指定 icon 和 url 才会起作用）
            defaultIconFolder: "glyphicon glyphicon-folder-open",
        }
    }

    function doFullScreen(fullscreen) {
        if (fullscreen) {
            var exitMethod = document.exitFullscreen || //W3C
                document.mozCancelFullScreen || //Chrome等
                document.webkitExitFullscreen || //FireFox
                document.webkitExitFullscreen; //IE11
            if (exitMethod) {
                exitMethod.call(document);
            }
            else if (typeof window.ActiveXObject !== "undefined") {//for Internet Explorer
                var wscript = new ActiveXObject("WScript.Shell");
                if (wscript !== null) {
                    wscript.SendKeys("{F11}");
                }
            }
        } else {
            var element = document.documentElement;
            // 判断各种浏览器，找到正确的方法
            var requestMethod = element.requestFullScreen || //W3C
                element.webkitRequestFullScreen || //Chrome等
                element.mozRequestFullScreen || //FireFox
                element.msRequestFullScreen; //IE11
            if (requestMethod) {
                requestMethod.call(element);
            } else if (typeof window.ActiveXObject !== "undefined") {//for Internet Explorer
                var wscript = new ActiveXObject("WScript.Shell");
                if (wscript !== null) {
                    wscript.SendKeys("{F11}");
                }
            }
        }
    }

    function closeAllDropdown() {
        $("ul.navbar-nav li").removeClass("open");
    }

    function init(user) {
        new Vue({
            el: "#app",
            data: function () {
                return Object.assign({
                    currentPageUrl: null,
                    currentModuleId: null,
                    isAlarm: false,
                    alarmList: [],
                    //信号灯样式，"0"灰灯"1"绿灯"2"橘灯"3"红灯"4"黑
                    states: {
                        "0": "",
                        "1": "runing",
                        "2": "warning",
                        "3": "stoped",
                        //采集系统罢工
                        "4": "collectSystemError"
                    },
                    showstate: {
                        //Auth: 1,
                        PLC: "0",
                        Service: "0"
                    },
                    dateTime: "",

                    dialogRegister: {
                        title: "注册存查样柜",
                        templateForever: "已授权，功能正常使用",
                        templateBad: "超过试用期限，存样功能无法使用<br/>请联系长沙开元仪器有限公司获取授权码",
                        templateLess30: "距离试用期限不足30天！<br/>请提前联系长沙开元仪器有限公司获取授权码",
                        templateGreater30: "正处于试用期，距离试用截止日期30天以上",
                        templateError: "授权功能异常",
                        content: "",
                        show: false,//element dialog改成jquery ifram，没用了
                        source: {
                            machineID: "123",
                            registerCode: "abc"
                        }
                    },
                    //dialogDelay: {
                    //    title: "试用管理",

                    //    show: true,
                    //    source: {
                    //        deadDate: "",
                    //        newDeadDate: ""
                    //    }
                    //}
                }, defaultData)
            },
            computed: {
                treemenu: function () {
                    return getLevel2Tree(getTreeData(this.menulist, this.menuoption));
                }
            },
            created: function () {
                var _this = this;

                this.user = user;

                query.menus().then(function (data) {
                    _this.menulist = data;
                })
                this.updateMainInfo();
                this.version.number = versionInfo.version;

                this.$nextTick(function () {
                    document.addEventListener("click", function () {
                        closeAllDropdown();
                    })
                })

                iofpsocket.onCmdMsg("Open", data => {
                    var result = JSON.parse(data.C);
                    if (this.showstate.PLC !== result.PLC) {
                        this.showstate.PLC = result.PLC;
                    }
                    if (this.showstate.Service != result.Service) {
                        this.showstate.Service = result.Service;
                    }
                    if (this.ctrlstate != result.Auth) {
                        this.ctrlstate = result.Auth;
                        //common.setCtrlState(result.Auth);
                    }
                    //this.log(JSON.stringify(data), "接收");
                })

                iofpsocket.on("error", () => {
                    this.showstate.PLC = "3";
                    this.showstate.Service = "3";
                    this.ctrlstate = "3";
                    //var result = JSON.parse(data);
                });
                iofpsocket.onCmdMsg("RAuth", data => {
                    var result = JSON.parse(data.C);
                    if (result.Result == "1") {
                        this.ctrlstate = "1";
                    }
                    else {
                        this.ctrlstate = "3"
                    }
                    if (result.Result == 0) {
                        $.confirm(result.Msg + "\r\n是否继续强制申请权限？", function (ok) {
                            if (ok) {
                                _this.sendCtrlCommand("1");
                            } else {
                            }
                        });
                    }
                    console.log(JSON.stringify(data), "接收");
                })
                this.getAlarm();

                //daiabin要求报警刷新在1s内
                setInterval(() => {
                    this.getAlarm();
                }, 1000);
                setInterval(() => {
                    let d = new Date();
                    let year = d.getFullYear();
                    let month = d.getMonth() + 1 < 10 ? "0" + (d.getMonth() + 1) : d.getMonth() + 1;
                    let date = d.getDate() < 10 ? "0" + d.getDate() : d.getDate();
                    let hh = d.getHours() < 10 ? "0" + d.getHours() : d.getHours();
                    let mm = d.getMinutes() < 10 ? "0" + d.getMinutes() : d.getMinutes();
                    let ss = d.getSeconds() < 10 ? "0" + d.getSeconds() : d.getSeconds();
                    this.dateTime = year + "年" + month + "月" + date + "日" + " " + hh + ":" + mm + ':' + ss;
                }, 1000);

                //setInterval(() => {
                //this.alertRegistration();
                //}, 300000);
                // 显示登录信息，用户确认后 再弹出 服务状态
                //common.getLoginUser()
                //    .then(user => new Promise((resolve, reject) => $.alert({
                //        title: "登录提醒",
                //        message: user.RoleIds,
                //        callback: resolve
                //    })))
                //    .then(() => this.openFwzt())
                //daiabin王震宇不要弹欢迎，放到别地方
                //this.openWelcome().then(() => { _this.openFwzt(); })
                _this.renderLabel();

                _this.openFwzt();

            },
            methods: {
                open: function (command) {
                    open[command]();
                },
                // 二级菜单容器点击事件
                submenubox_click() {
                    console.log(this.$refs.submenubox);
                    closeAllDropdown()
                },
                openFwzt: function () {
                    this.confirm({
                        url: "/pages/main/fwzt.html",
                        full: false,
                        width: 400,
                        height: 200,
                        title: "控制管理"
                    }).then(function (data) {
                    });
                },
                openWelcome: function () {
                    return this.confirm({
                        url: "/pages/main/LoginWelcome.html",
                        full: false,
                        width: 400,
                        height: 250,
                        title: "欢迎"
                    });
                },
                confirm: function (option) {
                    return new Promise(function (resolve, reject) {
                        try {
                            var win = $.showIframe($.extend({ title: "", url: "", parameter: {} }, option, {
                                onhide: function () {
                                    var rev = win.returnValue();
                                    return option && option.verify && option.verify(rev) || true;
                                },
                                onhidden: function () {
                                    resolve(win.returnValue());
                                }
                            }));
                        } catch (ex) {
                            reject(ex);
                        }
                    })
                },
                // 更新配置
                updateMainInfo: function () {
                    var _this = this;
                    query.iofpsettings().then(data => {
                        this.title = data.MainName;

                        this.mainpage = data.HomePage;
                        this.deviceName = data.Name;
                        this.currentPageUrl = this.mainpage;
                        this.authKey = data.AuthKey;

                        common.setAuthKey(data.AuthKey);
                        iofpsocket.on("open", function () {
                            _this.sendCtrlCommand("0");
                        });
                        //this.openFirstPage(data.HomePage);

                    })
                },

                getAlarm: function () {
                    var _this = this;
                    //暂时不请求这个了
                    //query.getIndexAlarmList().then(data => {
                    //    this.alarmList = data;
                    //    if (data && data.length > 0) {
                    //        this.isAlarm = true;
                    //    } else {
                    //        this.isAlarm = false;
                    //    }
                    //})
                },

                sendCtrlCommand: function (mandatory) {
                    var userObj = common.getLoginUserSync();
                    var pwd = MD5(this.authKey + userObj.UsiLoginName).toString();
                    iofpsocket.sendCommand("SAuth", { User: userObj.UsiLoginName, Pwd: pwd, Mandatory: mandatory });
                },
                doFullScreen: function () {
                    doFullScreen(this.fullscreen)
                    this.fullscreen = !this.fullscreen;
                },
                showmenu: function (event, menuitem) {
                    if (menuitem[this.menuoption.url]) {
                        this.showpage(menuitem[this.menuoption.url], menuitem);
                    };
                    if (menuitem.allchildren && menuitem.allchildren.length) {
                        this.showdrop(event);
                    }
                },
                getmenuitemicon: function (item) {
                    if (item[this.menuoption.icon] && item[this.menuoption.icon].indexOf("-") > -1) {
                        return [item[this.menuoption.icon].split("-")[0], item[this.menuoption.icon]]
                    }
                    return this.menuoption.defaultIcon;
                },
                showdrop: function (event) {
                    var ele = $(event.currentTarget.parentElement);
                    if (ele.hasClass("open")) {
                        closeAllDropdown()
                    } else {
                        closeAllDropdown()
                        ele.addClass("open");
                        //this.getAlarm();
                    }
                },
                showpage: function (url, data) {
                    closeAllDropdown()

                    this.currentModuleId = data && data.module && data.module[this.menuoption.idfield] || url;

                    if (this.currentPageUrl == url) { return; }

                    this.currentPageUrl = url;
                    //$("#ifra").attr("src",url||this.mainpage);
                },
                isactive: function (item) {
                    return (this.currentPageUrl === item[this.menuoption.url]) || (this.currentModuleId == item[this.menuoption.idfield])
                },
                alarmClear: function () {
                    var content = common.createCommandArgs({ CMD: "ALARM_RESET", Parameter: {} });
                    iofpsocket.sendCommand("SControl", content, function () {
                        alert(JSON.stringify(arguments))
                    });
                },
                loginOut: function () {
                    $.confirm("是否注销当前登录帐号", function (ok) {
                        if (ok) {
                            userService.loginOut({}).then((data) => {
                                if (data.code == '0') {
                                    window.location.href = data.url + "?f=" + window.decodeURIComponent(window.location.href);
                                }
                            }).catch((err) => {
                                console.log("注销时服务器错误")
                                console.log(err);
                            })
                        } else {
                        }
                    });
                },
                renderLabel: function () {
                    query.loadAvailableMenu(user.roleIds).then(function (res) {
                        let box = $('#tagList').empty();
                        $(res.data.availableMenu).each(function (i, item) {
                            $('<span class="label label-primary">' + item.MenuName + '</span>')
                                .appendTo(box)
                        })
                        let box2 = $('#tagList2').empty();
                        $(res.data.unavailableMenu).each(function (i, item) {
                            $('<span class="label label-primary">' + item.MenuName + '</span>')
                                .appendTo(box2)
                        })
                    })
                },

                //提醒注册todo国产化
                alertRegistration: function () {
                    let _this = this;
                    query.getRegistrationInfo().then(function (data) {
                        //-1—验证未通过，不能开始试验；
                        // 0—注册码验证通过；
                        // 1—没有注册码，正处于试用期，且距离试用截止日期30天以上；
                        // 2—没有注册码，正处于试用期，但距离试用截止日期不到30天，需要主动提醒用户。
                        let ok = data.RegisterResult;
                        if (ok == 0 || ok == 1) {
                            //_this.dialogRegister.show = false;
                            //改用jquery ifram弹窗
                            return;
                        }
                        if (ok == -1) {
                            _this.dialogRegister.content = _this.dialogRegister.templateBad;
                        }
                        if (ok == 2) {
                            _this.dialogRegister.content = _this.dialogRegister.templateLess30;
                        }
                        _this.dialogRegister.source.machineID = data.MyMachineID;
                        _this.dialogRegister.source.registerCode = data.KYRegisterCode;
                        //_this.dialogRegister.show = true;
                        _this.openRegisterFramwork();
                        return;
                    })
                },
                //todo国产化
                register: function () {
                    let _this = this;
                    let param = {
                        RegistrationCode: _this.dialogRegister.source.registerCode
                    }
                    query.setRegistration(param).then(function (res) {
                        if (res.success == true) {
                            //-1—验证未通过，不能开始试验；
                            // 0—注册码验证通过；
                            // 1—没有注册码，正处于试用期，且距离试用截止日期30天以上；
                            // 2—没有注册码，正处于试用期，但距离试用截止日期不到30天，需要主动提醒用户。
                            let ok = res.data.RegisterResult;
                            if (0 == ok) {
                                _this.dialogRegister.show = false;
                                $.bootoast.success("授权成功");
                                return;
                            }
                            if (ok == 1 || ok == 2) {
                                _this.dialogRegister.show = false;
                                $.bootoast.success("产品试用");
                                return;
                            }
                            $.bootoast.danger("授权失败");
                            return;
                        }
                        else {
                            $.bootoast.danger("授权失败");
                        }
                    })
                },
                //todo国产化
                openRegister: function () {
                    let _this = this;
                    query.getRegistrationInfo().then(function (data) {
                        //-999授权功能异常
                        //-1—验证未通过，不能开始试验；
                        // 0—注册码验证通过；
                        // 1—没有注册码，正处于试用期，且距离试用截止日期30天以上；
                        // 2—没有注册码，正处于试用期，但距离试用截止日期不到30天，需要主动提醒用户。
                        let ok = -1;
                        _this.dialogRegister.content = "";
                        _this.dialogRegister.source.machineID = "";
                        _this.dialogRegister.source.registerCode = "";
                        if (data != undefined) {
                            ok = data.RegisterResult;
                            _this.dialogRegister.source.machineID = data.MyMachineID;
                            _this.dialogRegister.source.registerCode = data.KYRegisterCode;
                        }

                        if (0 == ok) {
                            _this.dialogRegister.content = _this.dialogRegister.templateForever;
                        }
                        if (-1 == ok) {
                            _this.dialogRegister.content = _this.dialogRegister.templateBad;
                        }
                        if (1 == ok) {
                            _this.dialogRegister.content = _this.dialogRegister.templateGreater30;
                        }
                        if (2 == ok) {
                            _this.dialogRegister.content = _this.dialogRegister.templateLess30;
                        }
                        if (-999 == ok) {
                            _this.dialogRegister.content = _this.dialogRegister.templateError;
                        }
                        //_this.dialogRegister.show = true;
                        //改用jquery ifram弹窗
                        _this.openRegisterFramwork();
                    })
                },
                //todo国产化
                openRegisterFramwork: function () {
                    $.showIframe({
                        title: "注册存查样柜",
                        url: "/pages/main/register.html",
                        full: false,
                        width: 400,
                        height: 250,
                        dialogRegister: this.dialogRegister,
                        //onhidden: function () {
                        //    alert(this.returnValue())
                        //}
                    });
                }
            }
        })
    }
    return function () {
        return query.loginUser()
            .then(function (data) {
                init(data);
            }).catch(function () {
                //daiabin登录页是可配置的。
                //window.location.href = "/index/login.html?f=" + window.decodeURIComponent(window.location.href);
            })
    }
})
