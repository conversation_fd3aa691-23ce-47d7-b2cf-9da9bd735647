﻿using COM.IFP.SqlSugarN;
using SqlSugar;
using System;
namespace ORM.IFP.DbModel
{
    /// <summary>
    /// 报警记录表原名IFP_SM_HISTORYALARMINFO
    /// </summary>
    [SugarTable("IFP_SM_ALARM_RECORD")]
    public partial class IFP_SM_ALARM_RECORD
    {
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "nvarchar(36)")]
        public Field<string> Gid { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        [SugarColumn(ColumnName = "MACHINE_CODE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> MachineCode { get; set; }
        /// <summary>
        /// 2级设备编码
        /// </summary>
        [SugarColumn(ColumnName = "MACHINE_CODE2", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> MachineCode2 { get; set; }

        /// <summary>
        /// 报警编号
        /// </summary>
        [SugarColumn(ColumnName = "ALARM_CODE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(36)")]
        public Field<string> AlarmCode { get; set; }

        /// <summary>
        /// 报警名称
        /// </summary>
        [SugarColumn(ColumnName = "ALARM_NAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> AlarmName { get; set; }
        /// <summary>
        /// 报警概述
        /// </summary>
        [SugarColumn(ColumnName = "ALARM_SUMMARY", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> AlarmSummary { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "CREATOR", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(128)")]
        public Field<string> Creator { get; set; }
        /// <summary>
        /// 报警时间
        /// </summary>
        [SugarColumn(ColumnName = "CREATE_TIME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> CreateTime { get; set; }

        /// <summary>
        /// 报警级别 1警告报警 2次要报警 3主要报警 4严重报警
        /// </summary>
        [SugarColumn(ColumnName = "ALARM_LEVEL", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "INT")]
        public Field<int> AlarmLevel { get; set; }

        /// <summary>
        /// 状态 1报警中 2已解除
        /// </summary>
        [SugarColumn(ColumnName = "ALARM_STATUS", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "INT")]
        public Field<int> AlarmStatus { get; set; }

        /// <summary>
        /// 解除时间
        /// </summary>
        [SugarColumn(ColumnName = "REMOVE_TIME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> RemoveTime { get; set; }

        /// <summary>
        /// 解除人员
        /// </summary>
        [SugarColumn(ColumnName = "REMOVE_USER", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> RemoveUser { get; set; }

        ///// <summary>
        ///// 
        ///// </summary>
        //[SugarColumn(ColumnName = "REMARK", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(3000)")]
        //public Field<string> Remark { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "DELT", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> Delt { get; set; }
    }


    /* 以下为对应的Json对象
	{
		GID: null,
		MACHINECODE: null,
		ALARMCODE: null,		//报警编号
		ALARMNAME: null,		//报警名称
		ALARMDATE: null,		//报警时间
		USERID: null,		//解除人员
		ALARMLEVEL: null,		//报警级别 1警告报警 2次要报警 3主要报警 4严重报警
		ALARMSTATUS: null,		//状态 1报警中 2已解除
		CREATOR: null,
		CREATETIME: null,
		REMARK: null,
		DELT: null,
		REMOVEDATE: null		//解除时间
	}
	*/
    //public partial class IFP_SM_HISTORYALARMINFO
    //{
    //    //------------------------查询条件-------------------------
    //    public DateTime? AlarmDate_DSTART { get; set; }

    //    public DateTime? AlarmDate_DEND { get; set; }

    //    //展示类型 0分组汇总展示  1按报警类型展示
    //    public int? showType { get; set; }
    //}
}
