﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DAL.ICS.CodeSetting
{
    public interface ICodeRule
    {
        /// <summary>
        /// 编码前的业务方法判断，例如：批次规则查询业务表，是否在一个批次里面。如果是，就返回该批次编码。
        /// </summary>
        /// <param name="obj">用于业务判断的实体类</param>
        /// <returns>
        /// success : false 表示找到了编码 ，不需要再重新生成,true-需要生成
        /// code : success == false && code is not null 找到了编码，不需要生成，否则抛出异常
        /// </returns>
        public CodeRuleResult QueryYwDataCode(Object obj);
    }

    public class CodeRuleResult
    {
        //false 表示找到了编码 ，不需要再重新生成,true-需要生成
        public bool success { get; set; }

        public string msg { get; set; }
        //success == false && code is not null 找到了编码，不需要生成，否则抛出异常
        public string code { get; set; }

        public DateTime codeDate { get; set; }
        
    }
}
