﻿<!DOCTYPE html>

<html lang="zh-CN">
<head>
    <title>元数据模型测试</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar :close="true">
            <el-button icon="el-icon-plus" @click="onCreate">新增</el-button>
            <el-button icon="el-icon-collection" v-bind:disabled="disable.saveDisabled" @click="onSave">保存</el-button>
            <el-button icon="el-icon-edit" v-bind:disabled="disable.updateDisabled" @click="onUpdate">修改</el-button>
        </ifp-toolbar>
        <div class="flex flex-row flex-item padding">
            <div class="flex" style="width:250px;">
                <div class="flex flex-row">
                    <div style="width:65%">
                        <ifp-input v-model="sreachMaterialName">
                            <el-button slot="append" @click="OnGetLocation" icon="el-icon-search"></el-button>
                        </ifp-input>
                    </div>
                    <div style="margin-left:1%;width:34%">
                        <el-select v-model="SearchState" placeholder="请选择" style="width:100%">
                            <el-option label="启用" value="0"></el-option>
                            <el-option label="停用" value="1"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="flex-item" style="overflow: auto; background: white; margin-top: 2px">
                    <el-tree :data="treeData"
                             :props="treeProps"
                             :default-expand-all="true"
                             :expand-on-click-node="false"
                             :filter-node-method="filterNode"
                             @node-click="OnMaterialClick"
                             ref="tree">
                    </el-tree>
                </div>
            </div>
            <div class="flex-item" style="margin-left: 1rem; background: white; overflow: auto;">
                <div class="form-header">
                    <span>煤种基本信息</span>
                </div>
                <div style="width: 1000px">
                    <el-form ref="currentMaterial" labelPosition="right" :model="currentMaterial" label-width="80px" style="padding: 1rem;" :disabled="disable.saveDisabled">
                        <el-row :gutter="0">
                            <el-col :span="8">
                                <el-form-item label="煤种名称" prop="Bname" :rules="{required:true, message:'煤种名称不能为空'}">
                                    <ifp-input v-model="currentMaterial.Bname"></ifp-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="煤种简称">
                                    <ifp-input v-model="currentMaterial.Sname"></ifp-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="0">
                            <el-col :span="8">
                                <el-form-item label="颜色分类">
                                    <template>
                                        <div class="flex flex-row">
                                            <ifp-input v-model="currentMaterial.Color"></ifp-input>
                                            <el-color-picker v-model="currentMaterial.Color"></el-color-picker>
                                        </div>
                                    </template>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="状态">
                                    <el-radio v-model="currentMaterial.Zfbz" :label="0">启用</el-radio>
                                    <el-radio v-model="currentMaterial.Zfbz" :label="1">停用</el-radio>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="0">
                            <el-col :span="16">
                                <el-form-item label="备注">
                                    <ifp-input type="textarea"
                                              :rows="2"
                                              placeholder="请输入内容"
                                              v-model="currentMaterial.Beizhu">
                                    </ifp-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div class="form-header padding">
                    <span>煤种指标区间</span>
                    <el-button @click="OnAddRow" :disabled="disable.saveDisabled">新增行</el-button>
                    <el-button @click="onDeleteRow" :disabled="disable.saveDisabled">删除行</el-button>
                </div>
                <div>
                    <el-table border>
                        <ifp-table-column prop="Ljgx1003" edit-type="ifpSelectYwlx" :edit-option="{ywlx:1003}" label="关系"></ifp-table-column>
                        <ifp-table-column prop="Value1" edit-type="input" label="左区间"></ifp-table-column>
                        <ifp-table-column prop="ywlx1007" edit-type="ifpSelectYwlx" :edit-option="{ywlx:1007}" label="运算符"></ifp-table-column>
                        <ifp-table-column prop="Hyzj1004" edit-type="ifpSelectYwlx" :edit-option="{ywlx:1004}" label="指标"></ifp-table-column>
                        <ifp-table-column prop="Qjfh2" edit-type="ifpSelectYwlx" :edit-option="{ywlx:1007}" label="运算符2"></ifp-table-column>
                        <ifp-table-column prop="Value2" edit-type="input" label="左区间"></ifp-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>