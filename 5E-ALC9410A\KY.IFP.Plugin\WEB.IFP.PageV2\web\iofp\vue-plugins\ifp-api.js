define(["configs/config.service","axios","ELEMENT"],function(serviceurl,Axios,element){
    const axios = Axios.create({})
    
    
    let segment = document.location.pathname.split('/').filter(x => x != '');
    if (segment[0] == "identity" && segment[1].length == 32) {
        axios.defaults.headers.post['identity'] = segment[1];
    }
    

    const ywlxurl = serviceurl._http_getYwlxCombox_;
    const isDelete = Symbol("delete");
    // 根据参数生成
    function genKey(...args){
        return btoa(encodeURI(args.map(item=>{
            if(item===null){return ''}
            switch(typeof item){
                case "undefined": return ''
                case "string": return item; 
                case "boolean": 
                case "number": 
                case "symbol":
                case "array":
                case "function": 
                    return item.toString()
                case "object":
                    return JSON.stringify(item)
                default:
                    toString.call(a)
            }
        }).join("")+args.length))
    }

    let CACHEDATA = window.CACHEDATA =  {};
    let progress = {}; // 标识请求中的key
    // 挂起的请求回调
    let _events = {}

    let CACHE = {
        has:function(key){
            return Object.hasOwnProperty.call(CACHEDATA,key) && (CACHEDATA[key]!==isDelete)
        },
        get:function(key){
            if(CACHE.has(key)){
                return CACHEDATA[key];
            }
        },
        set:function(key,data){
            CACHEDATA[key] = data;
        },
        remove(key){
            CACHEDATA[key] = isDelete;
        },
        on:function(key,cb){
            // 注册回调
            _events[key]=_events[key]||[];
            _events[key].push(cb)
        }
    };

    function post(url,args,opt,cache){
        cache = opt===true||cache===true;
        let key = genKey(url,args);
        if(cache && CACHE.has(key)){
            // 使用缓存，并已存在缓存
            return Promise.resolve(CACHE.get(key))
        }else if(cache && progress[key]===true){
            // 使用缓存，并已在请求中
            return new Promise((resolve,reject)=>{
                // 注册回调，待请求完成后返回
                CACHE.on(key,resolve)
            })
        } else {
            // 标识请求中
            progress[key] = true;
            

            return axios.post(...arguments)
            .then(d=>{
                if(cache){
                    progress[key] = false;
                    CACHE.set(key,d.data);

                    if(_events[key]){
                        // 触发所有回调，并清空
                        _events[key].forEach((fn,i)=>{
                            fn(d.data);
                            _events[key][i]=null;
                        })
                        _events[key]=null;
                    }

                    // 1s 后清除当前缓存，1s 内使用当前缓存
                    setTimeout(function(){
                        CACHE.remove(key);
                    },1000)
                }
                return d.data
            }).catch(ex=>{
                element.Message({message:`请求失败：${url},${ex||ex.message||""}`,type: 'error'})
                throw ex;
            })
        }
    }

    function get(...args){
        return axios.get(...args).then(d=>d.data)
    }
    function getJSON(...args){
        return get(...args).then(d=>(typeof d === "string")?JSON.parse(d):d)
    }
    function ywlx(ywlx,args,cache){
        return post(ywlxurl,{
            ywlx,
            ...args
        },cache)
    }
    
    return {
        post,
        get,
        ywlx,
        axios,
        getJSON,
        cache:{
            // 清除缓存
            remove(...args){
                CACHE.delete(genKey(...args));
            }
        },
    };
})