/**
 * 渲染工具.
 * @module core/renderer
 * @see module:core/js/com.kjsoft.renderer.js
 * @exports core/renderer
 */
define(["zutil","require","log","util","bootoast"],function(zutil,require,Log,util,bootoast){

	
	//开始错误提醒
	//Log.initLog();
	var log = Log.getLog("renderer");

	var deps = [
		"controls/textbox.encrypt",
		"controls/modal",
		"controls/modal.info",
		"controls/dialog.IFrameWindow",
		"controls/modal.window",
	]
	
	var showerror = function(msg){
		bootoast({
			message: msg,
			type:"danger",
			position:'center-center',
			timeout:false
		});
	}
	
	var renderer = /** @alias module:core/renderer */ {
		rquirePageControlPlus:function(){
			return new Promise(function(resolve, reject){
			
				var codes = $("[controller]").map(function(){
					return zutil.getControllerName(this)
				}).toArray();
				
				if(codes.length==0){
					codes.push("controller");
				}			
	
				codes = codes.concat($("[controller][typeid]").map(function(){
					return F.getMetaModalUrl($(this).attr("typeid"));
				}).toArray());
				//表格中配置的控件
	
				$("table td[option]").each(function(i,item){
					var str = $(this).attr("option");
					try{
						var option = window.eval("["+ str +"]")[0];
					}
					catch(ex){
						log.error("解析表格列控件配置失败："+str);
						return;
					}
					option.control&&codes.push(option.control);
					//表格中配置的接口
					if(option.controloption&&option.controloption.service){
						codes.push(option.controloption.service);
					}
				});
				
				codes = codes.concat($.map(["control","form"],function(modelType){
					return $("["+ modelType +"]").map(function(i,item){
						return $(item).attr(modelType);
					}).toArray();
				}));
				
				codes = codes.concat(deps);
				require(["require"].concat(codes),function(){
					log.info("成功加载模块["+codes.length+"]个");
					resolve(arguments);
					//callback.apply(this,arguments);
				})
			})["catch"](function(err){
				console&&console.error(err);
			});
		},
		/** 
		 * 渲染所有control。 
		 */
		controls:function(controller,form,container){
			//:not([form])
			var settings = $("[control]:not([isrendered])",$(container)).map(function(i,item){
				var $item = $(item);
				return {
					code:$item.attr("control"),
					container:item,
					id:$item.attr("id")
					//,option:util.str2json($item.attr("option"))
				}
			}).toArray();
			
			var ps = [];
			$.map(settings,function(item,i){
				var Control = require(
						item.id&&controller.hasEncryptControl(item.id)
						?"controls/textbox.encrypt":item.code
				);
				//合并元数据模型
				var id = item.container.id;
				if(id&&controller&&controller.MetaModel){
					var opt = controller.getMetaModel(id);
					if(opt){
						item.option = $.extend(true,opt,item.option)
					}
				}
				ps.push(
					(new Promise(function(resolve,reject){
						log.info("开始加载控件"+item.code);
						
						var rev = new Control(item.container,item.option,controller,form);
						rev._INITRETRUN
						.then(function(){
							log.info("加载控件"+item.code+"完成");
							controller.controls[rev.id] = rev;
							resolve();
						})
						["catch"](function(ex){
							console.error("初始化控件 ["+ item.code +"] ["+id+"] 失败:",ex);
							//console.error(ex);
							showerror("初始化控件 ["+ item.code +"] ["+id+"] 失败:"+ex)
							resolve();
						})
						setTimeout(function(){
							reject(new Error(item.code+"加载超时"));
						},10000);
					}))
				);
			});
			return Promise.all(ps).then(function(){
				log.info("加载control"+settings.length+"个");
			})["catch"](function(error){
				log.error(error.message+"\n"+error.stack);
			})
		},

		/** 渲染所有forms。 */
		forms:function(controller,container){
			var settings = $("[form]:not([isrendered])",$(container)).map(function(i,item){
				var $item = $(item);
				return {
					code:$item.attr("form"),
					container:item,
					option:util.str2json($item.attr("option"))
				}
			}).toArray();
			
			var ps = [];
			$.map(settings,function(item,i){
				var Form = require(item.code);
				ps.push(
					(new Promise(function(resolve,reject){
						var rev = new Form(item.container,item.option,controller);
						rev._INITRETRUN
						.then(function(){
							controller.forms[rev.id] = rev;
							resolve();
						})
						["catch"](reject);
						setTimeout(function(){
							reject(new Error(item.code+"加载超时"));
						},10000);
					}))
				);
			});
			
			return Promise.all(ps).then(function(){
				log.info("加载form"+settings.length+"个");
			})["catch"](function(error){
				log.error(error.message+"\n"+error.stack);
			})
		},

		/** 渲染页面中所有controller */
		controllers:function(callback){
			window.controllers = window.controllers || {};
			
			var settings = $("[controller]:not([isRendered])").map(function(i,item){
				var $item = $(item);
				return {
					code:zutil.getControllerName($item[0]),
					container:item,
					typeid:$item.attr("typeid")
					//,option:util.str2json($item.attr("option"))
				}
			}).toArray();
			
			if(settings.length==0){
				settings.push({
					code:"controller",
					container:document.body,
					option:{}
				});
			}

			var ps = [];
			$.map(settings,function(item,i){
				var Controller = require(item.code);
				item.option = item.option || {};
				item.option.url = util.parseURL(window.location.href);
				item.option.type = window.parent == window?"window":"iframe";
				item.option.onAfterLoad = callback;
				ps.push(
					(new Promise(function(resolve,reject){
						log.info("开始加载控制器"+item.code);
						var rev = new Controller(item.container,item.option,item.typeid);
						rev._INITRETRUN
						.then(function(){
							log.info("加载控制器"+item.code+"完成");
							window.controllers[rev.id] = rev;
							resolve();
						})
						["catch"](function(ex){
							console.error(ex);
							resolve();
						})
						setTimeout(function(){
							reject(new Error(item.code+"加载超时"));
						},10000);
					}))
				);
			});

			return Promise.all(ps).then(function(){
				log.info("控制器加载完毕");
			})["catch"](function(error){
				log.error(error.message+"\n"+error.stack);
			})
		}
	}
	return renderer;
});
