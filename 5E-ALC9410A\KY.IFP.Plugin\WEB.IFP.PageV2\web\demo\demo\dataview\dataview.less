

#app {
    width: 100%;
    height: 100%;
    background-color: #030409;
    color: #fff;
  
    #dv-full-screen-container {
      background-image: url('./img/bg.png');
      background-size: 100% 100%;
      box-shadow: 0 0 3px blue;
      display: flex;
      flex-direction: column;
    }
  
    .main-header {
      height: 80px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
  
      .mh-left {
        font-size: 20px;
        color: rgb(1,134,187);
  
        a:visited {
          color: rgb(1,134,187);
        }
      }
  
      .mh-middle {
        font-size: 30px;
      }
  
      .mh-left, .mh-right {
        width: 450px;
      }
    }
  
    .main-container {
      height: calc(~"100% - 80px");
  
      .border-box-content {
        padding: 20px;
        box-sizing: border-box;
        display: flex;
      }
    }
  
    .left-chart-container {
      width: 22%;
      padding: 10px;
      box-sizing: border-box;
  
      .border-box-content {
        flex-direction: column;
      }
    }
  
    .right-main-container {
      width: 78%;
      padding-left: 5px;
      height: 100%;
      box-sizing: border-box;
    }
  
    .rmc-top-container {
      height: 65%;
      display: flex;
    }
  
    .rmctc-left-container {
      width: 65%;
    }
  
    .rmctc-right-container {
      width: 35%;
    }
  
    .rmc-bottom-container {
      height: 35%;
    }
  
    .rmctc-chart-1, .rmctc-chart-2 {
      height: 50%;
    }
  }