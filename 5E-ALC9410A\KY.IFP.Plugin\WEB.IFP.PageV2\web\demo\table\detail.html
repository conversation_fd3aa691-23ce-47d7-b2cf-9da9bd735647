<!DOCTYPE html>
<html lang="zh-Hans">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详情页</title>
</head>

<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar>
            <ifp-button type="primary">按钮1</ifp-button>
            <ifp-button>按钮1</ifp-button>
        </ifp-toolbar>
        
        <ifp-panel title="标题" flex-item>
            <ifp-form inline label-width="60px">
                <ifp-form-item label="名称">
                    <ifp-input></ifp-input>
                </ifp-form-item>
                <ifp-form-item label="名称">
                    <ifp-input></ifp-input>
                </ifp-form-item>
                <ifp-form-item label="名称">
                    <ifp-input></ifp-input>
                </ifp-form-item>
                <ifp-form-item label="名称">
                    <ifp-input></ifp-input>
                </ifp-form-item>
                <ifp-form-item label="名称">
                    <ifp-input></ifp-input>
                </ifp-form-item>
            </ifp-form>
        </ifp-panel>

        <ifp-layout-footer>
            <ifp-button type="primary">按钮1</ifp-button>
            <ifp-button>按钮1</ifp-button>
        </ifp-layout-footer>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>

</html>