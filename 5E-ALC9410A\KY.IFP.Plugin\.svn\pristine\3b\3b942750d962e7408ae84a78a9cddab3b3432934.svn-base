﻿using COM.IFP.Common;
//using COM.IFP.LinqDB;
using COM.IFP.SqlSugarN;
using DAL.ICS.BasicData;
using ORM.IFP;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Linq;

namespace API.ICS.BaseData
{
    public class Ywdx4005API
    {
        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();
        readonly Lazy<Ywdx4005DAL> dal = Entity.Create<Ywdx4005DAL>();

        /// <summary>
        /// 基本查询
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public List<YWDX4005> Select(JsonElement json)
        {
            YWDX4005 filter = json.GetValue<YWDX4005>("filter");
            return dal.Value.Select(filter);
        }

        /// <summary>
        /// 根据GID和期别查找对象
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public YWDX4005 GetByGIdAndQB(JsonElement json)
        {
            YWDX4005 entity = json.GetValue<ORM.IFP.YWDX4005>();
            return dal.Value.GetByGIdAndQB(entity);
        }

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PFActionResult Submit(JsonElement json)
        {
            //待保存的对象
            YWDX4005 entity = json.GetValue<ORM.IFP.YWDX4005>();
            return dal.Value.Submit(entity); ;
        }
    }
}
