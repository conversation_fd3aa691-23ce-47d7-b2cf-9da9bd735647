﻿using COM.IFP.Common;
using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using NPOI.SS.Formula.Functions;
using ORM.IFP.DbModel.UM;
using ORM.IFP.ViewModel;
using ORM.IFP.www.DbModel.UM;
using ORM.IFP.www.DTO;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using PageModel = COM.IFP.Common.PageModel;

namespace DAL.IFP.Department
{
    /// <summary>
    /// 部门管理DAL
    /// </summary>
    public class Department
    {
        /// <summary>
        /// 获取部门列表
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="paging">分页信息</param>
        /// <returns>部门列表或分页数据</returns>
        public object GetDepartmentList(IList<IFP_UM_DEPARTMENT> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                ISugarQueryable<IFP_UM_DEPARTMENT> query = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.DELT == 0);

                if (filter != null)
                {
                    query = query.Query(filter.ToArray());
                }

                if (paging == null)
                {
                    return query.OrderBy(x => x.DPMSerNum).ToList();
                }
                else
                {
                    return query.OrderBy(x => x.DPMSerNum).Fetch(paging);
                }
            }
        }

        /// <summary>
        /// 获取部门树形结构
        /// </summary>
        /// <param name="parentCode">父部门编码</param>
        /// <returns>树形结构的部门列表</returns>
        public List<DepartmentTreeNode> GetDepartmentTree(string parentCode)
        {
            using (var db = DB.Create())
            {
                var allDepts = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.DELT == 0)
                    .OrderBy(x => x.DPMSerNum)
                    .ToList();

                // 获取每个部门的用户数
                var deptUserCounts = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                    .Where(x => x.Delt == 0)
                    .GroupBy(x => x.DeptGid)
                    .Select(x => new
                    {
                        DeptGid = (string)x.DeptGid,
                        Count = SqlFunc.AggregateCount(x.DeptGid)
                    })
                    .ToList();

                // 如果没有指定父编码，查找顶级部门（父编码为 "ROOT" 或空字符串）
                if (string.IsNullOrWhiteSpace(parentCode))
                {
                    parentCode = "ROOT"; // 现在顶级部门的父编码是 "ROOT"
                }

                var result = BuildDepartmentTree(allDepts, parentCode, deptUserCounts);

                // 将返回给前端的数据中的 "ROOT" 转换为空字符串
                ConvertRootToEmptyForFrontend(result);

                return result;
            }
        }

        /// <summary>
        /// 构建部门树
        /// </summary>
        private List<DepartmentTreeNode> BuildDepartmentTree(
            List<IFP_UM_DEPARTMENT> allDepts,
            string parentCode,
            dynamic userCounts)
        {
            var tree = new List<DepartmentTreeNode>();

            // 如果没有指定父编码，默认为空字符串（顶级部门）
            //if (parentCode == null)
            //    parentCode = "";

            var currentLevelDepts = allDepts.Where(d => d.DPMFatherCode.Value == parentCode).ToList();

            foreach (var dept in currentLevelDepts)
            {
                var userCount = ((IEnumerable<dynamic>)userCounts).FirstOrDefault(x => x.DeptGid == dept.Gid.Value)?.Count ?? 0;

                var node = new DepartmentTreeNode
                {
                    Gid = dept.Gid.Value,
                    DpmCode = dept.DpmCode.Value,
                    DpmName = dept.DpmName.Value,
                    DpmFatherCode = dept.DPMFatherCode.Value,
                    DpmSerNum = dept.DPMSerNum.Value,
                    DpmMainUser = dept.DPMMainUser.Value,
                    UserCount = userCount,
                    Delt = dept.DELT.Value,
                    CreateTime = dept.CreateTime.Value,
                    Remark = dept.Remark.Value,
                };

                // 递归构建子节点
                node.Children = BuildDepartmentTree(allDepts, dept.DpmCode.Value, userCounts);

                tree.Add(node);
            }

            return tree;
        }

        /// <summary>
        /// 新增或更新部门
        /// </summary>
        /// <param name="entity">部门实体</param>
        /// <returns>操作结果</returns>
        public PFActionResult SaveOrUpdateDepartment(IFP_UM_DEPARTMENT entity)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    // 处理顶级部门的父编码问题
                    if (string.IsNullOrWhiteSpace(entity.DPMFatherCode.Value))
                    {
                        entity.DPMFatherCode = "ROOT"; // 使用 "ROOT" 表示顶级部门
                    }
                    // 检查部门编码是否重复
                    if (CheckDepartmentCodeExists(entity.DpmCode.Value, entity.Gid.Value))
                    {
                        result.success = false;
                        result.msg = "部门编码已存在";
                        return result;
                    }

                    // 如果是更新，检查是否会造成循环引用
                    if (!string.IsNullOrWhiteSpace(entity.Gid.Value))
                    {
                        if (WouldCauseCircularReference(entity.Gid.Value, entity.DPMFatherCode.Value))
                        {
                            result.success = false;
                            result.msg = "不能将部门设置为其子部门的下级";
                            return result;
                        }
                    }

                    // 更新部门全称
                    entity.DpmAllName = GetDepartmentFullName(entity.DPMFatherCode.Value, entity.DpmName.Value);

                    int n = 0;
                    if (string.IsNullOrWhiteSpace(entity.Gid.Value))
                    {
                        // 新增
                        entity.Gid = Guid.NewGuid().ToString("N");
                        entity.CreateTime = DateTime.Now;
                        entity.DELT = 0;

                        // 生成部门GUID
                        if (string.IsNullOrWhiteSpace(entity.DpmGuid.Value))
                        {
                            entity.DpmGuid = Guid.NewGuid().ToString("N");
                        }

                        n = db.Insertable(entity).ExecuteCommand();
                    }
                    else
                    {
                        // 更新时处理 DPMGUID 字段
                        // 如果前端没有传递 DPMGUID 或为空，从数据库获取原值
                        if (string.IsNullOrWhiteSpace(entity.DpmGuid.Value))
                        {
                            var existingDept = db.Queryable<IFP_UM_DEPARTMENT>()
                                .Where(x => x.Gid == entity.Gid && x.DELT == 0)
                                .First();

                            if (existingDept != null)
                            {
                                entity.DpmGuid = existingDept.DpmGuid; // 保持原有的 DPMGUID
                            }
                            else
                            {
                                entity.DpmGuid = Guid.NewGuid().ToString("N"); // 如果找不到原记录，生成新的
                            }
                        }
                        // 更新
                        n = db.Updateable(entity)
                            .IgnoreColumns(x => new { x.CreateTime, x.Creator })
                            .ExecuteCommand();

                        // 如果部门名称改变，需要更新所有子部门的全称
                        if (n > 0)
                        {
                            UpdateChildrenFullName(entity.DpmCode.Value);
                        }
                    }

                    result.success = n > 0;
                    result.msg = result.success ? "保存成功" : "保存失败";
                    result.data = entity;
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"保存部门失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "保存失败：" + ex.Message;
                }
            }

            return result;
        }

        /// <summary>
        /// 删除部门
        /// </summary>
        /// <param name="gid">部门ID</param>
        /// <returns>操作结果</returns>
        public PFActionResult DeleteDepartment(string gid)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 检查是否有子部门
                    var dept = db.Queryable<IFP_UM_DEPARTMENT>()
                        .Where(x => x.Gid == gid && x.DELT == 0)
                        .First();

                    if (dept == null)
                    {
                        result.success = false;
                        result.msg = "部门不存在或已被删除";
                        db.RollbackTran();
                        return result;
                    }

                    var hasChildren = db.Queryable<IFP_UM_DEPARTMENT>()
                        .Where(x => x.DPMFatherCode == dept.DpmCode && x.DELT == 0)
                        .Any();

                    if (hasChildren)
                    {
                        result.success = false;
                        result.msg = "该部门下有子部门，不能删除";
                        db.RollbackTran();
                        return result;
                    }

                    // 检查是否有用户
                    var hasUsers = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                        .Where(x => x.DeptGid == gid && x.Delt == 0)
                        .Any();

                    if (hasUsers)
                    {
                        result.success = false;
                        result.msg = "该部门下有用户，不能删除";
                        db.RollbackTran();
                        return result;
                    }

                    // 软删除部门
                    int n = db.Updateable<IFP_UM_DEPARTMENT>()
                        .SetColumns(x => x.DELT == 1)
                        .Where(x => x.Gid == gid)
                        .ExecuteCommand();

                    db.CommitTran();

                    result.success = n > 0;
                    result.msg = result.success ? "删除成功" : "删除失败";
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"删除部门失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "删除失败：" + ex.Message;
                }
            }

            return result;
        }

        /// <summary>
        /// 获取部门下的用户列表
        /// </summary>
        /// <param name="deptGid">部门ID</param>
        /// <param name="includeSub">是否包含子部门用户</param>
        /// <returns>用户列表</returns>
        public List<IFP_UM_USER_INFO> GetDepartmentUsers(string deptGid, bool includeSub)
        {
            using (var db = DB.Create())
            {
                var deptGids = new List<string> { deptGid };

                if (includeSub)
                {
                    // 获取所有子部门ID
                    var dept = db.Queryable<IFP_UM_DEPARTMENT>()
                        .Where(x => x.Gid == deptGid && x.DELT == 0)
                        .First();

                    if (dept != null)
                    {
                        var subDeptIds = GetAllSubDepartmentIds(dept.DpmCode.Value);
                        deptGids.AddRange(subDeptIds);
                    }
                }

                // 查询用户
                var users = db.Queryable<IFP_UM_USER_INFO, IFP_UM_USER_DEPARTMENT>((u, ud) =>
                        new JoinQueryInfos(
                            JoinType.Inner, u.Gid == ud.UserGid
                        ))
                    .Where((u, ud) => u.Delt == 0 && ud.Delt == 0 && deptGids.Contains((string)ud.DeptGid))
                    .Select((u, ud) => u)
                    .Distinct()
                    .ToList();

                return users;
            }
        }

        /// <summary>
        /// 分配用户到部门
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <param name="deptGid">部门ID</param>
        /// <param name="isPrimary">是否主部门</param>
        /// <returns>操作结果</returns>
        public PFActionResult AssignUserToDepartment(string userGid, string deptGid, bool isPrimary)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 检查用户和部门是否存在
                    var user = db.Queryable<IFP_UM_USER_INFO>()
                        .Where(x => x.Gid == userGid && x.Delt == 0)
                        .First();

                    var dept = db.Queryable<IFP_UM_DEPARTMENT>()
                        .Where(x => x.Gid == deptGid && x.DELT == 0)
                        .First();

                    if (user == null || dept == null)
                    {
                        result.success = false;
                        result.msg = "用户或部门不存在";
                        db.RollbackTran();
                        return result;
                    }

                    // 检查是否已存在关联
                    var existing = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                        .Where(x => x.UserGid == userGid && x.DeptGid == deptGid && x.Delt == 0)
                        .First();

                    if (existing != null)
                    {
                        // 更新是否主部门
                        if (existing.IsPrimary.Value != (isPrimary ? 1 : 0))
                        {
                            if (isPrimary)
                            {
                                // 先取消其他主部门
                                db.Updateable<IFP_UM_USER_DEPARTMENT>()
                                    .SetColumns(x => x.IsPrimary == 0)
                                    .Where(x => x.UserGid == userGid && x.Delt == 0)
                                    .ExecuteCommand();
                            }

                            db.Updateable<IFP_UM_USER_DEPARTMENT>()
                                .SetColumns(x => x.IsPrimary == (isPrimary ? 1 : 0))
                                .Where(x => x.Gid == existing.Gid)
                                .ExecuteCommand();
                        }
                    }
                    else
                    {
                        // 如果设置为主部门，先取消其他主部门
                        if (isPrimary)
                        {
                            db.Updateable<IFP_UM_USER_DEPARTMENT>()
                                .SetColumns(x => x.IsPrimary == 0)
                                .Where(x => x.UserGid == userGid && x.Delt == 0)
                                .ExecuteCommand();
                        }

                        // 新增关联
                        var userDept = new IFP_UM_USER_DEPARTMENT
                        {
                            Gid = Guid.NewGuid().ToString("N"),
                            UserGid = userGid,
                            DeptGid = deptGid,
                            IsPrimary = isPrimary ? 1 : 0,
                            CreateTime = DateTime.Now,
                            Delt = 0
                        };

                        db.Insertable(userDept).ExecuteCommand();
                    }

                    // 如果设置为主部门，更新用户表的部门信息
                    if (isPrimary)
                    {
                        db.Updateable<IFP_UM_USER_INFO>()
                            .SetColumns(x => new IFP_UM_USER_INFO
                            {
                                DpmGuid = dept.DpmGuid,
                                DeptName = dept.DpmName,
                                DeptFullName = dept.DpmAllName
                            })
                            .Where(x => x.Gid == userGid)
                            .ExecuteCommand();
                    }

                    db.CommitTran();

                    result.success = true;
                    result.msg = "分配成功";
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"分配用户到部门失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "分配失败：" + ex.Message;
                }
            }

            return result;
        }

        /// <summary>
        /// 批量分配用户到部门
        /// </summary>
        /// <param name="userGids">用户ID列表</param>
        /// <param name="deptGid">部门ID</param>
        /// <returns>操作结果</returns>
        public PFActionResult BatchAssignUsersToDepartment(List<string> userGids, string deptGid)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 检查部门是否存在
                    var dept = db.Queryable<IFP_UM_DEPARTMENT>()
                        .Where(x => x.Gid == deptGid && x.DELT == 0)
                        .First();

                    if (dept == null)
                    {
                        result.success = false;
                        result.msg = "部门不存在";
                        db.RollbackTran();
                        return result;
                    }

                    int successCount = 0;
                    foreach (var userGid in userGids)
                    {
                        // 检查用户是否存在
                        var userExists = db.Queryable<IFP_UM_USER_INFO>()
                            .Where(x => x.Gid == userGid && x.Delt == 0)
                            .Any();

                        if (!userExists)
                            continue;

                        // 检查是否已存在关联
                        var existing = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                            .Where(x => x.UserGid == userGid && x.DeptGid == deptGid && x.Delt == 0)
                            .Any();

                        if (!existing)
                        {
                            var userDept = new IFP_UM_USER_DEPARTMENT
                            {
                                Gid = Guid.NewGuid().ToString("N"),
                                UserGid = userGid,
                                DeptGid = deptGid,
                                IsPrimary = 0,
                                CreateTime = DateTime.Now,
                                Delt = 0
                            };

                            db.Insertable(userDept).ExecuteCommand();
                            successCount++;
                        }
                    }

                    db.CommitTran();

                    result.success = true;
                    result.msg = $"成功分配 {successCount} 个用户到部门";
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"批量分配用户到部门失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "批量分配失败：" + ex.Message;
                }
            }

            return result;
        }

        /// <summary>
        /// 从部门移除用户
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <param name="deptGid">部门ID</param>
        /// <returns>操作结果</returns>
        public PFActionResult RemoveUserFromDepartment(string userGid, string deptGid)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 检查是否是主部门
                    var userDept = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                        .Where(x => x.UserGid == userGid && x.DeptGid == deptGid && x.Delt == 0)
                        .First();

                    if (userDept == null)
                    {
                        result.success = false;
                        result.msg = "用户不属于该部门";
                        db.RollbackTran();
                        return result;
                    }

                    // 如果是主部门，需要清除用户表中的部门信息
                    if (userDept.IsPrimary.Value == 1)
                    {
                        db.Updateable<IFP_UM_USER_INFO>()
                            .SetColumns(x => new IFP_UM_USER_INFO
                            {
                                DpmGuid = "",
                                DeptName = "",
                                DeptFullName = ""
                            })
                            .Where(x => x.Gid == userGid)
                            .ExecuteCommand();
                    }

                    // 软删除关联记录
                    int n = db.Updateable<IFP_UM_USER_DEPARTMENT>()
                        .SetColumns(x => x.Delt == 1)
                        .Where(x => x.UserGid == userGid && x.DeptGid == deptGid)
                        .ExecuteCommand();

                    db.CommitTran();

                    result.success = n > 0;
                    result.msg = result.success ? "移除成功" : "移除失败";
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"从部门移除用户失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "移除失败：" + ex.Message;
                }
            }

            return result;
        }

        /// <summary>
        /// 批量从部门移除用户
        /// </summary>
        /// <param name="userGids">用户ID列表</param>
        /// <param name="deptGid">部门ID</param>
        /// <returns>操作结果</returns>
        public PFActionResult BatchRemoveUsersFromDepartment(List<string> userGids, string deptGid)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    int successCount = 0;
                    foreach (var userGid in userGids)
                    {
                        // 检查是否是主部门
                        var userDept = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                            .Where(x => x.UserGid == userGid && x.DeptGid == deptGid && x.Delt == 0)
                            .First();

                        if (userDept == null)
                            continue;

                        // 如果是主部门，需要清除用户表中的部门信息
                        if (userDept.IsPrimary.Value == 1)
                        {
                            db.Updateable<IFP_UM_USER_INFO>()
                                .SetColumns(x => new IFP_UM_USER_INFO
                                {
                                    DpmGuid = "",
                                    DeptName = "",
                                    DeptFullName = ""
                                })
                                .Where(x => x.Gid == userGid)
                                .ExecuteCommand();
                        }

                        // 软删除关联记录
                        int n = db.Updateable<IFP_UM_USER_DEPARTMENT>()
                            .SetColumns(x => x.Delt == 1)
                            .Where(x => x.UserGid == userGid && x.DeptGid == deptGid)
                            .ExecuteCommand();

                        if (n > 0)
                            successCount++;
                    }

                    db.CommitTran();

                    result.success = true;
                    result.msg = $"成功移除 {successCount} 个用户";
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"批量移除用户失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "批量移除失败：" + ex.Message;
                }
            }

            return result;
        }

        /// <summary>
        /// 设置用户主部门
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <param name="deptGid">部门ID</param>
        /// <returns>操作结果</returns>
        public PFActionResult SetUserPrimaryDepartment(string userGid, string deptGid)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 检查用户部门关联是否存在
                    var userDept = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                        .Where(x => x.UserGid == userGid && x.DeptGid == deptGid && x.Delt == 0)
                        .First();

                    if (userDept == null)
                    {
                        result.success = false;
                        result.msg = "用户不属于该部门";
                        db.RollbackTran();
                        return result;
                    }

                    // 获取部门信息
                    var dept = db.Queryable<IFP_UM_DEPARTMENT>()
                        .Where(x => x.Gid == deptGid && x.DELT == 0)
                        .First();

                    if (dept == null)
                    {
                        result.success = false;
                        result.msg = "部门不存在";
                        db.RollbackTran();
                        return result;
                    }

                    // 取消其他主部门
                    db.Updateable<IFP_UM_USER_DEPARTMENT>()
                        .SetColumns(x => x.IsPrimary == 0)
                        .Where(x => x.UserGid == userGid && x.Delt == 0)
                        .ExecuteCommand();

                    // 设置新的主部门
                    db.Updateable<IFP_UM_USER_DEPARTMENT>()
                        .SetColumns(x => x.IsPrimary == 1)
                        .Where(x => x.Gid == userDept.Gid)
                        .ExecuteCommand();

                    // 更新用户表的部门信息
                    db.Updateable<IFP_UM_USER_INFO>()
                        .SetColumns(x => new IFP_UM_USER_INFO
                        {
                            DpmGuid = dept.DpmGuid,
                            DeptName = dept.DpmName,
                            DeptFullName = dept.DpmAllName
                        })
                        .Where(x => x.Gid == userGid)
                        .ExecuteCommand();

                    db.CommitTran();

                    result.success = true;
                    result.msg = "设置主部门成功";
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"设置用户主部门失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "设置失败：" + ex.Message;
                }
            }

            return result;
        }

        /// <summary>
        /// 获取部门ID和名称的映射关系
        /// </summary>
        /// <returns>ID和名称的映射列表</returns>
        public List<IdTextModel> GetDepartmentIdTextList()
        {
            using (var db = DB.Create())
            {
                var depts = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.DELT == 0)
                    .OrderBy(x => x.DPMSerNum)
                    .ToList();

                return depts.Select(d => new IdTextModel
                {
                    id = d.Gid,
                    text = d.DpmName.Value,
                    sxh = d.DpmCode.Value
                }).ToList();
            }
        }

        /// <summary>
        /// 移动部门
        /// </summary>
        /// <param name="gid">部门ID</param>
        /// <param name="newParentCode">新父部门编码</param>
        /// <returns>操作结果</returns>
        public PFActionResult MoveDepartment(string gid, string newParentCode)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    var dept = db.Queryable<IFP_UM_DEPARTMENT>()
                        .Where(x => x.Gid == gid && x.DELT == 0)
                        .First();

                    if (dept == null)
                    {
                        result.success = false;
                        result.msg = "部门不存在";
                        db.RollbackTran();
                        return result;
                    }

                    // 检查是否会造成循环引用
                    if (WouldCauseCircularReference(gid, newParentCode))
                    {
                        result.success = false;
                        result.msg = "不能将部门移动到其子部门下";
                        db.RollbackTran();
                        return result;
                    }

                    // 更新父部门编码
                    dept.DPMFatherCode = newParentCode;

                    // 更新部门全称
                    dept.DpmAllName = GetDepartmentFullName(newParentCode, dept.DpmName.Value);

                    db.Updateable(dept)
                        .IgnoreColumns(x => new { x.CreateTime, x.Creator })
                        .ExecuteCommand();

                    // 更新所有子部门的全称
                    UpdateChildrenFullName(dept.DpmCode.Value);

                    db.CommitTran();

                    result.success = true;
                    result.msg = "移动部门成功";
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"移动部门失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "移动失败：" + ex.Message;
                }
            }

            return result;
        }

        /// <summary>
        /// 获取用户所属的所有部门
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <returns>部门列表</returns>
        public List<UserDepartmentInfo> GetUserDepartments(string userGid)
        {
            using (var db = DB.Create())
            {
                var departments = db.Queryable<IFP_UM_USER_DEPARTMENT, IFP_UM_DEPARTMENT>((ud, d) =>
                        new JoinQueryInfos(
                            JoinType.Inner, ud.DeptGid == d.Gid
                        ))
                    .Where((ud, d) => ud.UserGid == userGid && ud.Delt == 0 && d.DELT == 0)
                    .Select((ud, d) => new UserDepartmentInfo
                    {
                        DeptGid = (string)d.Gid,
                        DeptCode = (string)d.DpmCode,
                        DeptName = (string)d.DpmName,
                        DeptFullName = (string)d.DpmAllName,
                        IsPrimary = (int)ud.IsPrimary == 1,
                        AssignTime = (DateTime)ud.CreateTime
                    })
                    .ToList();

                return departments;
            }
        }

        /// <summary>
        /// 检查部门编码是否存在
        /// </summary>
        /// <param name="dpmCode">部门编码</param>
        /// <param name="excludeGid">排除的部门ID</param>
        /// <returns>是否存在</returns>
        public bool CheckDepartmentCodeExists(string dpmCode, string excludeGid = null)
        {
            using (var db = DB.Create())
            {
                var query = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.DpmCode == dpmCode && x.DELT == 0);

                if (!string.IsNullOrWhiteSpace(excludeGid))
                {
                    query = query.Where(x => x.Gid != excludeGid);
                }

                return query.Any();
            }
        }

        /// <summary>
        /// 获取部门统计信息
        /// </summary>
        /// <param name="deptGid">部门ID</param>
        /// <returns>统计信息</returns>
        public DepartmentStatistics GetDepartmentStatistics(string deptGid)
        {
            using (var db = DB.Create())
            {
                var statistics = new DepartmentStatistics();

                // 获取部门信息
                var dept = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.Gid == deptGid && x.DELT == 0)
                    .First();

                if (dept == null)
                    return statistics;

                // 直接用户数
                statistics.DirectUserCount = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                    .Where(x => x.DeptGid == deptGid && x.Delt == 0)
                    .Count();

                // 子部门数
                statistics.SubDepartmentCount = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.DPMFatherCode == dept.DpmCode && x.DELT == 0)
                    .Count();

                // 获取所有子部门ID
                var allSubDeptIds = GetAllSubDepartmentIds(dept.DpmCode.Value);
                statistics.TotalSubDepartmentCount = allSubDeptIds.Count;

                // 包含子部门的总用户数
                var allDeptIds = new List<string> { deptGid };
                allDeptIds.AddRange(allSubDeptIds);

                statistics.TotalUserCount = db.Queryable<IFP_UM_USER_DEPARTMENT>()
                    .Where(x => allDeptIds.Contains(x.DeptGid.Value) && x.Delt == 0)
                    .Select(x => x.UserGid)
                    .Distinct()
                    .Count();

                return statistics;
            }
        }

        /// <summary>
        /// 批量导入部门
        /// </summary>
        /// <param name="departments">部门列表</param>
        /// <returns>导入结果</returns>
        public PFActionResult BatchImportDepartments(List<IFP_UM_DEPARTMENT> departments)
        {
            PFActionResult result = new PFActionResult();

            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    int successCount = 0;
                    var errors = new List<string>();

                    foreach (var dept in departments)
                    {
                        // 检查部门编码是否已存在
                        if (CheckDepartmentCodeExists(dept.DpmCode.Value))
                        {
                            errors.Add($"部门编码 {dept.DpmCode.Value} 已存在");
                            continue;
                        }

                        // 检查父部门是否存在
                        if (!string.IsNullOrWhiteSpace(dept.DPMFatherCode.Value))
                        {
                            var parentExists = db.Queryable<IFP_UM_DEPARTMENT>()
                                .Where(x => x.DpmCode == dept.DPMFatherCode && x.DELT == 0)
                                .Any();

                            if (!parentExists)
                            {
                                errors.Add($"部门 {dept.DpmCode.Value} 的父部门编码 {dept.DPMFatherCode.Value} 不存在");
                                continue;
                            }
                        }

                        // 设置默认值
                        if (string.IsNullOrWhiteSpace(dept.Gid.Value))
                        {
                            dept.Gid = Guid.NewGuid().ToString("N");
                        }
                        if (string.IsNullOrWhiteSpace(dept.DpmGuid.Value))
                        {
                            dept.DpmGuid = Guid.NewGuid().ToString("N");
                        }
                        dept.CreateTime = DateTime.Now;
                        dept.DELT = 0;

                        // 更新部门全称
                        dept.DpmAllName = GetDepartmentFullName(dept.DPMFatherCode.Value, dept.DpmName.Value);

                        db.Insertable(dept).ExecuteCommand();
                        successCount++;
                    }

                    db.CommitTran();

                    result.success = successCount > 0;
                    result.msg = $"成功导入 {successCount} 个部门";
                    if (errors.Count > 0)
                    {
                        result.msg += $"，失败 {errors.Count} 个";
                        result.data = errors;
                    }
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"批量导入部门失败: {ex.Message}", ex);
                    result.success = false;
                    result.msg = "批量导入失败：" + ex.Message;
                }
            }

            return result;
        }

        #region 辅助方法

        /// <summary>
        /// 获取部门全称
        /// </summary>
        private Field<string> GetDepartmentFullName(string parentCode, string deptName)
        {
            using (var db = DB.Create())
            {
                // 处理顶级部门或 ROOT 的情况
                if (string.IsNullOrWhiteSpace(parentCode) || parentCode == "ROOT")
                {
                    return new Field<string>(deptName);
                }

                var parent = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.DpmCode == parentCode && x.DELT == 0)
                    .First();

                if (parent == null)
                {
                    return new Field<string>(deptName);
                }

                return new Field<string>($"{parent.DpmAllName.Value}/{deptName}");
            }
        }

        /// <summary>
        /// 检查是否会造成循环引用
        /// </summary>
        private bool WouldCauseCircularReference(string deptGid, string newParentCode)
        {
            if (string.IsNullOrWhiteSpace(newParentCode))
                return false;

            using (var db = DB.Create())
            {
                var dept = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.Gid == deptGid && x.DELT == 0)
                    .First();

                if (dept == null)
                    return false;

                // 获取所有子部门编码
                var subDeptCodes = GetAllSubDepartmentCodes(dept.DpmCode.Value);

                // 检查新父部门编码是否在子部门列表中
                return subDeptCodes.Contains(newParentCode);
            }
        }

        /// <summary>
        /// 获取所有子部门ID
        /// </summary>
        private List<string> GetAllSubDepartmentIds(string parentCode)
        {
            using (var db = DB.Create())
            {
                var subDeptIds = new List<string>();
                GetSubDepartmentIdsRecursive(db, parentCode, subDeptIds);
                return subDeptIds;
            }
        }

        /// <summary>
        /// 递归获取子部门ID
        /// </summary>
        private void GetSubDepartmentIdsRecursive(SqlSugarClient db, string parentCode, List<string> subDeptIds)
        {
            var subDepts = db.Queryable<IFP_UM_DEPARTMENT>()
                .Where(x => x.DPMFatherCode == parentCode && x.DELT == 0)
                .ToList();

            foreach (var subDept in subDepts)
            {
                subDeptIds.Add(subDept.Gid.Value);
                GetSubDepartmentIdsRecursive(db, subDept.DpmCode.Value, subDeptIds);
            }
        }

        /// <summary>
        /// 获取所有子部门编码
        /// </summary>
        private List<string> GetAllSubDepartmentCodes(string parentCode)
        {
            using (var db = DB.Create())
            {
                var subDeptCodes = new List<string>();
                GetSubDepartmentCodesRecursive(db, parentCode, subDeptCodes);
                return subDeptCodes;
            }
        }

        /// <summary>
        /// 递归获取子部门编码
        /// </summary>
        private void GetSubDepartmentCodesRecursive(SqlSugarClient db, string parentCode, List<string> subDeptCodes)
        {
            var subDepts = db.Queryable<IFP_UM_DEPARTMENT>()
                .Where(x => x.DPMFatherCode == parentCode && x.DELT == 0)
                .ToList();

            foreach (var subDept in subDepts)
            {
                subDeptCodes.Add(subDept.DpmCode.Value);
                GetSubDepartmentCodesRecursive(db, subDept.DpmCode.Value, subDeptCodes);
            }
        }

        /// <summary>
        /// 更新子部门全称
        /// </summary>
        private void UpdateChildrenFullName(string parentCode)
        {
            using (var db = DB.Create())
            {
                var children = db.Queryable<IFP_UM_DEPARTMENT>()
                    .Where(x => x.DPMFatherCode == parentCode && x.DELT == 0)
                    .ToList();

                foreach (var child in children)
                {
                    child.DpmAllName = GetDepartmentFullName(parentCode, child.DpmName.Value);

                    db.Updateable(child)
                        .UpdateColumns(x => x.DpmAllName)
                        .ExecuteCommand();

                    // 递归更新子部门的子部门
                    UpdateChildrenFullName(child.DpmCode.Value);
                }
            }
        }


        /// <summary>
        /// 将返回给前端的 "ROOT" 转换为空字符串
        /// </summary>
        private void ConvertRootToEmptyForFrontend(List<DepartmentTreeNode> nodes)
        {
            if (nodes == null) return;

            foreach (var node in nodes)
            {
                // 将 "ROOT" 转换为空字符串给前端显示
                if (node.DpmFatherCode == "ROOT")
                {
                    node.DpmFatherCode = "";
                }

                // 递归处理子节点
                if (node.Children != null && node.Children.Count > 0)
                {
                    ConvertRootToEmptyForFrontend(node.Children);
                }
            }
        }
        #endregion
    }


}
