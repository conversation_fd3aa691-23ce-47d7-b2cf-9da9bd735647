<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹出页子页面[vue]</title>
    <style>
        /* 这里写的样式，只有单独打开才起作用，作为组件使用时会被丢弃 */
        html,body{
            padding:0;margin:0;
            height: 100%;
        }
    </style>
</head>
<body controller="element_sub1.js" option="{platform:'vue'}">
    <div id="app" class="flex padding" style="height: 100%;">
        <div class="">
            <a class="btn btn-default" >btn1</a>
            <a class="btn btn-default" >btn2</a>
            <a class="btn btn-default" >btn3</a>
            <a class="btn btn-primary" href="element_sub1.html">单独打开我</a>
        </div>
        <div class="margin-top flex-item padding" style="background-color: #eee;border:1px solid #aaa;">
            页面:element_sub1<br />
            来自子页面{{title}}<br />
            来自父页面props：{arg1:{{arg1}}}
            <div class="element_sub1_class">
                测试文字
            </div>
        </div>
    </div>
    <script src="/iofp/starter.js"></script> 
</body>
</html>