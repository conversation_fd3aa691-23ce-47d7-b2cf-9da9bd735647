;define([
"require",
"controllers/base",
"jquery",
"jclass"],
function(require,base,$,jclass){
	return jclass(base,{
		name:"gssjInfo",
		openWin:function(){
			this.log.info("打开弹出页");
		},
		bindEvent:function(){
			var _this = this;		
			this.bind("showBtn","click",this.queryObj,this);		
			//新增事件			
			this.bind("addBtn","click",this.addObj,this);
			//修改事件			
			this.bind("editBtn","click",this.editObj,this);		
			//删除事件			
			this.bind("delBtn","click",this.delObj,this);
			//查询事件			
			this.bind("searchBtn","click",this.searchObj,this);
			//重置事件			
			this.bind("resetBtn","click",this.resetObj,this);
			
			//选中行事件
			this.bind("grid","onSelectRow",function(rowid,isSelected){						
				var rowdata = _this.controls.grid.getRowData(rowid);
				_this.controls.showBtn.disable(false);		
				_this.controls.editBtn.disable(false);	
				_this.controls.delBtn.disable(false);
			});
			
			//双击行事件
			this.bind("grid","ondblClickRow",function(rowid,isSelected){
				_this.showObj(rowid);				
			});
		},
		
		
		//查看明细数据.
		queryObj:function(){
			var _this = this;
			var id=$('#grid').jqGrid('getGridParam','selrow');
			if (id == null) {			 
				 $.alert("请选择数据查看!");
				 return;
			}
			_this.showObj(id);	
		},		
		//查看方法
		showObj:function(pGid){	
			var _this = this;
			_this.showframe("查看","show",pGid);					
		},
		//新增方法
		addObj:function(){			
			var _this = this;		
			_this.showframe("新增","add","");
		},
		//修改方法
		editObj:function(){
			var _this = this;
			var id=$('#grid').jqGrid('getGridParam','selrow');
			if (id == null) {			 
				 $.alert("请选择数据进行修改!");
				 return;
			 }			
			_this.showframe("修改","edit",id);
		},
		//打开页面
		showframe:function(ptext,pmode,pgid){
			var _this =this;			
			
			var opt = {
						url:"/pages/demo/gssjMx.html",
						full:true,
						title: ptext,
						onhidden:function(){
							_this.loadForm();
						},
						parameter: {
							pMode:pmode,
							pGid:pgid
						}
					};
			var win = $.showIframe(opt);
		},
		
		//删除方法
		delObj:function(){
			var _this = this;
			var id=$('#grid').jqGrid('getGridParam','selrow');
			if (id == null) {			 
				 $.alert("请选择数据进行删除!");
				 return;
			 }
			 
			$.confirm('信息：删除后将无法恢复，是否继续?',function(result){
	            if(result) {
	               
	    			F.ajax({
					url:"/www/Gssj/delGssj",
					data:{gid:id},
					success:function(resultObj){
					
						if(resultObj.success) {
							_this.loadForm();
							$.bootoast.success("删除成功");
						} else {
							$.bootoast.warning(resultObj.data);
						}
					}
				});
	            }else {
	               
	            }
	        });
		},
		
		//查询方法
		searchObj:function(){
			this.loadForm();
		},
		//重置方法
		resetObj:function(){
			this.initData();
		},
		//初始化页面
		initData:function(){		
			//var dt =F.common.postSyn("/com.kysoft.service/html/util/DateUtil/getFirstDateOfMonthAndToday.action",{});
			//dt = eval('(' + dt + ')');
			//this.controls.ywrq_dstart.value(dt[0]);
			//this.controls.ywrq_dend.value(dt[1]);
			this.loadForm();
		},
		//加载GIRD
		loadForm:function(){
			var _this = this;
			_this.controls.grid.reload({
				url:'/www/Gssj/gssjList',
				postData: {	
					ywrq_dstart:_this.controls.ywrq_dstart.value(),
					ywrq_dend:_this.controls.ywrq_dend.value()
				}
			});		
			_this.controls.showBtn.disable();	
			_this.controls.editBtn.disable();	
			_this.controls.delBtn.disable();
		},
	
		onLoad:function(){
			this.initData();
			this.bindEvent();	
		}
	})
});