<!DOCTYPE html>
<html lang="zh-Hans" class="flex">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例页面</title>

    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.6/lib/theme-chalk/index.css">
    <style>
        /* 整个滚动条 */
        ::-webkit-scrollbar,
        /* 滚动条上的按钮 (上下箭头) */
        ::-webkit-scrollbar-button,
        /* 滚动条上的滚动滑块 */
        ::-webkit-scrollbar-thumb,
        /* 滚动条轨道 */
        ::-webkit-scrollbar-track,
        /* 滚动条没有滑块的轨道部分 */
        ::-webkit-scrollbar-track-piece,
        /* 当同时有垂直滚动条和水平滚动条时交汇的部分 */
        ::-webkit-scrollbar-corner,
        /* 某些元素的corner部分的部分样式(例:textarea的可拖动按钮) */
        ::-webkit-resizer,
        .scrollbar-min::-webkit-scrollbar {
            appearance: auto;
            width: 6px;
            height: 6px;
        }
        .scrollbar-min::-webkit-scrollbar-thumb {
            border-radius: 4px;
            border:0px solid #0001;
            background-color: #0001;
            transition: all 1s;
        }
        .scrollbar-min:hover::-webkit-scrollbar-thumb {
            border-radius: 4px;
            border:0px solid #0001;
            background-color: #0003;
        }
        .listbox{
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-wrap: wrap;
            align-content: center;
        }
        .listbox .el-button{
            margin:0.5rem !important;
        }
        .shadow{
            box-shadow: 0px 5px 20px 0px rgba(0,0,0,0.1);
        }
        .shadow2{
            box-shadow: 5px 0px 20px 0px rgba(0,0,0,0.1);
        }
        .shadow3{
            box-shadow: 0px -5px 20px 0px rgba(0,0,0,0.1);
        }
        html{
            height:100%;
        }
        iframe{
            border:0;
        }
    </style>

    <style>

        /*
        * layout.css
        * create : 2020-06-28 / zq
        */
        /* flex */
        .flex {
        display: flex;
        flex-direction: column;
        }
        .flex.flex-row {
        flex-direction: row;
        }
        .flex.flex-row > .flex-item {
        width: 0;
        height: auto;
        }
        .flex > :not(.flex-item) {
        flex-shrink: 0;
        }
        .flex > .flex-item {
        height: 0;
        overflow: auto;
        }
        .flex-item {
        flex: 1;
        }
        /* align-items: flex-start | flex-end | center | baseline | stretch; */
        .flex-align-stretch {
        align-items: stretch;
        }
        .flex-align-center {
        align-items: center;
        }
        .flex-content-center {
        justify-content: center;
        }
        .flex-item-center {
        align-items: center;
        }
        .flex-item-stretch {
        align-items: stretch;
        }
        /* margin */
        .margin {
        margin: 8px;
        }
        .margin-top {
        margin-top: 8px;
        }
        .margin-bottom {
        margin-bottom: 8px;
        }
        .margin-left {
        margin-left: 8px;
        }
        .margin-right {
        margin-right: 8px;
        }
        .no-margin {
        margin: 0;
        }
        .no-margin-top {
        margin-top: 0;
        }
        .no-margin-bottom {
        margin-bottom: 0;
        }
        .no-margin-left {
        margin-left: 0;
        }
        .no-margin-right {
        margin-right: 0;
        }
        /* padding */
        .padding {
        padding: 8px;
        }
        .padding-top {
        padding-top: 8px;
        }
        .padding-bottom {
        padding-bottom: 8px;
        }
        .padding-left {
        padding-left: 8px;
        }
        .padding-right {
        padding-right: 8px;
        }
        .no-padding {
        padding: 0;
        }
        .no-padding-top {
        padding-top: 0;
        }
        .no-padding-bottom {
        padding-bottom: 0;
        }
        .no-padding-left {
        padding-left: 0;
        }
        .no-padding-right {
        padding-right: 0;
        }
        /* align */
        .text-center {
        text-align: center;
        }
        .text-left {
        text-align: left;
        }
        .text-right {
        text-align: right;
        }
        /* nowrap */
        .nowrap {
        white-space: nowrap;
        }
        /* nowrap */
        .nowrap {
        white-space: nowrap;
        }
        .overflow-auto {
        overflow: auto;
        }
        .overflow-hidden {
        overflow: hidden;
        }
        .overflow-clean {
        overflow: unset;
        }
        .height-0 {
        height: 0;
        }
        .margin {
        margin: 8px;
        }

    </style>
</head>
<body class="flex flex-item" style="background-color: #fff;">
    <div id="app" class="flex flex-item">
        <div class="flex flex-row shadow" style="z-index: 1;">
            <div class="flex-item padding-left padding"><el-link href="./">首页</el-link></div>
            <div class="padding-right padding-top" v-if="currentpath">
                <el-link title="单独打开" :href="'.'+currentpath">
                    <i class="margin-right el-icon el-icon-copy-document"></i>{{renderCurrentPath}}
                </el-link>
            </div>
        </div>
        <div class="flex flex-item flex-row" style="min-width: 100px;">
            <div class="shadow2 scrollbar-min" style="z-index: 1; width:auto; padding:1rem; background-color:#fff; overflow: auto;">
                <el-tree ref="tree" :current-node-key="currentpath" :data="data" node-key="path" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
            </div>
            <iframe v-if="currentpath!==''" class="flex-item" :src="'./'+currentpath"></iframe>
            <div class="flex-item flex" v-else>
                <div class="padding">
                    <el-input v-model="filterText"
                    prefix-icon="el-icon-search" 
                    placeholder="筛选"></el-input>
                </div>
                <div class="padding-left">
                    关键字：
                    <el-button v-for="item in keys" 
                    type="text"
                    :key="item" @click="filterText=item">{{item}}</el-button>
                </div>
                <div class="flex-item listbox scrollbar-min">
                    <el-button v-for="item in renderList" 
                    type="primary" plain round
                    :key="item.path" @click="jump(item.path)">{{item[defaultProps.label]}}</el-button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.6/lib/index.js"></script>
	<script>
        Vue.use(ELEMENT)
        fetch("./files.json")
        .then(d=>d.json())
        .then(data=>{
            new Vue(createApp(data))
        })

        function createApp(data) {
            function delayering(childs){
                const list = [];
                childs.forEach(item=>{
                    list.push(item);
                    if(item.children && item.children.length){
                        list.push(...delayering(item.children))
                    }
                })
                return list;
            }


            return {
                el:"#app",
                data(){
                    data.children = data.children.filter(item=>item.file!=="index.html");
                    return {
                        currentpath:'',
                        defaultActive:"1",
                        activeIndex2:"1",
                        title:"test",
                        data: data.children,
                        filterText:'',
                        defaultProps: {
                        children: 'children',
                        label: 'title'
                        }
                    }
                },
                computed:{
                    renderCurrentPath(){
                        return decodeURIComponent(this.currentpath);
                    },
                    list(){
                        return delayering(this.data).filter(item=>!item.isdir)
                    },
                    keys(){
                        let keys = new Set();
                        this.list.forEach(item=>{
                            item.keywords?.forEach(i=>keys.add(i))
                        });
                        return [...keys];
                    },
                    renderList(){
                        return this.list.filter(item=>{
                            return item.file.indexOf(this.filterText)>-1
                            || (item.title && item.title.indexOf(this.filterText)>-1)
                            || item.keywords.find(w=>w.indexOf(this.filterText)>-1)
                        })
                    }
                },
                watch:{
                    filterText(){
                    }
                },
                mounted(){
                    this.updateHash();
                    window.addEventListener('hashchange', ()=> {
                        this.updateHash();
                    }, false);
                    let search = window.location.search.match(/[\?\&]s=(.*)&?$/)
                    if(search){
                        this.filterText = decodeURIComponent(search[1]);
                    }
                },
                methods:{
                    goHome(){
                        window.location = "./"
                    },
                    jump(path){
                        window.location.hash='#'+path
                    },
                    updateHash(){
                        let hash = window.location.hash
                        if(hash){
                            this.currentpath = hash.replace('#','')
                        }else{
                            this.currentpath = ''
                        }
                    },
                    handleNodeClick(node){
                        if(node.isdir){return;}
                        this.currentpath = node.path;
                        window.location.hash = node.path;
                        //this.$message(node.path)
                    }
                }
            }
        }
    </script>
</body>
</html>