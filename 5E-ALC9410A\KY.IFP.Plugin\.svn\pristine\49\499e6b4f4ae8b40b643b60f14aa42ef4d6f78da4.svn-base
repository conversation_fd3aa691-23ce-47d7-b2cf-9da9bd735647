﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>excel导入</title>
</head>
<body controller="importExcel.js">
	<div form="forms/toolbar" class="layout-h">
		<a id="dowLoadBtn" control="controls/button" option="{}">模板下载</a>
		<a id="returnBtn" control="controls/button" option="{command:'close'}">退出</a>
	</div>
	<div class="layout-c padding" style="padding-top: 50px;">
		<form id="form1" enctype="multipart/form-data" method="post">
			<table class="formtable">
				<tr>
					<td style="padding-left: 20px;">导入文件：</td>
					<td>
						<input id="input" type="file" name="input" accept=".xlsx" size="50" />
					</td>
					<td style="padding-left: 20px;">
						<input id="submitBtn" control="controls/button" value="  导入   " />
					</td>
				</tr>
			</table>

			<input id="modleName" type="hidden" name="modleName" />
			<!-- 解析Excel后的返回实体 -->
			<input id="className" type="hidden" name="className" />
			<!-- 解析Excel后需要调用的处理类 -->
			<input id="method" type="hidden" name="method" />
			<!-- 解析Excel后需要调用的处理方法 -->
			<input id="modleKeys" type="hidden" name="modleKeys" />
			<!-- 解析Excel时需要与返回实体的对应关系 -->
			<input id="startNum" type="hidden" name="startNum" />
			<!-- 自定义参数 -->
			<input id="param" type="hidden" name="param" />
			<!--
			URL参数：
			modleName=com.kysoft.service.context.model.sysmanage.UserVO   	解析Excel后的返回实体
			&className=testContext										           解析Excel后需要调用的处理类
			&method=excelImpTest											解析Excel后需要调用的处理方法
			&modleKeys=userId,userName,isUsed								解析Excel时需要与返回实体的对应关系
			&startNum=1 													解析Excel时的起始行
			&param={a:123,b:333} 											自定义参数，字符格式，回调函数中通过request获得
			-->
		</form>
	</div>
</body>
<script src="/iofp/starter.js"></script>
</html>
