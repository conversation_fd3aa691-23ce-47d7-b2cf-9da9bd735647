define(['module', 'jquery'], function (module, $) {
    var defaultConfig = {
        /* 从 starter.js 中获取配置
        hander:["click"],//监听事件
        filter:["button,a,input[type='button']","[log]"],//筛选器
        url:"/API/IFP/Rights/UserLog/SaveLog"
        */
    };
    var config = $.extend({}, defaultConfig, module.config());
    console.log(config);
    // 判断元素是否符合条件
    function checkele(ele) {
        for (var i = 0; i < config.filter.length; i++) {
            if (!$(ele).is(config.filter[i])) {
                return false;
            }
        }
        return true;
    }

    // 从事件元素路径中获取符合条件的元素
    function getBtnElement(e) {
        console.log($(e));
        return;
        // if (e.path == undefined) return null;//daiabin老是为null导致find方法报错
        return e.path.find(function (ele) {
            return checkele(ele);
        });
    }

    // 辅助函数，用于获取点击的元素，处理id和class查找情况
    function getClickedElement(e) {
        var $ele;
        // 先判断是否有id属性，优先尝试通过id查找元素
        if (e.target.id) {
            $ele = $('#' + e.target.id);
            if ($ele.length > 0 && $ele[0] === e.target) {
                return $ele;
            }
        }
        // 如果通过id没找到或者找到的不符合要求，再尝试通过class查找元素
        if (e.target.classList.length > 0) {
            var classNames = e.target.classList;
            for (var i = 0; i < classNames.length; i++) {
                $ele = $('.' + classNames[i]);
                for (var j = 0; j < $ele.length; j++) {
                    if ($ele[j] === e.target) {
                        return $ele.eq(j);
                    }
                }
            }
        }
        return $ele;
    }
    function getData(ele) {
        var $ele = $(ele);
        var rev = {
            btnId: $ele.attr('log-id') || $ele.attr('id') || '',
            btnName: $ele.attr('log-name') || $ele.text().trim() || $ele.val().trim() || ($ele.attr('title') && $ele.attr('title').trim()) || '',
            pageName: $ele.attr('log-page-name') || document.title || '',
            pageUrl: $ele.attr('log-page-id') || window.location.pathname,
        };
        return rev;
    }

    function post(data) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: config.url,
                data: JSON.stringify(data),
                success: function (result) {
                    resolve();
                },
            });
        });
    }

    var start = false;
    return function () {
        if (start) {
            return;
        }
        config.hander.forEach(function (evenName) {
            window.addEventListener(evenName, function (e) {
                // var ele = getBtnElement(e); // 不使用此方法获取元素
                // if (!ele) {
                //     return;
                // }

                // 使用jQuery直接获取点击元素
                var $ele = getClickedElement(e);
                if ($ele === undefined || $ele.length === 0) {
                    return;
                }
                // console.log($ele);

                // 判断元素是否存在log属性
                var elHasLog = $ele.attr('log') !== undefined; // 判断元素是否存在log属性
                if ($ele && elHasLog) {
                    post(getData($ele))
                        .then(function () {
                            console.log('button log submit');
                        })
                        .catch(function (params) {
                            console.error('button log submit error');
                        });
                }
            });
        });
    };
});
