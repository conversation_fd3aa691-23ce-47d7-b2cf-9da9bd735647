﻿//using LinqToDB.Mapping;
//using ColumnAttribute = LinqToDB.Mapping.ColumnAttribute;
using COM.IFP.SqlSugarN;
using SqlSugar;
namespace ORM.IFP.DbModel.PLC
{

    /// <summary>
	/// 页面点表对应关系
	/// </summary>
	[SugarTable("PLC_PAGEPOINT")]
    public partial class PLC_PAGEPOINT
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(50)")]
        public Field<string> Gid { get; set; }

        /// <summary>
        /// 页面Id
        /// </summary>
        [SugarColumn(ColumnName = "PAGEID", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(50)")]
        public Field<string> PageID { get; set; }

        /// <summary>
        /// PLC点位名
        /// </summary>
        [SugarColumn(ColumnName = "PLCPOINT", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(500)")]
        public Field<string> PLCPoint { get; set; }

    }


    /* 以下为对应的Json对象
	{
		GID: null,
		PAGEID: null,		//页面Id
		PLCPOINT: null		//PLC点位名
	}
	*/

    ///// <summary>
    ///// 数据表：页面点表对应关系
    ///// </summary>
    //[SugarTable("PLC_PAGEPOINT")]
    //public partial class PLC_PAGEPOINT
    //{
    //    /// <summary>
    //    /// GID
    //    /// </summary>
    //    [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, Length = 50)]
    //    public string Gid { set; get; }

    //    /// <summary>
    //    /// 页面Id
    //    /// </summary>
    //    [SugarColumn(ColumnName = "PageID", Length = 50)]
    //    public string PageID { set; get; }

    //    /// <summary>
    //    /// PLC点位名/id
    //    /// </summary>
    //    [SugarColumn(ColumnName = "PLCPoint", Length = 500)]
    //    public string PLCPoint { set; get; }
    //}

    //[LinqToDB.Mapping.Table("PLC_PAGEPOINT")]
    //public partial class PLC_PAGEPOINT
    //{

    //    /// <summary>
    //    /// GID
    //    /// </summary>
    //    [Column("GID"), Key, MaxLength(50), PrimaryKey]
    //    public string Gid { set; get; }
    //    /// <summary>
    //    /// 页面Id
    //    /// </summary>
    //    [Column("PageID"), MaxLength(50)]
    //    public string PageID { set; get; }

    //    /// <summary>
    //    /// PLC点位名/id
    //    /// </summary>
    //    [Column("PLCPoint"), MaxLength(500)]
    //    public string PLCPoint { set; get; }
    //}
    public partial class PLC_PAGEPOINT
    {
        /// <summary>
        /// PLC点值
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string PointValue { set; get; }

        /// <summary>
        /// 点位值类型
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string PLCPointType { set; get; }
    }
}
