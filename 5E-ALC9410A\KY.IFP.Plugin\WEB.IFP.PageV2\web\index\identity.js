(function(){
    let segment = document.location.pathname.split('/').filter(x => x != '');
    if (segment[0] != "identity" || segment[1].length != 32) {
        var args = [['_vrt',Date.now()]];
        var refresh = true;
        if(location.search){
            args = location.search.replace('?', '').split('&').map(item => item.split('='));
            var _var = args.find(function (item) { return item[0] == '_vrt' });
            if(_var){
                var v = Number(_var[1]);
                console.log(v - Date.now() - 1000);
                if (!isNaN(v) && v > Date.now() - 1000) {
                    refresh = false;
                } else {
                    _var[1] = Date.now();
                }
            }else{
                args.push(['_vrt',Date.now()])
            }
        }
        if(refresh){
            window.location.href = window.location.pathname + '?' + args.map(item=>item.join('=')).join('&')
        }
    }
}())