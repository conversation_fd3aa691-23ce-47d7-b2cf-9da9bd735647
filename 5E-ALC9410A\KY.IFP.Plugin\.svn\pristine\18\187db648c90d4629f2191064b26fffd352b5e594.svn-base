; define([
    "controllers/base",
    "jclass",
    "iofp/lib/excelExport"],
    function (base, jclass, excelExport) {
        // grid 中两个 UserId 导致导出异常，正确的是一个转化成工号，二个转化成用户名
        // 第二个 UserId 没有正确解析成用户名
        // 列的表头按照ID检索，而数据行用的索引，导致同ID表头相同而数据转化正确的。
        // 这了修改用户名列的ID，避免第二个ID解析成同样的表头。
        var dataFilter = function (d) {
            let rev = JSON.parse(d);
            rev.rows = rev.rows.map(item => {
                item.UserId2 = item.UserId;
                return item;
            })
            return JSON.stringify(rev);
        }
        return jclass(base, {
            name: "userLog",
            openWin: function () {
                this.log.info("打开弹出页");
            },
            bindEvent: function () {
                var _this = this;
                //导出事件
                this.bind("exportBtn", "click", this.exportObj, this);
                //查询事件			
                this.bind("searchBtn", "click", this.searchObj, this);
                //重置事件			
                this.bind("resetBtn", "click", this.resetObj, this);

                //页面选项切换刷新按钮选项
                this.bind("PageName", "change", function (e, data) {
                    _this.GetBtnNameList();
                });
            },
            //导出事件
            exportObj: function () {
                var _this = this;
                var sidx = _this.controls.grid.$container.getGridParam("sortname");
                var sord = _this.controls.grid.$container.getGridParam("sortorder");
                let param = {
                    PageName: _this.controls.PageName.value(),
                    BtnName: _this.controls.BtnName.value(),
                    LogTime_DSTART: _this.controls.startDate.value(),
                    LogTime_DEND: _this.endDateHandle(_this.controls.endDate.value()),
                    UserId: _this.controls.UserId.value(),
                    page: 1,
                    rows: 10000,
                    sidx: (sidx == null ? "OperateTime" : sidx),
                    sord: (sord == null ? "desc" : sord)
                }
                F.ajax({
                    url: '/API/IFP/Rights/UserLog/GetLogList',
                    data: param,
                    dataFilter: dataFilter,
                    success: function (resp) {
                        //所有的数据行
                        var rows = resp.rows;

                        //构造Excel Workbook并导出
                        var ExcelWorkbook = {
                            FileName: "操作记录.xls",
                            Sheets: [
                                {
                                    SheetName: "操作记录",
                                    Tables: [
                                        {
                                            TableType: 1,
                                            Heads: excelExport.getJqGridHeads(_this.controls["grid"]),
                                            Datas: excelExport.getJqGridDatas(_this.controls["grid"], rows)
                                        }
                                    ]
                                }
                            ]
                        };
                        excelExport.export(ExcelWorkbook);
                    }
                })
            },
            //查询方法
            searchObj: function () {
                this.loadForm();
            },
            //重置方法
            resetObj: function () {
                //清空控件内容
                this.controls.startDate.clear();
                this.controls.endDate.clear();
                this.controls.machineCode.clear();
                this.controls.doorState.clear();
                this.initData();
            },
            //初始化页面
            initData: function () {
                //daiabin，默认当天数据
                var myDate = new Date;
                var year = myDate.getFullYear(); //获取当前年
                var mon = myDate.getMonth() + 1; //获取当前月
                var date = myDate.getDate(); //获取当前日
                var time = year + "-" + mon + "-" + date;
                this.controls.startDate.value(time);
                this.controls.endDate.value(time);

                this.loadForm();
                this.GetPageNameList();
                this.GetUserIdTextList();
            },
            endDateHandle: function (endDate) {
                //结束时间应当时次日0点
                if (endDate == undefined || endDate == "") return endDate;
                let newEndDate = new Date(endDate);
                newEndDate.setDate(newEndDate.getDate() + 1);
                return newEndDate;
            },
            //加载GIRD
            loadForm: function () {
                var _this = this;
                let param = {
                    PageName: _this.controls.PageName.value(),
                    BtnName: _this.controls.BtnName.value(),
                    LogTime_DSTART: _this.controls.startDate.value(),
                    LogTime_DEND: _this.endDateHandle(_this.controls.endDate.value()),
                    UserId: _this.controls.UserId.value(),
                }
                _this.controls.grid.reload({
                    url: '/API/IFP/Rights/UserLog/GetLogList',
                    postData: param,
                    ajaxGridOptions: {
                        dataFilter: dataFilter
                    }
                });
            },
            //给页面名称下拉框赋值
            GetPageNameList: function () {
                var _this = this;
                F.ajax({
                    url: '/API/IFP/Rights/UserLog/GetPageNameList',
                    type: "post",
                    contentType: "application/json",
                    data: {},
                    success: function (result) {
                        _this.controls.PageName.data(result);
                    },
                    error: function () {

                    }
                });
            },
            //选择页面名称后给按钮下拉框赋值
            GetBtnNameList: function () {
                var _this = this;
                F.ajax({
                    url: '/API/IFP/Rights/UserLog/GetBtnNameList',
                    type: "post",
                    contentType: "application/json",
                    data: JSON.stringify({ PageName: _this.controls.PageName.value() }),
                    success: function (result) {
                        _this.controls.BtnName.data(result);
                    },
                    error: function () {

                    }
                });
            },
            //获取用户下拉框
            GetUserIdTextList: function () {
                var _this = this;
                F.ajax({
                    url: '/API/IFP/Rights/User/GetUserIdTextList',
                    type: "post",
                    contentType: "application/json",
                    data: {},
                    success: function (result) {
                        _this.controls.UserId.data(result);
                    },
                    error: function () {

                    }
                });
            },
            onLoad: function () {
                this.initData();
                this.bindEvent();
            }
        })
    });