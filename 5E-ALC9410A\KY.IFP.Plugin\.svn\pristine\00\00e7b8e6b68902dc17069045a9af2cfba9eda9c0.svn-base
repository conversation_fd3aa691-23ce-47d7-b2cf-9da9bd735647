# IFP 前端开发手册

## 一、概述

本开发手册旨在帮助前端开发人员和相关用户了解和正确使用前端界面及相关功能并提供一个可供参考的前端标准化开发文档。

## 二、工具及知识要求

**推荐开发工具：`VScode`**

浏览器：推荐使用最新版本的 **Chrome**、Firefox、Safari 等现代浏览器，以确保最佳的性能和兼容性。

<!-- 前置知识：[Vue 风格指南](https://v2.cn.vuejs.org/v2/style-guide/index.html/)<a href="https://developer.mozilla.org/zh-CN/docs/Learn" target="_blank">HTML、CSS、JavaScript</a>、<a href="https://v2.cn.vuejs.org/v2/guide/" target="_blank">Vue(2)</a>、<a href="https://element.eleme.cn/#/zh-CN" target="_blank">ElementUI</a> -->
前置知识：[HTML、CSS、JavaScript](https://developer.mozilla.org/zh-CN/docs/Learn/)、[Vue(2)](https://v2.cn.vuejs.org/v2/guide/)、[ElementUI](https://element.eleme.cn/#/zh-CN/)，**上述几个必会，否则前端开发难以展开。**

进阶：[Vue(3)](https://cn.vuejs.org/)、[ElementPlus](https://element-plus.org/zh-CN/")、[TypeScript](https://ts.nodejs.cn/")、[NodeJs](https://nodejs.cn/)，上述可选学，学会可加强对于前端的理解。

## 三、开发规范

对于elementUI的大部分组件都做了二次封装，封装的文件夹为`/web/iofp/mixins/components/`,具体使用可参考上面两个文档`前端UI API`及`前端UI Demo`。

公司当前前端模块化开发遵循AMD规范，即使用requirejs进行模块化开发，参考[requirejs](http://requirejs.org/)。

IFP 页面采用 vue 组件化开发，`页面即组件`

必须符合以下规范

* js 必须符合 AMD 规范
* 页面必须可以能单独访问
* 控制器 js 不能强依赖 url
* 同一项目下所有页面中只能引入一个js
* 页面独有的 css 只能以 css! 的方式导入

* js 与 html 同名，且在同一目录下
* `body` 节点下必须且只能存在一个 `div`
* 一般情况 `body` 无需配置 `controller`，除非 js 与 html 的文件名不同

### 示例

example.html

```HTML
<body>
    <div>{{msg}}</div>
    <script src="/ifp/starter.js"></script>
</body>
```

example.js

```Javascript
define(function(){
    return {
        el:"#app",
        data:function(){
            return {
                msg:"Hello World!"
            }
        }
    }
})
```

<!-- 关于 kyadmin 的文档 请访问<a href="/kyadmin/docs/index.html">kyadmin-docs<a> -->

<!-- > 所有文档采用 markdown 格式编写 -->

以上操作手册仅供参考，具体的操作和功能可能会因前端应用业务的不同而有所差异。在使用过程中，如有任何问题，请及时联系相关技术人员进行解决。
