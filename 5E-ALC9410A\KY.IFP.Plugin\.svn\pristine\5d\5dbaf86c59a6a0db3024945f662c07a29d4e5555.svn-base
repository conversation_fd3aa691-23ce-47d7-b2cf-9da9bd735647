;define([
	"controllers/base","controls/grid","jclass",
	"jquery","lib/kjlib/mergetable/mergetable"
],function(base,Grid,jclass,$){
	return jclass(base,{
		name:"mergetable",
		genValue:function(){
			return [
				{gid:F.util.uuid(),project:"项目1",module:"模块1",function:"功能1",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目1",module:"模块1",function:"功能2",remark1:"备注1",remark2:"备注1"},
				{gid:F.util.uuid(),project:"项目1",module:"模块2",function:"功能1",remark1:"备注1",remark2:"备注1"},
				{gid:F.util.uuid(),project:"项目1",module:"模块2",function:"功能2",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目2",module:"模块1",function:"功能1",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目2",module:"模块1",function:"功能2",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目2",module:"模块1",function:"功能3",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目2",module:"模块2",function:"功能1",remark1:"备注1",remark2:"备注1"},
				{gid:F.util.uuid(),project:"项目2",module:"模块3",function:"功能1",remark1:"备注1",remark2:"备注1"},
				{gid:F.util.uuid(),project:"项目2",module:"模块3",function:"功能2",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目3",module:"模块1",function:"功能1",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目3",module:"模块1",function:"功能1",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目3",module:"模块1",function:"功能2",remark1:"备注1",remark2:"备注2"},
				{gid:F.util.uuid(),project:"项目3",module:"模块1",function:"功能3",remark1:"备注1",remark2:"备注2"},
			];
		},
		reset:function(){
			this.destroyGrid();
			var $container = $(".layout-c").empty();
			$table = $("<table class='gridtable layout-full' id='table1'></table>").appendTo($container);
			$table.html([
				`<thead><tr><th>序号</th><th>项目</th><th>模块</th><th>功能</th><th>备注1</th><th>备注2</th></tr></thead><tbody>`,
				$.map(this.genValue(),function(item,i){
					return `<tr><td>${i+1}</td><td>${item.project}</td>
					<td>${item.module}</td><td>${item.function}</td><td>${item.remark1}</td><td>${item.remark2}</td></tr>`
				}).join(""),
				`</tbody>`
			].join(""));
			$.layout();
		},
		destroyGrid:function(){
			this.controls["table1"]&&this.controls["table1"].destroy();
		},
		createGrid:function(){
			var $table = $("#table1");
			if($table.length&&$table.data("control")){
				return $.bootoast.danger("已存在控件");
			}
			var $container = $(".layout-c").empty();
			
			$table = $("<table id='table1'></table>").appendTo($container);
			
			var grid = new Grid($table,{
				colNames : [ '项目', '模块', '功能', '备注1', '备注2' ],
				cellEdit:true,
				colModel : [ 
					{name : 'project',index : 'project',editable:false,sortable : false,align : "center"},
					{name : 'module',index : 'module',editable:false,sortable : false,align : "center"}, 
					{name : 'function',index : 'function',editable:false,sortable : false,align : "center"}, 
					{name : 'remark1',index : 'remark1',editable:false,sortable : false,align : "center"}, 
					{name : 'remark2',index : 'remark2',editable:false,sortable : false,align : "center"}
				],
				afterRender:()=>{
					grid.value(this.genValue());
				}
			},this);
			$.layout();
		},
		getTalbe : function(){
			var table = $("table.gridtable");
			if(table.length==0){
				table = this.controls.table1.$container;
			}
			return table;
		},
		bindEvent:function(){
			this.bind("btnMergeRow","click",function(){
				var table = this.getTalbe.call(this);
				if(table.hasClass("gridtable")){
					table.mergetable("row",[1,2,3]);
				}else{
					table.mergetable("row",[1,2,3]);
				}
				
			},this)
			
			this.bind("btnMergeCol","click",function(){
				this.getTalbe.call(this).mergetable("col",4,5);
			},this)
			
			this.bind("btnReset","click",this.reset,this);
			
			this.bind("btnCreate","click",this.createGrid,this);
		},
		
		onLoad:function(){
			this.reset();
			this.bindEvent();
		}
	})
});