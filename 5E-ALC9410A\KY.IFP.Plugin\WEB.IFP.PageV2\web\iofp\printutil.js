﻿define(["iofp/api", "platform/vue", "iofp/util", "lodop"], function (API, pVue, util, LodopFuncs) {

    return {
		/**
		 * btnId   打印按钮的ID
		 * barList 条码列表
		 *		   [{code:'40000005684000000372',title:'碘化钾AR500ml'},{code:'40000005684000000373',title:'碘化钾AR800ml'}]
		 *		   或
		 *		   ['40000005684000000372','40000005684000000373']
		 */
		printBarCode: function (btnId, barList) {
			var _this = this;

			if (barList == undefined || barList == null || barList.length == 0) {
				return;
			}

			var param = {
				Pageurl: util.urlGetPageID() || (document.location.pathname + document.location.search),
				Btnid: btnId
			}
			//首先查找打印设置
			API.GetAction("API/IFP/www/sys/BarCodePrintAPI/Select", param).then(x => {
				var width = x.Pagewidth;	//纸张宽度
				var height = x.Pageheight;	//纸张高度
				var top = x.Toppadding;		//“条码顶端”与“打印区域顶部”的距离
				var left = x.Leftpadding;	//“条码左边”与“打印区域左边”的距离
				var barwidth = x.Barwidth;	//条码宽度
				var barheight = x.Barheight;//条码高度
				var formater = x.Formater;	//条码格式，128A，128B，128C，128Auto，QRCode等
				var preview = x.Preview

				//启动打印
				LodopFuncs.loadLodop().then(function (LODOP) {
					LODOP.PRINT_INIT("条码打印");
					LODOP.SET_PRINT_PAGESIZE(1, width + "mm", height + "mm");
					for (var i = 0; i < barList.length; i++) {
						LODOP.NewPage();
						if (typeof (barList[i]) == "object") {
							var barLeft = _this.getBarLeft(barList[i].title, width);
							LODOP.ADD_PRINT_HTM("2mm", barLeft + "mm", width + "mm", "8mm", barList[i].title);
							LODOP.ADD_PRINT_BARCODE(top + "mm", left + "mm", barwidth + "mm", barheight + "mm", formater, barList[i].code);
						} else {
							LODOP.ADD_PRINT_BARCODE(top + "mm", left + "mm", barwidth + "mm", barheight + "mm", formater, barList[i]);
						}
					}
					if (preview) {
						LODOP.PREVIEW();
					} else {
						LODOP.PRINT();
					}
				});
			}).catch(e => {
				_this.$message.error(e);
			});
		},

		/**
		 * 根据条码内容和条码纸宽度，确定二维码标题左边距
		 */
		getBarLeft: function (code, width) {
			//条码长度（汉字算2个，字母数字算1个）
			var len = 0;
			for (var i = 0; i < code.length; i++) {
				var a = code.charAt(i);
				if (a.match(/[^\x00-\xff]/ig) != null) {
					len += 2;
				} else {
					len += 1;
				}
			}
			//每个条码字符的宽度（mm）
			var cwidth = 2;
			var left = (width - cwidth * len) / 2;
			return left < 2 ? 2 : left;
		}
    }
});