﻿using COM.IFP.Common;
using LinqToDB;
using LinqToDB.Data;
using System;
using System.Collections.Generic;
using System.Data.SqlTypes;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace COM.IFP.LinqDB
{
    #region 数据字段
    /// <summary>
    /// 字段接口
    /// </summary>
    public interface IField :INullable, IComparable, IConvertible
    {
        /*
        1、继承INullable是因为创建DataParameter时会先行判断INullable，然后才进行进一步的类型转换。
        2、继承IConvertible是为了类型不符时的常规转换，除GUID、byte[]外常见简单类型都有实现该接口。
        */        

        public bool HasInput { get; }
        public bool HasValue { get; }

        public Type IType { get; }

        public object Input { get; }
        public object Value { get; }

        public Match Match { get; }
        public Order Order { get; }
        public Merge Merge { get; }
        
        /// <summary>
        /// 
        /// </summary>
        public string Group { get; }
        public string Index { get; }
        public string Which { get; }
    }

    /// <summary>
    /// 数据字段
    /// </summary>
    /// <typeparam name="T"></typeparam>
    [JsonConverter(typeof(FieldConverter))]
    public struct Field<T> : IField where T : IComparable, IConvertible
    {
        /*
        1、用Field存储数据库字段时，必须明确指定其DataType，否则某些情况下可能导致创建DataParameter失败。
        */

        private string group;
        private string index;

        public bool HasValue { get; internal set; }
        public bool HasInput { get; internal set; }

        public Type IType { get => typeof(T); }

        public object Input { get; internal set; }
        public T Value { get; internal set; }

        /// <summary>
        /// 匹配模式
        /// </summary>
        [JsonConverter(typeof(MatchConverter))]
        public Match Match { get; set; }
        /// <summary>
        /// 排序顺序
        /// </summary>
        public Order Order { get; set; }
        /// <summary>
        /// 组合模式
        /// </summary>
        public Merge Merge { get; set; }

        /// <summary>
        /// 组合编码
        /// </summary>
        public string Group { get => string.IsNullOrWhiteSpace(group) ? "0" : group; set => group = string.IsNullOrWhiteSpace(value) ? "0" : value; }
        /// <summary>
        /// 优先级别
        /// </summary>
        public string Index { get => string.IsNullOrWhiteSpace(index) ? "0" : index; set => index = string.IsNullOrWhiteSpace(value) ? "0" : value; }
        /// <summary>
        /// 指定成员
        /// </summary>
        public string Which { get; set; }

        #region 构造函数
        public Field(T value) : this(value, value) { }

        public Field(object input, T value)
        {
            Input = input;
            HasInput = true;

            Value = value;
            HasValue = true;

            Match = Match.EQU;
            Order = Order.NONE;
            Merge = Merge.AND;

            group = "0";
            index = "0";
            Which = null;
        }
        #endregion

        #region 接口实现

        #region IField
        object IField.Value => Value;
        public bool IsNull => !HasValue;
        #endregion

        #region IComparable
        public int CompareTo(object other)
        {
            return (other is IField field) ? Value.CompareTo(field.Value) : Value.CompareTo(other); //这个地方可能需要优化
        }
        #endregion

        #region IConvertible
        public TypeCode GetTypeCode() => TypeCode.Object;
        public bool ToBoolean(IFormatProvider provider) => Value.ToBoolean(provider);
        public byte ToByte(IFormatProvider provider) => Value.ToByte(provider);
        public char ToChar(IFormatProvider provider) => Value.ToChar(provider);
        public DateTime ToDateTime(IFormatProvider provider) => HasValue ? Value.ToDateTime(provider) : DateTime.Now;   //数据库时间范围应该是从197*年开始而非DateTime.MinValue
        public decimal ToDecimal(IFormatProvider provider) => Value.ToDecimal(provider);
        public double ToDouble(IFormatProvider provider) => Value.ToDouble(provider);
        public short ToInt16(IFormatProvider provider) => Value.ToInt16(provider);
        public int ToInt32(IFormatProvider provider) => Value.ToInt32(provider);
        public long ToInt64(IFormatProvider provider) => Value.ToInt64(provider);
        public sbyte ToSByte(IFormatProvider provider) => Value.ToSByte(provider);
        public float ToSingle(IFormatProvider provider) => Value.ToSingle(provider);
        public string ToString(IFormatProvider provider) => Value.ToString(provider);
        public object ToType(Type conversionType, IFormatProvider provider) => Value.ToType(conversionType, provider);
        public ushort ToUInt16(IFormatProvider provider) => Value.ToUInt16(provider);
        public uint ToUInt32(IFormatProvider provider) => Value.ToUInt32(provider);
        public ulong ToUInt64(IFormatProvider provider) => Value.ToUInt64(provider);
        #endregion

        #endregion

        #region 方法重载
        public override bool Equals(object other)
        {
            if (!HasValue) return other == null;
            if (other == null) return false;
            if (other is Field<T> field)
            {
                return Value.Equals(field.Value);
            }
            else
            {
                return Value.Equals(other);
            }
        }
        public override int GetHashCode()
        {
            return HasValue ? Value.GetHashCode() : 0;
        }
        public override string ToString()
        {
            return IsNull ? null : Value.ToString();
        }
        public new Type GetType()
        {
            return typeof(T);
        }
        #endregion

        #region 转换重载
        public static implicit operator Field<T>(T value)
        {
            return new Field<T>(value);
        }
        public static explicit operator T(Field<T> field)
        {
            return field.Value;
        }
        /// <summary>
        /// 可以将null赋值给除Field<string>外的Field<T>
        /// </summary>
        /// <param name="_"></param>
        public static implicit operator Field<T>(DBNull _)
        {
            return default;
        }
        //public static implicit operator DataParameter(Field<T> field)
        //{
        //    return new DataParameter() { Value = field.HasValue ? field.Value : null };
        //}
        //public static implicit operator Field<T>(DBNull value)
        //{
        //    return default;
        //}
        //public static implicit operator DBNull(Field<T> field)
        //{
        //    return field.HasValue ? null : DBNull.Value;
        //}
        #endregion

        #region 运算重载
        /// <summary>
        /// 可以将null与除Field<string>外的Field<T>执行==操作
        /// </summary>
        /// <param name="left"></param>
        /// <param name="_"></param>
        /// <returns></returns>
        public static bool operator ==(Field<T> left, DBNull _)
        {
            return left.IsNull;
        }
        /// <summary>
        /// 可以将null与除Field<string>外的Field<T>执行!=操作
        /// </summary>
        /// <param name="left"></param>
        /// <param name="_"></param>
        /// <returns></returns>
        public static bool operator !=(Field<T> left, DBNull _)
        {
            return !left.IsNull;
        }
        public static bool operator ==(Field<T> left, Field<T> right)
        {
            return left.Equals(right);
        }
        public static bool operator !=(Field<T> left, Field<T> right)
        {
            return !(left == right);
        }
        public static bool operator <(Field<T> left, Field<T> right)
        {
            return left.Value.CompareTo(right.Value) < 0;
        }
        public static bool operator <=(Field<T> left, Field<T> right)
        {
            return left.Value.CompareTo(right.Value) <= 0;
        }
        public static bool operator >(Field<T> left, Field<T> right)
        {
            return left.Value.CompareTo(right.Value) > 0;
        }
        public static bool operator >=(Field<T> left, Field<T> right)
        {
            return left.Value.CompareTo(right.Value) >= 0;
        }

        public static bool operator ==(Field<T> left, T right)
        {
            return left.Equals(right);
        }
        public static bool operator !=(Field<T> left, T right)
        {
            return !(left == right);
        }
        public static bool operator <(Field<T> left, T right)
        {
            return left.Value.CompareTo(right) < 0;
        }
        public static bool operator <=(Field<T> left, T right)
        {
            return left.Value.CompareTo(right) <= 0;
        }
        public static bool operator >(Field<T> left, T right)
        {
            return left.Value.CompareTo(right) > 0;
        }
        public static bool operator >=(Field<T> left, T right)
        {
            return left.Value.CompareTo(right) >= 0;
        }
        #endregion
    }

    /// <summary>
    /// 静态字段
    /// </summary>
    public static class Field
    {
        /// <summary>
        /// 后台创建IN查询
        /// </summary>
        /// <typeparam name="T">字段类型</typeparam>
        /// <param name="input">原始集合</param>
        /// <returns></returns>
        public static Field<T> Build<T>(List<T> input) where T : IComparable, IConvertible
        {
            var field = new Field<T>();
            field.Match = Match.BIN;
            field.Input = input;
            field.HasInput = true;
            return field;
        }

        /// <summary>
        /// 后台创建IN查询
        /// </summary>
        /// <typeparam name="T">字段类型</typeparam>
        /// <param name="input">原始集合</param>
        /// <returns></returns>
        public static Field<T> Build<T>(List<Field<T>> input) where T : IComparable, IConvertible
        {
            var field = new Field<T>();
            field.Match = Match.BIN;
            field.Input = input;
            field.HasInput = true;
            return field;
        }

        /// <summary>
        /// 后台创建IN查询
        /// </summary>
        /// <typeparam name="X">原始类型</typeparam>
        /// <typeparam name="T">字段类型</typeparam>
        /// <param name="input">原始集合</param>
        /// <param name="which">转换委托</param>
        /// <returns></returns>
        public static Field<T> Build<X, T>(List<X> input, Func<X, Field<T>> which) where T : IComparable, IConvertible
        {
            var field = new Field<T>();
            field.Match = Match.BIN;
            field.Input = input.Select(x => which(x)).Where(x => !x.IsNull).Select(x => x.Value).ToList();
            field.HasInput = true;
            return field;
        }     
    }
    #endregion

    #region 枚举定义
    /// <summary>
    /// 比较类型
    /// </summary>
    [JsonConverter(typeof(MatchConverter))]
    public enum Match
    {
        /// <summary>
        /// 无效
        /// </summary>
        NON = -1,
        /// <summary>
        /// 等于
        /// </summary>
        EQU = 0,
        /// <summary>
        /// 不等于
        /// </summary>
        NEQ = 1,
        /// <summary>
        /// 小于
        /// </summary>
        LSS = 2,
        /// <summary>
        /// 小于等于
        /// </summary>
        LEQ = 3,
        /// <summary>
        /// 大于
        /// </summary>
        GTR = 4,
        /// <summary>
        /// 大于等于
        /// </summary>
        GEQ = 5,
        /// <summary>
        /// （字符串）包含
        /// </summary>
        HAS = 6,
        /// <summary>
        /// （字符串）打头
        /// </summary>
        PRE = 7,
        /// <summary>
        /// （字符串）结尾
        /// </summary>
        END = 8,
        /// <summary>
        /// IS NULL
        /// </summary>
        NUL = 9,
        /// <summary>
        /// IS NOT NULL
        /// </summary>
        ANY = 10,
        /// <summary>
        /// IN（被包含）
        /// </summary>
        BIN = 11
    }
    /// <summary>
    /// 排序类型
    /// </summary>
    public enum Order
    {
        /// <summary>
        /// 不排序
        /// </summary>
        NONE = 0,
        /// <summary>
        /// 升序
        /// </summary>
        ASC = 1,
        /// <summary>
        /// 降序
        /// </summary>
        DESC = 2,
    }
    /// <summary>
    /// 组合规则
    /// </summary>
    public enum Merge
    {
        /// <summary>
        /// 与
        /// </summary>
        AND = 0,
        /// <summary>
        /// 并
        /// </summary>
        OR = 1,
        /// <summary>
        /// 非
        /// </summary>
        NOT = 2
    }
    #endregion

    #region 序列转换
    internal class FieldConverter : JsonConverterFactory
    {
        class JsonConverter<T> : System.Text.Json.Serialization.JsonConverter<Field<T>> where T : IComparable, IConvertible
        {
            public override Field<T> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
            {
                if (typeof(T) != typeof(string) && reader.TokenType == JsonTokenType.String && string.IsNullOrWhiteSpace(reader.GetString())) return default;
                var result = new Field<T>();
                switch (reader.TokenType)
                {
                    case JsonTokenType.None:
                    case JsonTokenType.Null:
                        return default;
                    case JsonTokenType.String:
                    case JsonTokenType.Number:
                    case JsonTokenType.True:
                    case JsonTokenType.False:
                        return new Field<T>(JsonSerializer.Deserialize<T>(ref reader, options));
                    default:
                        var avatar = JsonSerializer.Deserialize<JsonElement>(ref reader, options);
                        if (avatar.TryGetProperty("Value", out JsonElement value) && value.ValueKind != JsonValueKind.Undefined && value.ValueKind != JsonValueKind.Null)
                        {
                            result.Input = value;
                            result.HasInput = true;                            
                            try
                            {
                                if (value.ValueKind != JsonValueKind.String || !string.IsNullOrWhiteSpace(value.GetString()))
                                {
                                    result.Value = JsonSerializer.Deserialize<T>(value.GetRawText(), options);
                                    result.HasValue = true;
                                }
                            }
                            catch { }
                        }
                        if (avatar.TryGetProperty("Match", out JsonElement match) && match.ValueKind != JsonValueKind.Undefined && match.ValueKind != JsonValueKind.Null)
                        {
                            var setting = new JsonSerializerOptions();
                            setting.Converters.Add(new MatchConverter());
                            result.Match = JsonSerializer.Deserialize<Match>(match.GetRawText(), setting);
                        }
                        if (avatar.TryGetProperty("Merge", out JsonElement merge) && merge.ValueKind != JsonValueKind.Undefined && merge.ValueKind != JsonValueKind.Null)
                        {
                            result.Merge = JsonSerializer.Deserialize<Merge>(merge.GetRawText(), options);
                        }
                        if (avatar.TryGetProperty("Order", out JsonElement order) && order.ValueKind != JsonValueKind.Undefined && order.ValueKind != JsonValueKind.Null)
                        {
                            result.Order = JsonSerializer.Deserialize<Order>(order.GetRawText(), options);
                        }
                        if (avatar.TryGetProperty("Group", out JsonElement group) && group.ValueKind != JsonValueKind.Undefined && group.ValueKind != JsonValueKind.Null)
                        {
                            result.Group = group.GetString();
                        }
                        if (avatar.TryGetProperty("Index", out JsonElement index) && index.ValueKind != JsonValueKind.Undefined && index.ValueKind != JsonValueKind.Null)
                        {
                            result.Index = index.GetString();
                        }
                        if (avatar.TryGetProperty("Which", out JsonElement which) && which.ValueKind != JsonValueKind.Undefined && which.ValueKind != JsonValueKind.Null)
                        {
                            result.Which = which.GetString();
                        }
                        return result;
                }
            }

            public override void Write(Utf8JsonWriter writer, Field<T> value, JsonSerializerOptions options)
            {
                if (!value.HasValue)
                {
                    writer.WriteNullValue();    //这里会导致空值过滤无效，暂时没想到解决办法。
                }
                else
                {
                    JsonSerializer.Serialize(writer, value.Value);
                }
            }
        }

        public override bool CanConvert(Type typeToConvert)
        {
            return typeToConvert.IsGenericType && typeToConvert.GetGenericTypeDefinition() == typeof(Field<>);
        }

        public override JsonConverter CreateConverter(Type typeToConvert, JsonSerializerOptions options)
        {
            return (JsonConverter)Activator.CreateInstance(typeof(JsonConverter<>).MakeGenericType(typeToConvert.GetGenericArguments()));
        }
    }

    internal class MatchConverter : JsonConverter<Match>
    {
        static readonly Dictionary<string, Match> reader = new Dictionary<string, Match>(StringComparer.OrdinalIgnoreCase)
        {
            {"NON",Match.NON },
            {"EQU",Match.EQU },
            {"NEQ",Match.NEQ },
            {"LSS",Match.LSS },
            {"LEQ",Match.LEQ },
            {"GTR",Match.GTR },
            {"GEQ",Match.GEQ },
            {"HAS",Match.HAS },
            {"PRE",Match.PRE },
            {"END",Match.END },
            {"NUL",Match.NUL },
            {"ANY",Match.ANY },
            {"BIN",Match.BIN },

            {"None",Match.NON },
            {"==",Match.EQU },
            {"!=",Match.NEQ },
            {"<",Match.LSS },
            {"<=",Match.LEQ },
            {">",Match.GTR },
            {">=",Match.GEQ },
            {"%*%",Match.HAS },
            {"Contains",Match.HAS },
            {"*%",Match.PRE },
            {"StartsWith",Match.PRE },
            {"%*",Match.END },
            {"EndsWith",Match.END },
            {"Null",Match.NUL },
            {"IsNull",Match.NUL },
            {"Is Null",Match.NUL },
            {"NotNull",Match.ANY },
            {"IsNotNull",Match.ANY },
            {"Is Not Null",Match.ANY },
            {"In",Match.BIN},
            {"IsIn",Match.BIN},
            {"Is In",Match.BIN},
        };

        static readonly Dictionary<Match, string> writer = new Dictionary<Match, string>()
        {
            {Match.EQU, "==" },
            {Match.NEQ,"!=" },
            {Match.LSS,"<" },
            {Match.LEQ,"<=" },
            {Match.GTR,">" },
            {Match.GEQ,">=" },
            {Match.HAS,"%*%" },
            {Match.PRE,"*%" },
            {Match.END,"%*" },
            {Match.BIN,"In" },
        };

        public override Match Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return reader.TokenType switch
            {
                JsonTokenType.None or JsonTokenType.Null => Match.EQU,
                JsonTokenType.Number => (Match)reader.GetInt32(),
                JsonTokenType.String => MatchConverter.reader[reader.GetString()],
                _ => throw new InvalidCastException(),
            };
        }

        public override void Write(Utf8JsonWriter writer, Match value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(MatchConverter.writer[value]);
        }
    }
    #endregion
}