define([
    "iofp/util",
    "kyadmin/utils/util",
    "moment",
    "pagemessage"
], function (ifputil, util, moment, pagemessage) {
    return {
        inject: {
            ifpDialog: { default: '' }
        },
        data() {
            return {
            }
        },
        filters: {
            formatDatetime(v, format) {
                return moment(v).format(format || 'yyyy-MM-DD HH:mm:ss');
            }
        },

        created() {
            // 所有 vue 对象 create 时都会先执行这里代码
            if (this.$options.$option) {
                const option = this.$options.$option;
            }

            /*
            if(this.$options.$option?.data?.length){
                return this.$store.dispatch("ywlx/addItems",this.$options.$option.data)
                .then(()=>this.$emitIFPLoad())
            }else{
                this.$emitIFPLoad()
            }
            */

            // 页面组件
            if (this.$isPageOrDetail()) {

                // 如果是在dialog中打开
                if (this.ifpDialog) {
                    // 传递 opened
                    this.ifpDialog.$on('opened', () => {
                        this.$emit('opened')
                    })
                    // 传递 open
                    this.ifpDialog.$on('open', () => {
                        this.$emit('open')
                    })
                }

                this.$on("close", (...args) => {
                    this.$close(...args);
                })
                this.$on("cancel", (...args) => {
                    this.$close(...args);
                })
            }
        },
        methods: {
            // 根节点才认为是页面
            $isPage() {
                return this.$root === this;
            },
            $isPageOrDetail() {
                return this.$options.$ISKYADMIN || this.$options.$KYADMINCOMP;
            },
            $getMenuID() {
                return util.queryString("pk")
            },
            $isMenuPage() {
                return util.queryString("_m") && util.queryString("pk")
            },
            $emitIFPLoad() {
                if (this.$options.$ifpload) { this.$options.$ifpload() }
                this.$emit("ifp-load")
            },
            $close(...args) {
                if (this.$isPageOrDetail()) {
                    if (this.$isPage()) {
                        if (window.parent.F) {//挂接模式，调用父窗口的pagemessage方法通知tab页签进行关闭
                            window.parent.F.pagemessage.send("close", util.queryString("_m"));
                        } else {//当前项目模式，调用pagemessage方法通知tab页签进行关闭
                            pagemessage.send("close", util.queryString("_m"))
                        }

                    } else {
                        if (["IFPDialog", "ElDialog"].includes(this.$parent.$options.name)) {
                            this.$parent.$emit("update:visible", false);
                        }
                    }
                }
            },
            $getPageOrCompVM(vm) {
                let current = vm || this;
                while (!(current.$options.$ISKYADMIN || current.$options.$KYADMINCOMP || current === this.$root)) {
                    current = current.$parent
                }
                return current;
            },
            $getOutRefid() {
                let refid;
                let vm = this.$getPageOrCompVM(this)

                for (let n in vm.$refs) {
                    if (vm.$refs[n] === this) {
                        refid = n;
                    }
                }

                return refid;
            },

            /**
             * 返回页面信息
             * @returns 
             */
            $getPageInfo() {
                let vm = this.$getPageOrCompVM();

                let rev = {
                    url: null,
                    id: null,
                    menuid: null,
                    menuurl: null,
                    isComponentPage: false,
                    js: null
                };

                if (vm.$options.$KYADMINCOMP) {
                    let info = vm.$options.$KYADMINCOMP || {}
                    rev.url = info.html || null;
                    rev.js = info.js || null;
                    rev.isComponentPage = true;
                } else {
                    let pathname = location.pathname;
                    if (pathname[pathname.length - 1] == "/") { pathname += "index.html"; }

                    rev.url = pathname + location.search + location.hash;
                    rev.menuid = util.queryString("_m") || null;
                    rev.menuurl = util.queryString("pk") || null;
                    if (rev.menuurl) {
                        rev.menuurl = ifputil.decodeBase64(rev.menuurl)
                    }
                }

                rev.id = vm.$IFPPageID || rev.menuurl || rev.url || rev.js;

                return rev;
            }
        }
    }
})