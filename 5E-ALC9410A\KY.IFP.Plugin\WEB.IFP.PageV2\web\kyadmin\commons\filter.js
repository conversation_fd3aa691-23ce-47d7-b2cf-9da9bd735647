//转换器
//全局过滤器
define(function(){
	// 一旦使用过，不能再删除
	
	return {
		create:function(str,controller){
			var rev = function(){return true};
			if(this[str] && typeof this[str] == "function"){
				rev = this[str];
			}
			else if(controller&&controller[str]&&typeof controller[str] == "function"){
				rev = controller[str];
			} else{
				if(str.indexOf("return")==-1){
					str = "return "+str;
				}
				rev = new Function("id","item","controller","filter",str);
			}
			return rev;
		},
		
		//数据上报状态
		useSBZT:function(item){
			return !item.sjzt;
		},
		
		//不使用过滤器，全部返回true
		nofilter:function(item){
			return true;
		},
		
		//过滤业务类型
		useywlx:function(item){
			if(item.ywlx=="4005"){
				return (item.zfbz !== 1 && item.sfsjbc==1);
			}else{
				return item.zfbz !== 1;
			}
		},
		
		//班次下拉框，实际班次和非实际班次都可选（目前只有排班管理在使用这个设置）
		bcfilter:function(item){
			return item.zfbz !== 1;
		},

		//判断
		usesjbc:function(item){
			return item.zfbz !== 1 && item.sfsjbc == 1;
		}
	}
});