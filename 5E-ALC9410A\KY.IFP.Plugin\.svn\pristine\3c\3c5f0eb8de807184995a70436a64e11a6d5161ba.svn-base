﻿define([
    "iofp/common",
    "jquery",
    "jquery.ky.dialog"
], function (common, $) {
    //daiabin此页面没使用
    return {
        el: "#app",
        data: function () {
            return {
                currentUser: {
                    //用户名
                    userName: "undefined",
                    //角色列表
                    roleList: [],
                    //DeptFullName
                    deptFullName:"",
                    //用户表主键
                    Gid: ""
                    
                },
                displayMsg: {
                    message: "",
                    //用户可用的菜单列表
                    menuNameList: []
                }
            }

        },
        created: function () {
            var _this = this;
            common.getLoginUser().then((user) => {
                _this.currentUser.userName = user.UsiName;
                if (_this.currentUser.userName == null ||
                    _this.currentUser.userName == undefined ||
                    _this.currentUser.userName == "") {
                    _this.currentUser.userName = "未设置用户名"
                }
                _this.currentUser.deptFullName = user.DeptFullName;
                if (_this.currentUser.deptFullName == null ||
                    _this.currentUser.deptFullName == undefined ||
                    _this.currentUser.deptFullName == "") {
                    _this.currentUser.deptFullName="未设置部门"
                }
                
                $.ajax({
                    url: "/API/IFP/Rights/User/Welcome",
                    data: JSON.stringify({}),
                    type: "post",
                    dataType: "text",
                    contentType: "application",
                    success: function (msg) {
                        var tmp = JSON.parse(msg);
                        _this.displayMsg.message = tmp["message"];
                        _this.displayMsg.menuNameList = tmp["menuNameList"];
                    },
                    error: function () {
                        $.bootoast.danger("服务器异常。");
                        //F.util.hideWait();//关闭等待
                    }
                });
                
            }
            );
            setTimeout(() => { window.close(); }, 1000);
        },
        methods: {

        }
    }
})