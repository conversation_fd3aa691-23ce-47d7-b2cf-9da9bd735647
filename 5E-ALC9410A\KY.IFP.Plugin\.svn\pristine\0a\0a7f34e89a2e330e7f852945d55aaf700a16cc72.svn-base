/* 首页tabs组件 */
define(["jquery","css!./menutabs2.css"],function($){
	// body 内容
	var debug = false
	return {
		el:"#app",
		props:{
			items:{default:function(){return []}}
		},
		
		created(){
			this.items.forEach(item=>this.tabs.push(this.createTabItem(item)));
		},

		data: function () {
			return {
				tabs:[],
				boxWidth:0,
				innerWidth:0,
				scrollLeft:0,
				currentid:null,
				currentTab:null,
				renderlist:[] // 已渲染列表
			}
		},
		mounted:function(){
			this.updateScroll();
		},
		
		computed:{
			current(){
				return this.tabs.find(item=>item.id)
			},
			disabledLeft:function(){
				return this.scrollLeft == 0;
			},
			disabledRight:function(){
				return this.innerWidth - this.scrollLeft - this.boxWidth <=0;
			},
			hasScroll:function(){
				return this.boxWidth-this.innerWidth<0
			},
			step:function(){
				return this.boxWidth*0.8 || 100;
			},
			renderitems(){
				return this.tabs.filter(item=>item.rendered)
			}
		},

		methods:{
			createTabItem(data){
				const tabitem = Object.assign({
					id:null,
					title:"页签",
					url:null,
					rendered:false,
					noClose:false,
					module:null
				},data)

				
				if(tabitem.url){
					tabitem.url+=tabitem.url.indexOf("?")==-1?"?":"&";
					tabitem.url+="_m="+tabitem.id;
					if(window.currentVersion){
						tabitem.url+="&ver=" + window.currentVersion;
					}
				}

				return tabitem;
			},
			showTab:function(){
				debug&&console.log("showTab");
				this.$nextTick(function(){
					var activeEl = $("li.active",this.$refs["listwrap"]);
					if(activeEl.length){
						var l = activeEl.position().left;
						if(l-20<0){
							this.scrollTo(l-20);
						}
						var w = activeEl.width();
						if(l>this.boxWidth-w){
							this.scrollTo(l-this.boxWidth+w-20);
						}
					}
				});
				//this.selectModule = this.current.module||"";
			},
			updateScroll:function(){
				this.$nextTick(function(){
					var wraper = $(this.$refs["listwrap"]);
					var box = wraper.children().eq(0);
					this.boxWidth=wraper.width();
					this.innerWidth=box.width();
					this.scrollLeft = wraper.scrollLeft();
				});
			},
			getTabById:function(id){
				return this.tabs.find(function(item){return item.id == id});
			},
			selectTab:function(item){
				debug&&console.log("selectTab");
				this.currentid = item.id;
				this.getTabById(item.id).rendered=true;
				this.updateScroll();
				this.showTab();
				this.$emit("showtab",this.current.module||"");
			},
			selectLast:function(){
				this.currentid = this.tabs.length&&(this.tabs[this.tabs.length-1])||""
			},
			getNextTab:function(id){
				//如果非最后一个，则取后一个，否则取前一个，若只有一个则直接返回空
				
				let index = this.tabs.findIndex(item=>item.id==id);
				return this.tabs[index+1]||this.tabs[index-1]||null;
			},
			deleteTab:function(id){
				//找到下一个页签
				var nextTab = this.getNextTab(id);
				this.currentid = nextTab && nextTab.id || "";
				let index = this.tabs.findIndex(item=>item.id==id);
				this.tabs.splice(index,1);
				this.updateScroll();
				this.showTab();
			},

			addTab:function(data,selected){
				if(!data.id){throw "addTab must has id!"}
				var item = this.getTabById(data.id);
				if(!item){
					item = this.createTabItem(data)
					this.tabs.push(item);
				}
				if(selected){
					this.selectTab(item);
				}
				this.updateScroll();
				this.showTab();
			},

			scrollTo:function(v,useanimate){
				var el = $(this.$refs["listwrap"]);
				el.scrollLeft(this.scrollLeft + v);
				this.updateScroll();
			},
			
			onScroll:function(e){
				this.scrollTo(e.deltaY,false);
			},
			scrollLeftClick:function(){
				this.scrollTo(this.step*-1);
			},
			scrollRightClick:function(){
				this.scrollTo(this.step);
			}
		}
	}
})