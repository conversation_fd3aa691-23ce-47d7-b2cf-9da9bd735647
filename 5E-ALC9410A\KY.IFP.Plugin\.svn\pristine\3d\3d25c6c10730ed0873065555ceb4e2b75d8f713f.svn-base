define(["require","jclass","jquery","util","dialog","configs/config.selector"],function(require,jclass,$,util,Dialog,SelectorConfig){
	
	var defaultOption = {
		url:null,
		width:800,
		height:600,
		closeByBackdrop:false,
		closable:true
	}
	
	return jclass(Object,{
		option:null,
		win:null,
		show:function(){
			this.win.open();
		},
		setCloseble:function(closeble){
			this.win.setCloseble(closeble);
		},
		close:function(){
			this.win.close();
		},
		callback:null,
		bind:function(type,fn){
			this.callback[type] = this.callback[type] || [];
			this.callback[type].push(fn);
		},
		getMessage:function(dialogRef){
			var url = this.option.url;
			if(url&&window.currentVersion){
				url+=url.indexOf("?")==-1?"?":"&";
				url+="ver=" + window.currentVersion;
			}
			return $('<iframe style="width:100%;height:100%;" src="'+ url +'"></iframe>');
		},
		createWindow:function(option){
			var _this = this;
			var opt = $.extend(true,{},option,{
				message:function(){
					return _this.getMessage.apply(_this,arguments)
				}
			});
			
			if((this.option!=true 
				&& !isNaN(this.option.height) && $(window).height()<this.option.height-40) ||
				!isNaN(this.option.width) && $(window).width()<this.option.width)
			{
				this.option.full = true;
			}
			
			return new Dialog(opt);
		},
		realize:function(){
			this.win.realize();
			var $modal = this.win.getModal();
			$modal.addClass("dialog-iframe");
			var $dialog = this.win.getModalDialog();
			var $body = this.win.$modalBody.children().children();
			var $modalContent = this.win.$modalContent;
			if(!this.option.title){
		        this.win.getModalHeader().hide();
			}
	        this.win.getModalFooter().hide();
			
			if(this.option.full){
				var fstyle = {
						width: "100%",
					    height: "100%",
					    margin: "0px"
					}
				$modal.css(fstyle);
				$dialog.css(fstyle);
				$modalContent.css(fstyle).css({
					"display": "flex",
    				"flex-direction": "column",
				});
				this.win.$modalBody.children().css(fstyle);
				this.win.$modalBody.children().children().css(fstyle);
				
				this.win.$modalHeader.addClass("layout-h");
				this.win.$modalFooter.addClass("layout-h");
				this.win.$modalBody.addClass("layout-c").css({
					flex: 1
				});
				
			}else{
				$dialog.width(this.option.width);
				$body.height(this.option.height);
			}
		},
		trigger:function(type,context,args){
			var _this = this;
			var rev = true;
			$.each(this.callback[type]||[],function(i,item){
				rev = rev && item.apply(context||_this,args)!== false;
			});
			return rev;
		},
		returnValue:function(v){
			if(arguments.length==0){
				return this.win.returnValue;
			}else{
				this.win.returnValue = v;
			}
		},
		title:function(v){
			if(arguments.length==0){
				return this.win.returnValue;
			}else{
				this.win.returnValue = v;
			}
		},
		init:function(option){
			if(option.type){
				var type = option.type;
				option = SelectorConfig.get(type,{dialogOption:util.delAttr(option,"type")}).dialogOption;
			}
			this.option = $.extend(true,{},defaultOption,option);
			this.callback = this.callback || {};

			var _this = this;
			
			$.each(["onshow","onshown","onhide","onhidden"],function(i,item){
				if(_this.option[item]){
					_this.bind(item,_this.option[item]);
					_this.option[item] = null;
				}
				
				_this.option[item] = function(){
					return _this.trigger(item,_this,arguments);
				};
			});
			
			this.parameter = option.parameter || null;
			
			this.id = util.genid();
			this.option.id = this.id;
			var url = this.option.url;
			this.option.url+=(url.indexOf("?")>-1?"&":"?")+"_w=" + this.id;
			
			this.win = this.createWindow(this.option);
			
			
			
			
	        Dialog.add(this.id,this);
	        
	        this.realize();
	        
			if(this.option.show !== false){
				this.show();
			}
		}
	});
});
