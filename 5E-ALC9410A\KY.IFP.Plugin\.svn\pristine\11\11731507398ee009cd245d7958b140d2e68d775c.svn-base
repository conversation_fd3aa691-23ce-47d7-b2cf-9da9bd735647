;define([
"require",
"controllers/base",
"jquery",
"jclass"],
function(require,base,$,jclass){
	return jclass(base,{
		name:"gssjInfo",
		openWin:function(){
			this.log.info("打开弹出页");
		},
		bindEvent:function(){
			var _this = this;		
			//this.bind("showBtn","click",this.queryObj,this);		
			//新增事件			
			this.bind("addBtn", "click", this.testObj,this);
			////修改事件			
			//this.bind("editBtn","click",this.editObj,this);		
			////删除事件			
			//this.bind("delBtn","click",this.delObj,this);
		},
		
		//test方法
		testObj: function () {
			let url = "/www/Excel/createExcel?id={'text':'1'}" ; //可以在路径中传递参数
			window.location.href = url;
			//F.ajax({
			//	url: "/www/Excel/createExcel",
			//	data: {},
			//	success: function (resultObj) {

			//		//if (resultObj.success) {
			//		//	_this.loadForm();
			//		//	$.bootoast.success("删除成功");
			//		//} else {
			//		//	$.bootoast.warning(resultObj.data);
			//		//}
			//	}
			//});
		},
		
		onLoad:function(){
			this.bindEvent();	
		}
	})
});