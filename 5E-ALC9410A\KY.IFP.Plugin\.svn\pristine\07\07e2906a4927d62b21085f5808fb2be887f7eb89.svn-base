﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Mapping;
using System;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{
    /// <summary>
    /// 矿点信息
    /// </summary>
	/// 
    [SugarTable("IFP_BS_YWDX4001")]
    public partial class YWDX4001 : BaseData<YWDX4001>
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public override Field<long> Gid { get; set; }

        /// <summary>
        /// 业务对象4002
        /// </summary>
        [SugarColumn(ColumnName = "GHDW4002", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Ghdw4002 { get; set; }

        /// <summary>
        /// 业务对象4006
        /// </summary>
        [SugarColumn(ColumnName = "FZ4006", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Fz4006 { get; set; }

        /// <summary>
        /// 年产量（万吨）
        /// </summary>
        [SugarColumn(ColumnName = "NCL", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,3)")]
        public Field<decimal> Ncl { get; set; }

        /// <summary>
        /// 坐标位置
        /// </summary>
        [SugarColumn(ColumnName = "ZBWZ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(36)")]
        public Field<string> Zbwz { get; set; }

        /// <summary>
        /// 所属地区枚举 --1015
        /// </summary>
        [SugarColumn(ColumnName = "SSDQ1015", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Ssdq1015 { get; set; }

        /// <summary>
        /// 存储矿点对应的编码
        /// </summary>
        [SugarColumn(ColumnName = "KDBM", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> Kdbm { get; set; }

        /// <summary>
        /// 所属区域
        /// </summary>
        [SugarColumn(ColumnName = "SSQY2010", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Ssqy2010 { get; set; }

        /// <summary>
        /// CHN化验周期
        /// </summary>
        [SugarColumn(ColumnName = "CHNHYZQ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Chnhyzq { get; set; }

        /// <summary>
        /// CHN最近化验时间
        /// </summary>
        [SugarColumn(ColumnName = "CHNZJHYSJ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "datetime")]
        public Field<DateTime> Chnzjhysj { get; set; }

        /// <summary>
        /// 汽车衡发货方标记
        /// </summary>
        [SugarColumn(ColumnName = "QCHFHFBJ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(50)")]
        public Field<string> Qchfhfbj { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(ColumnName = "CREATOR", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(128)")]
        public Field<string> Creator { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(3000)")]
        public Field<string> REMARK { get; set; }

        /// <summary>
        /// 运输方式多选，用于过滤
        /// </summary>
        [SugarColumn(ColumnName = "YSFS1001", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(256)")]
        public Field<string> YSFS1001 { get; set; }
    }

    //   [Table(Name = "IFP_BS_YWDX4001")]
    //public partial class YWDX4001 : BaseData<YWDX4001>
    //{
    //	/// <summary>
    //	/// 
    //	/// </summary>
    //	[Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.Int64, DbType = "bigint")]
    //	public override Field<long> Gid { get; set; }

    //	/// <summary>
    //	/// 业务对象4002
    //	/// </summary>
    //	[Column(Name = "GHDW4002", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Ghdw4002 { get; set; }

    //	/// <summary>
    //	/// 业务对象4006
    //	/// </summary>
    //	[Column(Name = "FZ4006", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Fz4006 { get; set; }

    //	/// <summary>
    //	/// 年产量（万吨）
    //	/// </summary>
    //	[Column(Name = "NCL", DataType = DataType.Decimal, DbType = "decimal(18,3)")]
    //	public Field<decimal> Ncl { get; set; }

    //	/// <summary>
    //	/// 坐标位置
    //	/// </summary>
    //	[Column(Name = "ZBWZ", DataType = DataType.NVarChar, DbType = "nvarchar(36)")]
    //	public Field<string> Zbwz { get; set; }

    //	/// <summary>
    //	/// 所属地区枚举 --1015
    //	/// </summary>
    //	[Column(Name = "SSDQ1015", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Ssdq1015 { get; set; }

    //	/// <summary>
    //	/// 存储矿点对应的编码
    //	/// </summary>
    //	[Column(Name = "KDBM", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //	public Field<string> Kdbm { get; set; }

    //	/// <summary>
    //	/// 所属区域
    //	/// </summary>
    //	[Column(Name = "SSQY2010", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Ssqy2010 { get; set; }

    //	/// <summary>
    //	/// CHN化验周期
    //	/// </summary>
    //	[Column(Name = "CHNHYZQ", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> Chnhyzq { get; set; }

    //	/// <summary>
    //	/// CHN最近化验时间
    //	/// </summary>
    //	[Column(Name = "CHNZJHYSJ", DataType = DataType.DateTime, DbType = "datetime")]
    //	public Field<DateTime> Chnzjhysj { get; set; }

    //	/// <summary>
    //	/// 汽车衡发货方标记
    //	/// </summary>
    //	[Column(Name = "QCHFHFBJ", DataType = DataType.NVarChar, DbType = "nvarchar(50)")]
    //	public Field<string> Qchfhfbj { get; set; }

    //	/// <summary>
    //	/// 创建人
    //	/// </summary>
    //	[Column(Name = "CREATOR", DataType = DataType.NVarChar, DbType = "nvarchar(128)")]
    //	public Field<string> Creator { get; set; }

    //	/// <summary>
    //	/// 备注
    //	/// </summary>
    //	[Column(Name = "REMARK", DataType = DataType.NVarChar, DbType = "nvarchar(3000)")]
    //	public Field<string> REMARK { get; set; }
    //       /// <summary>
    //       /// 运输方式多选，用于过滤
    //       /// </summary>
    //       [Column(Name = "YSFS1001", DbType = "nvarchar(256)")]
    //       public Field<string> YSFS1001 { get; set; }
    //   }


    /* 以下为对应的Json对象
	{
		Gid: null,
		Ghdw4002: null,		//业务对象4002
		Fz4006: null,		//业务对象4006
		Ncl: null,		//年产量（万吨）
		Zbwz: null,		//坐标位置
		Ssdq1015: null,		//所属地区枚举 --1015
		Kdbm: null,		//存储矿点对应的编码
		Ssqy2010: null,		//所属区域
		Chnhyzq: null,		//CHN化验周期
		Chnzjhysj: null,		//CHN最近化验时间
		Creator: null,           //创建人
	    REMARK: null,           //备注
	}
	*/
}
