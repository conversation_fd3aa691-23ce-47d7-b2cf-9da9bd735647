; define([
    "require",
    "controllers/base",
    "jquery",
    "jclass"],
    function (require, base, $, jclass, jobservice) {
        return jclass(base, {
            name: "conrnCreate",
            authuser: false,
            openWin: function () {
                this.log.info("打开弹出页");
            },
            bindEvent: function () {
                var _this = this;
                _this.bind("qdBtn", "click", this.qdFun, this);
            },

            qdFun: function () {
                var iframe = window.document.getElementById("tab");
                var cron = iframe.contentWindow.document.getElementById('cron').value;

                if (this.win) {
                    this.win.returnValue(cron);
                    this.win.close();
                }
            },


            //初始化页面
            initData: function () {
                var _this = this;

            },

            onLoad: function () {
                this.win = this.getDialogWindow();
                var param = this.win && this.win.parameter || {};
                this.initData();
                this.bindEvent();
            }
        })
    });
