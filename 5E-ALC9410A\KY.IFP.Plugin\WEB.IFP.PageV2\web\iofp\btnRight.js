/**
 * mixin：按钮权限
 */
define(["vue","iofp/util"],function(Vue,util){
    // 用来存放按钮列表
    /*
    const state = Vue.observable({ 
        btnRight: [] 
    })
    */
    
    return {
        data(){
            return {
                _btnRight:[]
            }
        },
        computed:{
            $btnRight(){
                return Object.fromEntries(this.$data._btnRight.map(item=>[
                    item,true
                ]))
            }
        },
        provide(){
            if(this.$isPageOrDetail.call()){
                return { 
                    btnRight: this.$data._btnRight 
                }
            }else{
                return {}
            }
        },
        created(){
            if(this.$isPageOrDetail()){

                //页面地址，例如：/pages/sys/liexian/index.html
                var basepage = util.urlGetPageID();
                //弹出页或子页面的URL
                var pageurl = this.$getPageInfo().menuurl;
                if (pageurl == null) {//pageurl == null 说明是子页面
                    pageurl = this.$getPageInfo().url;
                }

                this.$api.post("/API/IFP/Rights/User/GetHiddenBtnByUser", { 
                    basepage: basepage, 
                    pageurl: pageurl 
                }).then(keys=>{
                    this.$data._btnRight.splice(0,this.$data._btnRight.length,...keys)
                }).catch(e => {
                    this.$message.error(e);
                })
            }
        }
    }
})