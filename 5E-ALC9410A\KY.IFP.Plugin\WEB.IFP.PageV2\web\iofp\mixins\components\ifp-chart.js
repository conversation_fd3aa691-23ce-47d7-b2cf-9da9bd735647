define(["zutil",'util'],function(zutil,util){
    let echart;

    let defaultOption = {
        title:{
            x: 'center',
            y: 'top',
            text: '',//主标题文本，'\n'指定换行
        },
        legend:{
            top:"bottom"
        }
    }

    function getEcharts(){
        if(echart){return Promise.resolve(echart)}
        else{return new Promise((resolve,reject)=>{
            requirejs([
                "echarts",
                "iofp/mixins/components/ifp-echart-theme/echarts.theme.kjsoft"
            ],(echarts,theme)=>{
                resolve(echarts)
            },reject)
        })}
    }
    function format(options){
        let rev = zutil.extend(true,{},defaultOption,options)
        return rev;
    }

    function getEchartComponent(echarts){
        return {
            props:{
                autoFormat:{type:Boolean,default:false},
                minHeight:{default:"100px"},
                minWidth:{default:"200px"},
                option:{default(){
                    return {
                        tooltip:{
                            confine:true
                        },
                        title:{
                            textStyle:{
                                //color:"red"
                            },
                            left:"center",
                            top:"10",
                            text:"333"
                        },
                        series: []
                    }
                }}
            },
            watch:{
                option:{
                    handler: function (val, oldVal) {
                        this.setOption(this.renderOption,true);
						this?.echart?.resize();
                    },
                    deep: true
                }
            },
            computed:{
                renderOption(){
                    return this.autoFormat?format(this.option):this.option;
                },
                style(){
                    return {
                        minWidth:this.minWidth,
                        minHeight:this.minHeight
                    }
                }
            },
            mounted(){
                this.echart = echarts.init(this.$el,"kjsoft");
                this.setOption(this.renderOption,true);
                window.addEventListener("resize",util.debounce(()=>{
                    this?.echart?.resize();
                }))
            },
            template:'<div :style="style"></div>',
            methods:{
                setOption(option,nomerge){
                    this.echart.setOption(option,nomerge);
                }
            }
        }
    }

    return function(resolve, reject){
        getEcharts().then(echarts=>resolve(getEchartComponent(echarts)))
    }
})