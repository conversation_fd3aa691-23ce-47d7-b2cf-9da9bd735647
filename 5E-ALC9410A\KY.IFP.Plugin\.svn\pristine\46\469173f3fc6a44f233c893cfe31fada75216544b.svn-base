﻿namespace COM.IFP.PLC.SiemensS7.Protocol
{
    /// <summary>
    /// 读写PLC的报文都会用到，用于传寻址参数，和读的数量或写的数据。
    /// </summary>
    public class PlcS7Command
    {
        /// <summary>
        /// 字节数，只用于读PLC
        /// </summary>
        public int Count { get; set; }
        /// <summary>
        /// 数据字节数组，只用于写PLC
        /// </summary>
        public byte[] Data = null;
        /// <summary>
        /// 读写PLC都用，用于寻址表示存储区域，暂时只支持DB区，0x84表示DB区（V区）0x04 S,0x05 SM,0x06 AI,0x07 AQ,0x1E C,0x81 I,0x82 Q,0x83 M,0x84 V,0x1F T
        /// </summary>
        public int Area { get; set; }
        /// <summary>
        /// 读写PLC都用，用于寻址，其余时候为0，当存取区域为DB区时表示块号
        /// </summary>
        public int Block { get; set; }
        /// <summary>
        /// 读写PLC都用，用于寻址表示字节偏移，数据在存储区的字节偏移
        /// </summary>
        public int ByteOffset { get; set; }
    }
}
