﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>测试读卡</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
</head>
<body class="flex" controller option="{platform:'element'}">
    <div id="app" class="flex flex-item">
        <ifp-input v-model="cardNum" style="width:400px" v-loading="loading">
            <el-button slot="append" @click="readCard" style="margin-left:5px;">读卡</el-button>
            <el-button slot="append" @click="writeCard" style="margin-left:5px;">写卡</el-button>
        </ifp-input>
    </div>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>