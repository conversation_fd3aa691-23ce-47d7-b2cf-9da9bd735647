<!DOCTYPE html>
  <html lang="zh-<PERSON>">
  <head>
      <meta charset="UTF-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="keywords" content="示例页面">
      <title>综合示例</title>
  </head>
  <body class="flex" controller option="{platform:'element'}">
      <ifp-page id="app" v-loading="loading">
          
        <ifp-toolbar lxsz="table1" close>
            <ifp-button @click="onSelect()">刷新</ifp-button>
            <ifp-button type="primary" @click="onCreate()">新增</ifp-button>
            <ifp-button :disabled="true" @click="onUpdate">修改</ifp-button>
            <ifp-button @click="onDelete">删除</ifp-button>
        </ifp-toolbar>

        <ifp-searchbar @search="onSelect">
            <ifp-searchbar-item label="发站">
                <ifp-select-ywlx type="ywlx" ywlx="4002" v-model="search.fz"
                    style="width:84px;"></ifp-select-ywlx>
            </ifp-searchbar-item>
            <ifp-searchbar-item label="名称">
                <ifp-input v-model="viewer.filter.Bname.Value" placeholder="请输入内容" style="width:200px;"></ifp-input>
            </ifp-searchbar-item>
            <ifp-searchbar-item label="编码">
                <ifp-input v-model="viewer.filter.Gid.Value" placeholder="请输入内容" style="width:200px;"></ifp-input>
            </ifp-searchbar-item>
        </ifp-searchbar>

        <ifp-panel-table class="flex-item padding">
            <ifp-table :data="viewer.source" style="width:100%;" row-key="id"
                ref="table1" height="100%" highlight-current-row
                @current-change="onChange">
                <el-table-column prop="Gid" label="标识" sortable></el-table-column>
                <el-table-column prop="Kdbm" label="矿点编号" sortable></el-table-column>
                <el-table-column prop="Bname" label="矿点名称" sortable></el-table-column>
                <el-table-column prop="Sname" sortable label="矿点简称"></el-table-column>
                <el-table-column prop="Count" sortable :formatter="$TableFormatter.float(2)"
                    label="数量2"></el-table-column>
                <el-table-column prop="Count" sortable :formatter="$TableFormatter.thousands"
                    label="数量"></el-table-column>
                <el-table-column prop="Lasttime" sortable :formatter="$TableFormatter.dateFormat('YYYY年')"
                    label="更新时间"></el-table-column>
            </ifp-table>
            <template v-slot:pagination>
                <ifp-pagination 
                    @size-change="sizeChange" 
                    @current-change="pageChange" 
                    :current-page="viewer.paging.page"
                    :page-size="viewer.paging.size" 
                    :total="viewer.paging.records"
                ></ifp-pagination>
            </template>
        </ifp-panel-table>

        <ifp-dialog :title="dialogTitle" width="800px"
            :visible.sync="editor.dialog">
            <ifp-kd-detail class="flex-item" 
                @sucess="updateList()" 
                :source="editor.source" 
                :state="editor.state"
            ></ifp-kd-detail>
        </ifp-dialog>
    </ifp-page>
      <script src="/iofp/ics/starter.js"></script>
  </body>
  </html>
  