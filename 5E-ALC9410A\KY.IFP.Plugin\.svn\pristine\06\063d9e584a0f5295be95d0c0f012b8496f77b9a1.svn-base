{"version": 3, "sources": ["dist\\css\\bootstrap-theme-kykj.css", "less\\bootstrap\\theme-kykj.less", "less\\bootstrap\\mixins\\reset-filter.less"], "names": [], "mappings": "AAAA;;;ACkEA,KACE,cAAA,EASF,YALA,aAGA,UAFA,aACA,aAEA,aA5DE,mBAAA,KACQ,WAAA,KAmEP,mBADA,mBACA,oBADA,oBACA,iBADA,iBACA,oBADA,oBACA,oBADA,oBACA,oBADA,oBAnED,mBAAA,KACQ,WAAA,KAuEP,qBACA,sBADA,sBACA,uBADA,mBACA,oBADA,sBACA,uBADA,sBACA,uBADA,sBACA,uBACkB,+BAAA,gCAAA,6BAAA,gCAAA,gCAAA,gCAEjB,YAAA,EAAA,IAAA,EAAA,KAGF,mBAAA,oBAAA,iBAAA,oBAAA,oBAAA,oBACE,YAAA,KA8CD,YADA,YAEC,iBAAA,KAKJ,aA7CE,OAAA,IAAA,MAAA,QAEA,MAAA,KA3EE,iBAAA,QCvBF,OAAA,0DDqGA,kBAAA,SACA,aAAA,QA0CA,aAAA,KAvCC,mBADA,mBAEC,iBAAA,QACA,oBAAA,EAAA,MAID,oBADA,oBAEC,iBAAA,QACA,aAAA,QAGD,sBAQE,6BAFA,4BACA,6BAFA,4BADA,4BAHF,uBAOE,8BAFA,6BACA,8BAFA,6BADA,6BAFgB,gCAMhB,uCAFA,sCACA,uCAFA,sCADA,sCAKC,iBAAA,QACA,iBAAA,KAoBN,aAlDE,OAAA,IAAA,MAAA,QAEA,MAAA,KA3EE,iBAAA,QCvBF,OAAA,0DDqGA,kBAAA,SACA,aAAA,QAGC,mBADA,mBAEC,iBAAA,QACA,oBAAA,EAAA,MAID,oBADA,oBAEC,iBAAA,QACA,aAAA,QAGD,sBAQE,6BAFA,4BACA,6BAFA,4BADA,4BAHF,uBAOE,8BAFA,6BACA,8BAFA,6BADA,6BAFgB,gCAMhB,uCAFA,sCACA,uCAFA,sCADA,sCAKC,iBAAA,QACA,iBAAA,KAqBN,aAnDE,OAAA,IAAA,MAAA,QAEA,MAAA,KA3EE,iBAAA,QCvBF,OAAA,0DDqGA,kBAAA,SACA,aAAA,QAGC,mBADA,mBAEC,iBAAA,QACA,oBAAA,EAAA,MAID,oBADA,oBAEC,iBAAA,QACA,aAAA,QAGD,sBAQE,6BAFA,4BACA,6BAFA,4BADA,4BAHF,uBAOE,8BAFA,6BACA,8BAFA,6BADA,6BAFgB,gCAMhB,uCAFA,sCACA,uCAFA,sCADA,sCAKC,iBAAA,QACA,iBAAA,KAsBN,UApDE,OAAA,IAAA,MAAA,QAEA,MAAA,KA3EE,iBAAA,QCvBF,OAAA,0DDqGA,kBAAA,SACA,aAAA,QAGC,gBADA,gBAEC,iBAAA,QACA,oBAAA,EAAA,MAID,iBADA,iBAEC,iBAAA,QACA,aAAA,QAGD,mBAQE,0BAFA,yBACA,0BAFA,yBADA,yBAHF,oBAOE,2BAFA,0BACA,2BAFA,0BADA,0BAFgB,6BAMhB,oCAFA,mCACA,oCAFA,mCADA,mCAKC,iBAAA,QACA,iBAAA,KAuBN,aArDE,OAAA,IAAA,MAAA,QAEA,MAAA,KA3EE,iBAAA,QCvBF,OAAA,0DDqGA,kBAAA,SACA,aAAA,QAGC,mBADA,mBAEC,iBAAA,QACA,oBAAA,EAAA,MAID,oBADA,oBAEC,iBAAA,QACA,aAAA,QAGD,sBAQE,6BAFA,4BACA,6BAFA,4BADA,4BAHF,uBAOE,8BAFA,6BACA,8BAFA,6BADA,6BAFgB,gCAMhB,uCAFA,sCACA,uCAFA,sCADA,sCAKC,iBAAA,QACA,iBAAA,KAwBN,YAtDE,OAAA,IAAA,MAAA,QAEA,MAAA,KA3EE,iBAAA,QCvBF,OAAA,0DDqGA,kBAAA,SACA,aAAA,QAGC,kBADA,kBAEC,iBAAA,QACA,oBAAA,EAAA,MAID,mBADA,mBAEC,iBAAA,QACA,aAAA,QAGD,qBAQE,4BAFA,2BACA,4BAFA,2BADA,2BAHF,sBAOE,6BAFA,4BACA,6BAFA,4BADA,4BAFgB,+BAMhB,sCAFA,qCACA,sCAFA,qCADA,qCAKC,iBAAA,QACA,iBAAA,KAgCN,eADA,WApJE,mBAAA,KACQ,WAAA,KA8Ja,0BADA,0BAhJnB,iBAAA,QAmJF,iBAAA,QAEuB,yBAEG,+BADA,+BAtJxB,iBAAA,QAyJF,iBAAA,QASF,gBAlKI,iBAAA,KCvBF,OAAA,0DD4LA,cAAA,EAnLA,mBAAA,KACQ,WAAA,KAuLc,sCADF,oCAzKlB,iBAAA,QAdF,mBAAA,KACQ,WAAA,KA8LV,gBAjLI,iBAAA,QCvBF,OAAA,0DD2MA,cAAA,EAlMA,mBAAA,KACQ,WAAA,KAqMR,kBACE,MAAA,KAIoB,sCADF,oCA5LlB,iBAAA,QAdF,mBAAA,KACQ,WAAA,KA+MR,+BACE,aAAA,KAEC,qCADA,qCAEC,iBAAA,QAEF,yCACE,iBAAA,KAMD,uCADA,uCAEC,gBAAA,KACA,iBAAA,QAKN,cACiB,iBACf,YAAA,EAAA,IAAA,EAAA,sBAIF,gBA5NI,iBAAA,QCvBF,OAAA,0DDsPA,cAAA,EAEsB,sCADF,oCAhOlB,iBAAA,QAdF,mBAAA,KACQ,WAAA,KAmPR,8BACiB,iCACf,YAAA,EAAA,KAAA,EAAA,gBAOJ,qBADA,kBADA,mBAGE,cAAA,EAI6C,yBACM,mDAGhD,yDADA,yDAEC,MAAA,KAzPF,iBAAA,SAqQJ,OACE,YAAA,EAAA,IAAA,EAAA,qBApRA,mBAAA,KACQ,WAAA,KA+RV,eAlRI,iBAAA,QA8QF,aAAA,QAKF,YAnRI,iBAAA,QA8QF,aAAA,QAMF,eApRI,iBAAA,QA8QF,aAAA,QAOF,cArRI,iBAAA,QA8QF,aAAA,QAeF,UA7RI,iBAAA,QAuSJ,cAvSI,iBAAA,QAwSJ,sBAxSI,iBAAA,QAySJ,mBAzSI,iBAAA,QA0SJ,sBA1SI,iBAAA,QA2SJ,qBA3SI,iBAAA,QA+SJ,sBAlRI,iBAAkB,yKAClB,iBAAkB,oKAClB,iBAAkB,iKAyRtB,YACE,cAAA,EAvUA,mBAAA,KACQ,WAAA,KAyUM,wBAEO,8BADA,8BAErB,YAAA,EAAA,KAAA,EAAA,QA/TE,iBAAA,QAiUF,aAAA,QAEA,+BAAA,qCAAA,qCACE,YAAA,KAUJ,OA5VE,mBAAA,KACQ,WAAA,KAqWK,8BAxVX,iBAAA,QAyVW,8BAzVX,iBAAA,QA0VW,8BA1VX,iBAAA,QA2VQ,2BA3VR,iBAAA,QA4VW,8BA5VX,iBAAA,QA6VU,6BA7VV,iBAAA,QAoWJ,MApWI,iBAAA,QAsWF,aAAA,QApXA,mBAAA,KACQ,WAAA,KA0XV,cACE,cAAA,EAKF,mBACE,cAAA,EACC,4BACC,cAAA,EAED,4BACC,cAAA", "sourcesContent": ["/*!\n * Bootstrap-theme-kykj v1.0 (https://kykjsoft.com/)\n * Copyright 2016-2020 Twitter, Inc.\n */\n.btn {\n  border-radius: 0;\n}\n.btn-default,\n.btn-primary,\n.btn-success,\n.btn-info,\n.btn-warning,\n.btn-danger {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.btn-default:active,\n.btn-primary:active,\n.btn-success:active,\n.btn-info:active,\n.btn-warning:active,\n.btn-danger:active,\n.btn-default.active,\n.btn-primary.active,\n.btn-success.active,\n.btn-info.active,\n.btn-warning.active,\n.btn-danger.active {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.btn-default.disabled,\n.btn-primary.disabled,\n.btn-success.disabled,\n.btn-info.disabled,\n.btn-warning.disabled,\n.btn-danger.disabled,\n.btn-default[disabled],\n.btn-primary[disabled],\n.btn-success[disabled],\n.btn-info[disabled],\n.btn-warning[disabled],\n.btn-danger[disabled],\nfieldset[disabled] .btn-default,\nfieldset[disabled] .btn-primary,\nfieldset[disabled] .btn-success,\nfieldset[disabled] .btn-info,\nfieldset[disabled] .btn-warning,\nfieldset[disabled] .btn-danger {\n  text-shadow: 0 1px 0 #999;\n}\n.btn-default .badge,\n.btn-primary .badge,\n.btn-success .badge,\n.btn-info .badge,\n.btn-warning .badge,\n.btn-danger .badge {\n  text-shadow: none;\n}\n.btn:active,\n.btn.active {\n  background-image: none;\n}\n.btn-default {\n  border: 1px solid #C2DAFF;\n  color: #000;\n  background-color: #C2DAFF;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  background-repeat: repeat-x;\n  border-color: #7bafff;\n  border-color: #ccc;\n}\n.btn-default:hover,\n.btn-default:focus {\n  background-color: #aeceff;\n  background-position: 0 -15px;\n}\n.btn-default:active,\n.btn-default.active {\n  background-color: #8fbbff;\n  border-color: #7bafff;\n}\n.btn-default.disabled,\n.btn-default[disabled],\nfieldset[disabled] .btn-default,\n.btn-default.disabled:hover,\n.btn-default[disabled]:hover,\nfieldset[disabled] .btn-default:hover,\n.btn-default.disabled:focus,\n.btn-default[disabled]:focus,\nfieldset[disabled] .btn-default:focus,\n.btn-default.disabled.focus,\n.btn-default[disabled].focus,\nfieldset[disabled] .btn-default.focus,\n.btn-default.disabled:active,\n.btn-default[disabled]:active,\nfieldset[disabled] .btn-default:active,\n.btn-default.disabled.active,\n.btn-default[disabled].active,\nfieldset[disabled] .btn-default.active {\n  background-color: #5c9cff;\n  background-image: none;\n}\n.btn-primary {\n  border: 1px solid #3578ff;\n  color: #fff;\n  background-color: #3578ff;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  background-repeat: repeat-x;\n  border-color: #004eec;\n}\n.btn-primary:hover,\n.btn-primary:focus {\n  background-color: #206aff;\n  background-position: 0 -15px;\n}\n.btn-primary:active,\n.btn-primary.active {\n  background-color: #0256ff;\n  border-color: #004eec;\n}\n.btn-primary.disabled,\n.btn-primary[disabled],\nfieldset[disabled] .btn-primary,\n.btn-primary.disabled:hover,\n.btn-primary[disabled]:hover,\nfieldset[disabled] .btn-primary:hover,\n.btn-primary.disabled:focus,\n.btn-primary[disabled]:focus,\nfieldset[disabled] .btn-primary:focus,\n.btn-primary.disabled.focus,\n.btn-primary[disabled].focus,\nfieldset[disabled] .btn-primary.focus,\n.btn-primary.disabled:active,\n.btn-primary[disabled]:active,\nfieldset[disabled] .btn-primary:active,\n.btn-primary.disabled.active,\n.btn-primary[disabled].active,\nfieldset[disabled] .btn-primary.active {\n  background-color: #0044ce;\n  background-image: none;\n}\n.btn-success {\n  border: 1px solid #5cb85c;\n  color: #fff;\n  background-color: #5cb85c;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  background-repeat: repeat-x;\n  border-color: #3e8f3e;\n}\n.btn-success:hover,\n.btn-success:focus {\n  background-color: #4eb24e;\n  background-position: 0 -15px;\n}\n.btn-success:active,\n.btn-success.active {\n  background-color: #449d44;\n  border-color: #3e8f3e;\n}\n.btn-success.disabled,\n.btn-success[disabled],\nfieldset[disabled] .btn-success,\n.btn-success.disabled:hover,\n.btn-success[disabled]:hover,\nfieldset[disabled] .btn-success:hover,\n.btn-success.disabled:focus,\n.btn-success[disabled]:focus,\nfieldset[disabled] .btn-success:focus,\n.btn-success.disabled.focus,\n.btn-success[disabled].focus,\nfieldset[disabled] .btn-success.focus,\n.btn-success.disabled:active,\n.btn-success[disabled]:active,\nfieldset[disabled] .btn-success:active,\n.btn-success.disabled.active,\n.btn-success[disabled].active,\nfieldset[disabled] .btn-success.active {\n  background-color: #357935;\n  background-image: none;\n}\n.btn-info {\n  border: 1px solid #5bc0de;\n  color: #fff;\n  background-color: #5bc0de;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  background-repeat: repeat-x;\n  border-color: #28a4c9;\n}\n.btn-info:hover,\n.btn-info:focus {\n  background-color: #4ab9db;\n  background-position: 0 -15px;\n}\n.btn-info:active,\n.btn-info.active {\n  background-color: #31b0d5;\n  border-color: #28a4c9;\n}\n.btn-info.disabled,\n.btn-info[disabled],\nfieldset[disabled] .btn-info,\n.btn-info.disabled:hover,\n.btn-info[disabled]:hover,\nfieldset[disabled] .btn-info:hover,\n.btn-info.disabled:focus,\n.btn-info[disabled]:focus,\nfieldset[disabled] .btn-info:focus,\n.btn-info.disabled.focus,\n.btn-info[disabled].focus,\nfieldset[disabled] .btn-info.focus,\n.btn-info.disabled:active,\n.btn-info[disabled]:active,\nfieldset[disabled] .btn-info:active,\n.btn-info.disabled.active,\n.btn-info[disabled].active,\nfieldset[disabled] .btn-info.active {\n  background-color: #2390b0;\n  background-image: none;\n}\n.btn-warning {\n  border: 1px solid #f0ad4e;\n  color: #fff;\n  background-color: #f0ad4e;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  background-repeat: repeat-x;\n  border-color: #e38d13;\n}\n.btn-warning:hover,\n.btn-warning:focus {\n  background-color: #eea43b;\n  background-position: 0 -15px;\n}\n.btn-warning:active,\n.btn-warning.active {\n  background-color: #ec971f;\n  border-color: #e38d13;\n}\n.btn-warning.disabled,\n.btn-warning[disabled],\nfieldset[disabled] .btn-warning,\n.btn-warning.disabled:hover,\n.btn-warning[disabled]:hover,\nfieldset[disabled] .btn-warning:hover,\n.btn-warning.disabled:focus,\n.btn-warning[disabled]:focus,\nfieldset[disabled] .btn-warning:focus,\n.btn-warning.disabled.focus,\n.btn-warning[disabled].focus,\nfieldset[disabled] .btn-warning.focus,\n.btn-warning.disabled:active,\n.btn-warning[disabled]:active,\nfieldset[disabled] .btn-warning:active,\n.btn-warning.disabled.active,\n.btn-warning[disabled].active,\nfieldset[disabled] .btn-warning.active {\n  background-color: #c77c11;\n  background-image: none;\n}\n.btn-danger {\n  border: 1px solid #d9534f;\n  color: #fff;\n  background-color: #d9534f;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  background-repeat: repeat-x;\n  border-color: #b92c28;\n}\n.btn-danger:hover,\n.btn-danger:focus {\n  background-color: #d5433e;\n  background-position: 0 -15px;\n}\n.btn-danger:active,\n.btn-danger.active {\n  background-color: #c9302c;\n  border-color: #b92c28;\n}\n.btn-danger.disabled,\n.btn-danger[disabled],\nfieldset[disabled] .btn-danger,\n.btn-danger.disabled:hover,\n.btn-danger[disabled]:hover,\nfieldset[disabled] .btn-danger:hover,\n.btn-danger.disabled:focus,\n.btn-danger[disabled]:focus,\nfieldset[disabled] .btn-danger:focus,\n.btn-danger.disabled.focus,\n.btn-danger[disabled].focus,\nfieldset[disabled] .btn-danger.focus,\n.btn-danger.disabled:active,\n.btn-danger[disabled]:active,\nfieldset[disabled] .btn-danger:active,\n.btn-danger.disabled.active,\n.btn-danger[disabled].active,\nfieldset[disabled] .btn-danger.active {\n  background-color: #a02622;\n  background-image: none;\n}\n.thumbnail,\n.img-thumbnail {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.dropdown-menu > li > a:hover,\n.dropdown-menu > li > a:focus {\n  background-color: #f5f5f5;\n  background-color: #e8e8e8;\n}\n.dropdown-menu > .active > a,\n.dropdown-menu > .active > a:hover,\n.dropdown-menu > .active > a:focus {\n  background-color: #3578ff;\n  background-color: #1b67ff;\n}\n.navbar-default {\n  background-color: #ffffff;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  border-radius: 0px;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.navbar-default .navbar-nav > .open > a,\n.navbar-default .navbar-nav > .active > a {\n  background-color: #dbdbdb;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.navbar-primary {\n  background-color: #0086f1;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  border-radius: 0px;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.navbar-primary a {\n  color: #eee;\n}\n.navbar-primary .navbar-nav > .open > a,\n.navbar-primary .navbar-nav > .active > a {\n  background-color: #0065b6;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.navbar-primary .navbar-toggle {\n  border-color: #ddd;\n}\n.navbar-primary .navbar-toggle:hover,\n.navbar-primary .navbar-toggle:focus {\n  background-color: #006abe;\n}\n.navbar-primary .navbar-toggle .icon-bar {\n  background-color: #fff;\n}\n.navbar-primary .navbar-nav > li > a:hover,\n.navbar-primary .navbar-nav > li > a:focus {\n  text-decoration: none;\n  background-color: #004d8b;\n}\n.navbar-brand,\n.navbar-nav > li > a {\n  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.25);\n}\n.navbar-inverse {\n  background-color: #3c3c3c;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  border-radius: 0px;\n}\n.navbar-inverse .navbar-nav > .open > a,\n.navbar-inverse .navbar-nav > .active > a {\n  background-color: #080808;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.navbar-inverse .navbar-brand,\n.navbar-inverse .navbar-nav > li > a {\n  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);\n}\n.navbar-static-top,\n.navbar-fixed-top,\n.navbar-fixed-bottom {\n  border-radius: 0;\n}\n@media (max-width: 767px) {\n  .navbar .navbar-nav .open .dropdown-menu > .active > a,\n  .navbar .navbar-nav .open .dropdown-menu > .active > a:hover,\n  .navbar .navbar-nav .open .dropdown-menu > .active > a:focus {\n    color: #fff;\n    background-color: #3578ff;\n  }\n}\n.alert {\n  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.alert-success {\n  background-color: #dff0d8;\n  border-color: #b2dba1;\n}\n.alert-info {\n  background-color: #d9edf7;\n  border-color: #9acfea;\n}\n.alert-warning {\n  background-color: #fcf8e3;\n  border-color: #f5e79e;\n}\n.alert-danger {\n  background-color: #f2dede;\n  border-color: #dca7a7;\n}\n.progress {\n  background-color: #ebebeb;\n}\n.progress-bar {\n  background-color: #3578ff;\n}\n.progress-bar-success {\n  background-color: #5cb85c;\n}\n.progress-bar-info {\n  background-color: #5bc0de;\n}\n.progress-bar-warning {\n  background-color: #f0ad4e;\n}\n.progress-bar-danger {\n  background-color: #d9534f;\n}\n.progress-bar-striped {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n.list-group {\n  border-radius: 0px;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.list-group-item.active,\n.list-group-item.active:hover,\n.list-group-item.active:focus {\n  text-shadow: 0 -1px 0 #0256ff;\n  background-color: #3578ff;\n  border-color: #0f5eff;\n}\n.list-group-item.active .badge,\n.list-group-item.active:hover .badge,\n.list-group-item.active:focus .badge {\n  text-shadow: none;\n}\n.panel {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.panel-default > .panel-heading {\n  background-color: #f5f5f5;\n}\n.panel-primary > .panel-heading {\n  background-color: #3578ff;\n}\n.panel-success > .panel-heading {\n  background-color: #dff0d8;\n}\n.panel-info > .panel-heading {\n  background-color: #d9edf7;\n}\n.panel-warning > .panel-heading {\n  background-color: #fcf8e3;\n}\n.panel-danger > .panel-heading {\n  background-color: #f2dede;\n}\n.well {\n  background-color: #e8e8e8;\n  border-color: #dcdcdc;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.form-control {\n  border-radius: 0px;\n}\n.input-group-addon {\n  border-radius: 0px;\n}\n.input-group-addon.input-sm {\n  border-radius: 0px;\n}\n.input-group-addon.input-lg {\n  border-radius: 0px;\n}\n/*# sourceMappingURL=bootstrap-theme-kykj.css.map */", "// stylelint-disable selector-no-qualifying-type, selector-max-compound-selectors\n\n/*!\n * Bootstrap-theme-kykj v1.0 (https://kykjsoft.com/)\n * Copyright 2016-2020 Twitter, Inc.\n */\n\n//\n// Load core variables and mixins\n// --------------------------------------------------\n\n@import \"variables-kykj.less\";\n@import \"mixins.less\";\n\n.box-shadow2(@shadow) {\n  -webkit-box-shadow: none;\n          box-shadow: none;\n}\n//\n\n#gradient2 {\n  .horizontal(@start-color: #555; @end-color: #333; @start-percent: 0%; @end-percent: 100%) {\n    background-image: -webkit-linear-gradient(left, @start-color @start-percent, @end-color @end-percent); // Safari 5.1-6, Chrome 10+\n    background-image: -o-linear-gradient(left, @start-color @start-percent, @end-color @end-percent); // Opera 12\n    background-image: linear-gradient(to right, @start-color @start-percent, @end-color @end-percent); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+\n    filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=1)\", argb(@start-color), argb(@end-color))); // IE9 and down\n    background-repeat: repeat-x;\n  }\n  .vertical(@start-color: #555; @end-color: #333; @start-percent: 0%; @end-percent: 100%) {\n    background-color:@start-color;\n  }\n\n  .directional(@start-color: #555; @end-color: #333; @deg: 45deg) {\n    background-image: -webkit-linear-gradient(@deg, @start-color, @end-color); // Safari 5.1-6, Chrome 10+\n    background-image: -o-linear-gradient(@deg, @start-color, @end-color); // Opera 12\n    background-image: linear-gradient(@deg, @start-color, @end-color); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+\n    background-repeat: repeat-x;\n  }\n  .horizontal-three-colors(@start-color: #00b3ee; @mid-color: #7a43b6; @color-stop: 50%; @end-color: #c3325f) {\n    background-image: -webkit-linear-gradient(left, @start-color, @mid-color @color-stop, @end-color);\n    background-image: -o-linear-gradient(left, @start-color, @mid-color @color-stop, @end-color);\n    background-image: linear-gradient(to right, @start-color, @mid-color @color-stop, @end-color);\n    filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=1)\", argb(@start-color), argb(@end-color))); // IE9 and down, gets no color-stop at all for proper fallback\n    background-repeat: no-repeat;\n  }\n  .vertical-three-colors(@start-color: #00b3ee; @mid-color: #7a43b6; @color-stop: 50%; @end-color: #c3325f) {\n    background-image: -webkit-linear-gradient(@start-color, @mid-color @color-stop, @end-color);\n    background-image: -o-linear-gradient(@start-color, @mid-color @color-stop, @end-color);\n    background-image: linear-gradient(@start-color, @mid-color @color-stop, @end-color);\n    filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=0)\", argb(@start-color), argb(@end-color))); // IE9 and down, gets no color-stop at all for proper fallback\n    background-repeat: no-repeat;\n  }\n  .radial(@inner-color: #555; @outer-color: #333) {\n    background-image: -webkit-radial-gradient(circle, @inner-color, @outer-color);\n    background-image: radial-gradient(circle, @inner-color, @outer-color);\n    background-repeat: no-repeat;\n  }\n  .striped(@color: rgba(255, 255, 255, .15); @angle: 45deg) {\n    background-image: -webkit-linear-gradient(@angle, @color 25%, transparent 25%, transparent 50%, @color 50%, @color 75%, transparent 75%, transparent);\n    background-image: -o-linear-gradient(@angle, @color 25%, transparent 25%, transparent 50%, @color 50%, @color 75%, transparent 75%, transparent);\n    background-image: linear-gradient(@angle, @color 25%, transparent 25%, transparent 50%, @color 50%, @color 75%, transparent 75%, transparent);\n  }\n}\n//\n// Buttons\n// --------------------------------------------------\n.btn{\n  border-radius: 0;\n}\n\n// Common styles\n.btn-default,\n.btn-primary,\n.btn-success,\n.btn-info,\n.btn-warning,\n.btn-danger {\n  //text-shadow: 0 -1px 0 rgba(0, 0, 0, .2);\n  @shadow: inset 0 4px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);\n  .box-shadow2(@shadow);\n\n  // Reset the shadow\n  &:active,\n  &.active {\n    .box-shadow2(inset 0 3px 5px rgba(0, 0, 0, .125));\n  }\n\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    //.box-shadow(@shadow);\n    text-shadow: 0 1px 0 #999;\n  }\n\n  .badge {\n    text-shadow: none;\n  }\n}\n\n// Mixin for generating new styles\n.btn-styles(@btn-color: #555,@btn-text-color: #555) {\n\n  border:1px solid @btn-color;\n  \n  color:@btn-text-color;\n  #gradient2 > .vertical(@start-color: @btn-color; @end-color: darken(@btn-color, 12%));\n  .reset-filter(); // Disable gradients for IE9 because filter bleeds through rounded corners; see https://github.com/twbs/bootstrap/issues/10620\n  background-repeat: repeat-x;\n  border-color: darken(@btn-color, 14%);\n\n  &:hover,\n  &:focus  {\n    background-color: darken(@btn-color, 4%);\n    background-position: 0 -15px;\n  }\n\n  &:active,\n  &.active {\n    background-color: darken(@btn-color, 10%);\n    border-color: darken(@btn-color, 14%);\n  }\n\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    &,\n    &:hover,\n    &:focus,\n    &.focus,\n    &:active,\n    &.active {\n      background-color: darken(@btn-color, 20%);\n      background-image: none;\n    }\n  }\n}\n\n// Common styles\n.btn {\n  // Remove the gradient for the pressed/active state\n  &:active,\n  &.active {\n    background-image: none;\n  }\n}\n\n// Apply the mixin to the buttons\n.btn-default {\n  .btn-styles(@btn-default-bg,@btn-default-color);\n  //text-shadow: 0 1px 0 #fff;\n  border-color: #ccc;\n}\n.btn-primary { .btn-styles(@btn-primary-bg,@btn-primary-color); }\n.btn-success { .btn-styles(@btn-success-bg,@btn-success-color); }\n.btn-info    { .btn-styles(@btn-info-bg,@btn-info-color); }\n.btn-warning { .btn-styles(@btn-warning-bg,@btn-warning-color); }\n.btn-danger  { .btn-styles(@btn-danger-bg,@btn-danger-color); }\n\n\n//\n// Images\n// --------------------------------------------------\n\n.thumbnail,\n.img-thumbnail {\n  .box-shadow2(0 1px 2px rgba(0, 0, 0, .075));\n}\n\n\n//\n// Dropdowns\n// --------------------------------------------------\n\n.dropdown-menu > li > a:hover,\n.dropdown-menu > li > a:focus {\n  #gradient2 > .vertical(@start-color: @dropdown-link-hover-bg; @end-color: darken(@dropdown-link-hover-bg, 5%));\n  background-color: darken(@dropdown-link-hover-bg, 5%);\n}\n.dropdown-menu > .active > a,\n.dropdown-menu > .active > a:hover,\n.dropdown-menu > .active > a:focus {\n  #gradient2 > .vertical(@start-color: @dropdown-link-active-bg; @end-color: darken(@dropdown-link-active-bg, 5%));\n  background-color: darken(@dropdown-link-active-bg, 5%);\n}\n\n\n//\n// Navbar\n// --------------------------------------------------\n\n// Default navbar\n.navbar-default {\n  #gradient2 > .vertical(@start-color: lighten(@navbar-default-bg, 10%); @end-color: @navbar-default-bg);\n  .reset-filter(); // Remove gradient in IE<10 to fix bug where dropdowns don't get triggered\n  border-radius: @navbar-border-radius;\n  @shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 5px rgba(0, 0, 0, .075);\n  .box-shadow2(@shadow);\n\n  .navbar-nav > .open > a,\n  .navbar-nav > .active > a {\n    #gradient2 > .vertical(@start-color: darken(@navbar-default-link-active-bg, 5%); @end-color: darken(@navbar-default-link-active-bg, 2%));\n    .box-shadow2(inset 0 3px 9px rgba(0, 0, 0, .075));\n  }\n}\n\n// Primary navbar\n.navbar-primary {\n  #gradient2 > .vertical(@start-color: @navbar-primary-bg; @end-color: @navbar-primary-bg);\n  .reset-filter(); // Remove gradient in IE<10 to fix bug where dropdowns don't get triggered\n  border-radius: @navbar-border-radius;\n  @shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 5px rgba(0, 0, 0, .075);\n  .box-shadow2(@shadow);\n\n  a{\n    color:@navbar-primary-color;\n  }\n\n  .navbar-nav > .open > a,\n  .navbar-nav > .active > a {\n    #gradient2 > .vertical(@start-color: darken(@navbar-primary-link-active-bg, 5%); @end-color: darken(@navbar-primary-link-active-bg, 2%));\n    .box-shadow2(inset 0 3px 9px rgba(0, 0, 0, .075));\n  }\n\n  .navbar-toggle {\n    border-color: @navbar-primary-toggle-border-color;\n    &:hover,\n    &:focus {\n      background-color: @navbar-primary-toggle-hover-bg;\n    }\n    .icon-bar {\n      background-color: @navbar-primary-toggle-icon-bar-bg;\n    }\n  }\n\n  .navbar-nav > li > a {\n    &:hover,\n    &:focus {\n      text-decoration: none;\n      background-color: darken(@navbar-primary-bg,20%);\n    }\n  }\n}\n\n.navbar-brand,\n.navbar-nav > li > a {\n  text-shadow: 0 1px 0 rgba(255, 255, 255, .25);\n}\n\n// Inverted navbar\n.navbar-inverse {\n  #gradient2 > .vertical(@start-color: lighten(@navbar-inverse-bg, 10%); @end-color: @navbar-inverse-bg);\n  .reset-filter(); // Remove gradient in IE<10 to fix bug where dropdowns don't get triggered; see https://github.com/twbs/bootstrap/issues/10257\n  border-radius: @navbar-border-radius;\n  .navbar-nav > .open > a,\n  .navbar-nav > .active > a {\n    #gradient2 > .vertical(@start-color: @navbar-inverse-link-active-bg; @end-color: lighten(@navbar-inverse-link-active-bg, 2.5%));\n    .box-shadow2(inset 0 3px 9px rgba(0, 0, 0, .25));\n  }\n\n  .navbar-brand,\n  .navbar-nav > li > a {\n    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);\n  }\n}\n\n// Undo rounded corners in static and fixed navbars\n.navbar-static-top,\n.navbar-fixed-top,\n.navbar-fixed-bottom {\n  border-radius: 0;\n}\n\n// Fix active state of dropdown items in collapsed mode\n@media (max-width: @grid-float-breakpoint-max) {\n  .navbar .navbar-nav .open .dropdown-menu > .active > a {\n    &,\n    &:hover,\n    &:focus {\n      color: #fff;\n      #gradient2 > .vertical(@start-color: @dropdown-link-active-bg; @end-color: darken(@dropdown-link-active-bg, 5%));\n    }\n  }\n}\n\n\n//\n// Alerts\n// --------------------------------------------------\n\n// Common styles\n.alert {\n  text-shadow: 0 1px 0 rgba(255, 255, 255, .2);\n  @shadow: inset 0 1px 0 rgba(255, 255, 255, .25), 0 1px 2px rgba(0, 0, 0, .05);\n  .box-shadow2(@shadow);\n}\n\n// Mixin for generating new styles\n.alert-styles(@color) {\n  #gradient2 > .vertical(@start-color: @color; @end-color: darken(@color, 7.5%));\n  border-color: darken(@color, 15%);\n}\n\n// Apply the mixin to the alerts\n.alert-success    { .alert-styles(@alert-success-bg); }\n.alert-info       { .alert-styles(@alert-info-bg); }\n.alert-warning    { .alert-styles(@alert-warning-bg); }\n.alert-danger     { .alert-styles(@alert-danger-bg); }\n\n\n//\n// Progress bars\n// --------------------------------------------------\n\n// Give the progress background some depth\n.progress {\n  #gradient2 > .vertical(@start-color: darken(@progress-bg, 4%); @end-color: @progress-bg);\n}\n\n// Mixin for generating new styles\n.progress-bar-styles(@color) {\n  #gradient2 > .vertical(@start-color: @color; @end-color: darken(@color, 10%));\n}\n\n// Apply the mixin to the progress bars\n.progress-bar            { .progress-bar-styles(@progress-bar-bg); }\n.progress-bar-success    { .progress-bar-styles(@progress-bar-success-bg); }\n.progress-bar-info       { .progress-bar-styles(@progress-bar-info-bg); }\n.progress-bar-warning    { .progress-bar-styles(@progress-bar-warning-bg); }\n.progress-bar-danger     { .progress-bar-styles(@progress-bar-danger-bg); }\n\n// Reset the striped class because our mixins don't do multiple gradients and\n// the above custom styles override the new `.progress-bar-striped` in v3.2.0.\n.progress-bar-striped {\n  #gradient2 > .striped();\n}\n\n\n//\n// List groups\n// --------------------------------------------------\n\n.list-group {\n  border-radius: @border-radius-base;\n  .box-shadow2(0 1px 2px rgba(0, 0, 0, .075));\n}\n.list-group-item.active,\n.list-group-item.active:hover,\n.list-group-item.active:focus {\n  text-shadow: 0 -1px 0 darken(@list-group-active-bg, 10%);\n  #gradient2 > .vertical(@start-color: @list-group-active-bg; @end-color: darken(@list-group-active-bg, 7.5%));\n  border-color: darken(@list-group-active-border, 7.5%);\n\n  .badge {\n    text-shadow: none;\n  }\n}\n\n\n//\n// Panels\n// --------------------------------------------------\n\n// Common styles\n.panel {\n  .box-shadow2(0 1px 2px rgba(0, 0, 0, .05));\n}\n\n// Mixin for generating new styles\n.panel-heading-styles(@color) {\n  #gradient2 > .vertical(@start-color: @color; @end-color: darken(@color, 5%));\n}\n\n// Apply the mixin to the panel headings only\n.panel-default > .panel-heading   { .panel-heading-styles(@panel-default-heading-bg); }\n.panel-primary > .panel-heading   { .panel-heading-styles(@panel-primary-heading-bg); }\n.panel-success > .panel-heading   { .panel-heading-styles(@panel-success-heading-bg); }\n.panel-info > .panel-heading      { .panel-heading-styles(@panel-info-heading-bg); }\n.panel-warning > .panel-heading   { .panel-heading-styles(@panel-warning-heading-bg); }\n.panel-danger > .panel-heading    { .panel-heading-styles(@panel-danger-heading-bg); }\n\n\n//\n// Wells\n// --------------------------------------------------\n\n.well {\n  #gradient2 > .vertical(@start-color: darken(@well-bg, 5%); @end-color: @well-bg);\n  border-color: darken(@well-bg, 10%);\n  @shadow: inset 0 1px 3px rgba(0, 0, 0, .05), 0 1px 0 rgba(255, 255, 255, .1);\n  .box-shadow2(@shadow);\n}\n\n\n// .form-control\n.form-control{\n  border-radius: 0px;\n}\n\n// Text input groups\n// -------------------------\n.input-group-addon {\n  border-radius: @input-border-radius;\n  &.input-sm {\n    border-radius: @input-border-radius-small;\n  }\n  &.input-lg {\n    border-radius: @input-border-radius-large;\n  }\n}", "// Reset filters for IE\n//\n// When you need to remove a gradient background, do not forget to use this to reset\n// the IE filter for IE9 and below.\n\n.reset-filter() {\n  filter: e(%(\"progid:DXImageTransform.Microsoft.gradient(enabled = false)\"));\n}\n"]}