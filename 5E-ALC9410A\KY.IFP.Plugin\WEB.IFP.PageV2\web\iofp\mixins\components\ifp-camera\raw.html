<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<body>
  <img id="img" style="width:640px; height:480px;" src="http://*************:6380" />
  <script>
    
  </script>
  <script>
    var img = document.getElementById("img");
    var src = img.src;
    var pre = Date.now();
    var tic = 66; //一般监控视频的帧率不会超过每秒30帧，故延时建议设置为33（之后程序会自动减去网络延时），可根据带宽等实际情况适当提高延时值，但低于7帧时，肉眼会明显卡顿

    /*方式一，不存在跨域问题*/    
    //img.onload = function onLoaded() {
    //  let tmp = pre;
    //  pre = Date.now();
    //  //console.log(tic - (pre - tmp));
    //  setTimeout(() => {
    //    img.src = src + "?" + Math.random();
    //  }, Math.max(0, tic - (pre - tmp)));      
    //}

    /*方式二，兼容跨域（需服务器配合）*/
    var xhr = new XMLHttpRequest();
    xhr.withCredentials = true; //跨域允许附带自定义数据
    xhr.responseType = "blob";   
    xhr.timeout = 60000;  //毫秒，0表示永不超时
    xhr.onload = function () {
      if (xhr.status == 200) {
        window.URL.revokeObjectURL(img.src);  //一定要释放，否则必会内存泄露
        img.src = window.URL.createObjectURL(xhr.response);
      }
    }
    xhr.onloadend = function () {
      //无论成功与否，都会触发
      fun();
    }
    var fun = function () {
      let tmp = pre;
      pre = Date.now();
      //console.log(tic - (pre - tmp));
      setTimeout(() => {
        xhr.open("GET", src);
        xhr.send();
      }, Math.max(0, tic - (pre - tmp)));
    }
    fun();

    let lastTime = Date.now();
    img.onload = function(){
      // 显示当前帧率
      let curTime = Date.now();
      let fps = curTime===lastTime?0:1000/(curTime-lastTime);
      lastTime = curTime
      console.log(fps + " FPS");
    }

    /*方式三，websocket*/
    //TODO

    //方式三，预计效率最高，但稳定性可能也最差
    //方式二，推荐使用，效率稍优于方式一，但不明显，稳定性好，容易处理断线重连等异常情况
    //方式一，最简单粗暴
  </script>
</body>
</html>