﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace API.IFP.SignalR
{
    /// <summary>
    /// 从服务器到客户端的通信接口
    /// </summary>
    public interface IServer2Client
    {
        /// <summary>
        /// 获取要发送给客户端的消息内容
        /// </summary>
        /// <param name="client">客户端信息</param>
        /// <returns>消息内容</returns>
        public string MessageContent(SignalRClient client);
    }
}
