<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>math</title>
</head>
<body controller option="{platform:'vue'}">
    <div id="app" class="padding">
        <h1>使用 mathjs 计算表达式</h1>
        
        
        表达式：<input v-model="formula" />
        <input type="button" value="计算" @click="run"/>
        结果：<input readonly :value="result">

        
        
        <br/>
        api:<a href="https://mathjs.org/">https://mathjs.org/</a>
    </div>
    <script src="../starter.js"></script>
</body>
</html>