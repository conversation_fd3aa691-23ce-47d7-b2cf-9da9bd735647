<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>大屏展示</title>
        <style>
            html,
            body,
            #app {
                width: 100%;
                height: 100%;
                margin: 0px;
                padding: 0px;
            }

            .border-box-content {
                color: rgb(66, 185, 131);
                font-size: 40px;
                font-weight: bold;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        </style>
        <link rel="stylesheet" href="dataview.css" />
        <link rel="stylesheet" href="../../kyadmin/css/layout.css" />
    </head>
    <body controller="dataview.js" option="{platform:null}" style="background-color: #000">
        <div id="app">
            <dv-loading v-if="loading">Loading...</dv-loading>

            <dv-full-screen-container>
                <div class="main-header">
                    <div style="margin: auto; width: 340px">
                        <dv-border-box-8> 机电设备电子档案 </dv-border-box-8>
                    </div>
                </div>

                <dv-border-box-1 class="main-container">
                    <dv-border-box-3 class="left-chart-container">
                        <div style="height: 33%; width: 100%">
                            <dv-scroll-ranking-board :config="dv_scroll_ranking_board_config" />
                        </div>
                        <div style="height: 33%; width: 100%">
                            <dv-water-level-pond :config="dv_water_level_pond_config" />
                            <dv-charts :option="option" />
                        </div>
                        <div style="height: 33%; width: 100%">
                            <dv-charts :option="option" />
                        </div>
                    </dv-border-box-3>

                    <div class="right-main-container">
                        <div class="rmc-top-container">
                            <dv-border-box-3 class="rmctc-left-container">
                                <dv-flyline-chart :dev="true" :config="dv_flyline_chart_config" style="width: 100%; height: 100%" />
                            </dv-border-box-3>

                            <div class="rmctc-right-container">
                                <dv-border-box-3 class="rmctc-chart-1">
                                    <dv-capsule-chart :config="data1" style="width: 300px; height: 200px" />
                                </dv-border-box-3>

                                <dv-border-box-4 class="rmctc-chart-2" :reverse="true">
                                    <dv-percent-pond :config="percent_pond_config" style="width: 200px; height: 100px" />
                                </dv-border-box-4>
                            </div>
                        </div>

                        <dv-border-box-2 class="rmc-bottom-container">
                            <dv-scroll-board :config="dv_scroll_board_config" />
                        </dv-border-box-2>
                    </div>
                </dv-border-box-1>
            </dv-full-screen-container>
        </div>
        <script src="/kyadmin/starter.js"></script>
    </body>
</html>
