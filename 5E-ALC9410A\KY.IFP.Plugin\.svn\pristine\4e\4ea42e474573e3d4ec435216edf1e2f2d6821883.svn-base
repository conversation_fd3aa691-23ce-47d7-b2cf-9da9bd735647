;
define(["require"], function (require) {
	return {
		//在node节点中查找jggrid中指定class的DOM元素，找到后存放到list这个数组中
		findJqgridDom: function (node, className, nodeName, list) {
			var _this = this;
			if (node.childNodes.length = 0) {
				return;
			}
			for (var i = 0; i < node.childNodes.length; i++) {
				var childNode = node.childNodes[i];
				if (childNode.nodeName == nodeName && childNode.className.indexOf(className) > -1) {
					list.push(childNode);
				} else {
					_this.findJqgridDom(childNode, className, nodeName, list);
				}
			}
		},

		//得到JqGrid表格头部
		getJqGridHeads: function (gridControl) {
			var _this = this;

			var gridId = gridControl.id;

			//grid表格区域
			var jqGridDiv = $("#gbox_" + gridId)[0].cloneNode(true);

			//头部区域
			var gridHeadDiv = [];
			_this.findJqgridDom(jqGridDiv, "ui-jqgrid-hbox", "DIV", gridHeadDiv);

			//头部行
			var rowNodes = gridHeadDiv[0].childNodes[0].childNodes[0].childNodes;

			//传送给后台的数据行集合
			var ExcelRows = [];

			//循环每一行
			for (var i = 0; i < rowNodes.length; i++) {
				//如果多行表头的话，rowNodes里的第1行是多余的
				if (rowNodes[i].ariaHidden || rowNodes[i].ariaHidden == "true" || rowNodes[i].className.indexOf("jqg-first-row-header") > -1)
				{
					continue;
				}
				//传输给后台的数据行
				var ExcelRow = { Cells: [] };
				//循环所有列
				var thNodes = rowNodes[i].childNodes;
				for (var j = 0; j < thNodes.length; j++) {
					var thNode = thNodes[j];
					//序号列，checkbox勾选列剔除
					if (thNode.id == gridId + "_rn" || thNode.id == gridId + "_cb") {
						continue;
					}
					//隐藏列剔除
					var style = thNode.getAttribute("style");
					if (style && style.toLowerCase().replace(/ /g, "").indexOf("display:none") > -1) {
						continue;
					}
					var rowspan = Number(thNode.getAttribute("rowspan") || "1");
					var colspan = Number(thNode.getAttribute("colspan") || "1");
					var className = $.trim(thNode.className);
					var innerText = "";
					if (className.indexOf("ui-th-column") > -1 && className.indexOf("ui-th-column-header") == -1) {
						innerText = $("#jqgh_" + thNode.id)[0].innerText;
					}
					if (className.indexOf("ui-th-column-header") > -1) {
						innerText = thNode.innerText;
                    }
					ExcelRow.Cells.push({ Rowspan: rowspan, Colspan: colspan, Value: innerText });
				}
				ExcelRows.push(ExcelRow);
			}

			return ExcelRows;
		},

		//得到JqGrid表格列定义
		getJqGridColModel: function (gridControl) {
			var colModel = $("#" + gridControl.id).jqGrid("getGridParam").colModel.filter(function (item) {
				var ifHidden = (item.hidedlg && item.name == "cb") ||
					(item.hidedlg && item.name == "rn") ||
					(item.hidden || item.hidden == "true");
				return !ifHidden;
			});
			return colModel;
		},

		//得到JqGrid表格需要合并的列索引
		getJqGridMergeColIndex: function (gridControl) {
			var _this = this;
			var mergeColIndex = [];
			var colModelList = _this.getJqGridColModel(gridControl);
			for (var i = 0; i < colModelList.length; i++) {
				if (colModelList[i].merge || colModelList[i].merge == "true") {
					mergeColIndex.push(i);
				}
			}
			return mergeColIndex;
		},

		//得到JqGrid数据区域
		getJqGridDatas: function (gridControl, exportDataList) {
			var _this = this;
			//列定义
			var colModelList = _this.getJqGridColModel(gridControl);
			//数据行
			var dataList = exportDataList || gridControl.value();
			//返回的结果
			var Datas = [];
			for (var i = 0; i < dataList.length; i++) {
				var ExcelRow = { Cells: [] };
				for (var j = 0; j < colModelList.length; j++) {
					//列控件
					var Control = colModelList[j].control || "controls/textbox";

					//列名称
					var Name = colModelList[j].name;

					//数据类型
					var Datatype = 0;
					if (Control == "controls/textbox" || Control == "controls/select2") Datatype = 1;
					if (Control == "controls/datepicker") Datatype = 3;
					if (Control == "controls/number") Datatype = 2;

					//对齐方式
					var Align = colModelList[j].align;
					if (!Align) {
						//默认左对齐
						Align = "left";
						if (Control == "controls/textbox" || Control == "controls/select2") Align = "left";
						if (Control == "controls/datepicker") Align = "center";
						if (Control == "controls/number") Align = "right";
					}
					if (Align == "left") Align = 1;
					if (Align == "center") Align = 2;
					if (Align == "right") Align = 3;

					//基础资料业务类型
					var Ywlx = colModelList[j].Ywlx || colModelList[j].ywlx || "";

					//格式化字符串
					var Formater = colModelList[j].formater || "";

					//小数位数
					var DecimalDigits = colModelList[j].decimalDigits;
					if (Datatype == 2 && (DecimalDigits == undefined || DecimalDigits == null || DecimalDigits === ""))
					{
						DecimalDigits = 2;
					}

					//data属性，例如：data:[{id:'1',text:'已结算'},{id:'2',text:'未结算'}]
					var Data = colModelList[j].data;

					var Value = dataList[i][Name];
					if (Value == undefined || Value == null) {
						Value = "";
					}
					var cell = { Name: Name, Value: Value, Datatype: Datatype, Ywlx: Ywlx, Formater: Formater, DecimalDigits: DecimalDigits, Data: Data, Align: Align };
					ExcelRow.Cells.push(cell);
				}
				Datas.push(ExcelRow);
			}
			return Datas;
		},

		//导出接口，生成EXCEL文件
		export: function (workbook) {
			F.util.showWait();
			F.ajax({
				url: "/API/IFP/BaseInfo/ExcelExport/export",
				data: workbook,
				success: function (resp) {
					F.util.hideWait();
					var fileName = resp["fileName"];
					if (fileName == "") {
						$.bootoast.danger("系统错误。");
						return;
					}
					var href = "/fileDownLoad/" + encodeURI(fileName);
					if ($("#excel_export_link_1478965").length == 0) {
						$(document.body).append("<a href=\"" + href + "\" id=\"excel_export_link_1478965\" style=\"display:none\">导出链接</a>");
					}
					$("#excel_export_link_1478965").attr("href", href);
					$("#excel_export_link_1478965")[0].click();
				}
			})
		}
	}
});