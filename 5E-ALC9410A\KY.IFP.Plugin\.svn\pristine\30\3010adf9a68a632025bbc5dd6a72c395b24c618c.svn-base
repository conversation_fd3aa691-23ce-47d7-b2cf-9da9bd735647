﻿using COM.IFP.SqlSugarN;
using ORM.IFP.DbModel;
using SqlSugar;
using System.Collections.Generic;

namespace ORM.IFP.www.DbModel.SM
{
    /// <summary>
    /// 参数分组 有哪些组
    /// </summary>
    [SugarTable("IFP_SM_CSFENZU")]
    public partial class IFP_SM_CSFENZU
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(36)")]
        public Field<string> Gid { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>
        [SugarColumn(ColumnName = "NAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> Name { get; set; }

        /// <summary>
        /// 是否停用
        /// </summary>
        [SugarColumn(ColumnName = "STOPSTATE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "INT")]
        public Field<int> StopState { get; set; }

        /// <summary>
        /// 顺序号
        /// </summary>
        [SugarColumn(ColumnName = "SXH", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "INT")]
        public Field<int> Sxh { get; set; }

    }


    /* 以下为对应的Json对象
	{
		GID: null,
		NAME: null,		//分组名称
		STOPSTATE: null,		//是否停用
		SXH: null		//顺序号
	}
	*/

    public partial class IFP_SM_CSFENZU
    {
        ////------------------------查询条件-------------------------
        ///// <summary>
        ///// 删除分组 由要删除的分组的Gid构成
        ///// </summary>
        //public List<int> DeleteFenZu { get; set; }

        /// <summary>
        /// 保存后的分组
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<IFP_SM_CSMAPFENZU> SaveGroup { get; set; }

        /// <summary>
        /// 无参数的分组是否要填充一个空对象 填充适用于要获得所有分组的情况
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool flag { get; set; }
    }
}