!function(t){"use strict";var g=(t=t&&t.hasOwnProperty("default")?t.default:t).window,n=t.self,e=t.console,r=t.setTimeout,s=t.clearTimeout,i=g&&g.document,o=g&&g.navigator,a=function(){var e="qunit-test-string";try{return t.sessionStorage.setItem(e,e),t.sessionStorage.removeItem(e),t.sessionStorage}catch(e){return}}();function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var l,c={warn:(l="warn",function(){e&&e[l].apply(e,arguments)})},d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f=function(e,t,n){return t&&h(e.prototype,t),n&&h(e,n),e};function h(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}var p=function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)},m=Object.prototype.toString,v=Object.prototype.hasOwnProperty,b=Date.now||function(){return(new Date).getTime()},y=g&&void 0!==g.performance&&"function"==typeof g.performance.mark&&"function"==typeof g.performance.measure,k=y?g.performance:void 0,w=y?k.now.bind(k):b;function x(e,t,n){try{k.measure(e,t,n)}catch(e){c.warn("performance.measure could not be executed because of ",e.message)}}var E={document:g&&void 0!==g.document,setTimeout:void 0!==r};function T(e,t){for(var n,s=e.slice(),r=0;r<s.length;r++)for(n=0;n<t.length;n++)if(s[r]===t[n]){s.splice(r,1),r--;break}return s}function C(e,t){return-1!==t.indexOf(e)}function q(e){var t,n,s=_("array",e)?[]:{};for(t in e)v.call(e,t)&&(n=e[t],s[t]=n===Object(n)?q(n):n);return s}function N(e,t,n){for(var s in t)v.call(t,s)&&(void 0===t[s]?delete e[s]:n&&void 0!==e[s]||(e[s]=t[s]));return e}function M(e){if(void 0===e)return"undefined";if(null===e)return"null";var t=m.call(e).match(/^\[object\s(.*)\]$/),n=t&&t[1];switch(n){case"Number":return isNaN(e)?"nan":"number";case"String":case"Boolean":case"Array":case"Set":case"Map":case"Date":case"RegExp":case"Function":case"Symbol":return n.toLowerCase();default:return void 0===e?"undefined":d(e)}}function _(e,t){return M(t)===e}function S(e,t){for(var n=e+""+t,s=0,r=0;r<n.length;r++)s=(s<<5)-s+n.charCodeAt(r),s|=0;var i=(4294967296+s).toString(16);return i.length<8&&(i="0000000"+i),i.slice(-8)}var j,R,A,I=(j=[],R=Object.getPrototypeOf||function(e){return e.__proto__},function(){var e=F.apply(void 0,arguments);return j.length=0,e});function O(e,t){return"object"===(void 0===e?"undefined":d(e))&&(e=e.valueOf()),"object"===(void 0===t?"undefined":d(t))&&(t=t.valueOf()),e===t}function L(e){return"flags"in e?e.flags:e.toString().match(/[gimuy]*$/)[0]}function H(t,n){return t===n||(-1===["object","array","map","set"].indexOf(M(t))?D(t,n):(j.every(function(e){return e.a!==t||e.b!==n})&&j.push({a:t,b:n}),1))}function D(e,t){var n=M(e);return M(t)===n&&A[n](e,t)}function F(e,t){var n,s;if(arguments.length<2)return!0;for(j=[{a:e,b:t}],n=0;n<j.length;n++)if((s=j[n]).a!==s.b&&!D(s.a,s.b))return!1;return 2===arguments.length||F.apply(this,[].slice.call(arguments,1))}var P={queue:[],blocking:!0,reorder:!0,altertitle:!0,collapse:!0,scrolltop:!0,maxDepth:5,requireExpects:!(A={string:O,boolean:O,number:O,null:O,undefined:O,symbol:O,date:O,nan:function(){return!0},regexp:function(e,t){return e.source===t.source&&L(e)===L(t)},function:function(){return!1},array:function(e,t){var n,s=e.length;if(s!==t.length)return!1;for(n=0;n<s;n++)if(!H(e[n],t[n]))return!1;return!0},set:function(e,t){var s,r=!0;return e.size===t.size&&(e.forEach(function(n){r&&(s=!1,t.forEach(function(e){var t;s||(t=j,F(e,n)&&(s=!0),j=t)}),s||(r=!1))}),r)},map:function(e,t){var i,n=!0;return e.size===t.size&&(e.forEach(function(s,r){n&&(i=!1,t.forEach(function(e,t){var n;i||(n=j,F([e,t],[s,r])&&(i=!0),j=n)}),i||(n=!1))}),n)},object:function(e,t){var n,s,r,i,o,a=[],u=[];if(!1==(r=t,i=R(s=e),o=R(r),s.constructor===r.constructor||(i&&null===i.constructor&&(i=null),o&&null===o.constructor&&(o=null),null===i&&o===Object.prototype||null===o&&i===Object.prototype)))return!1;for(n in e)if(a.push(n),(e.constructor===Object||void 0===e.constructor||"function"!=typeof e[n]||"function"!=typeof t[n]||e[n].toString()!==t[n].toString())&&!H(e[n],t[n]))return!1;for(n in t)u.push(n);return D(a.sort(),u.sort())}}),urlConfig:[],modules:[],currentModule:{name:"",tests:[],childModules:[],testsRun:0,unskippedTestsRun:0,hooks:{before:[],beforeEach:[],afterEach:[],after:[]}},callbacks:{},storage:a},U=g&&g.QUnit&&g.QUnit.config;g&&g.QUnit&&!g.QUnit.version&&N(P,U),P.modules.push(P.currentModule);var B,Q,G=(B=/^function (\w+)/,Q={parse:function(e,t,n){var s,r,i,o=(n=n||[]).indexOf(e);return-1!==o?"recursion("+(o-n.length)+")":(t=t||this.typeOf(e),"function"===(i=void 0===(r=this.parsers[t])?"undefined":d(r))?(n.push(e),s=r.call(this,e,n),n.pop(),s):"string"===i?r:this.parsers.error)},typeOf:function(e){var t,n=null===e?"null":void 0===e?"undefined":_("regexp",e)?"regexp":_("date",e)?"date":_("function",e)?"function":void 0!==e.setInterval&&void 0!==e.document&&void 0===e.nodeType?"window":9===e.nodeType?"document":e.nodeType?"node":(t=e,"[object Array]"===m.call(t)||"number"==typeof t.length&&void 0!==t.item&&(t.length?t.item(0)===t[0]:null===t.item(0)&&void 0===t[0])?"array":e.constructor===Error.prototype.constructor?"error":void 0===e?"undefined":d(e));return n},separator:function(){return this.multiline?this.HTML?"<br />":"\n":this.HTML?"&#160;":" "},indent:function(e){if(!this.multiline)return"";var t=this.indentChar;return this.HTML&&(t=t.replace(/\t/g,"   ").replace(/ /g,"&#160;")),new Array(this.depth+(e||0)).join(t)},up:function(e){this.depth+=e||1},down:function(e){this.depth-=e||1},setParser:function(e,t){this.parsers[e]=t},quote:Y,literal:$,join:z,depth:1,maxDepth:P.maxDepth,parsers:{window:"[Window]",document:"[Document]",error:function(e){return'Error("'+e.message+'")'},unknown:"[Unknown]",null:"null",undefined:"undefined",function:function(e){var t="function",n="name"in e?e.name:(B.exec(e)||[])[1];return n&&(t+=" "+n),z(t=[t+="(",Q.parse(e,"functionArgs"),"){"].join(""),Q.parse(e,"functionCode"),"}")},array:W,nodelist:W,arguments:W,object:function(e,t){var n,s,r,i,o,a=[];if(Q.maxDepth&&Q.depth>Q.maxDepth)return"[object Object]";for(s in Q.up(),n=[],e)n.push(s);for(i in o=["message","name"])(s=o[i])in e&&!C(s,n)&&n.push(s);for(n.sort(),i=0;i<n.length;i++)r=e[s=n[i]],a.push(Q.parse(s,"key")+": "+Q.parse(r,void 0,t));return Q.down(),z("{",a,"}")},node:function(e){var t,n,s,r=Q.HTML?"&lt;":"<",i=Q.HTML?"&gt;":">",o=e.nodeName.toLowerCase(),a=r+o,u=e.attributes;if(u)for(n=0,t=u.length;n<t;n++)(s=u[n].nodeValue)&&"inherit"!==s&&(a+=" "+u[n].nodeName+"="+Q.parse(s,"attribute"));return a+=i,3!==e.nodeType&&4!==e.nodeType||(a+=e.nodeValue),a+r+"/"+o+i},functionArgs:function(e){var t,n=e.length;if(!n)return"";for(t=new Array(n);n--;)t[n]=String.fromCharCode(97+n);return" "+t.join(", ")+" "},key:Y,functionCode:"[code]",attribute:Y,string:Y,date:Y,regexp:$,number:$,boolean:$,symbol:function(e){return e.toString()}},HTML:!1,indentChar:"  ",multiline:!0});function Y(e){return'"'+e.toString().replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"'}function $(e){return e+""}function z(e,t,n){var s=Q.separator(),r=Q.indent(),i=Q.indent(1);return t.join&&(t=t.join(","+s+i)),t?[e,i+t,r+n].join(s):e+n}function W(e,t){var n=e.length,s=new Array(n);if(Q.maxDepth&&Q.depth>Q.maxDepth)return"[object Array]";for(this.up();n--;)s[n]=this.parse(e[n],void 0,t);return this.down(),z("[",s,"]")}var V=(f(K,[{key:"start",value:function(e){var t;return e&&(this._startTime=w(),k&&(t=this.fullName.length,k.mark("qunit_suite_"+t+"_start"))),{name:this.name,fullName:this.fullName.slice(),tests:this.tests.map(function(e){return e.start()}),childSuites:this.childSuites.map(function(e){return e.start()}),testCounts:{total:this.getTestCounts().total}}}},{key:"end",value:function(e){var t,n;return e&&(this._endTime=w(),k&&(t=this.fullName.length,k.mark("qunit_suite_"+t+"_end"),n=this.fullName.join(" – "),x(0===t?"QUnit Test Run":"QUnit Test Suite: "+n,"qunit_suite_"+t+"_start","qunit_suite_"+t+"_end"))),{name:this.name,fullName:this.fullName.slice(),tests:this.tests.map(function(e){return e.end()}),childSuites:this.childSuites.map(function(e){return e.end()}),testCounts:this.getTestCounts(),runtime:this.getRuntime(),status:this.getStatus()}}},{key:"pushChildSuite",value:function(e){this.childSuites.push(e)}},{key:"pushTest",value:function(e){this.tests.push(e)}},{key:"getRuntime",value:function(){return this._endTime-this._startTime}},{key:"getTestCounts",value:function(e){var t=0<arguments.length&&void 0!==e?e:{passed:0,failed:0,skipped:0,todo:0,total:0},t=this.tests.reduce(function(e,t){return t.valid&&(e[t.getStatus()]++,e.total++),e},t);return this.childSuites.reduce(function(e,t){return t.getTestCounts(e)},t)}},{key:"getStatus",value:function(){var e=this.getTestCounts(),t=e.total,n=e.failed,s=e.skipped,r=e.todo;return n?"failed":s===t?"skipped":r===t?"todo":"passed"}}]),K);function K(e,t){u(this,K),this.name=e,this.fullName=t?t.fullName.concat(e):[],this.tests=[],this.childSuites=[],t&&t.pushChildSuite(this)}var J=!1,X=[];function Z(e,t,n){var s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{};"function"===M(t)&&(n=t,t=void 0);var r,i,o,a,u,l,c,d,f,h,p=(r=e,i=t,o=s,a=X.length?X.slice(-1)[0]:null,u=null!==a?[a.name,r].join(" > "):r,l=a?a.suiteReport:vt,c=null!==a&&a.skip||o.skip,d=null!==a&&a.todo||o.todo,f={name:u,parentModule:a,tests:[],moduleId:S(u),testsRun:0,unskippedTestsRun:0,childModules:[],suiteReport:new V(r,l),skip:c,todo:!c&&d},h={},a&&(a.childModules.push(f),N(h,a.testEnvironment)),N(h,i),f.testEnvironment=h,P.modules.push(f),f),m=p.testEnvironment,g=p.hooks={};y(g,m,"before"),y(g,m,"beforeEach"),y(g,m,"afterEach"),y(g,m,"after");var v={before:k(p,"before"),beforeEach:k(p,"beforeEach"),afterEach:k(p,"afterEach"),after:k(p,"after")},b=P.currentModule;function y(e,t,n){var s=t[n];e[n]="function"==typeof s?[s]:[],delete t[n]}function k(t,n){return function(e){t.hooks[n].push(e)}}"function"===M(n)&&(X.push(p),P.currentModule=p,n.call(p.testEnvironment,v),X.pop(),p=p.parentModule||b),P.currentModule=p}function ee(e,t,n){var s;J&&(s=P.modules.map(function(e){return e.moduleId}),!X.some(function(e){return s.includes(e.moduleId)}))||Z(e,t,n)}ee.only=function(){J||(P.modules.length=0,P.queue.length=0),Z.apply(void 0,arguments),J=!0},ee.skip=function(e,t,n){J||Z(e,t,n,{skip:!0})},ee.todo=function(e,t,n){J||Z(e,t,n,{todo:!0})};var te=Object.create(null),ne=["runStart","suiteStart","testStart","assertion","testEnd","suiteEnd","runEnd"];function se(e,t){if("string"!==M(e))throw new TypeError("eventName must be a string when emitting an event");for(var n=te[e],s=n?[].concat(p(n)):[],r=0;r<s.length;r++)s[r](t)}function re(e){return"function"==typeof e}var ie=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},oe=0,ae=void 0,ue=void 0,le=function(e,t){ge[oe]=e,ge[oe+1]=t,2===(oe+=2)&&(ue?ue(ve):xe())};var ce="undefined"!=typeof window?window:void 0,de=ce||{},fe=de.MutationObserver||de.WebKitMutationObserver,he="undefined"==typeof self&&"undefined"!=typeof process&&"[object process]"==={}.toString.call(process),pe="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function me(){var e=setTimeout;return function(){return e(ve,1)}}var ge=new Array(1e3);function ve(){for(var e=0;e<oe;e+=2){(0,ge[e])(ge[e+1]),ge[e]=void 0,ge[e+1]=void 0}oe=0}var be,ye,ke,we,xe=void 0;function Ee(e,t){var n=this,s=new this.constructor(qe);void 0===s[Ce]&&Pe(s);var r,i=n._state;return i?(r=arguments[i-1],le(function(){return De(i,s,r,n._result)})):Le(n,s,e,t),s}function Te(e){if(e&&"object"===(void 0===e?"undefined":d(e))&&e.constructor===this)return e;var t=new this(qe);return Re(t,e),t}xe=he?function(){return process.nextTick(ve)}:fe?(ye=0,ke=new fe(ve),we=document.createTextNode(""),ke.observe(we,{characterData:!0}),function(){we.data=ye=++ye%2}):pe?((be=new MessageChannel).port1.onmessage=ve,function(){return be.port2.postMessage(0)}):(void 0===ce&&"function"==typeof require?function(){try{var e=Function("return this")().require("vertx");return void 0!==(ae=e.runOnLoop||e.runOnContext)?function(){ae(ve)}:me()}catch(e){return me()}}:me)();var Ce=Math.random().toString(36).substring(2);function qe(){}var Ne=void 0,Me=1,_e=2;function Se(e,s,r){le(function(t){var n=!1,e=function(e,t,n,s){try{e.call(t,n,s)}catch(e){return e}}(r,s,function(e){n||(n=!0,(s!==e?Re:Ie)(t,e))},function(e){n||(n=!0,Oe(t,e))},t._label);!n&&e&&(n=!0,Oe(t,e))},e)}function je(e,t,n){var s,r;t.constructor===e.constructor&&n===Ee&&t.constructor.resolve===Te?(s=e,(r=t)._state===Me?Ie(s,r._result):r._state===_e?Oe(s,r._result):Le(r,void 0,function(e){return Re(s,e)},function(e){return Oe(s,e)})):void 0!==n&&re(n)?Se(e,t,n):Ie(e,t)}function Re(t,e){if(t===e)Oe(t,new TypeError("You cannot resolve a promise with itself"));else if(r=void 0===(s=e)?"undefined":d(s),null===s||"object"!==r&&"function"!==r)Ie(t,e);else{var n=void 0;try{n=e.then}catch(e){return void Oe(t,e)}je(t,e,n)}var s,r}function Ae(e){e._onerror&&e._onerror(e._result),He(e)}function Ie(e,t){e._state===Ne&&(e._result=t,e._state=Me,0!==e._subscribers.length&&le(He,e))}function Oe(e,t){e._state===Ne&&(e._state=_e,e._result=t,le(Ae,e))}function Le(e,t,n,s){var r=e._subscribers,i=r.length;e._onerror=null,r[i]=t,r[i+Me]=n,r[i+_e]=s,0===i&&e._state&&le(He,e)}function He(e){var t=e._subscribers,n=e._state;if(0!==t.length){for(var s=void 0,r=void 0,i=e._result,o=0;o<t.length;o+=3)s=t[o],r=t[o+n],s?De(n,s,r,i):r(i);e._subscribers.length=0}}function De(e,t,n,s){var r=re(n),i=void 0,o=void 0,a=!0;if(r){try{i=n(s)}catch(e){a=!1,o=e}if(t===i)return void Oe(t,new TypeError("A promises callback cannot return that same promise."))}else i=s;t._state!==Ne||(r&&a?Re(t,i):!1===a?Oe(t,o):e===Me?Ie(t,i):e===_e&&Oe(t,i))}var Fe=0;function Pe(e){e[Ce]=Fe++,e._state=void 0,e._result=void 0,e._subscribers=[]}var Ue=(f(Be,[{key:"_enumerate",value:function(e){for(var t=0;this._state===Ne&&t<e.length;t++)this._eachEntry(e[t],t)}},{key:"_eachEntry",value:function(t,e){var n=this._instanceConstructor,s=n.resolve;if(s===Te){var r,i=void 0,o=void 0,a=!1;try{i=t.then}catch(e){a=!0,o=e}i===Ee&&t._state!==Ne?this._settledAt(t._state,e,t._result):"function"!=typeof i?(this._remaining--,this._result[e]=t):n===Qe?(r=new n(qe),a?Oe(r,o):je(r,t,i),this._willSettleAt(r,e)):this._willSettleAt(new n(function(e){return e(t)}),e)}else this._willSettleAt(s(t),e)}},{key:"_settledAt",value:function(e,t,n){var s=this.promise;s._state===Ne&&(this._remaining--,e===_e?Oe(s,n):this._result[t]=n),0===this._remaining&&Ie(s,this._result)}},{key:"_willSettleAt",value:function(e,t){var n=this;Le(e,void 0,function(e){return n._settledAt(Me,t,e)},function(e){return n._settledAt(_e,t,e)})}}]),Be);function Be(e,t){u(this,Be),this._instanceConstructor=e,this.promise=new e(qe),this.promise[Ce]||Pe(this.promise),ie(t)?(this.length=t.length,this._remaining=t.length,this._result=new Array(this.length),0===this.length?Ie(this.promise,this._result):(this.length=this.length||0,this._enumerate(t),0===this._remaining&&Ie(this.promise,this._result))):Oe(this.promise,new Error("Array Methods must be provided an Array"))}var Qe=(f(Ge,[{key:"catch",value:function(e){return this.then(null,e)}},{key:"finally",value:function(t){var n=this.constructor;return re(t)?this.then(function(e){return n.resolve(t()).then(function(){return e})},function(e){return n.resolve(t()).then(function(){throw e})}):this.then(t,t)}}]),Ge);function Ge(e){u(this,Ge),this[Ce]=Fe++,this._result=this._state=void 0,this._subscribers=[],qe!==e&&("function"!=typeof e&&function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof Ge?function(t,e){try{e(function(e){Re(t,e)},function(e){Oe(t,e)})}catch(e){Oe(t,e)}}(this,e):function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())}Qe.prototype.then=Ee,Qe.all=function(e){return new Ue(this,e).promise},Qe.race=function(r){var i=this;return ie(r)?new i(function(e,t){for(var n=r.length,s=0;s<n;s++)i.resolve(r[s]).then(e,t)}):new i(function(e,t){return t(new TypeError("You must pass an array to race."))})},Qe.resolve=Te,Qe.reject=function(e){var t=new this(qe);return Oe(t,e),t},Qe._setScheduler=function(e){ue=e},Qe._setAsap=function(e){le=e},Qe._asap=le,Qe.polyfill=function(){var e=void 0;if("undefined"!=typeof global)e=global;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var t=e.Promise;if(t){var n=null;try{n=Object.prototype.toString.call(t.resolve())}catch(e){}if("[object Promise]"===n&&!t.cast)return}e.Promise=Qe},Qe.Promise=Qe;var Ye="undefined"!=typeof Promise?Promise:Qe;function $e(e,n){var t=P.callbacks[e];if("log"!==e)return t.reduce(function(e,t){return e.then(function(){return Ye.resolve(t(n))})},Ye.resolve([]));t.map(function(e){return e(n)})}var ze=(Ve(0)||"").replace(/(:\d+)+\)?/,"").replace(/.+\//,"");function We(e,t){var n,s,r;if(t=void 0===t?4:t,e&&e.stack){if(n=e.stack.split("\n"),/^error$/i.test(n[0])&&n.shift(),ze){for(s=[],r=t;r<n.length&&-1===n[r].indexOf(ze);r++)s.push(n[r]);if(s.length)return s.join("\n")}return n[t]}}function Ve(e){var t=new Error;if(!t.stack)try{throw t}catch(e){t=e}return We(t,e)}var Ke=0,Je=void 0,Xe=[];function Ze(){var e;e=b(),P.depth=(P.depth||0)+1,function e(t){{var n,s;Xe.length&&!P.blocking&&(n=b()-t,!E.setTimeout||P.updateRate<=0||n<P.updateRate?(s=Xe.shift(),Ye.resolve(s()).then(function(){Xe.length?e(t):Ze()})):r(Ze))}}(e),P.depth--,Xe.length||P.blocking||P.current||function(){if(!P.blocking&&!P.queue.length&&0===P.depth)return function(){var n=P.storage;et.finished=!0;var e=b()-P.started,t=P.stats.all-P.stats.bad;if(0===P.stats.all){if(P.filter&&P.filter.length)throw new Error('No tests matched the filter "'+P.filter+'".');if(P.module&&P.module.length)throw new Error('No tests matched the module "'+P.module+'".');if(P.moduleId&&P.moduleId.length)throw new Error('No tests matched the moduleId "'+P.moduleId+'".');if(P.testId&&P.testId.length)throw new Error('No tests matched the testId "'+P.testId+'".');throw new Error("No tests were run.")}se("runEnd",vt.end(!0)),$e("done",{passed:t,failed:P.stats.bad,total:P.stats.all,runtime:e}).then(function(){if(n&&0===P.stats.bad)for(var e=n.length-1;0<=e;e--){var t=n.key(e);0===t.indexOf("qunit-test-")&&n.removeItem(t)}})}();(function(e){Xe.push.apply(Xe,p(e))})(P.queue.shift()()),0<Ke&&Ke--;Ze()}()}var et={finished:!1,add:function(e,t,n){var s,r;t?P.queue.splice(Ke++,0,e):n?(Je||(r=parseInt(S(n),16)||-1,Je=function(){return r^=r<<13,r^=r>>>17,(r^=r<<5)<0&&(r+=4294967296),r/4294967296}),s=Math.floor(Je()*(P.queue.length-Ke+1)),P.queue.splice(Ke+s,0,e)):P.queue.push(e)},advance:Ze,taskCount:function(){return Xe.length}},tt=(f(nt,[{key:"start",value:function(e){return e&&(this._startTime=w(),k&&k.mark("qunit_test_start")),{name:this.name,suiteName:this.suiteName,fullName:this.fullName.slice()}}},{key:"end",value:function(e){return e&&(this._endTime=w(),k&&(k.mark("qunit_test_end"),x("QUnit Test: "+this.fullName.join(" – "),"qunit_test_start","qunit_test_end"))),N(this.start(),{runtime:this.getRuntime(),status:this.getStatus(),errors:this.getFailedAssertions(),assertions:this.getAssertions()})}},{key:"pushAssertion",value:function(e){this.assertions.push(e)}},{key:"getRuntime",value:function(){return this._endTime-this._startTime}},{key:"getStatus",value:function(){return this.skipped?"skipped":(0<this.getFailedAssertions().length?this.todo:!this.todo)?this.todo?"todo":"passed":"failed"}},{key:"getFailedAssertions",value:function(){return this.assertions.filter(function(e){return!e.passed})}},{key:"getAssertions",value:function(){return this.assertions.slice()}},{key:"slimAssertions",value:function(){this.assertions=this.assertions.map(function(e){return delete e.actual,delete e.expected,e})}}]),nt);function nt(e,t,n){u(this,nt),this.name=e,this.suiteName=t.name,this.fullName=t.fullName.concat(e),this.runtime=0,this.assertions=[],this.skipped=!!n.skip,this.todo=!!n.todo,this.valid=n.valid,this._startTime=0,this._endTime=0,t.pushTest(this)}var st=!1;function rt(e){var t,n;for(++rt.count,this.expected=null,this.assertions=[],this.semaphore=0,this.module=P.currentModule,this.steps=[],this.timeout=void 0,this.errorForStack=new Error,this.module.skip?(e.skip=!0,e.todo=!1):this.module.todo&&!e.skip&&(e.todo=!0),N(this,e),this.testReport=new tt(e.testName,this.module.suiteReport,{todo:e.todo,skip:e.skip,valid:this.valid()}),t=0,n=this.module.tests;t<n.length;t++)this.module.tests[t].name===this.testName&&(this.testName+=" ");if(this.testId=S(this.module.name,this.testName),this.module.tests.push({name:this.testName,testId:this.testId,skip:!!e.skip}),e.skip)this.callback=function(){},this.async=!1,this.expected=0;else{if("function"!=typeof this.callback){var s=this.todo?"todo":"test";throw new TypeError("You must provide a function as a test callback to QUnit."+s+'("'+e.testName+'")')}this.assert=new ht(this)}}function it(){if(!P.current)throw new Error("pushFailure() assertion outside test context, in "+Ve(2));var e=P.current;return e.pushFailure.apply(e,arguments)}function ot(){if(P.pollution=[],P.noglobals)for(var e in t)if(v.call(t,e)){if(/^qunit-test-output/.test(e))continue;P.pollution.push(e)}}function at(e,t){st||new rt({testName:e,callback:t}).queue()}function ut(t){var e,n=!1;return t.semaphore+=1,P.blocking=!0,E.setTimeout&&(e=void 0,"number"==typeof t.timeout?e=t.timeout:"number"==typeof P.testTimeout&&(e=P.testTimeout),"number"==typeof e&&0<e&&(s(P.timeout),P.timeoutHandler=function(e){return function(){it("Test took longer than "+e+"ms; test timed out.",Ve(2)),n=!0,lt(t)}},P.timeout=r(P.timeoutHandler(e),e))),function(){n||(n=!0,--t.semaphore,ct(t))}}function lt(e){e.semaphore=0,ct(e)}function ct(e){return isNaN(e.semaphore)?(e.semaphore=0,void it("Invalid value on test.semaphore",Ve(2))):!(0<e.semaphore)&&(e.semaphore<0?(e.semaphore=0,void it("Tried to restart test while already started (test's semaphore was 0 already)",Ve(2))):void(E.setTimeout?(P.timeout&&s(P.timeout),P.timeout=r(function(){0<e.semaphore||(P.timeout&&s(P.timeout),qt())})):qt()))}function dt(e){for(var t=[].concat(e.tests),n=[].concat(p(e.childModules));n.length;){var s=n.shift();t.push.apply(t,s.tests),n.push.apply(n,p(s.childModules))}return t}function ft(e){return dt(e).length}rt.count=0,rt.prototype={get stack(){return We(this.errorForStack,2)},before:function(){var e=this,t=this.module;return function(e){for(var t=e,n=[];t&&0===t.testsRun;)n.push(t),t=t.parentModule;return n.reverse()}(t).reduce(function(e,t){return e.then(function(){return t.stats={all:0,bad:0,started:b()},se("suiteStart",t.suiteReport.start(!0)),$e("moduleStart",{name:t.name,tests:t.tests})})},Ye.resolve([])).then(function(){return(P.current=e).testEnvironment=N({},t.testEnvironment),e.started=b(),se("testStart",e.testReport.start(!0)),$e("testStart",{name:e.testName,module:t.name,testId:e.testId,previousFailure:e.previousFailure}).then(function(){P.pollution||ot()})})},run:function(){var t;if((P.current=this).callbackStarted=b(),P.notrycatch)e(this);else try{e(this)}catch(e){this.pushFailure("Died on test #"+(this.assertions.length+1)+" "+this.stack+": "+(e.message||e),We(e,0)),ot(),P.blocking&&lt(this)}function e(e){t=e.callback.call(e.testEnvironment,e.assert),e.resolvePromise(t),0===e.timeout&&0!==e.semaphore&&it("Test did not finish synchronously even though assert.timeout( 0 ) was used.",Ve(2))}},after:function(){!function(){var e,t,n=P.pollution;ot(),0<(e=T(P.pollution,n)).length&&it("Introduced global variable(s): "+e.join(", "));0<(t=T(n,P.pollution)).length&&it("Deleted global variable(s): "+t.join(", "))}()},queueHook:function(t,n,e){function s(){var e=t.call(r.testEnvironment,r.assert);r.resolvePromise(e,n)}var r=this;return function(){if("before"===n){if(0!==e.unskippedTestsRun)return;r.preserveEnvironment=!0}if("after"!==n||e.unskippedTestsRun===dt(e).filter(function(e){return!e.skip}).length-1||!(0<P.queue.length||2<et.taskCount()))if(P.current=r,P.notrycatch)s();else try{s()}catch(e){r.pushFailure(n+" failed on "+r.testName+": "+(e.message||e),We(e,0))}}},hooks:function(r){var i=[];return this.skip||function e(t,n){if(n.parentModule&&e(t,n.parentModule),n.hooks[r].length)for(var s=0;s<n.hooks[r].length;s++)i.push(t.queueHook(n.hooks[r][s],r,n))}(this,this.module),i},finish:function(){var e;(P.current=this).callback=void 0,this.steps.length&&(e=this.steps.join(", "),this.pushFailure("Expected assert.verifySteps() to be called before end of test after using assert.step(). Unverified steps: "+e,this.stack)),P.requireExpects&&null===this.expected?this.pushFailure("Expected number of assertions to be defined, but expect() was not called.",this.stack):null!==this.expected&&this.expected!==this.assertions.length?this.pushFailure("Expected "+this.expected+" assertions, but "+this.assertions.length+" were run",this.stack):null!==this.expected||this.assertions.length||this.pushFailure("Expected at least one assertion, but none were run - call expect(0) to accept zero assertions.",this.stack);var t,n=this.module,s=n.name,r=this.testName,i=!!this.skip,o=!!this.todo,a=0,u=P.storage;for(this.runtime=b()-this.started,P.stats.all+=this.assertions.length,n.stats.all+=this.assertions.length,t=0;t<this.assertions.length;t++)this.assertions[t].result||(a++,P.stats.bad++,n.stats.bad++);!function(e,t){e.testsRun++,t||e.unskippedTestsRun++;for(;e=e.parentModule;)e.testsRun++,t||e.unskippedTestsRun++}(n,i),u&&(a?u.setItem("qunit-test-"+s+"-"+r,a):u.removeItem("qunit-test-"+s+"-"+r)),se("testEnd",this.testReport.end(!0)),this.testReport.slimAssertions();var l=this;return $e("testDone",{name:r,module:s,skipped:i,todo:o,failed:a,passed:this.assertions.length-a,total:this.assertions.length,runtime:i?0:this.runtime,assertions:this.assertions,testId:this.testId,get source(){return l.stack}}).then(function(){if(n.testsRun===ft(n)){for(var e=[n],t=n.parentModule;t&&t.testsRun===ft(t);)e.push(t),t=t.parentModule;return e.reduce(function(e,t){return e.then(function(){return(e=t).hooks={},se("suiteEnd",e.suiteReport.end(!0)),$e("moduleDone",{name:e.name,tests:e.tests,failed:e.stats.bad,passed:e.stats.all-e.stats.bad,total:e.stats.all,runtime:b()-e.stats.started});var e})},Ye.resolve([]))}}).then(function(){P.current=void 0})},preserveTestEnvironment:function(){this.preserveEnvironment&&(this.module.testEnvironment=this.testEnvironment,this.testEnvironment=N({},this.module.testEnvironment))},queue:function(){var e,t,n=this;this.valid()&&(e=P.storage&&+P.storage.getItem("qunit-test-"+this.module.name+"-"+this.testName),t=P.reorder&&!!e,this.previousFailure=!!e,et.add(function(){return[function(){return n.before()}].concat(p(n.hooks("before")),[function(){n.preserveTestEnvironment()}],p(n.hooks("beforeEach")),[function(){n.run()}],p(n.hooks("afterEach").reverse()),p(n.hooks("after").reverse()),[function(){n.after()},function(){return n.finish()}])},t,P.seed),et.finished&&et.advance())},pushResult:function(e){if(this!==P.current)throw new Error("Assertion occurred after test had finished.");var t,n={module:this.module.name,name:this.testName,result:e.result,message:e.message,actual:e.actual,testId:this.testId,negative:e.negative||!1,runtime:b()-this.started,todo:!!this.todo};v.call(e,"expected")&&(n.expected=e.expected),e.result||(t=e.source||Ve())&&(n.source=t),this.logAssertion(n),this.assertions.push({result:!!e.result,message:e.message})},pushFailure:function(e,t,n){if(!(this instanceof rt))throw new Error("pushFailure() assertion outside test context, was "+Ve(2));this.pushResult({result:!1,message:e||"error",actual:n||null,source:t})},logAssertion:function(e){$e("log",e);var t={passed:e.result,actual:e.actual,expected:e.expected,message:e.message,stack:e.source,todo:e.todo};this.testReport.pushAssertion(t),se("assertion",t)},resolvePromise:function(e,t){var n,s,r,i=this;null!=e&&"function"===M(n=e.then)&&(s=ut(i),P.notrycatch?n.call(e,function(){s()}):n.call(e,function(){s()},function(e){r="Promise rejected "+(t?t.replace(/Each$/,""):"during")+' "'+i.testName+'": '+(e&&e.message||e),i.pushFailure(r,We(e,0)),ot(),lt(i)}))},valid:function(){var e=P.filter,t=/^(!?)\/([\w\W]*)\/(i?$)/.exec(e),n=P.module&&P.module.toLowerCase(),s=this.module.name+": "+this.testName;return!(!this.callback||!this.callback.validTest)||!(P.moduleId&&0<P.moduleId.length&&!function e(t){return C(t.moduleId,P.moduleId)||t.parentModule&&e(t.parentModule)}(this.module))&&(!(P.testId&&0<P.testId.length&&!C(this.testId,P.testId))&&(!(n&&!function e(t){return(t.name?t.name.toLowerCase():null)===n||!!t.parentModule&&e(t.parentModule)}(this.module))&&(!e||(t?this.regexFilter(!!t[1],t[2],t[3],s):this.stringFilter(e,s)))))},regexFilter:function(e,t,n,s){return new RegExp(t,n).test(s)!==e},stringFilter:function(e,t){e=e.toLowerCase(),t=t.toLowerCase();var n="!"!==e.charAt(0);return n||(e=e.slice(1)),-1!==t.indexOf(e)?n:!n}};var ht=(f(pt,[{key:"timeout",value:function(e){if("number"!=typeof e)throw new Error("You must pass a number as the duration to assert.timeout");var t;this.test.timeout=e,P.timeout&&(s(P.timeout),P.timeoutHandler&&0<this.test.timeout&&(t=this.test.timeout,s(P.timeout),P.timeout=r(P.timeoutHandler(t),t)))}},{key:"step",value:function(e){var t=e,n=!!e;this.test.steps.push(e),"undefined"===M(e)||""===e?t="You must provide a message to assert.step":"string"!==M(e)&&(n=!(t="You must provide a string value to assert.step")),this.pushResult({result:n,message:t})}},{key:"verifySteps",value:function(e,t){var n=this.test.steps.slice();this.deepEqual(n,e,t),this.test.steps.length=0}},{key:"expect",value:function(e){if(1!==arguments.length)return this.test.expected;this.test.expected=e}},{key:"async",value:function(e){var t=this.test,n=!1,s=e;void 0===s&&(s=1);var r=ut(t);return function(){if(P.current!==t)throw Error("assert.async callback called after test finished.");n?t.pushFailure("Too many calls to the `assert.async` callback",Ve(2)):0<--s||(n=!0,r())}}},{key:"push",value:function(e,t,n,s,r){return c.warn("assert.push is deprecated and will be removed in QUnit 3.0. Please use assert.pushResult instead (https://api.qunitjs.com/assert/pushResult)."),(this instanceof pt?this:P.current.assert).pushResult({result:e,actual:t,expected:n,message:s,negative:r})}},{key:"pushResult",value:function(e){var t=this,n=t instanceof pt&&t.test||P.current;if(!n)throw new Error("assertion outside test context, in "+Ve(2));return t instanceof pt||(t=n.assert),t.test.pushResult(e)}},{key:"ok",value:function(e,t){t=t||(e?"okay":"failed, expected argument to be truthy, was: "+G.parse(e)),this.pushResult({result:!!e,actual:e,expected:!0,message:t})}},{key:"notOk",value:function(e,t){t=t||(e?"failed, expected argument to be falsy, was: "+G.parse(e):"okay"),this.pushResult({result:!e,actual:e,expected:!1,message:t})}},{key:"equal",value:function(e,t,n){var s=t==e;this.pushResult({result:s,actual:e,expected:t,message:n})}},{key:"notEqual",value:function(e,t,n){var s=t!=e;this.pushResult({result:s,actual:e,expected:t,message:n,negative:!0})}},{key:"propEqual",value:function(e,t,n){e=q(e),t=q(t),this.pushResult({result:I(e,t),actual:e,expected:t,message:n})}},{key:"notPropEqual",value:function(e,t,n){e=q(e),t=q(t),this.pushResult({result:!I(e,t),actual:e,expected:t,message:n,negative:!0})}},{key:"deepEqual",value:function(e,t,n){this.pushResult({result:I(e,t),actual:e,expected:t,message:n})}},{key:"notDeepEqual",value:function(e,t,n){this.pushResult({result:!I(e,t),actual:e,expected:t,message:n,negative:!0})}},{key:"strictEqual",value:function(e,t,n){this.pushResult({result:t===e,actual:e,expected:t,message:n})}},{key:"notStrictEqual",value:function(e,t,n){this.pushResult({result:t!==e,actual:e,expected:t,message:n,negative:!0})}},{key:"throws",value:function(e,t,n){var s,r=void 0,i=!1,o=this instanceof pt&&this.test||P.current;if("string"===M(t)){if(null!=n)throw new Error("throws/raises does not accept a string value for the expected argument.\nUse a non-string object value (e.g. regExp) instead if it's necessary.");n=t,t=null}o.ignoreGlobalErrors=!0;try{e.call(o.testEnvironment)}catch(e){r=e}o.ignoreGlobalErrors=!1,r&&(s=M(t),t?"regexp"===s?(i=t.test(mt(r)),t=String(t)):"function"===s&&r instanceof t?i=!0:"object"===s?(i=r instanceof t.constructor&&r.name===t.name&&r.message===t.message,t=mt(t)):"function"===s&&!0===t.call({},r)&&(i=!(t=null)):i=!0),o.assert.pushResult({result:i,actual:r&&mt(r),expected:t,message:n})}},{key:"rejects",value:function(t,n,s){var r=!1,i=this instanceof pt&&this.test||P.current;if("string"===M(n)){if(void 0!==s)return s="assert.rejects does not accept a string value for the expected argument.\nUse a non-string object value (e.g. validator function) instead if necessary.",void i.assert.pushResult({result:!1,message:s});s=n,n=void 0}var e=t&&t.then;if("function"===M(e)){var o=this.async();return e.call(t,function(){var e='The promise returned by the `assert.rejects` callback in "'+i.testName+'" did not reject.';i.assert.pushResult({result:!1,message:e,actual:t}),o()},function(e){var t=M(n);void 0===n?r=!0:"regexp"===t?(r=n.test(mt(e)),n=String(n)):"function"===t&&e instanceof n?r=!0:"object"===t?(r=e instanceof n.constructor&&e.name===n.name&&e.message===n.message,n=mt(n)):"function"===t?(r=!0===n.call({},e),n=null):(r=!1,s='invalid expected value provided to `assert.rejects` callback in "'+i.testName+'": '+t+"."),i.assert.pushResult({result:r,actual:e&&mt(e),expected:n,message:s}),o()})}var a='The value provided to `assert.rejects` in "'+i.testName+'" was not a promise.';i.assert.pushResult({result:!1,message:a,actual:t})}}]),pt);function pt(e){u(this,pt),this.test=e}function mt(e){var t=e.toString();if("[object"!==t.substring(0,7))return t;var n=e.name?e.name.toString():"Error",s=e.message?e.message.toString():"";return n&&s?n+": "+s:n||(s||"Error")}ht.prototype.raises=ht.prototype.throws;var gt={},vt=new V;P.currentModule.suiteReport=vt;var bt,yt,kt,wt,xt=!1,Et=!1;function Tt(){Et=!0,E.setTimeout?r(function(){qt()}):qt()}function Ct(){P.blocking=!1,et.advance()}function qt(){var e,t,n=[];if(P.started)Ct();else{for(P.started=b(),""===P.modules[0].name&&0===P.modules[0].tests.length&&P.modules.shift(),e=0,t=P.modules.length;e<t;e++)n.push({name:P.modules[e].name,tests:P.modules[e].tests});se("runStart",vt.start(!0)),$e("begin",{totalTests:rt.count,modules:n}).then(Ct)}}function Nt(e){return decodeURIComponent(e.replace(/\+/g,"%20"))}gt.isLocal=!(E.document&&"file:"!==g.location.protocol),gt.version="2.10.1",N(gt,{on:function(e,t){if("string"!==M(e))throw new TypeError("eventName must be a string when registering a listener");if(!C(e,ne)){var n=ne.join(", ");throw new Error('"'+e+'" is not a valid event; must be one of: '+n+".")}if("function"!==M(t))throw new TypeError("callback must be a function when registering a listener");te[e]||(te[e]=[]),C(t,te[e])||te[e].push(t)},module:ee,test:at,todo:function(e,t){st||new rt({testName:e,callback:t,todo:!0}).queue()},skip:function(e){st||new rt({testName:e,skip:!0}).queue()},only:function(e,t){st||(P.queue.length=0,st=!0),new rt({testName:e,callback:t}).queue()},start:function(e){var t=xt;if(P.current)throw new Error("QUnit.start cannot be called inside a test context.");if(xt=!0,Et)throw new Error("Called start() while test already started running");if(t||1<e)throw new Error("Called start() outside of a test context too many times");if(P.autostart)throw new Error("Called start() outside of a test context when QUnit.config.autostart was true");if(!P.pageLoaded)return P.autostart=!0,void(E.document||gt.load());Tt()},config:P,is:_,objectType:M,extend:N,load:function(){P.pageLoaded=!0,N(P,{stats:{all:0,bad:0},started:0,updateRate:1e3,autostart:!0,filter:""},!0),Et||(P.blocking=!1,P.autostart&&Tt())},stack:function(e){return Ve(e=(e||0)+2)},onError:function(e){for(var t=arguments.length,n=Array(1<t?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];if(P.current){if(P.current.ignoreGlobalErrors)return!0;it.apply(void 0,[e.message,e.stacktrace||e.fileName+":"+e.lineNumber].concat(n))}else at("global failure",N(function(){it.apply(void 0,[e.message,e.stacktrace||e.fileName+":"+e.lineNumber].concat(n))},{validTest:!0}));return!1},onUnhandledRejection:function(e){var t={result:!1,message:e.message||"error",actual:e,source:e.stack||Ve(3)},n=P.current;n?n.assert.pushResult(t):at("global failure",N(function(e){e.pushResult(t)},{validTest:!0}))}}),gt.pushFailure=it,gt.assert=ht.prototype,gt.equiv=I,gt.dump=G,function(e){var t,n,s,r=["begin","done","log","testStart","testDone","moduleStart","moduleDone"];function i(t){return function(e){if("function"!==M(e))throw new Error("QUnit logging methods require a callback function as their first parameters.");P.callbacks[t].push(e)}}for(t=0,n=r.length;t<n;t++)s=r[t],"undefined"===M(P.callbacks[s])&&(P.callbacks[s]=[]),e[s]=i(s)}(gt),function(e){if(E.document){if(g.QUnit&&g.QUnit.version)throw new Error("QUnit has already been defined.");g.QUnit=e}"undefined"!=typeof module&&module&&module.exports&&(module.exports=e,module.exports.QUnit=e),"undefined"!=typeof exports&&exports&&(exports.QUnit=e),"function"==typeof define&&define.amd&&(define(function(){return e}),e.config.autostart=!1),n&&n.WorkerGlobalScope&&n instanceof n.WorkerGlobalScope&&(n.QUnit=e)}(gt),void 0!==g&&void 0!==i&&(bt=gt.config,yt=Object.prototype.hasOwnProperty,gt.begin(function(){var e;yt.call(bt,"fixture")||(e=i.getElementById("qunit-fixture"))&&(bt.fixture=e.cloneNode(!0))}),gt.testStart(function(){var e,t,n;null!=bt.fixture&&(e=i.getElementById("qunit-fixture"),"string"===d(bt.fixture)?((t=i.createElement("div")).setAttribute("id","qunit-fixture"),t.innerHTML=bt.fixture,e.parentNode.replaceChild(t,e)):(n=bt.fixture.cloneNode(!0),e.parentNode.replaceChild(n,e)))})),(wt=void 0!==g&&g.location)&&(kt=function(){var e,t,n,s,r=Object.create(null),i=wt.search.slice(1).split("&"),o=i.length;for(e=0;e<o;e++)i[e]&&(t=i[e].split("="),n=Nt(t[0]),s=1===t.length||Nt(t.slice(1).join("=")),r[n]=n in r?[].concat(r[n],s):s);return r}(),gt.urlParams=kt,gt.config.moduleId=[].concat(kt.moduleId||[]),gt.config.testId=[].concat(kt.testId||[]),gt.config.module=kt.module,gt.config.filter=kt.filter,!0===kt.seed?gt.config.seed=Math.random().toString(36).slice(2):kt.seed&&(gt.config.seed=kt.seed),gt.config.urlConfig.push({id:"hidepassed",label:"Hide passed tests",tooltip:"Only show tests and assertions that fail. Stored as query-strings."},{id:"noglobals",label:"Check for Globals",tooltip:"Enabling this will test if any test introduces new properties on the global object (`window` in Browsers). Stored as query-strings."},{id:"notrycatch",label:"No try-catch",tooltip:"Enabling this will run tests outside of a try-catch block. Makes debugging exceptions in IE reasonable. Stored as query-strings."}),gt.begin(function(){for(var e,t=gt.config.urlConfig,n=0;n<t.length;n++)"string"!=typeof(e=gt.config.urlConfig[n])&&(e=e.id),void 0===gt.config[e]&&(gt.config[e]=kt[e])}));var Mt,_t,St,jt,Rt,At,It,Ot,Lt,Ht={passedTests:0,failedTests:0,skippedTests:0,todoTests:0};function Dt(e){return e?(e+="").replace(/['"<>&]/g,function(e){switch(e){case"'":return"&#039;";case'"':return"&quot;";case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;"}}):""}function Ft(e,t,n){e.addEventListener(t,n,!1)}function Pt(e,t,n){e.removeEventListener(t,n,!1)}function Ut(e,t,n){for(var s=e.length;s--;)Ft(e[s],t,n)}function Bt(e,t){return 0<=(" "+e.className+" ").indexOf(" "+t+" ")}function Qt(e,t){Bt(e,t)||(e.className+=(e.className?" ":"")+t)}function Gt(e,t,n){(n||void 0===n&&!Bt(e,t)?Qt:Yt)(e,t)}function Yt(e,t){for(var n=" "+e.className+" ";0<=n.indexOf(" "+t+" ");)n=n.replace(" "+t+" "," ");e.className="function"==typeof n.trim?n.trim():n.replace(/^\s+|\s+$/g,"")}function $t(e){return St.getElementById&&St.getElementById(e)}function zt(){var e=$t("qunit-abort-tests-button");return e&&(e.disabled=!0,e.innerHTML="Aborting..."),gt.config.queue.length=0,!1}function Wt(e){return Jt(),e&&e.preventDefault&&e.preventDefault(),!1}function Vt(){var e,t,n=this,s={},r="selectedIndex"in n?n.options[n.selectedIndex].value||void 0:n.checked?n.defaultValue||!0:void 0;if(s[n.name]=r,e=Kt(s),"hidepassed"===n.name&&"replaceState"in g.history){if(gt.urlParams[n.name]=r,Mt[n.name]=r||!1,t=$t("qunit-tests")){var i=t.children.length,o=t.children;if(n.checked){for(var a=0;a<i;a++){var u=o[a];u&&-1<u.className.indexOf("pass")&&_t.push(u)}var l=!0,c=!1,d=void 0;try{for(var f,h=_t[Symbol.iterator]();!(l=(f=h.next()).done);l=!0){var p=f.value;t.removeChild(p)}}catch(e){c=!0,d=e}finally{try{!l&&h.return&&h.return()}finally{if(c)throw d}}}else for(;null!=(u=_t.pop());)t.appendChild(u)}g.history.replaceState(null,"",e)}else g.location=e}function Kt(e){var t,n,s,r="?",i=g.location;for(t in e=gt.extend(gt.extend({},gt.urlParams),e))if(Rt.call(e,t)&&void 0!==e[t])for(n=[].concat(e[t]),s=0;s<n.length;s++)r+=encodeURIComponent(t),!0!==n[s]&&(r+="="+encodeURIComponent(n[s])),r+="&";return i.protocol+"//"+i.host+i.pathname+r.slice(0,-1)}function Jt(){for(var e=[],t=$t("qunit-modulefilter-dropdown-list").getElementsByTagName("input"),n=$t("qunit-filter-input").value,s=0;s<t.length;s++)t[s].checked&&e.push(t[s].value);g.location=Kt({filter:""===n?void 0:n,moduleId:0===e.length?void 0:e,module:void 0,testId:void 0})}function Xt(){var e=St.createElement("span");return e.innerHTML=function(){for(var e,t,n,s,r=!1,i=Mt.urlConfig,o="",a=0;a<i.length;a++)if("string"==typeof(t=Mt.urlConfig[a])&&(t={id:t,label:t}),n=Dt(t.id),s=Dt(t.tooltip),t.value&&"string"!=typeof t.value){if(o+="<label for='qunit-urlconfig-"+n+"' title='"+s+"'>"+t.label+": </label><select id='qunit-urlconfig-"+n+"' name='"+n+"' title='"+s+"'><option></option>",gt.is("array",t.value))for(e=0;e<t.value.length;e++)o+="<option value='"+(n=Dt(t.value[e]))+"'"+(Mt[t.id]===t.value[e]?(r=!0," selected='selected'"):"")+">"+n+"</option>";else for(e in t.value)Rt.call(t.value,e)&&(o+="<option value='"+Dt(e)+"'"+(Mt[t.id]===e?(r=!0," selected='selected'"):"")+">"+Dt(t.value[e])+"</option>");Mt[t.id]&&!r&&(o+="<option value='"+(n=Dt(Mt[t.id]))+"' selected='selected' disabled='disabled'>"+n+"</option>"),o+="</select>"}else o+="<label for='qunit-urlconfig-"+n+"' title='"+s+"'><input id='qunit-urlconfig-"+n+"' name='"+n+"' type='checkbox'"+(t.value?" value='"+Dt(t.value)+"'":"")+(Mt[t.id]?" checked='checked'":"")+" title='"+s+"' />"+Dt(t.label)+"</label>";return o}(),Qt(e,"qunit-url-config"),Ut(e.getElementsByTagName("input"),"change",Vt),Ut(e.getElementsByTagName("select"),"change",Vt),e}function Zt(){var o,a,s=St.createElement("form"),e=St.createElement("label"),u=St.createElement("input"),r=St.createElement("div"),t=St.createElement("span"),n=St.createElement("button"),i=St.createElement("button"),l=St.createElement("label"),c=St.createElement("input"),d=St.createElement("ul"),f=!1;function h(){function n(e){var t=s.contains(e.target);27!==e.keyCode&&t||(27===e.keyCode&&t&&u.focus(),r.style.display="none",Pt(St,"click",n),Pt(St,"keydown",n),u.value="",p())}"none"===r.style.display&&(r.style.display="block",Ft(St,"click",n),Ft(St,"keydown",n))}function p(){for(var e,t=u.value.toLowerCase(),n=d.children,s=0;s<n.length;s++)e=n[s],!t||-1<e.textContent.toLowerCase().indexOf(t)?e.style.display="":e.style.display="none"}function m(e){var t,n,s=e&&e.target||c,r=d.getElementsByTagName("input"),i=[];for(Gt(s.parentNode,"checked",s.checked),f=!1,s.checked&&s!==c&&(c.checked=!1,Yt(c.parentNode,"checked")),t=0;t<r.length;t++)n=r[t],e?s===c&&s.checked&&(n.checked=!1,Yt(n.parentNode,"checked")):Gt(n.parentNode,"checked",n.checked),f=f||n.checked!==n.defaultChecked,n.checked&&i.push(n.parentNode.textContent);o.style.display=a.style.display=f?"":"none",u.placeholder=i.join(", ")||c.parentNode.textContent,u.title="Type to filter list. Current selection:\n"+(i.join("\n")||c.parentNode.textContent)}return u.id="qunit-modulefilter-search",u.autocomplete="off",Ft(u,"input",p),Ft(u,"input",h),Ft(u,"focus",h),Ft(u,"click",h),e.id="qunit-modulefilter-search-container",e.innerHTML="Module: ",e.appendChild(u),n.textContent="Apply",n.style.display="none",i.textContent="Reset",i.type="reset",i.style.display="none",c.type="checkbox",c.checked=0===Mt.moduleId.length,l.className="clickable",Mt.moduleId.length&&(l.className="checked"),l.appendChild(c),l.appendChild(St.createTextNode("All modules")),t.id="qunit-modulefilter-actions",t.appendChild(n),t.appendChild(i),t.appendChild(l),o=t.firstChild,a=o.nextSibling,Ft(o,"click",Jt),d.id="qunit-modulefilter-dropdown-list",d.innerHTML=function(){for(var e,t="",n=0;n<Mt.modules.length;n++)""!==Mt.modules[n].name&&(t+="<li><label class='clickable"+((e=-1<Mt.moduleId.indexOf(Mt.modules[n].moduleId))?" checked":"")+"'><input type='checkbox' value='"+Mt.modules[n].moduleId+"'"+(e?" checked='checked'":"")+" />"+Dt(Mt.modules[n].name)+"</label></li>");return t}(),r.id="qunit-modulefilter-dropdown",r.style.display="none",r.appendChild(t),r.appendChild(d),Ft(r,"change",m),m(),s.id="qunit-modulefilter",s.appendChild(e),s.appendChild(r),Ft(s,"submit",Wt),Ft(s,"reset",function(){g.setTimeout(m)}),s}function en(){var e,t,n,s,r=St.createElement("span");return r.id="qunit-toolbar-filters",r.appendChild((e=St.createElement("form"),t=St.createElement("label"),n=St.createElement("input"),s=St.createElement("button"),Qt(e,"qunit-filter"),t.innerHTML="Filter: ",n.type="text",n.value=Mt.filter||"",n.name="filter",n.id="qunit-filter-input",s.innerHTML="Go",t.appendChild(n),e.appendChild(t),e.appendChild(St.createTextNode(" ")),e.appendChild(s),Ft(e,"submit",Wt),e)),r.appendChild(Zt()),r}function tn(){var e,t,n=$t("qunit-tests"),s=$t("qunit-testresult");s&&s.parentNode.removeChild(s),n&&(n.innerHTML="",(s=St.createElement("p")).id="qunit-testresult",s.className="result",n.parentNode.insertBefore(s,n),s.innerHTML='<div id="qunit-testresult-display">Running...<br />&#160;</div><div id="qunit-testresult-controls"></div><div class="clearfix"></div>',e=$t("qunit-testresult-controls")),e&&e.appendChild(((t=St.createElement("button")).id="qunit-abort-tests-button",t.innerHTML="Abort",Ft(t,"click",zt),t))}function nn(){var e,t,n,s,r,i=$t("qunit");i&&(i.innerHTML="<h1 id='qunit-header'>"+Dt(St.title)+"</h1><h2 id='qunit-banner'></h2><div id='qunit-testrunner-toolbar'></div>"+(!(e=gt.config.testId)||e.length<=0?"":"<div id='qunit-filteredTest'>Rerunning selected tests: "+Dt(e.join(", "))+" <a id='qunit-clearFilter' href='"+Dt(At)+"'>Run all tests</a></div>")+"<h2 id='qunit-userAgent'></h2><ol id='qunit-tests'></ol>"),(t=$t("qunit-header"))&&(t.innerHTML="<a href='"+Dt(At)+"'>"+t.innerHTML+"</a> "),(n=$t("qunit-banner"))&&(n.className=""),tn(),(s=$t("qunit-userAgent"))&&(s.innerHTML="",s.appendChild(St.createTextNode("QUnit "+gt.version+"; "+o.userAgent))),(r=$t("qunit-testrunner-toolbar"))&&(r.appendChild(Xt()),r.appendChild(en()),r.appendChild(St.createElement("div")).className="clearfix")}function sn(e,t){var n="";return t&&(n="<span class='module-name'>"+Dt(t)+"</span>: "),n+="<span class='test-name'>"+Dt(e)+"</span>"}function rn(e){return e.replace(/<\/?[^>]+(>|$)/g,"").replace(/&quot;/g,"").replace(/\s+/g,"")}function on(){}void 0!==g&&g.document&&(Mt=gt.config,_t=[],St=g.document,jt=!1,Rt=Object.prototype.hasOwnProperty,At=Kt({filter:void 0,module:void 0,moduleId:void 0,testId:void 0}),It=[],gt.begin(function(e){for(var t,n=0;n<e.modules.length;n++)(t=e.modules[n]).name&&It.push(t.name);It.sort(function(e,t){return e.localeCompare(t)}),nn()}),gt.done(function(e){var t,n,s,r=$t("qunit-banner"),i=$t("qunit-tests"),o=$t("qunit-abort-tests-button"),a=[Ht.passedTests+Ht.skippedTests+Ht.todoTests+Ht.failedTests," tests completed in ",e.runtime," milliseconds, with ",Ht.failedTests," failed, ",Ht.skippedTests," skipped, and ",Ht.todoTests," todo.<br />","<span class='passed'>",e.passed,"</span> assertions of <span class='total'>",e.total,"</span> passed, <span class='failed'>",e.failed,"</span> failed."].join("");if(o&&o.disabled){a="Tests aborted after "+e.runtime+" milliseconds.";for(var u=0;u<i.children.length;u++)""!==(t=i.children[u]).className&&"running"!==t.className||(t.className="aborted",s=t.getElementsByTagName("ol")[0],(n=St.createElement("li")).className="fail",n.innerHTML="Test aborted.",s.appendChild(n))}!r||o&&!1!==o.disabled||(r.className=Ht.failedTests?"qunit-fail":"qunit-pass"),o&&o.parentNode.removeChild(o),i&&($t("qunit-testresult-display").innerHTML=a),Mt.altertitle&&St.title&&(St.title=[Ht.failedTests?"✖":"✔",St.title.replace(/^[\u2714\u2716] /i,"")].join(" ")),Mt.scrolltop&&g.scrollTo&&g.scrollTo(0,0)}),gt.testStart(function(e){var t,n,s,r,i,o,a,u,l,c,d,f,h;s=e.name,r=e.testId,i=e.module,(c=$t("qunit-tests"))&&((o=St.createElement("strong")).innerHTML=sn(s,i),(a=St.createElement("a")).innerHTML="Rerun",a.href=Kt({testId:r}),(u=St.createElement("li")).appendChild(o),u.appendChild(a),u.id="qunit-test-output-"+r,(l=St.createElement("ol")).className="qunit-assert-list",u.appendChild(l),c.appendChild(u)),(t=$t("qunit-testresult-display"))&&(Qt(t,"running"),n=gt.config.reorder&&e.previousFailure,t.innerHTML=[n?"Rerunning previously failed test: <br />":"Running: <br />",sn(e.name,e.module),(d=b()-Mt.started,f=Ht,h=rt.count,["<br />",f.passedTests+f.skippedTests+f.todoTests+f.failedTests," / ",h," tests completed in ",d," milliseconds, with ",f.failedTests," failed, ",f.skippedTests," skipped, and ",f.todoTests," todo."].join(""))].join(""))}),gt.log(function(e){var t,n,s,r,i,o,a=!1,u=$t("qunit-test-output-"+e.testId);u&&(s="<span class='test-message'>"+(s=Dt(e.message)||(e.result?"okay":"failed"))+"</span>",s+="<span class='runtime'>@ "+e.runtime+" ms</span>",!e.result&&Rt.call(e,"expected")?(r=e.negative?"NOT "+gt.dump.parse(e.expected):gt.dump.parse(e.expected),i=gt.dump.parse(e.actual),s+="<table><tr class='test-expected'><th>Expected: </th><td><pre>"+Dt(r)+"</pre></td></tr>",i!==r?(s+="<tr class='test-actual'><th>Result: </th><td><pre>"+Dt(i)+"</pre></td></tr>","number"==typeof e.actual&&"number"==typeof e.expected?isNaN(e.actual)||isNaN(e.expected)||(a=!0,o=(0<(o=e.actual-e.expected)?"+":"")+o):"boolean"!=typeof e.actual&&"boolean"!=typeof e.expected&&(a=rn(o=gt.diff(r,i)).length!==rn(r).length+rn(i).length),a&&(s+="<tr class='test-diff'><th>Diff: </th><td><pre>"+o+"</pre></td></tr>")):-1!==r.indexOf("[object Array]")||-1!==r.indexOf("[object Object]")?s+="<tr class='test-message'><th>Message: </th><td>Diff suppressed as the depth of object is more than current max depth ("+gt.config.maxDepth+").<p>Hint: Use <code>QUnit.dump.maxDepth</code> to  run with a higher max depth or <a href='"+Dt(Kt({maxDepth:-1}))+"'>Rerun</a> without max depth.</p></td></tr>":s+="<tr class='test-message'><th>Message: </th><td>Diff suppressed as the expected and actual results have an equivalent serialization</td></tr>",e.source&&(s+="<tr class='test-source'><th>Source: </th><td><pre>"+Dt(e.source)+"</pre></td></tr>"),s+="</table>"):!e.result&&e.source&&(s+="<table><tr class='test-source'><th>Source: </th><td><pre>"+Dt(e.source)+"</pre></td></tr></table>"),t=u.getElementsByTagName("ol")[0],(n=St.createElement("li")).className=e.result?"pass":"fail",n.innerHTML=s,t.appendChild(n))}),gt.testDone(function(e){var t,n,s,r,i,o,a,u,l,c,d,f,h=$t("qunit-tests");h&&(Yt(s=$t("qunit-test-output-"+e.testId),"running"),i=0<e.failed?"failed":e.todo?"todo":e.skipped?"skipped":"passed",r=s.getElementsByTagName("ol")[0],o=e.passed,a=e.failed,(d=0<e.failed?e.todo:!e.todo)?Qt(r,"qunit-collapsed"):Mt.collapse&&(jt?Qt(r,"qunit-collapsed"):jt=!0),u=a?"<b class='failed'>"+a+"</b>, <b class='passed'>"+o+"</b>, ":"",(t=s.firstChild).innerHTML+=" <b class='counts'>("+u+e.assertions.length+")</b>",e.skipped?(Ht.skippedTests++,s.className="skipped",(l=St.createElement("em")).className="qunit-skipped-label",l.innerHTML="skipped",s.insertBefore(l,t)):(Ft(t,"click",function(){Gt(r,"qunit-collapsed")}),s.className=d?"pass":"fail",e.todo&&((f=St.createElement("em")).className="qunit-todo-label",f.innerHTML="todo",s.className+=" todo",s.insertBefore(f,t)),(n=St.createElement("span")).className="runtime",n.innerHTML=e.runtime+" ms",s.insertBefore(n,r),d?e.todo?Ht.todoTests++:Ht.passedTests++:Ht.failedTests++),e.source&&((c=St.createElement("p")).innerHTML="<strong>Source: </strong>"+Dt(e.source),Qt(c,"qunit-source"),d&&Qt(c,"qunit-collapsed"),Ft(t,"click",function(){Gt(c,"qunit-collapsed")}),s.appendChild(c)),Mt.hidepassed&&"passed"===i&&(_t.push(s),h.removeChild(s)))}),!((Ot=g.phantom)&&Ot.version&&0<Ot.version.major)&&"complete"===St.readyState?gt.load():Ft(g,"load",gt.load),Lt=g.onerror,g.onerror=function(e,t,n,s,r){var i,o=!1;if(Lt){for(var a=arguments.length,u=Array(5<a?a-5:0),l=5;l<a;l++)u[l-5]=arguments[l];o=Lt.call.apply(Lt,[this,e,t,n,s,r].concat(u))}return!0!==o&&(i={message:e,fileName:t,lineNumber:n},r&&r.stack&&(i.stacktrace=We(r,0)),o=gt.onError(i)),o},g.addEventListener("unhandledrejection",function(e){gt.onUnhandledRejection(e.reason)})),gt.diff=(on.prototype.DiffMain=function(e,t,n){var s,r,i,o,a,u=(new Date).getTime()+1e3;if(null===e||null===t)throw new Error("Null input. (DiffMain)");return e===t?e?[[0,e]]:[]:(void 0===n&&(n=!0),s=n,r=this.diffCommonPrefix(e,t),i=e.substring(0,r),e=e.substring(r),t=t.substring(r),r=this.diffCommonSuffix(e,t),o=e.substring(e.length-r),e=e.substring(0,e.length-r),t=t.substring(0,t.length-r),a=this.diffCompute(e,t,s,u),i&&a.unshift([0,i]),o&&a.push([0,o]),this.diffCleanupMerge(a),a)},on.prototype.diffCleanupEfficiency=function(e){for(var t=!1,n=[],s=0,r=null,i=0,o=!1,a=!1,u=!1,l=!1;i<e.length;)0===e[i][0]?(r=e[i][1].length<4&&(u||l)?(o=u,a=l,e[n[s++]=i][1]):(s=0,null),u=l=!1):(-1===e[i][0]?l=!0:u=!0,r&&(o&&a&&u&&l||r.length<2&&o+a+u+l===3)&&(e.splice(n[s-1],0,[-1,r]),e[n[s-1]+1][0]=1,s--,r=null,o&&a?(u=l=!0,s=0):(i=0<--s?n[s-1]:-1,u=l=!1),t=!0)),i++;t&&this.diffCleanupMerge(e)},on.prototype.diffPrettyHtml=function(e){for(var t,n,s=[],r=0;r<e.length;r++)switch(t=e[r][0],n=e[r][1],t){case 1:s[r]="<ins>"+Dt(n)+"</ins>";break;case-1:s[r]="<del>"+Dt(n)+"</del>";break;case 0:s[r]="<span>"+Dt(n)+"</span>"}return s.join("")},on.prototype.diffCommonPrefix=function(e,t){var n,s,r,i;if(!e||!t||e.charAt(0)!==t.charAt(0))return 0;for(r=0,n=s=Math.min(e.length,t.length),i=0;r<n;)e.substring(i,n)===t.substring(i,n)?i=r=n:s=n,n=Math.floor((s-r)/2+r);return n},on.prototype.diffCommonSuffix=function(e,t){var n,s,r,i;if(!e||!t||e.charAt(e.length-1)!==t.charAt(t.length-1))return 0;for(r=0,n=s=Math.min(e.length,t.length),i=0;r<n;)e.substring(e.length-n,e.length-i)===t.substring(t.length-n,t.length-i)?i=r=n:s=n,n=Math.floor((s-r)/2+r);return n},on.prototype.diffCompute=function(e,t,n,s){var r,i,o,a,u,l,c,d,f,h,p,m;return e?t?(i=e.length>t.length?e:t,o=e.length>t.length?t:e,-1!==(a=i.indexOf(o))?(r=[[1,i.substring(0,a)],[0,o],[1,i.substring(a+o.length)]],e.length>t.length&&(r[0][0]=r[2][0]=-1),r):1===o.length?[[-1,e],[1,t]]:(u=this.diffHalfMatch(e,t))?(l=u[0],d=u[1],c=u[2],f=u[3],h=u[4],p=this.DiffMain(l,c,n,s),m=this.DiffMain(d,f,n,s),p.concat([[0,h]],m)):n&&100<e.length&&100<t.length?this.diffLineMode(e,t,s):this.diffBisect(e,t,s)):[[-1,e]]:[[1,t]]},on.prototype.diffHalfMatch=function(e,t){var f,n,s,r,i,o,a,u,l=e.length>t.length?e:t,c=e.length>t.length?t:e;if(l.length<4||2*c.length<l.length)return null;function d(e,t,n){for(var s,r,i,o,a,u,l=e.substring(n,n+Math.floor(e.length/4)),c=-1,d="";-1!==(c=t.indexOf(l,c+1));)s=f.diffCommonPrefix(e.substring(n),t.substring(c)),r=f.diffCommonSuffix(e.substring(0,n),t.substring(0,c)),d.length<r+s&&(d=t.substring(c-r,c)+t.substring(c,c+s),i=e.substring(0,n-r),o=e.substring(n+s),a=t.substring(0,c-r),u=t.substring(c+s));return 2*d.length>=e.length?[i,o,a,u,d]:null}return f=this,o=d(l,c,Math.ceil(l.length/4)),a=d(l,c,Math.ceil(l.length/2)),o||a?(u=!a||o&&o[4].length>a[4].length?o:a,e.length>t.length?(n=u[0],i=u[1],r=u[2],s=u[3]):(r=u[0],s=u[1],n=u[2],i=u[3]),[n,i,r,s,u[4]]):null},on.prototype.diffLineMode=function(e,t,n){var s,r,i,o,a,u,l,c,d=this.diffLinesToChars(e,t);for(e=d.chars1,t=d.chars2,r=d.lineArray,s=this.DiffMain(e,t,!1,n),this.diffCharsToLines(s,r),this.diffCleanupSemantic(s),s.push([0,""]),o=a=i=0,u=l="";i<s.length;){switch(s[i][0]){case 1:o++,u+=s[i][1];break;case-1:a++,l+=s[i][1];break;case 0:if(1<=a&&1<=o){for(s.splice(i-a-o,a+o),i=i-a-o,c=(d=this.DiffMain(l,u,!1,n)).length-1;0<=c;c--)s.splice(i,0,d[c]);i+=d.length}a=o=0,u=l=""}i++}return s.pop(),s},on.prototype.diffBisect=function(e,t,n){for(var s,r,i,o,a,u,l,c,d,f,h,p,m,g,v,b=e.length,y=t.length,k=Math.ceil((b+y)/2),w=k,x=2*k,E=new Array(x),T=new Array(x),C=0;C<x;C++)E[C]=-1,T[C]=-1;for(E[w+1]=0,r=(s=b-y)%2!=(T[w+1]=0),m=u=a=o=i=0;m<k&&!((new Date).getTime()>n);m++){for(g=-m+i;g<=m-o;g+=2){for(c=w+g,h=(d=g===-m||g!==m&&E[c-1]<E[c+1]?E[c+1]:E[c-1]+1)-g;d<b&&h<y&&e.charAt(d)===t.charAt(h);)d++,h++;if(b<(E[c]=d))o+=2;else if(y<h)i+=2;else if(r&&0<=(l=w+s-g)&&l<x&&-1!==T[l]&&(f=b-T[l])<=d)return this.diffBisectSplit(e,t,d,h,n)}for(v=-m+a;v<=m-u;v+=2){for(l=w+v,p=(f=v===-m||v!==m&&T[l-1]<T[l+1]?T[l+1]:T[l-1]+1)-v;f<b&&p<y&&e.charAt(b-f-1)===t.charAt(y-p-1);)f++,p++;if(b<(T[l]=f))u+=2;else if(y<p)a+=2;else if(!r&&0<=(c=w+s-v)&&c<x&&-1!==E[c]&&(h=w+(d=E[c])-c,(f=b-f)<=d))return this.diffBisectSplit(e,t,d,h,n)}}return[[-1,e],[1,t]]},on.prototype.diffBisectSplit=function(e,t,n,s,r){var i=e.substring(0,n),o=t.substring(0,s),a=e.substring(n),u=t.substring(s),l=this.DiffMain(i,o,!1,r),c=this.DiffMain(a,u,!1,r);return l.concat(c)},on.prototype.diffCleanupSemantic=function(e){for(var t,n,s,r,i=!1,o=[],a=0,u=null,l=0,c=0,d=0,f=0,h=0;l<e.length;)0===e[l][0]?(c=f,d=h,h=f=0,u=e[o[a++]=l][1]):(1===e[l][0]?f+=e[l][1].length:h+=e[l][1].length,u&&u.length<=Math.max(c,d)&&u.length<=Math.max(f,h)&&(e.splice(o[a-1],0,[-1,u]),e[o[a-1]+1][0]=1,a--,l=0<--a?o[a-1]:-1,h=f=d=c=0,i=!(u=null))),l++;for(i&&this.diffCleanupMerge(e),l=1;l<e.length;)-1===e[l-1][0]&&1===e[l][0]&&(t=e[l-1][1],n=e[l][1],s=this.diffCommonOverlap(t,n),(r=this.diffCommonOverlap(n,t))<=s?(s>=t.length/2||s>=n.length/2)&&(e.splice(l,0,[0,n.substring(0,s)]),e[l-1][1]=t.substring(0,t.length-s),e[l+1][1]=n.substring(s),l++):(r>=t.length/2||r>=n.length/2)&&(e.splice(l,0,[0,t.substring(0,r)]),e[l-1][0]=1,e[l-1][1]=n.substring(0,n.length-r),e[l+1][0]=-1,e[l+1][1]=t.substring(r),l++),l++),l++},on.prototype.diffCommonOverlap=function(e,t){var n,s,r,i,o,a=e.length,u=t.length;if(0===a||0===u)return 0;if(u<a?e=e.substring(a-u):a<u&&(t=t.substring(0,a)),n=Math.min(a,u),e===t)return n;for(s=0,r=1;;){if(i=e.substring(n-r),-1===(o=t.indexOf(i)))return s;r+=o,0!==o&&e.substring(n-r)!==t.substring(0,r)||(s=r,r++)}},on.prototype.diffLinesToChars=function(e,t){var o,a;function n(e){for(var t,n="",s=0,r=-1,i=o.length;r<e.length-1;)-1===(r=e.indexOf("\n",s))&&(r=e.length-1),t=e.substring(s,r+1),s=r+1,(a.hasOwnProperty?a.hasOwnProperty(t):void 0!==a[t])?n+=String.fromCharCode(a[t]):(n+=String.fromCharCode(i),a[t]=i,o[i++]=t);return n}return a={},(o=[])[0]="",{chars1:n(e),chars2:n(t),lineArray:o}},on.prototype.diffCharsToLines=function(e,t){for(var n,s,r,i=0;i<e.length;i++){for(n=e[i][1],s=[],r=0;r<n.length;r++)s[r]=t[n.charCodeAt(r)];e[i][1]=s.join("")}},on.prototype.diffCleanupMerge=function(e){var t,n,s,r,i,o,a,u;for(e.push([0,""]),s=n=t=0,r=i="";t<e.length;)switch(e[t][0]){case 1:s++,r+=e[t][1],t++;break;case-1:n++,i+=e[t][1],t++;break;case 0:1<n+s?(0!==n&&0!==s&&(0!==(o=this.diffCommonPrefix(r,i))&&(0<t-n-s&&0===e[t-n-s-1][0]?e[t-n-s-1][1]+=r.substring(0,o):(e.splice(0,0,[0,r.substring(0,o)]),t++),r=r.substring(o),i=i.substring(o)),0!==(o=this.diffCommonSuffix(r,i))&&(e[t][1]=r.substring(r.length-o)+e[t][1],r=r.substring(0,r.length-o),i=i.substring(0,i.length-o))),0===n?e.splice(t-s,n+s,[1,r]):0===s?e.splice(t-n,n+s,[-1,i]):e.splice(t-n-s,n+s,[-1,i],[1,r]),t=t-n-s+(n?1:0)+(s?1:0)+1):0!==t&&0===e[t-1][0]?(e[t-1][1]+=e[t][1],e.splice(t,1)):t++,n=s=0,r=i=""}for(""===e[e.length-1][1]&&e.pop(),a=!1,t=1;t<e.length-1;)0===e[t-1][0]&&0===e[t+1][0]&&((u=e[t][1]).substring(u.length-e[t-1][1].length)===e[t-1][1]?(e[t][1]=e[t-1][1]+e[t][1].substring(0,e[t][1].length-e[t-1][1].length),e[t+1][1]=e[t-1][1]+e[t+1][1],e.splice(t-1,1),a=!0):u.substring(0,e[t+1][1].length)===e[t+1][1]&&(e[t-1][1]+=e[t+1][1],e[t][1]=e[t][1].substring(e[t+1][1].length)+e[t+1][1],e.splice(t+1,1),a=!0)),t++;a&&this.diffCleanupMerge(e)},function(e,t){var n=new on,s=n.DiffMain(e,t);return n.diffCleanupEfficiency(s),n.diffPrettyHtml(s)})}(function(){return this}());