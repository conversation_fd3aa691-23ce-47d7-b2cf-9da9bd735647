<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="724px" height="1021px" viewBox="-0.5 -0.5 724 1021" content="&lt;mxfile host=&quot;389c86a1-6a43-4c7e-8cad-6b1b04de0413&quot; modified=&quot;2020-10-27T08:45:03.078Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code-Insiders/1.51.0-insider Chrome/83.0.4103.122 Electron/9.3.2 Safari/537.36&quot; version=&quot;13.6.5&quot; etag=&quot;XogE5thRvFagL_BSv7NL&quot;&gt;&lt;diagram id=&quot;6j8Hq4jNZIDxsgRAk3qB&quot; name=&quot;第 1 页&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(241, 250, 238);"><defs/><g><path d="M 630 50 L 630 84.88" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630 90.13 L 626.5 83.13 L 630 84.88 L 633.5 83.13 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="630" cy="25" rx="45" ry="25" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 25px; margin-left: 586px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">开始</div></div></div></foreignObject><text x="630" y="29" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">开始</text></switch></g><path d="M 630.06 135 L 630.06 145.12 Q 630.06 155.12 630.06 161.87 L 630.06 168.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630.06 173.88 L 626.56 166.88 L 630.06 168.63 L 633.56 166.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><rect x="575" y="91.25" width="110" height="43.75" rx="6.56" ry="6.56" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 113px; margin-left: 576px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">接收到服务端消息</div></div></div></foreignObject><text x="630" y="117" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">接收到服务端消息</text></switch></g><path d="M 197.5 55 L 197.49 65 Q 197.47 75 197.47 72.53 L 197.47 71.29 Q 197.47 70.06 197.47 76.85 L 197.47 83.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.47 88.88 L 193.97 81.88 L 197.47 83.63 L 200.97 81.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="197.5" cy="27.5" rx="45" ry="27.5" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 28px; margin-left: 154px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">开始</div></div></div></foreignObject><text x="198" y="31" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">开始</text></switch></g><path d="M 197.5 240 L 197.5 273.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.5 278.88 L 194 271.88 L 197.5 273.63 L 201 271.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><rect x="137.5" y="90" width="120" height="50" rx="7.5" ry="7.5" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 115px; margin-left: 139px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">调用 <br />sendCmd</div></div></div></foreignObject><text x="198" y="119" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">调用...</text></switch></g><path d="M 130.13 310.06 L 40.06 310.06 Q 30.06 310.06 30.06 320.06 L 30.06 883.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 30.06 888.88 L 26.56 881.88 L 30.06 883.63 L 33.56 881.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 313px; margin-left: 32px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; background-color: #F1FAEE; white-space: nowrap; ">否</div></div></div></foreignObject><text x="32" y="316" fill="#1D3557" font-family="Helvetica" font-size="11px" text-anchor="middle">否</text></switch></g><path d="M 197.5 890 L 197.5 933.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.5 938.88 L 194 931.88 L 197.5 933.63 L 201 931.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><path d="M 163.75 295 L 188.36 284.06 Q 197.5 280 206.64 284.06 L 255.86 305.94 Q 265 310 255.86 314.06 L 206.64 335.94 Q 197.5 340 188.36 335.94 L 139.14 314.06 Q 130 310 139.14 305.94 Z" fill="none" stroke="#457b9d" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 310px; margin-left: 131px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">合规</div></div></div></foreignObject><text x="198" y="314" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">合规</text></switch></g><path d="M 330 215 L 257.5 215" fill="none" stroke="#457b9d" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="330" y="192.5" width="140" height="45" rx="6.75" ry="6.75" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/><path d="M 344 192.5 L 344 237.5 M 456 192.5 L 456 237.5" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 215px; margin-left: 345px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><span>jsonschame<br />iofp/commands.js</span></div></div></div></foreignObject><text x="400" y="219" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">jsonschame...</text></switch></g><ellipse cx="197.5" cy="980" rx="40" ry="40" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 980px; margin-left: 159px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">结束</div></div></div></foreignObject><text x="198" y="984" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">结束</text></switch></g><path d="M 120 920.06 L 187.47 920.06 Q 197.47 920.06 197.48 926.85 L 197.49 933.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.5 938.88 L 193.99 931.89 L 197.49 933.63 L 200.99 931.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="890" width="120" height="60" rx="9" ry="9" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 920px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">错误提示</div></div></div></foreignObject><text x="60" y="924" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">错误提示</text></switch></g><path d="M 135.09 420.06 L 70.06 420.06 Q 60.06 420.06 60.06 430.06 L 60.06 860.06 Q 60.06 870.06 70.06 870.06 L 131.13 870.06" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 136.38 870.06 L 129.38 873.56 L 131.13 870.06 L 129.38 866.56 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 425px; margin-left: 91px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; background-color: #F1FAEE; white-space: nowrap; ">否</div></div></div></foreignObject><text x="91" y="428" fill="#1D3557" font-family="Helvetica" font-size="11px" text-anchor="middle">否</text></switch></g><path d="M 166.25 400 L 189.08 385.39 Q 197.5 380 205.92 385.39 L 251.58 414.61 Q 260 420 251.58 425.39 L 205.92 454.61 Q 197.5 460 189.08 454.61 L 143.42 425.39 Q 135 420 143.42 414.61 Z" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 123px; height: 1px; padding-top: 420px; margin-left: 136px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">配置了 回调函数</div></div></div></foreignObject><text x="198" y="424" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">配置了 回调函数</text></switch></g><path d="M 197.5 340 L 197.5 373.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.5 378.88 L 194 371.88 L 197.5 373.63 L 201 371.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 360px; margin-left: 198px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; background-color: #F1FAEE; white-space: nowrap; ">是</div></div></div></foreignObject><text x="198" y="363" fill="#1D3557" font-family="Helvetica" font-size="11px" text-anchor="middle">是</text></switch></g><rect x="138.75" y="500" width="117.5" height="70" rx="10.5" ry="10.5" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 116px; height: 1px; padding-top: 535px; margin-left: 140px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">生成 标识符 I<br />获取配置对应的 <br />返回命令</div></div></div></foreignObject><text x="198" y="539" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">生成 标识符 I...</text></switch></g><path d="M 197.47 459.98 L 197.47 470.06 Q 197.47 480.06 197.53 486.85 L 197.59 493.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.59 498.88 L 194.09 491.88 L 197.59 493.63 L 201.09 491.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 480px; margin-left: 198px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; background-color: #F1FAEE; white-space: nowrap; ">是</div></div></div></foreignObject><text x="198" y="483" fill="#1D3557" font-family="Helvetica" font-size="11px" text-anchor="middle">是</text></switch></g><path d="M 330 565 L 303.12 565 Q 293.12 565 293.12 555 L 293.12 545 Q 293.12 535 283.12 535 L 256.25 535" fill="none" stroke="#457b9d" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="330" y="547.5" width="140" height="35" rx="5.25" ry="5.25" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/><path d="M 344 547.5 L 344 582.5 M 456 547.5 L 456 582.5" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 565px; margin-left: 345px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><span>iofp/starter.js</span></div></div></div></foreignObject><text x="400" y="569" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">iofp/starter.js</text></switch></g><path d="M 260 645 L 332.53 645 Q 342.53 645 342.53 655 L 342.53 713.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 342.53 718.88 L 339.03 711.88 L 342.53 713.63 L 346.03 711.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><path d="M 166.25 632.5 L 188.22 623.71 Q 197.5 620 206.78 623.71 L 250.72 641.29 Q 260 645 250.72 648.71 L 206.78 666.29 Q 197.5 670 188.22 666.29 L 144.28 648.71 Q 135 645 144.28 641.29 Z" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 123px; height: 1px; padding-top: 645px; margin-left: 136px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">有配置</div></div></div></foreignObject><text x="198" y="649" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">有配置</text></switch></g><path d="M 197.59 570 L 197.59 585 Q 197.59 595 197.53 604.32 L 197.47 613.64" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.47 618.89 L 193.97 611.89 L 197.47 613.64 L 200.97 611.89 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><rect x="137.5" y="720" width="120" height="60" rx="9" ry="9" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 750px; margin-left: 139px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">单次订阅<br />回调命令</div></div></div></foreignObject><text x="198" y="754" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">单次订阅&#xa;回调命令</text></switch></g><path d="M 197.5 670 L 197.5 713.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.5 718.88 L 194 711.88 L 197.5 713.63 L 201 711.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><path d="M 342.53 780 L 342.53 860.06 Q 342.53 870.06 332.53 870.06 L 263.87 870.06" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 258.62 870.06 L 265.62 866.56 L 263.87 870.06 L 265.62 873.56 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><rect x="282.5" y="720" width="120" height="60" rx="9" ry="9" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 750px; margin-left: 284px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">单次订阅 I</div></div></div></foreignObject><text x="343" y="754" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">单次订阅 I</text></switch></g><rect x="137.5" y="830" width="120" height="60" rx="9" ry="9" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 860px; margin-left: 139px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">发送 H C I</div></div></div></foreignObject><text x="198" y="864" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">发送 H C I</text></switch></g><path d="M 197.5 780 L 197.5 823.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.5 828.88 L 194 821.88 L 197.5 823.63 L 201 821.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><rect x="137.5" y="190" width="120" height="50" rx="7.5" ry="7.5" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 215px; margin-left: 139px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">校验命令</div></div></div></foreignObject><text x="198" y="219" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">校验命令</text></switch></g><path d="M 197.5 140 L 197.5 183.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197.5 188.88 L 194 181.88 L 197.5 183.63 L 201 181.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><rect x="470" y="920" width="252.5" height="95" rx="14.25" ry="14.25" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 245px; height: 1px; padding-top: 910px; margin-left: 475px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; max-height: 105px; overflow: hidden; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><h1>IOFPSOCKET.JS</h1><p>左侧是指令由浏览器向服务器的发送流程，右侧是浏览器接收服务器指令后的处理流程，消息内容为json格式，包含 H C I 三个字段</p></div></div></div></foreignObject><text x="475" y="922" fill="#1D3557" font-family="Helvetica" font-size="12px">IOFPSOCKET.JS...</text></switch></g><rect x="330" y="93.75" width="140" height="42.5" rx="6.38" ry="6.38" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/><path d="M 344 93.75 L 344 136.25 M 456 93.75 L 456 136.25" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 115px; margin-left: 345px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><span>components/<br />iofpsocket.js<br /></span></div></div></div></foreignObject><text x="400" y="119" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">components/...</text></switch></g><path d="M 330 115 L 303.71 115 Q 293.71 115 283.71 115 L 257.5 115" fill="none" stroke="#457b9d" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 630 235 L 630 278.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630 283.88 L 626.5 276.88 L 630 278.63 L 633.5 276.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><path d="M 620 235 L 580 235 Q 570 235 573.16 225.51 L 586.84 184.49 Q 590 175 600 175 L 680 175 Q 690 175 686.84 184.49 L 673.16 225.51 Q 670 235 660 235 Z" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 205px; margin-left: 571px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">触发 I 订阅</div></div></div></foreignObject><text x="630" y="209" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">触发 I 订阅</text></switch></g><path d="M 630 345 L 630 398.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630 403.88 L 626.5 396.88 L 630 398.63 L 633.5 396.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><path d="M 620 345 L 580 345 Q 570 345 573.16 335.51 L 586.84 294.49 Q 590 285 600 285 L 680 285 Q 690 285 686.84 294.49 L 673.16 335.51 Q 670 345 660 345 Z" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 571px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">触发 H:I 订阅</div></div></div></foreignObject><text x="630" y="319" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">触发 H:I 订阅</text></switch></g><path d="M 630 465 L 630 518.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630 523.88 L 626.5 516.88 L 630 518.63 L 633.5 516.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><path d="M 620 465 L 580 465 Q 570 465 573.16 455.51 L 586.84 414.49 Q 590 405 600 405 L 680 405 Q 690 405 686.84 414.49 L 673.16 455.51 Q 670 465 660 465 Z" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 435px; margin-left: 571px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">触发 H 订阅</div></div></div></foreignObject><text x="630" y="439" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">触发 H 订阅</text></switch></g><path d="M 630 585 L 630 668.63" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630 673.88 L 626.5 666.88 L 630 668.63 L 633.5 666.88 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><rect x="570" y="525" width="120" height="60" rx="9" ry="9" fill="none" stroke="#457b9d" pointer-events="all"/><path d="M 582 525 L 582 585 M 678 525 L 678 585" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 555px; margin-left: 583px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">移除单词订阅</div></div></div></foreignObject><text x="630" y="559" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">移除单词订阅</text></switch></g><ellipse cx="630" cy="715" rx="40" ry="40" fill="none" stroke="#457b9d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 715px; margin-left: 591px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">结束</div></div></div></foreignObject><text x="630" y="719" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">结束</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>