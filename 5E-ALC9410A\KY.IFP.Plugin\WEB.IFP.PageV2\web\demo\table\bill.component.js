define(function () {
    var ifpBillHeaderItem={
        props:{
            alignLeft:{type:Boolean, default:false},
            alignCenter:{type:Boolean, default:false},
            alignRight:{type:Boolean, default:false},
            width:{defualt:"auto"}
        },
        template:`
        <div :style="{width:width}" :class="{
            'flex-item':width==='auto',
            'text-left':alignLeft,
            'text-center':alignCenter,
            'text-right':alignRight
        }"><slot></slot></div>
        `
    }
    var ifpBillHeader={
        components:{ifpBillHeaderItem},
        props:{
            left:{default:''},
            middle:{default:''},
            right:{default:''}
        },
        template:`
        <div class="flex flex-row" style="width:100%;">
            <ifp-bill-header-item width="auto" align-left v-if="left || (right&&middle)">{{left||' '}}</ifp-bill-header-item>
            <slot></slot>
            <ifp-bill-header-item width="auto" align-center v-if="middle">{{middle}}</ifp-bill-header-item>
            <ifp-bill-header-item width="auto" align-right v-if="right || (left&&middle)">{{right||' '}}</ifp-bill-header-item>
        </div>
        `
    }
    
    var ifpBill={
        props:{
            title:{default:''},
        },
        template:`
        <div class="ifp-bill-wrapper">
            <div class="ifp-bill">
                <div v-if="title" class="ifp-bill-title">{{title}}</div>
                <slot name="header"></slot>
                <slot></slot>
                <slot name="footer"></slot>
            </div>
        </div>
        `
    }
    return {
        components:{ifpBillHeaderItem,ifpBillHeader,ifpBill},
        el: "#app",
        data(){
            return {
                bgrq:'2021-01-01',
                shrq:'2021-01-01'
            }
        }
    }
})