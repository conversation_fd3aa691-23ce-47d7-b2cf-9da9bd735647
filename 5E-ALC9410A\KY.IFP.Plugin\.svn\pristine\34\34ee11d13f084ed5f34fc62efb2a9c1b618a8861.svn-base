﻿using COM.IFP.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace API.ICS.BaseData
{
    public class SampleType
    {
        readonly Lazy<DAL.ICS.BaseData.SampleType> service = Entity.Create<DAL.ICS.BaseData.SampleType>();
        /// <summary>
        /// 根据作废标志查询4024的基础信息
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public List<ORM.IFP.YWDX4024> SampleTypeList(JsonElement json)
        {
            int? zfbz = json.GetValue<int?>("zfbz");

            return service.Value.SampleTypeList(zfbz);
        }
        /// <summary>
        /// 通过场景查询样品类型集合
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns> 
        public List<ORM.IFP.YWDX4024> SampleTypeListByScene(JsonElement json)
        {
            long ywcj = json.GetValue<long>("ywcj");

            return service.Value.SampleTypeListByScene(ywcj);
        }
        /// <summary>
        /// 新增/修改
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PFActionResult Submit(JsonElement json)
        {
            ORM.IFP.YWDX4024 obj = json.GetValue<ORM.IFP.YWDX4024>();
            obj.Lasttime = DateTime.Now;
            //没有创建时间
            if (!obj.Addtime.HasValue) obj.Addtime = DateTime.Now;
            List<ORM.IFP.YWDX4024> source = new List<ORM.IFP.YWDX4024>() { obj };
            return service.Value.Submit(source);
        }
        /// <summary>
        /// 排序改变。
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PFActionResult OrderChange(JsonElement json)
        {
            List<ORM.IFP.YWDX4024> source = json.GetValue<List<ORM.IFP.YWDX4024>>();
            return service.Value.OrderChange(source);
        }

        /// <summary>
        /// 场景列表
        /// </summary>
        /// <returns></returns>
        public List<ORM.IFP.BaseData> SceneList()
        {
            return service.Value.SceneList();
        }

        /// <summary>
        /// 编码规则列表
        /// </summary>
        /// <returns></returns>
        public List<ORM.IFP.IFP_BS_CODE_CONFIG> CodeRuleList()
        {
            return service.Value.CodeRuleList();
        }
    }
}
