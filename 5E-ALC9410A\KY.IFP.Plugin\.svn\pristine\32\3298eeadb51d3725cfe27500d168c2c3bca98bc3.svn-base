﻿using COM.IFP.Common;
//using COM.IFP.LinqDB;
using COM.IFP.SqlSugarN;
using DAL.IFP.Rights;
using LinqToDB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using ORM.IFP;

namespace API.ICS.BaseData
{
    public class TransportStation
    {

        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();
        private Lazy<DAL.ICS.BasicData.TransportStation> service = COM.IFP.Common.Entity.Create< DAL.ICS.BasicData.TransportStation> ();

        public object Select(JsonElement json)
        {
            int isstop=  json.GetValue<int>("isStop");
            string str= json.GetValue<string>("whereStr");
            var pack = json.GetValue<PageModel<ORM.IFP.YWDX4006>>();
            if (isstop == 2)
            { lazy.Value.Select(x => x.Bname.ToString().Contains(str), pack); }
            else
            {
                lazy.Value.Select(x => x.Zfbz == isstop && x.Bname.ToString().Contains(str), pack);
            }
            return pack;
        }

        /// <summary>
        /// 新增与修改
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public PFActionResult Submit(JsonElement json)
        {
            YWDX4006 obj = json.GetValue<YWDX4006>();
            obj.Creator = UserCache.GetUserName();
            obj.Lasttime = DateTime.Now;
            return service.Value.Submit(obj);
        }

        public void Delete(JsonElement json)
        {
            var item = json.GetValue<ORM.IFP.YWDX4006>();
            lazy.Value.Delete(item);
        }
    }
}
