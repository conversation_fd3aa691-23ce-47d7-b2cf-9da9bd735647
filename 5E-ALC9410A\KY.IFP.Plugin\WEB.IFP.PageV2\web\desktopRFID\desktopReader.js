﻿; define([
    "util",
    "platform/vue",
    "iofp/api",
    "moment"
], function (util, pVue, API) {
    //返回，6个方法一样的返回格式
    //{Message: null,Params: { CardNo: "722E404D", CardContent: "CEabcdabC48EE72C", Count: 2, CardNum: "CEabcdabC48EE72C" },Type: 5}
    //Type=5表示成功,其他失败，
    return {
        kyReader: {
            ReadCard: function (vueThis) {
                return new Promise((resolve, reject) => {
                    var _this = this;
                    API.GetAction("http://127.0.0.1:15009/RC/ReadCard/ReadCard", {}).then(x => {
                        if (util.isEmpty(x)) {
                            //return null;
                            reject("读取失败");
                        }
                        if (x.Type == 5) {
                            //cardNum = x.Params.CardNum;
                            resolve(x.Params.CardNum);
                        } else {
                            _this.showConfigPage(vueThis, x.Message);
                            //return null;
                            reject("读取失败");
                        }
                    }).catch(e => {
                        console.log(e);
                        _this.showDownLoadMsg(vueThis);
                        reject(e)
                    });
                })
            },
            //参数{Data:'123456'}，返回
            WriteCard: function (vueThis,cardNum) {
                return new Promise((resolve, reject) => {
                    var _this = this;
                    var obj = {};
                    obj.Data = cardNum;
                    API.GetAction("http://127.0.0.1:15009/RC/ReadCard/WriteCard", obj).then(x => {
                        if (util.isEmpty(x)) {
                            //return null;
                            reject("写卡失败");
                        }
                        if (x.Type == 5) {
                            //cardNum = x.Params.CardNum;
                            resolve(x.Params.CardContent);
                        } else {
                            _this.showConfigPage(vueThis, x.Message);
                            //return null;
                            reject("写卡失败");
                        }
                    }).catch(e => {
                        console.log(e);
                        _this.showDownLoadMsg(vueThis);
                        reject(e)
                    });
                });
            },
            showDownLoadMsg: function (vueThis) {
                vueThis.$confirm('没有安装或启动读卡器驱动服务。\n是否下载驱动服务？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    window.open("/desktopRFID/DesktopRFID.rar");
                }).catch(() => {
                });
            },
            showConfigPage: function (vueThis, msg) {
                vueThis.$confirm(msg + '\n是否打开配置页面？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    window.open("http://127.0.0.1:15009/pages/RC/Reader.html");
                }).catch(() => {
                });
            }
        },
        wqReader: {
            ReadCard: function (vueThis) {
                return new Promise((resolve, reject) => {
                    var _this = this;
                    API.GetAction("http://127.0.0.1:15009/RC/HH1030Reader/ReadCard", {}).then(x => {
                        if (util.isEmpty(x)) {
                            //return null;
                            reject("读取失败");
                        }
                        if (x.Type == 5) {
                            //cardNum = x.Params.CardNum;
                            resolve(x.Params.CardNum);
                        } else {
                            _this.showConfigPage(vueThis, x.Message);
                            //return null;
                            reject("读取失败");
                        }
                    }).catch(e => {
                        console.log(e);
                        _this.showDownLoadMsg(vueThis);
                        reject(e)
                    });
                })
            },

            WriteCard: function (vueThis, cardNum) {
                return new Promise((resolve, reject) => {
                    var _this = this;
                    var obj = {};
                    obj.Data = xpid;
                    API.GetAction("http://127.0.0.1:15009/RC/HH1030Reader/WriteCard", obj).then(x => {
                        if (util.isEmpty(x)) {
                            //return null;
                            reject("写卡失败");
                        }
                        if (x.Type == 5) {
                            //cardNum = x.Params.CardNum;
                            resolve(x.Params.CardNum);
                        } else {
                            _this.showConfigPage(vueThis, x.Message);
                            //return null;
                            reject("写卡失败");
                        }
                    }).catch(e => {
                        console.log(e);
                        _this.showDownLoadMsg(vueThis);
                        reject(e)
                    });
                });
            },
            showDownLoadMsg: function (vueThis) {
                vueThis.$confirm('没有安装或启动读卡器驱动服务。\n是否下载驱动服务？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    window.open("/desktopRFID/DesktopRFID.rar");
                }).catch(() => {
                });
            },
            showConfigPage: function (vueThis, msg) {
                vueThis.$confirm(msg + '\n是否打开配置页面？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    window.open("http://127.0.0.1:15009/pages/RC/HH1030Reader.html");
                }).catch(() => {
                });
            }
        },
        //宜科485读卡器，样桶编码
        ykReader: {
            ReadCard: function (vueThis) {
                return new Promise((resolve, reject) => {
                    var _this = this;
                    API.GetAction("http://127.0.0.1:15009/RC/YK485Reader/ReadCard", {}).then(x => {
                        if (util.isEmpty(x)) {
                            //return null;
                            reject("读取失败");
                        }
                        if (x.Type == 5) {
                            //cardNum = x.Params.CardNum;
                            resolve(x.Params.CardNum);
                        } else {
                            _this.showConfigPage(vueThis, x.Message);
                            //return null;
                            reject("读取失败");
                        }
                    }).catch(e => {
                        console.log(e);
                        _this.showDownLoadMsg(vueThis);
                        reject(e)
                    });
                })
            },
            //参数{Data:'123456'}，返回
            WriteCard: function (vueThis, cardNum) {
                return new Promise((resolve, reject) => {
                    var _this = this;
                    var obj = {};
                    obj.Data = cardNum;
                    API.GetAction("http://127.0.0.1:15009/RC/YK485Reader/WriteCard", obj).then(x => {
                        if (util.isEmpty(x)) {
                            //return null;
                            reject("写卡失败");
                        }
                        if (x.Type == 5) {
                            //cardNum = x.Params.CardNum;
                            resolve(x.Params.CardContent);
                        } else {
                            _this.showConfigPage(vueThis, x.Message);
                            //return null;
                            reject("写卡失败");
                        }
                    }).catch(e => {
                        console.log(e);
                        _this.showDownLoadMsg(vueThis);
                        reject(e)
                    });
                });
            },
            showDownLoadMsg: function (vueThis) {
                vueThis.$confirm('没有安装或启动读卡器驱动服务。\n是否下载驱动服务？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    window.open("/desktopRFID/DesktopRFID.rar");
                }).catch(() => {
                });
            },
            showConfigPage: function (vueThis, msg) {
                vueThis.$confirm(msg + '\n是否打开配置页面？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    window.open("http://127.0.0.1:15009/pages/RC/YK485Reader.html");
                }).catch(() => {
                });
            }
        }
    }
})