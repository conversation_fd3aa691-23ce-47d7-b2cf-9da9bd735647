﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{
    /// <summary>
    /// 火车车型
    /// </summary>
    [SugarTable("IFP_BS_YWDX4017")]
    public partial class YWDX4017 : BaseData<YWDX4017>
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public override Field<long> Gid { get; set; }

        /// <summary>
        /// 皮重
        /// </summary>
        [SugarColumn(ColumnName = "PZ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,4)")]
        public Field<decimal> PZ { get; set; }

        /// <summary>
        /// 载重
        /// </summary>
        [SugarColumn(ColumnName = "ZHAIZ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "decimal(18,4)")]
        public Field<decimal> ZHAIZ { get; set; }

        /// <summary>
        /// 是否特殊车型
        /// </summary>
        [SugarColumn(ColumnName = "SFTSCX1099", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> SFTSCX1099 { get; set; }

        /// <summary>
        /// 长
        /// </summary>
        [SugarColumn(ColumnName = "Length", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Length { get; set; }

        /// <summary>
        /// 宽
        /// </summary>
        [SugarColumn(ColumnName = "Width", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Width { get; set; }

        /// <summary>
        /// 高
        /// </summary>
        [SugarColumn(ColumnName = "Height", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Height { get; set; }

        /// <summary>
        /// 底板高度
        /// </summary>
        [SugarColumn(ColumnName = "FloorHeight", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> FloorHeight { get; set; }

        /// <summary>
        /// 是否自备车
        /// </summary>
        [SugarColumn(ColumnName = "SFZBC1099", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> SFZBC1099 { get; set; }
    }

    //[Table(Name = "IFP_BS_YWDX4017")]
    //public partial class YWDX4017 : BaseData<YWDX4017>
    //{
    //	/// <summary>
    //	/// 
    //	/// </summary>
    //	[Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.Int64, DbType = "bigint")]
    //	public override Field<long> Gid { get; set; }

    //	/// <summary>
    //	/// 皮重
    //	/// </summary>
    //	[Column(Name = "PZ", DataType = DataType.Decimal, DbType = "decimal(18,4)")]
    //	public Field<decimal> PZ { get; set; }

    //	/// <summary>
    //	/// 载重
    //	/// </summary>
    //	[Column(Name = "ZHAIZ", DataType = DataType.Decimal, DbType = "decimal(18,4)")]
    //	public Field<decimal> ZHAIZ { get; set; }

    //	/// <summary>
    //	/// 是否特殊车型
    //	/// </summary>
    //	[Column(Name = "SFTSCX1099", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> SFTSCX1099 { get; set; }

    //	/// <summary>
    //	/// 长
    //	/// </summary>
    //	[Column(Name = "Length", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> Length { get; set; }

    //	/// <summary>
    //	/// 宽
    //	/// </summary>
    //	[Column(Name = "Width", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> Width { get; set; }

    //	/// <summary>
    //	/// 高
    //	/// </summary>
    //	[Column(Name = "Height", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> Height { get; set; }

    //	/// <summary>
    //	/// 底板高度
    //	/// </summary>
    //	[Column(Name = "FloorHeight", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> FloorHeight { get; set; }

    //	/// <summary>
    //	/// 是否自备车
    //	/// </summary>
    //	[Column(Name = "SFZBC1099", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> SFZBC1099 { get; set; }
    //}


    /* 以下为对应的Json对象
	{
		GID: null,
		PZ: null,		//皮重
		ZHAIZ: null,		//载重
		SFTSCX1099: null,		//是否特殊车型
		Length: null,		//长
		Width: null,		//宽
		Height: null,		//高
		FloorHeight: null,		//底板高度
		SFZBC1099: null		//是否自备车
	}
	*/
}
