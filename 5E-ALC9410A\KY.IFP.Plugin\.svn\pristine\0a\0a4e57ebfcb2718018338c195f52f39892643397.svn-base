﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>读卡器设置</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" v-loading="loading">
        <ifp-toolbar close>

            <ifp-button @click="add" log>新增</ifp-button>
            <ifp-button @click="detail" log>查看</ifp-button>
            <ifp-button @click="modify" log>修改</ifp-button>
            <ifp-button @click="remove" log>删除</ifp-button>


            <div style="float:right;">
                <span class="dicb dicb-tuxing" style="font-size:16px;color:#0075D3"></span>
                <span style="font-size: 14px; font-weight: bold;">
                    读卡器设置
                </span>
            </div>
        </ifp-toolbar>
       
        <ifp-panel-table class="flex-item padding">
            <ifp-table ref="tableRef"
                       :data="data"
                       row-key="Gid"
                       :border="true"
                       :highlight-current-row="true"
                       @row-click="rowClick">
                <el-table-column type="index"
                                 :index="indexMethod"
                                 width="50">
                </el-table-column>
                <el-table-column prop="ReadCode"
                                 label="读卡器编码"
                                 align="center"
                                 width="100">
                </el-table-column>
                <el-table-column prop="IPaddr"
                                 label="网络地址"
                                 align="center"
                                 width="140">
                </el-table-column>
                <el-table-column prop="Port"
                                 label="端口号"
                                 align="center"
                                 width="80">
                </el-table-column>
                <el-table-column prop="SecNum"
                                 label="标签扇区"
                                 align="center"
                                 width="80">
                </el-table-column>
                <el-table-column prop="BlockNum"
                                 label="标签块号"
                                 align="center"
                                 width="80">
                </el-table-column>
                <el-table-column prop="Count"
                                 label="读写次数"
                                 align="center"
                                 width="80">

                </el-table-column>
                <el-table-column prop="IPList"
                                 label="可用IP"
                                 align="center"
                                 width="180">
                </el-table-column>
                <el-table-column prop="Remark"
                                 label="备注"
                                 align="center"
                                 width="150">
                </el-table-column>
            </ifp-table>
            <template v-slot:pagination>
                <ifp-pagination @size-change="sizeChange"
                                @current-change="pageChange"
                                :current-page="paging.page"
                                :page-sizes="[10,20,100,10000]"
                                :page-size="paging.size"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="paging.records">
                </ifp-pagination>
            </template>
        </ifp-panel-table>

        <ifp-dialog :visible.sync="editor.show" :title="editor.title" width="800px">
            <ifp-detail @cancel="editor.show=false;"
                        @submit="editor.show=false,onSelect()"
                        :data="editor.data"
                        :czlx="editor.czlx"></ifp-detail>
        </ifp-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>