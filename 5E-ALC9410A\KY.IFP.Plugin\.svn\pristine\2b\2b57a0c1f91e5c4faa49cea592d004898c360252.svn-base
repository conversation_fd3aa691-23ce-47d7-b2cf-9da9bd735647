﻿using COM.IFP.Common;
//using COM.IFP.LinqDB;
using COM.IFP.SqlSugarN;
using DAL.IFP.Rights;
//using LinqToDB.Linq;
//using LinqToDB;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data.SqlTypes;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using PageModel = COM.IFP.Common.PageModel;

namespace DAL.ICS.BaseData
{
    public class Mineral
    {
        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();

        public object Select(IList<ORM.IFP.YWDX4001> kdxx, IList<ORM.IFP.BaseData> ghdw, IList<ORM.IFP.BaseData> fzxx, PageModel page)
        {
            using var db = DB.Create();
            var q = db.Queryable<ORM.IFP.YWDX4001>()
                       .LeftJoin<ORM.IFP.BaseData>((a, b) => b.Gid == a.Ghdw4002)
                       .LeftJoin<ORM.IFP.BaseData>((a, b, c) => c.Gid == a.Fz4006)
                       .Select((a, b, c) => new { a, b, c });
            q = q.Query(x => x.a, kdxx, x => x.b, ghdw, x => x.c, fzxx);
            return q.Select(x => new { kdxx = x.a, ghdw = new { x.b.Bname, x.b.Sname }, fzxx = new { x.c.Bname, x.c.Sname } }).Fetch(page);
        }

        public void Submit(IList<ORM.IFP.YWDX4001> kdxx)
        {
            foreach (var one in kdxx)
            {
                if (one.Addtime == one.Lasttime)
                {
                    //更新时间等于修改时间  是新增 把创建人补上
                    //one.Creator = UserCache.loginUser == null ? "" : UserCache.loginUser.UsiName;
                    one.Creator = UserCache.GetUserName();
                }
            }
            lazy.Value.Submit(kdxx.ToArray());
        }

        public void Delete(IList<ORM.IFP.YWDX4001> kdxx)
        {
            lazy.Value.Delete(kdxx.ToArray());
        }
    }
    //public class Mineral
    //{
    //    readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();

    //    public object Select(IList<ORM.IFP.YWDX4001> kdxx, IList<ORM.IFP.BaseData> ghdw, IList<ORM.IFP.BaseData> fzxx, PageModel page)
    //    {
    //        using var db = DB.Create();
    //        var q = from a in db.GetQuery<ORM.IFP.YWDX4001>()
    //                from b in db.GetTable<ORM.IFP.BaseData>().Where(x => x.Gid == a.Ghdw4002).DefaultIfEmpty()
    //                from c in db.GetTable<ORM.IFP.BaseData>().Where(x => x.Gid == a.Fz4006).DefaultIfEmpty()
    //                select new { a, b, c };
    //        //q = q.Query(x => x.a, kdxx).Query(x => x.b, ghdw).Query(x => x.c, fzxx);
    //        q = q.Query(x => x.a, kdxx, x => x.b, ghdw, x => x.c, fzxx);
    //        return q.Select(x => new { kdxx = x.a, ghdw = new { x.b.Bname, x.b.Sname }, fzxx = new { x.c.Bname, x.c.Sname } }).Fetch(page);
    //    }

    //    public void Submit(IList<ORM.IFP.YWDX4001> kdxx)
    //    {
    //        foreach (var one in kdxx)
    //        {
    //            if (one.Addtime == one.Lasttime)
    //            {
    //                //更新时间等于修改时间  是新增 把创建人补上
    //                //one.Creator = UserCache.loginUser == null ? "" : UserCache.loginUser.UsiName;
    //                one.Creator = UserCache.GetUserName();
    //            }
    //        }
    //        lazy.Value.Submit(kdxx.ToArray());
    //    }

    //    public void Delete(IList<ORM.IFP.YWDX4001> kdxx)
    //    {
    //        lazy.Value.Delete(kdxx.ToArray());
    //    }
    //}
}
