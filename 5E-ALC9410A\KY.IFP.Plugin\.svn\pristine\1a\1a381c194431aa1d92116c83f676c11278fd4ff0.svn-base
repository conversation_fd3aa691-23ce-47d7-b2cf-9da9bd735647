﻿using COM.IFP.Common;
using ORM.IFP.DbModel;
using System;
using System.Text.Json;

namespace API.IFP.www.Device
{
    public class Reader
    {
        //daiabin
        //COM.IFP.Device.Reader read = new COM.IFP.Device.Reader();
        readonly Lazy<DAL.IFP.www.Device.Reader> readLazy = Entity.Create<DAL.IFP.www.Device.Reader>();
        /// <summary>
        /// 读卡
        /// </summary>
        /// <param name="jobj">
        /// <para>checkIp</para>
        /// <para>IPaddr</para>
        /// <para>Port</para>
        /// </param>    
        /// <returns></returns>
        public PFActionResult ReadCard(JsonElement json)
        {
            //IFP_SM_READCONFIG jobj = JsonConvert.DeserializeObject<IFP_SM_READCONFIG>(json.ToString());
            IFP_SM_READCONFIG jobj = json.GetValue<IFP_SM_READCONFIG>();
            return readLazy.Value.ReadCard(jobj);
        }

        public PFActionResult WriteCard(JsonElement json)
        {
            IFP_SM_READCONFIG jobj = json.GetValue<IFP_SM_READCONFIG>();
            return readLazy.Value.WriteCard(jobj);
        }


        public PFActionResult ReadCardDefault(JsonElement json)
        {
            return readLazy.Value.ReadCardDefault();
        }

    }
}
