; define([
    "require",
    "controllers/base",
    "jquery",
    "jclass",
    "iofp/services/service.job"],
    function (require, base, $, jclass, jobservice) {
        return jclass(base, {
            name: "jobInfo",
            openWin: function () {
                this.log.info("打开弹出页");
            },
            bindEvent: function () {
                var _this = this;

                _this.bind("addBtn", "click", this.addFun, this);
                _this.bind("searchBtn", "click", this.queryFun, this);
                _this.bind("resetBtn", "click", this.resetFun, this);
                _this.bind("viewBtn", "click", this.viewFun, this);
                _this.bind("modifyBtn", "click", this.modifyFun, this);
                _this.bind("delBtn", "click", this.delFun, this);

                _this.bind("startBtn", "click", this.startFun, this);
                _this.bind("stopBtn", "click", this.stopFun, this);
                _this.bind("runBtn", "click", this.runFun, this);

                //选中行事件
                this.bind("grid", "onSelectRow", function (rowid, isSelected) {
                    var rowdata = _this.controls.grid.getRowData(rowid);

                    if (rowdata.Status == 1) {
                        _this.controls.startBtn.disable();
                        _this.controls.stopBtn.disable(false);
                        _this.controls.delBtn.disable();
                    } else {
                        _this.controls.startBtn.disable(false);
                        _this.controls.stopBtn.disable();
                        _this.controls.delBtn.disable(false);
                    }

                });

            },

            startFun: function () {
                var _this = this;
                var temp = _this.controls.grid.getSelectRowData();
                if (temp == null) {
                    $.bootoast.warning("请选择一行记录。");
                    return;
                }
                return jobservice.startJob(temp).then(function (data) {
                    if (data.success) {
                        $.bootoast.success("启动成功。");
                        _this.controls.grid.updateRowData(temp.Gid, { Status: 1 });
                        _this.controls.startBtn.disable();
                        _this.controls.stopBtn.disable(false);
                        _this.controls.delBtn.disable(false);
                        //$.alert("启动成功。", function () {
                        //    _this.controls.grid.updateRowData(temp.Gid, { Status: 1 });
                        //    _this.controls.startBtn.disable();
                        //    _this.controls.stopBtn.disable(false);
                        //    _this.controls.delBtn.disable(false);
                        //});
                    } else {
                        $.alert("启动失败。");
                    }
                });
            },

            stopFun: function () {
                var _this = this;
                var temp = _this.controls.grid.getSelectRowData();
                if (temp == null) {
                    $.bootoast.warning("请选择一行记录。");
                    return;
                }
                return jobservice.stopJob(temp).then(function (data) {

                    if (data.success) {
                        $.bootoast.success("停止成功。");
                        _this.controls.grid.updateRowData(temp.Gid, { Status: 0 });
                        _this.controls.startBtn.disable(false);
                        _this.controls.stopBtn.disable();
                        _this.controls.delBtn.disable(false);
                        //$.alert("停止成功。", function () {
                        //    _this.controls.grid.updateRowData(temp.Gid, { Status: 0 });
                        //    _this.controls.startBtn.disable(false);
                        //    _this.controls.stopBtn.disable();
                        //    _this.controls.delBtn.disable(false);
                        //});
                    } else {
                        $.alert("停止失败。");
                    }
                });
            },

            runFun: function () {
                var _this = this;
                var data = _this.controls.grid.getSelectRowData();
                if (data == null) {
                    $.bootoast.warning("请选择一行记录。");
                    return;
                }
                F.util.showWait();
                return jobservice.runJob(data).then(function (data) {
                    if (data.success) {
                        F.util.hideWait();
                        $.bootoast.success("执行成功。");
                        //$.alert("执行成功。", function () {
                            
                        //});
                    } else {
                        F.util.hideWait();
                        $.alert(data.msg);
                    }
                });
            },


            delFun: function () {
                var _this = this;
                var data = _this.controls.grid.getSelectRowData();
                if (data == null) {
                    $.bootoast.warning("请选择一行记录。");
                    return;
                }
                $.confirm('删除后将无法恢复，是否继续?', function (result) {
                    if (result) {
                        //执行删除数据
                        jobservice.delJob(data).then(function (data) {
                            if (data.success) {
                                $.bootoast.success("删除成功。");
                                //_this.controls.grid.delRowData(data.Gid);
                                _this.queryFun();
                                //$.alert("删除成功。", function () {
                                //    _this.controls.grid.delRowData(data.Gid);
                                //});
                            } else {
                                $.alert("删除失败。");
                            }
                        });
                    }
                });
               
            },
            viewFun: function () {
                var _this = this;
                var data = _this.controls.grid.getSelectRowData();
                if (data == null) {
                    $.bootoast.warning("请选择一行记录。");
                    return;
                }
                var opt = {
                    url: "/pages/job/jobAdd.html",
                    full: true,
                    width: '80%',
                    height: 550,
                    title: "调度任务配置",
                    onhidden: function () {

                    }
                };
                // 参数
                opt.parameter = { "czlx": "view", "data": data };
                var win = $.showIframe(opt);
            },
            modifyFun: function () {
                var _this = this;
                var data = _this.controls.grid.getSelectRowData();
                if (data == null) {
                    $.bootoast.warning("请选择一行记录。");
                    return;
                }
                var opt = {
                    url: "/pages/job/jobAdd.html",
                    full: true,
                    width: '80%',
                    height: 550,
                    title: "调度任务配置",
                    onhidden: function () {
                        if (this.returnValue() != null) {
                            _this.controls.grid.updateRowData(data.Gid, this.returnValue());
                        }
                    }
                };
                // 参数
                opt.parameter = { "czlx": "modify", "data": data };
                var win = $.showIframe(opt);
            },
            addFun: function () {
                var _this = this;

                var opt = {
                    url: "/pages/job/jobAdd.html",
                    full: true,
                    width: '80%',
                    height: 550,
                    title: "调度任务配置",
                    onhidden: function () {
                        _this.queryFun();
                    }
                };
                // 参数
                opt.parameter = { "czlx": "add" };
                var win = $.showIframe(opt);
            },

            resetFun: function () {
                var _this = this;
                _this.controls.JobName_like.clear();
                _this.controls.DLLPath_like.clear();
                this.controls.CreateType.value(0);
                _this.queryFun();
            },

            queryFun: function () {
                var _this = this;
                _this.loadForm();

                _this.controls.startBtn.disable(false);
                _this.controls.stopBtn.disable(false);
                _this.controls.delBtn.disable(false);
            },

            //初始化页面
            initData: function () {
                this.controls.CreateType.value(0);
                this.loadForm();
            },
            //加载GIRD
            loadForm: function () {
                var _this = this;
                _this.controls.grid.reload({
                    url: '/API/IFP/Job/Job/JobList',
                    method: 'post',
                    postData: {
                        "JobName_like": _this.controls.JobName_like.value(),
                        "DLLPath_like": _this.controls.DLLPath_like.value(),
                        "CreateType": _this.controls.CreateType.value()
                    }
                });

            },

            onLoad: function () {
                this.initData();
                this.bindEvent();
            }
        })
    });
