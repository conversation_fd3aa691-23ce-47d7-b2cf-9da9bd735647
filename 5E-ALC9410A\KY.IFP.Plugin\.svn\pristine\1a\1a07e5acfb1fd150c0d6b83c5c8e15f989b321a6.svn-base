// 定制 jqgrid 功能
require([
	"jquery",

	// grid
	"jqgrid/minified/grid.base",
	"jqgrid/minified/grid.celledit", // 编辑单元格
	//"jqgrid/minified/grid.common",
	//"jqgrid/minified/grid.filter", 
	"jqgrid/minified/grid.formedit", // 自定义编辑
	"jqgrid/minified/grid.grouping", // 多行表头
	//"jqgrid/minified/grid.import",
	"jqgrid/minified/grid.inlinedit", // 行编辑
	//"jqgrid/minified/grid.pivot",
	//"jqgrid/minified/grid.subgrid",
	"jqgrid/minified/grid.treegrid", // 树表格
	//"jqgrid/minified/jqDnR",
	//"jqgrid/jqModal",
	"jqgrid/minified/jquery.fmatter",
	//"jqgrid/minified/grid.utils", //
	//"jqgrid/addons/ui.multiselect",
    "jqgrid/minified/i18n/grid.locale-cn",

    "css!lib/bower/jqGrid/css/ui.jqgrid-bootstrap.css"
], function ($) {
	"use strict";
	console.log("jqGrid-ky loaded!");
});