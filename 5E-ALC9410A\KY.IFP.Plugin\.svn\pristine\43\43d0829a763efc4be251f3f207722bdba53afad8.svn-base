﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输煤运行班次信息</title>
</head>
<body class="flex" controller option="{platform:'element'}">

    <ifp-page id="app">
        <ifp-toolbar close lxsz="tableBc">
            <ifp-button code="B1" @click="onAdd">新增</ifp-button>
            <ifp-button code="B2" @click="onView">查看</ifp-button>
            <ifp-button code="B3" @click="onUpdate">修改</ifp-button>
        </ifp-toolbar>

        <ifp-searchbar @search="onSelect" @reset="onReset">
            <ifp-form-item label="启停状态">
                <el-radio-group v-model="filter.zfbz" @change="onSelect" value="0">
                    <el-radio :label="-1">全部</el-radio>
                    <el-radio :label="0">启用</el-radio>
                    <el-radio :label="1">停用</el-radio>
                </el-radio-group>
            </ifp-form-item>
        </ifp-searchbar>

        <ifp-panel-table class="flex-item padding">
            <ifp-table ref="tableBc"
                       :data="tableData"
                       row-key="Gid"
                       :border="true"
                       :highlight-current-row="true"
                       @row-click="rowClick">
                <el-table-column type="index" :index="indexMethod"
                                 width="50">
                </el-table-column>
                <el-table-column prop="Bname" min-width="100"
                                 label="班次名称">
                </el-table-column>
                <el-table-column prop="KSSJ"
                                 label="开始时间">
                </el-table-column>
                <el-table-column prop="JSSJ"
                                 label="结束时间">
                </el-table-column>
                <el-table-column prop="Zfbz"
                                 align="center"
                                 label="启停状态">
                    <template slot-scope="scope">
                        <div v-if="scope.row.Zfbz!=1">启用</div>
                        <div v-else>停用</div>
                    </template>
                </el-table-column>

                <el-table-column prop="Beizhu" min-width="300"
                                 label="备注">
                </el-table-column>

            </ifp-table>
            <template v-slot:pagination>
                <ifp-pagination @size-change="sizeChange"
                                @current-change="pageChange"
                                :current-page="paging.page"
                                :page-sizes="[10,15,20,50]"
                                :page-size="paging.size"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="paging.records">
                </ifp-pagination>
            </template>
        </ifp-panel-table>
        <ifp-dialog class="subpage" :title="editor.title"
                   :visible.sync="editor.showdetail"
                   width="600px">
            <ifp-smyxbc-detail @cancel="editor.showdetail=false"
                               @success="editor.showdetail=false,onSelect()"
                               :gid="editor.gid"
                               :czlx="editor.czlx">
            </ifp-smyxbc-detail>
        </ifp-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>