; define(["controller", "jclass", "mockjax"],
	function (base, jclass) {

		$.mockjax({
			url: "/com.kysoft.service/html/baseinfo/queryMzfllist.action",
			responseText: `[
			{id:1,text:"1",pid:""},
			{id:11,text:"11",pid:"1"},
			{id:12,text:"12",pid:"1"},
			{id:2,text:"2",pid:""},
			{id:21,text:"21",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2",zfbz:1},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22,text:"22",pid:"2"},
			{id:22256,text:"22",pid:"2"}
		]`
		});
	//$.mockjax("/com.kysoft.service/html/baseinfo/queryMzfllist.action",function(option){
	//	var opt = JSON.parse(option.body);
	//	return [
	//		{id:1,text:"1",pid:""},
	//		{id:11,text:"11",pid:"1"},
	//		{id:12,text:"12",pid:"1"},
	//		{id:2,text:"2",pid:""},
	//		{id:21,text:"21",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2",zfbz:1},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22,text:"22",pid:"2"},
	//		{id:22256,text:"22",pid:"2"}
	//	].filter(opt.zfbz=="0"?(item)=>item.zfbz!=1:(item)=>item.zfbz==1);
	//})
		return jclass(base,{
		name:"maintable",
		openWin:function(){
			this.log.info("打开弹出页");
		},
		bindEvent:function(){
			var _this = this;
		},
		
		initTree:function(){
			setTimeout(()=>this.controls.tree1.selectNode(22256),1000)
			this.controls.tree3.value([
				{id:1,text:1,pid:""},
				{id:11,text:11,pid:"1"},
				{id:12,text:12,pid:"1"},
				{id:2,text:2,pid:""},
				{id:21,text:21,pid:"2"},
				{id:22,text:"22",pid:"2"}
			])
		},
		
		onLoad:function(){
			this.bindEvent();
			this.initTree();
		}
	})
});