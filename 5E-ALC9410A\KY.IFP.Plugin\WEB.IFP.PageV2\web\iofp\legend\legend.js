define(function(){
    
    return {
        el:"#app",
        props:{
            value:{default:''},
            code:{default:''},
            states:{default:()=>[]},
            title:{default:''}
        },
        computed:{
            current(){
                return this.states.find(item=>item.code === this.value)
            },
            icon(){
                return this.current && this.current.icon || ""
            },
            color(){
                return this.current && this.current.color || "#000000"
            },
            text(){
                return this.current && this.current.text || ""
            },
            name(){
                return this.current && this.current.text || ""
            }
        }
    }
})