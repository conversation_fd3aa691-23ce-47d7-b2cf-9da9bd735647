﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>基础资料修改</title>

    <link rel="stylesheet" href="..\..\kyadmin\lib\kjlib\bootstrap-theme-kykj\dist\css\dicb.min.css" />
</head>
<body controller="baseinfoAdd.js" option="{platform:'vue',use:['ELEMENT']}">
    <div id="app">
        <div form="forms/toolbar" class="layout-h padding">
            <!--<a id="saveBtn" control="controls/button" option="{}" log>保存</a>-->
            <button class="btn btn-default btn-sm" @click="saveBtn">
                <span class="kjicon kjicon-baocun" aria-hidden="true" title="保存">保存</span>
            </button>
            <!--<a id="exitBtn" control="controls/button" option="{command:'close'}">退出</a>-->
        </div>
        <div class="layout-h padding" id="maindiv">
            <div class="form-header">基本信息</div>
            <div class="form-content">
                <table>
                    <tr>
                        <td>对象ID</td>
                        <td>
                            <input control="controls/textbox" v-model="data.Gid" disabled />
                        </td>
                        <td>对象类型</td>
                        <td colspan="1">
                            <select control="controls/select" v-model="data.Ywlx">
                                <option v-for="typeName in typeNames" v-bind:value="typeName.Ywlx">
                                    {{ typeName.Basename }}
                                </option>
                            </select>
                        </td>
                        <!--<td>上级对象ID</td>
                        <td>
                            <input control="controls/textbox" v-model="data.Pgid" disabled/>
                        </td>-->

                    </tr>
                    <tr>
                        <td>名称</td>
                        <td colspan="1">
                            <input control="controls/textbox" v-model="data.Bname" />
                        </td>
                        <td>简称</td>
                        <td colspan="1">
                            <input control="controls/textbox" v-model="data.Sname" />
                        </td>

                    </tr>
                    <tr>

                        <td>作废标志</td>
                        <td colspan="1">
                            <select control="controls/select" v-model="data.Zfbz">
                                <option v-for="option in options" v-bind:value="option.value">
                                    {{ option.text }}
                                </option>
                            </select>
                        </td>
                        <td>业务编码</td>
                        <td colspan="1">
                            <input control="controls/textbox" v-model="data.Ywbm" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>


</body>
<script src="/iofp/starter.js"></script>
</html>
