define(["sysUtil","jquery","Log"],function(sysUtil,$,Log){
    var defaultOption = {
        autoRenderControls:true,
		print:{
			fontname:"宋体",
			zoom:1,
			left:"4%",
			top:"4%",
			right:"4%",
			bottom:"4%",
			page:{
			    //1---纵向打印，固定纸张； 
			    //2---横向打印，固定纸张；  
			    //3---纵向打印，宽度固定，高度按打印内容的高度自适应(见样例18)；
			    //0---方向不定，由操作者自行选择或按打印机缺省设置。
				orient:0,
				width:0,//默认单位 0.1mm
				height:0,//默认单位 0.1mm
				name:""//默认A4 一般不用设置
			}
		}
    }

    var name = "ControllerBase"

    var SetContainerID = function(container,name,id){
        id = $(container).attr(id) || util.genid(name)
    }

    function ControllerBase(container,option) {
        this.$container = this.$container = $(container);
        this.id = this.$container.attr("id") || option.id;
        this.name = name;
        this.option = $.extend(true,{},
            defaultOption,
            sysUtil.getOption(this.$container),
            option
        )
    }

    

    ControllerBase.prototype.onLoad = function(){

    }

    ControllerBase.prototype.render = function (){
        this.$container.attr("rendered",true)
    }

    ControllerBase.prototype.getNoRenderControls = function (){
        this.$.container.find("[control]:not([rendered])").filter(function(){

        })
    }

    ControllerBase.prototype.log = new Log("")

    return ControllerBase;
})