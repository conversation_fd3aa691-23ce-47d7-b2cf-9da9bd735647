﻿using COM.IFP.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace API.ICS.BaseData
{
    public class Selector
    {
        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();

        public object Option(JsonElement json)
        {
            return lazy.Value.Option(json.GetValue<List<ORM.IFP.BaseData>>());
        }
    }
}
