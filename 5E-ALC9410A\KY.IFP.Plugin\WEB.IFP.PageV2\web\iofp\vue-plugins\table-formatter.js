/**
 * el-table-column 专用的格式化模块
 */
define([
    "./helper",
    "kyadmin/utils/formater",
    "ramda"
],function(helper,formater,R){
    // moment.js 参考 https://momentjs.com/

    const toDate = helper.dateFormat("YYYY-MM-DD");
    const toDateTime = helper.dateFormat("YYYY-MM-DD HH:mm:ss");

    const cache = {};
    function createDateFormat(format){
        if(!cache[format]){
            cache[format] = helper.dateFormat(format)
        }
        return cache[format];
    };


    /**
     * 日期格式
     * @param {*} row 
     * @param {*} column 
     * @param {*} cellValue 
     * @param {*} index 
     * @returns 
     * @example
     * ```html
     * <el-table-column :formatter="$TableFormatter.date"    
     * ```
     */
    function date(row, column, cellValue, index){
        return toDate(cellValue)
    }

    /**
     * 日期时间格式
     * @param {*} row 
     * @param {*} column 
     * @param {*} cellValue 
     * @param {*} index 
     * @returns 
     * @example
     * ```html
     * <el-table-column :formatter="$TableFormatter.datetime"    
     * ```
     */
    function datetime(row, column, cellValue, index) {
        return toDateTime(cellValue)
    }
    
    /**
     * 创建一个自定义格式的格式化函数
     * @param {*} format 
     * @returns 
     * @example
     * ```html
     * <el-table-column :formatter="$TableFormatter.dateFormat('YYYY年')"    
     * ```
     */
    function dateFormat(format){
        return function (row, column, cellValue, index){
            return createDateFormat(format)(cellValue)
        }
    }

    /**
     * 千分位
     * @param {number|string} value
     * @returns {string} 使用千分位,分割的字符串
     * @example
     * ```html
     * <el-table-column :formatter="$TableFormatter.thousands"    
     * ``` 
     */
    function thousands(row, column, cellValue, index){
        return formater.thousands(cellValue)
    }

    /**
     * 保留小数位
     * @example
     * ```html
     * <!--保留默认位小数-->
     * <el-table-column :formatter="$TableFormatter.float"></el-table-column>
     * <!--保留指定位小数-->
     * <el-table-column :formatter="$TableFormatter.float(2)"></el-table-column>
     * ``` 
     */
    function float(len){
        // 指定了参数
        if(arguments.length !== 4){
            return function (row, column, cellValue, index){
                return formater.parseFloat(cellValue,len)
            }
        }else{
            return formater.parseFloat(arguments[2])
        }
    }

    /**
     * 动态构造一个格式化函数
     * @example
     * ```html
     * <!--先保留两位小数，再转为千分位-->
     * <el-table-column :formatter="$TableFormatter.$pipe($TableFormatter.thousands(2),$TableFormatter.thousands)"</el-table-column>
     * ``` 
     */
    function $pipe(...fns){
        let fn = R.pipe(...fns)
        return function (row, column, cellValue, index){
            return fn(cellValue,row, column,index)
        }
    }

    return {
        date,datetime,dateFormat,thousands,$pipe,float
    };
})