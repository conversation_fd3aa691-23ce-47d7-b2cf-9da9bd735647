﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
//using System.Threading.Tasks;

namespace ORM.IFP.Log
{
    [SugarTable("IFP_SM_LOGGER")]
    public partial class IFP_SM_LOGGER
    {
        public IFP_SM_LOGGER() { }

        [SugarColumn(ColumnName = "ID", IsPrimaryKey = true, ColumnDataType = "nvarchar(32)")]
        public string ID { get; set; }

        /// <summary>
        /// 日志时间
        /// </summary>
        [SugarColumn(ColumnName = "TrapTime", IsNullable = true, ColumnDataType = "DateTime")]
        public DateTime TrapTime { get; set; }

        /// <summary>
        /// 日志内容
        /// </summary>
        [SugarColumn(ColumnName = "TrapText", IsNullable = true, ColumnDataType = "nvarchar(500)")]
        public string TrapText { get; set; }

        /// <summary>
        /// 日志详情
        /// </summary>
        [SugarColumn(ColumnName = "TrapInfo", IsNullable = true, ColumnDataType = "nvarchar(2000)")]
        public string TrapInfo { get; set; }

        /// <summary>
        /// 日志类型
        /// </summary>
        [SugarColumn(ColumnName = "TrapType", IsNullable = true, ColumnDataType = "nvarchar(50)")]
        public string TrapType { get; set; }

        /// <summary>
        /// 日志来源
        /// </summary>
        [SugarColumn(ColumnName = "TrapFrom", IsNullable = true, ColumnDataType = "nvarchar(10)")]
        public string TrapFrom { get; set; }

        /// <summary>
        /// 是否换行
        /// </summary>
        [SugarColumn(ColumnName = "TrapWrap", IsNullable = true, ColumnDataType = "bit")]
        public bool? TrapWrap { get; set; }

        /// <summary>
        /// 源文路径
        /// </summary>
        [SugarColumn(ColumnName = "CodePath", IsNullable = true, ColumnDataType = "nvarchar(500)")]
        public string CodePath { get; set; }

        /// <summary>
        /// 源文名称
        /// </summary>
        [SugarColumn(ColumnName = "CodeFile", IsNullable = true, ColumnDataType = "nvarchar(255)")]
        public string CodeFile { get; set; }

        /// <summary>
        /// 源类类型
        /// </summary>
        [SugarColumn(ColumnName = "CodeType", IsNullable = true, ColumnDataType = "nvarchar(36)")]
        public string CodeType { get; set; }

        /// <summary>
        /// 源码方法
        /// </summary>
        [SugarColumn(ColumnName = "CodeCall", IsNullable = true, ColumnDataType = "nvarchar(36)")]
        public string CodeCall { get; set; }

        /// <summary>
        /// 源码行号
        /// </summary>
        [SugarColumn(ColumnName = "CodeLine", IsNullable = true, ColumnDataType = "int")]
        public int? CodeLine { get; set; }

        /// <summary>
        /// 源码列号
        /// </summary>
        [SugarColumn(ColumnName = "CodePose", IsNullable = true, ColumnDataType = "int")]
        public int? CodePose { get; set; }
    }
}
