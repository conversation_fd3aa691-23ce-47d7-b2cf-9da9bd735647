define([],function(){
	
	var defaultOption = {
		name:"选择", //选择框标题
		idfield:"id", //id字段名
		textfield:"text", //文本字段名
		url:null, //ID转名称接口地址
		postData:null, //ID转名称接口参数
		dialogOption:{ // 弹出页配置
			url:null, //页面地址
			parameter:{ //弹出页参数
				
			}
		}
	}
	
	var configs = {
		default:$.extend(true,{},defaultOption,{
			name:"通用选择"
		}),
		
		mc:$.extend(true,{},defaultOption,{
			title:"煤场选择",
			idfield:'gid',
			textfield:'mdmc',
			/**
			 * @update getMcSelectList
			 */
			url: window._http_core_ + window._root_url_ +"/html/coalyard/mcgl/mcsyt/getMcSelectList.action",
			postData:null,
			dialogOption:{
				title:"煤场选择",
				full:false,
				url:window._http_core_ + window._root_url_ +"/common/mcselector/selector.html",
				parameter:{}
			},
			converter:function(data){
				return data&&data.datas||data;
			},
			formater:F.common.converter.mcidToText//F.common.converter.mcidToText
		}),
		
		mc1104:$.extend(true,{},defaultOption,{
			title:"煤场选择",
			idfield:'gid',
			textfield:'mdmc',
			/**
			 * @update
			 */
			url:window._http_core_ + window._root_url_ +"/html/coalyard/mcgl/mcsyt/getMcSelectList.action",
			postData:null,
			dialogOption:{
				title:"煤场选择",
				full:false,
				/**
				 * @update
				 */
				url:window._http_core_ + window._root_url_ +"/common/mcselector/1104/selector.html",
				parameter:{}
			},
			converter:function(data){
				return data&&data.datas||data;
			},
			formater:F.common.converter.mcidToText//F.common.converter.mcidToText
		}),
		
		ywdx:$.extend(true,{},defaultOption,{
			textfield:'text',
			idfileld:'gid'
			/**
			 * @update
			 */
			,url:window._http_core_ + window._root_url_ +'/html/util/ywlx/getYwlxCombox.action'
			,postData:{ywlx:'4001'}
			,dialogOption:{
				/**
				 * @update
				 */
				url:window._http_core_ + window._root_url_ +'/common/selector.html',
				parameter:{
					test:1
				}
			},
			getTextById:function(v){
				var _this = this;
				if(typeof v == "string"){
					v = v.split(",");
				}
				if(this.option.url){
					var texts = [];
					var opt = this.option.postData||{};
					opt[this.option.idfield] = v;
					
					var tempOjb = {};
					for(var i=0;i<v.length;i++){
						tempOjb[v[i]]=v[i];
					}
					
					var data = F["enum"].getList(this.option.url,opt);
					
					for(var i=0;i<data.length;i++){
						var id = data[i][_this.option.idfield];
						if(tempOjb[id]){
							tempOjb[id] = data[i][_this.option.textfield];
						}
					}
					var rev = [];v
					for(var a in tempOjb){
						rev.push(tempOjb[a]);
					}
					v = rev;
				}
				return v.join(",");
			}
		}),
		//电厂公式设置
		jsgs:$.extend(true,{},defaultOption,{
			dialogOption:{
				title:"公式设置",
				full:false,
				width:472,
				height:560,
				/**
				 * @update
				 */
				url:window._http_core_ + window._root_url_ +"/common/gsselector/selector.html",
				parameter:{}
			},
			converter:function(data){
				return data&&data.datas||data;
			}
		}),
		jsgs_setting:$.extend(true,{},defaultOption,{
			dialogOption:{
				title:"公式设置",
				full:false,
				width:472,
				height:610,
				/**
				 * @update
				 */
				url:window._http_core_ + window._root_url_ +"/common/gsselector/jsgs_setting.html",
				parameter:{}
			},
			converter:function(data){
				return data&&data.datas||data;
			}
		}),

		fzgs_setting:$.extend(true,{},defaultOption,{
			dialogOption:{
				title:"公式设置",
				full:false,
				width:472,
				height:610,
				/**
				 * @update
				 */
				url:window._http_core_ + window._root_url_ +"/common/gsselector/fzgs_setting.html",
				parameter:{}
			},
			converter:function(data){
				return data&&data.datas||data;
			}
		}),
		
		//煤矿计算公式设置
		jsgs_mk:$.extend(true,{},defaultOption,{
			dialogOption:{
				title:"公式设置",
				full:false,
				width:472,
				height:560,
				/**
				 * @update
				 */
				url:window._http_core_ + window._root_url_ +"/common/gsselector/selector_mk.html",
				parameter:{}
			},
			converter:function(data){
				return data&&data.datas||data;
			}
		}),
		
		kh:$.extend(true,{},defaultOption,{
			idfield:'kuanghao',
			textfield:'kuanghao',
			dialogOption:{
				title:"矿号选择",
				full:false,
				closable:false,
				width:700,
				height:560,
				/**
				 * @update
				 */
				url:window._http_core_ + window._root_url_ +"/common/khselector/selector.html",
				parameter:{}
			},
			converter:function(data){
				return data&&data.datas||data;
			}
		}),
		img:$.extend(true,{},defaultOption,{
			idfield:'img',
			textfield:'img',
			dialogOption:{
				title:"图片选择",
				full:false,
				closable:false,
				width:700,
				height:560,
				/**
				 * @update
				 */
				url:window._http_core_ + window._root_url_ +"/common/selimg/imgselector.html",
				parameter:{
					width:200,
					height:100
				}
			},
			converter:function(data){
				return data&&data.datas||data;
			}
		})
	}
	
	return {
		get:function(type,option){
			var config = type && configs[type] || configs["default"];
			return $.extend(true,{},config,option)
		}
	}
});