﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
namespace SqlSugar
{
    /// <summary>
    /// BaseResolve-Append
    /// </summary>
    public partial class BaseResolve
    {

        private BaseResolve()
        {

        }
        public BaseResolve(ExpressionParameter parameter)
        {
            //Field`1(null)
            //if ((!parameter.CurrentExpression.ToString().Contains("=="))
            //    && (!parameter.CurrentExpression.ToString().Contains("!="))
            //    && (!parameter.CurrentExpression.ToString().Contains(">"))
            //    && (!parameter.CurrentExpression.ToString().Contains(">="))
            //    && (!parameter.CurrentExpression.ToString().Contains("<"))
            //    && (!parameter.CurrentExpression.ToString().Contains("<="))
            //    && parameter.CurrentExpression.ToString().Contains("Field")
            //    && parameter.CurrentExpression.ToString().Contains("null"))
            //{
            //    //ParameterExpression param = Expression.Parameter(typeof(parameter), "x"); // 参数 x
            //    //ConstantExpression constant = Expression.Constant(null);
            //    this.Expression = parameter.CurrentExpression = null;
            //}
            //else
            this.Expression = parameter.CurrentExpression;
            this.Context = parameter.Context;
            this.BaseParameter = parameter;
        }

        public BaseResolve Start()
        {
            Expression expression;
            ExpressionParameter parameter;
            SetParameter(out expression, out parameter);

            if ((!parameter.CurrentExpression.ToString().Contains("=="))
                && (!parameter.CurrentExpression.ToString().Contains("!="))
                && (!parameter.CurrentExpression.ToString().Contains(">"))
                && (!parameter.CurrentExpression.ToString().Contains(">="))
                && (!parameter.CurrentExpression.ToString().Contains("<"))
                && (!parameter.CurrentExpression.ToString().Contains("<="))
                && parameter.CurrentExpression.ToString().Contains("Field")
                && parameter.CurrentExpression.ToString().Contains("null"))
            {
                //expression = Expression.Parameter(typeof(string), "null");
                parameter.CurrentExpression = expression = Expression.Constant(null); // 常量 10

            }

            if (expression is LambdaExpression)
            {
                return new LambdaExpressionResolve(parameter);
            }
            else if (expression is BinaryExpression && expression.NodeType == ExpressionType.Coalesce)
            {
                return new CoalesceResolveItems(parameter);
            }
            else if (expression is BinaryExpression)
            {
                return new BinaryExpressionResolve(parameter);
            }
            else if (expression is BlockExpression)
            {
                Check.ThrowNotSupportedException("BlockExpression");
            }
            else if (expression is ConditionalExpression)
            {
                return new ConditionalExpressionResolve(parameter);
            }
            else if (expression is MethodCallExpression)
            {
                return new MethodCallExpressionResolve(parameter);
            }
            else if (expression is MemberExpression && ((MemberExpression)expression).Expression == null)
            {
                return new MemberNoExpressionResolve(parameter);
            }
            else if (expression is MemberExpression && ((MemberExpression)expression).Expression.NodeType == ExpressionType.Constant)
            {
                return new MemberConstExpressionResolve(parameter);
            }
            else if (expression is MemberExpression && ((MemberExpression)expression).Expression.NodeType == ExpressionType.New)
            {
                return new MemberNewExpressionResolve(parameter);
            }
            else if (expression is ConstantExpression)
            {
                return new ConstantExpressionResolve(parameter);
            }
            else if (expression is MemberExpression)
            {
                return new MemberExpressionResolve(parameter);
            }
            else if (expression is UnaryExpression)
            {
                return new UnaryExpressionResolve(parameter);
            }
            else if (expression is MemberInitExpression)
            {
                return new MemberInitExpressionResolve(parameter);
            }
            else if (expression is NewExpression)
            {
                return new NewExpressionResolve(parameter);
            }
            else if (expression is NewArrayExpression)
            {
                return new NewArrayExpessionResolve(parameter);
            }
            else if (expression is ParameterExpression)
            {
                return new TypeParameterExpressionReolve(parameter);
            }
            else if (expression != null && expression.NodeType.IsIn(ExpressionType.NewArrayBounds))
            {
                Check.ThrowNotSupportedException("ExpressionType.NewArrayBounds");
            }
            return null;
        }
    }
}
