﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>条码打印设置</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item" id="app">
        <div class="toolbar">
            
        </div>
        <div class="flex flex-item padding">
            <el-form ref="form" :model="form" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="纸张宽度">
                            <ifp-input-number v-model="form.Pagewidth"></ifp-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="纸张高度">
                            <ifp-input-number v-model="form.Pageheight"></ifp-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="条码宽度">
                            <ifp-input-number v-model="form.Barwidth"></ifp-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="条码高度">
                            <ifp-input-number v-model="form.Barheight"></ifp-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="条码左边距">
                            <ifp-input-number v-model="form.Leftpadding"></ifp-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="条码顶边距">
                            <ifp-input-number v-model="form.Toppadding"></ifp-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="条码格式">
                            <el-select v-model="form.Formater" placeholder="请选择条码格式" style="width:130px">
                                <el-option label="条形码" value="128Auto"></el-option>
                                <el-option label="二维码" value="QRCode"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否预览打印">
                            <el-select v-model="form.Preview" placeholder="请选择是否预览打印" style="width:130px">
                                <el-option label="预览打印" :value="1"></el-option>
                                <el-option label="直接打印" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8" style="text-align:center">
                         <el-button icon="el-icon-refresh" @click="onReset">恢复默认</el-button>
                    </el-col>
                    <el-col :span="8" style="text-align:center">
                        <el-button icon="kjicon kjicon-baocun" @click="onSave">保存</el-button>
                    </el-col>
                    <el-col :span="8" style="text-align:center">
                        <el-button icon="kjicon kjicon-tuichu" @click="onExit">退出</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>