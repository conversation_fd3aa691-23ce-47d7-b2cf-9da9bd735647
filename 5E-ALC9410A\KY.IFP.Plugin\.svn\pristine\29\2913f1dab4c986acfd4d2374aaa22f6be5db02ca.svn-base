﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户ID卡详情</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button @click="save" log>保存</ifp-button>
            <ifp-button @click="readCard" log>读卡</ifp-button>
            <!--<div style="float:right;">
                <span class="dicb dicb-tuxing" style="font-size:16px;color:#0075D3"></span>
                <span style="font-size: 14px; font-weight: bold;">
                    历史报警
                </span>
            </div>-->
        </ifp-toolbar>

        <ifp-panel-table class="flex-item padding" label="卡号信息">
            <el-form ref="form" :model="userData" label-width="80px">
                <el-form-item label="用户名"
                              prop="UsiName">
                    <ifp-input v-model="userData.UsiName" :disabled="true"></ifp-input>
                </el-form-item>
                <el-form-item label="卡号"
                              prop="CardNo">
                    <ifp-input v-model="userData.CardNo"></ifp-input>
                </el-form-item>

            </el-form>

        </ifp-panel-table>

    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>