// ifp 专用插件
define(["zutil"],function(zutil){
    const defaultPageModel = {
        id:null,
        name:"",
        actives:[]
    }


    // 模拟数据
    function getPageModel(id){
        return new Promise(function(resolve,reject){
            setTimeout(function(){
                resolve(zutil.extend(true,{},defaultPageModel,{
                    id:"pageid1",
                    name:"矿点",
                    actives:[
                        {id:"pageid1activeid1",code:"view",name:"查看",enabled:true},
                        {id:"pageid1activeid2",code:"add",name:"新增",enabled:false},
                        {id:"pageid1activeid2",code:"update",name:"修改",enabled:true},
                        {id:"pageid1activeid3",code:"delete",name:"删除",enabled:true,show:false}
                    ]
                }))
            },Math.random()*1000+100)
        })
    }

    function createActivesMap(model){
        let map = {}
        if(model&&model.actives&&model.actives.length){
            model.actives.forEach(item=>{
                map[item.code]=item;
            })
        }
        return map
    }

    const mixin = {
        data(){
            return {
                $PageModel:{
                    id:"",
                    name:"",
                    actives:[],
                    activesMap:{}
                }
            }
        },
        computed:{
            MA(){
                return this.$data.$PageModel.activesMap
            },
            MP(){
                return this.$data.$PageModel
            }
        },
        created(){
            if(this.PageID){
                this.$setPageID(this.PageID)
            }
        },
        methods:{
            $setPageID(id){
                getPageModel(id).then(model=>{
                    
                    this.$set(this.$data,"$PageModel",{
                        ...model,
                        activesMap:createActivesMap(model)
                    })
                    this.$emit("PageModelUpdate")
                })
            },
            $ca(...args){
                return this.$checkActive(...args)
            },
            $checkActive(code){
                const active = this.$data.$PageModel.activesMap[code]
                return active && active.enabled !== false?true:false;
            }
        }
    }

    return mixin
})