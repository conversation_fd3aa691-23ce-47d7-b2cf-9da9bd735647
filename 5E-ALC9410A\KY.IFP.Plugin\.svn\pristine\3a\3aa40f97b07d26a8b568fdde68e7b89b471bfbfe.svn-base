﻿
using COM.IFP.Common;
//using COM.IFP.LinqDB;
using COM.IFP.Log;
using DAL.IFP.Job;
using ORM.IFP.DbModel;
using ORM.IFP.www.DbModel.SM;
using Quartz;
using Quartz.Spi;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace API.IFP.Job
{
    /// <summary>
    /// 任务信息控制器
    /// </summary>

    public class Job
    {
        private Lazy<DAL.IFP.Job.Job> _service = Entity.Create<DAL.IFP.Job.Job>();

        /// <summary>
        /// 获取任务列表.
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public object JobList(JsonElement json)
        {
            //var filter = json.GetValue<IList<IFP_SM_JOBINFO>>("filter");
            //var pagin = json.GetValue<PageModel>("paging");

            IList<IFP_SM_JOBINFO> filter = null;
            var tmp1 = new JsonElement();
            if (json.TryGetProperty("filter", out tmp1) == true)
                filter = json.GetValue<IList<IFP_SM_JOBINFO>>("filter");

            PageModel paging = null;
            var tmp = new JsonElement();
            if (json.TryGetProperty("paging", out tmp) == true)
                paging = json.GetValue<PageModel>("paging");

            return _service.Value.JobList(filter, paging);
        }

        /// <summary>
        /// 保存任务
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PFActionResult SaveOrUpdateJob(JsonElement json)
        {
            PFActionResult result = new PFActionResult();
            IFP_SM_JOBINFO obj = json.GetValue<IFP_SM_JOBINFO>("model");
            obj.paramlist = json.GetValue<List<IFP_SM_JOBPARAM>>("paramlist");
            try
            {
                result = _service.Value.SaveOrUpdateJob(obj);
            }
            catch (Exception e)
            {
                LoggerHelper.Debug("保存调度任务失败", e);
                result.success = false;
                result.msg = $"保存调度任务失败:{e}";
            }
            return result;
        }

        /// <summary>
        /// 删除任务.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public PFActionResult DelJob(JsonElement json)
        {
            PFActionResult result = new PFActionResult();
            string gid = json.GetValue<string>("Gid");
            try
            {
                result = _service.Value.DelJob(gid);
            }
            catch (Exception e)
            {
                LoggerHelper.Debug("删除调度任务失败", e);
                result.success = false;
                result.msg = $"删除调度任务失败:{e}";
            }
            return result;
        }

        /// <summary>
        /// 根据gid查询任务.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public object QueryJobByGid(JsonElement json)
        {
            string gid = json.GetValue<string>("Gid");
            return _service.Value.QueryJobByGid(gid);
        }

        /// <summary>
        /// 获取方法的参数列表.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public PFActionResult GetParamList(JsonElement json)
        {
            IFP_SM_JOBINFO job = json.GetValue<IFP_SM_JOBINFO>();
            PFActionResult result = new PFActionResult();
            try
            {
                List<IFP_SM_JOBPARAM> list = _service.Value.GetParamList(job);
                result.data = list;
                result.success = true;
            }
            catch (Exception e)
            {
                LoggerHelper.Debug("获取参数列表失败。", e);
                result.success = false;
                result.msg = $"获取方法参数列表失败:{e}";
            }
            return result;
        }

        /// <summary>
        /// 启动任务.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public PFActionResult StartJob(JsonElement json)
        {
            IFP_SM_JOBINFO job = json.GetValue<IFP_SM_JOBINFO>();
            PFActionResult result = new PFActionResult();
            try
            {
                _service.Value.StartJob(job);
                result.success = true;
                result.msg = "启动调度任务成功。";
            }
            catch (Exception e)
            {
                LoggerHelper.Debug("启动调度任务失败。", e);
                result.success = false;
                result.msg = $"启动调度任务失败:{e}";
            }
            return result;
        }

        /// <summary>
        /// 停止任务.
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PFActionResult StopJob(JsonElement json)
        {
            IFP_SM_JOBINFO job = json.GetValue<IFP_SM_JOBINFO>();
            PFActionResult result = new PFActionResult();
            try
            {
                _service.Value.StopJob(job);
                result.success = true;
                result.msg = "停止调度任务成功。";
            }
            catch (Exception e)
            {
                LoggerHelper.Debug("停止调度任务失败。", e);
                result.success = false;
                result.msg = $"停止调度任务失败:{e}";
            }
            return result;
        }

        /// <summary>
        /// 立即运行任务.
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PFActionResult RunJob(JsonElement json)
        {
            IFP_SM_JOBINFO job = json.GetValue<IFP_SM_JOBINFO>();
            PFActionResult result = new PFActionResult();
            try
            {
                IFP_SM_JOBINFO tmp = _service.Value.QueryJobByGid(job.Gid.Value);
                QuartzUtil.RunJob(tmp);
                result.success = true;
                result.msg = $"立即执行调度任务成功";
            }
            catch (Exception e)
            {
                LoggerHelper.Debug("立即执行调度任务失败。", e);
                result.success = false;
                result.msg = $"执行失败:{e.Message}";
            }
            return result;
        }

        /// <summary>
        /// 获取corn最近5次时间.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<string> GetTaskeFireTime(JsonElement json)
        {
            string CronExpressionString = json.GetValue<string>("CronExpression");
            int numTimes = json.GetValue<int>("numTimes");
            if (numTimes < 0)
            {
                throw new Exception("参数numTimes值大于等于0");
            }
            List<string> list = new List<string>();
            try
            {
                //时间表达式
                ITrigger trigger = TriggerBuilder.Create().WithCronSchedule(CronExpressionString).Build();
                IList<DateTimeOffset> dates = (IList<DateTimeOffset>)TriggerUtils.ComputeFireTimes(trigger as IOperableTrigger, null, numTimes);

                foreach (DateTimeOffset dtf in dates)
                {
                    list.Add(TimeZoneInfo.ConvertTimeFromUtc(dtf.DateTime, TimeZoneInfo.Local).ToString("yyyy-MM-dd HH:mm:ss"));
                }
            }
            catch (Exception e)
            {
                LoggerHelper.Debug("获取corn最近5次时间失败。", e);
            }

            return list;
        }

    }
}
