﻿<!DOCTYPE html>

<html lang="zh-CN">
<head>
    <title>煤种信息</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button v-if="!$btnRight.B1" icon="el-icon-plus" @click="onCreate" v-show="btnshow.add">新增</ifp-button>
            <ifp-button v-if="!$btnRight.B2" icon="el-icon-collection" v-bind:disabled="disable.saveDisabled" @click="onSave">保存</ifp-button>
            <ifp-button v-if="!$btnRight.B3" icon="el-icon-edit" v-bind:disabled="disable.updateDisabled" @click="onUpdate">修改</ifp-button>
        </ifp-toolbar>

        <div class="flex flex-row flex-item padding">
            <ifp-panel flex style="width:250px;">
                <template v-slot:header>
                    <div class="flex flex-row">
                        <div style="width:65%">
                            <ifp-input v-model="sreachMaterialName">
                                <el-button slot="append" @click="OnGetLocation" icon="el-icon-search"></el-button>
                            </ifp-input>
                        </div>
                        <div style="margin-left:1%;width:34%">
                            <el-select v-model="SearchState" placeholder="请选择" style="width:100%">
                                <el-option label="启用" value="0"></el-option>
                                <el-option label="停用" value="1"></el-option>
                            </el-select>
                        </div>
                    </div>
                </template>
                <div class="flex-row" style=" text-align: right;">
                    <el-link v-if="treeSortChange" @click="saveTreeSort" type="primary">调整完成</el-link>
                </div>
                <div class="flex-item" style="overflow: auto; background: white; margin-top: 2px">
                    <el-tree :data="treeData"
                             :props="treeProps"
                             node-key="Gid"
                             :default-expand-all="true"
                             :expand-on-click-node="false"
                             :filter-node-method="filterNode"
                             @node-click="OnMaterialClick"
                             @node-drop="handleDrop"
                             draggable
                             :allow-drag="allowDrag"
                             :allow-drop="allowDrop"
                             ref="tree">
                    </el-tree>
                </div>
            </ifp-panel>

            <div class="flex-item" style="margin-left: 1rem; background: white; overflow: auto;">
                <ifp-panel title="煤种基本信息" border class="margin-bottom">
                    <el-form ref="form" style="max-width:600px;padding: 1rem;" class="padding" label-position="right" :model="currentMaterial" label-width="80px" :disabled="disable.saveDisabled">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="煤种名称" prop="Bname" :rules="{required:true, message:'煤种名称不能为空', trigger: 'change'}">
                                    <ifp-input v-model="currentMaterial.Bname"></ifp-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="煤种简称">
                                    <ifp-input v-model="currentMaterial.Sname"></ifp-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="颜色分类">
                                    <template>
                                        <div class="flex flex-row">
                                            <ifp-input v-model="currentMaterial.Color" style="width:120px"></ifp-input>
                                            <el-color-picker v-model="currentMaterial.Color"></el-color-picker>
                                        </div>
                                    </template>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="状态">
                                    <el-radio v-model="currentMaterial.Zfbz" :label="0">启用</el-radio>
                                    <el-radio v-model="currentMaterial.Zfbz" :label="1">停用</el-radio>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="是否通氮">
                                    <el-radio v-model="currentMaterial.N2" :label="0">不通氮</el-radio>
                                    <el-radio v-model="currentMaterial.N2" :label="1">通氮</el-radio>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="业务编码">
                                    <ifp-input v-model="currentMaterial.Ywbm"></ifp-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item label="备注">
                                    <ifp-input type="textarea"
                                               :rows="2"
                                               placeholder="请输入内容"
                                               v-model="currentMaterial.Beizhu">
                                    </ifp-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="运输方式:">
                                    <!-- 还是要把arr转存string不如复选框直观 -->
                                    <!--<ifp-select-ywlx ref="ysfs1001" v-model="currentMaterial.YSFS1001"
                     :canedit="true"
                     multiple
                     clearable
                     :ywlx="1001"
                     :showzf="false"></ifp-select-ywlx>-->
                                    <el-checkbox-group v-model="currentMaterial.YSFS1001">
                                        <el-checkbox v-for="item in ysfsList"
                                                     :label="item.id"
                                                     :key="item.id">{{item.text}}</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </ifp-panel>

                <ifp-panel-table title="煤种指标区间" class="margin-bottom" title-right="单位：MJ/Kg,%,℃">

                    <template v-slot:header>
                        <ifp-button role="新增" type="default"
                                    @click="OnAddRow" :disabled="disable.saveDisabled">
                            新增行
                        </ifp-button>
                        <ifp-button role="删除" type="default"
                                    @click="onDeleteRow" :disabled="disable.saveDisabled">删除行</ifp-button>
                    </template>

                    <el-table :data="MaterialQJ"
                              style="width:100%;min-height:200px;"
                              row-key="Gid"
                              border
                              highlight-current-row
                              :row-class-name="tableRowClassName"
                              @row-click="tableSelect">
                        <el-table-column type="index"
                                         label="序号"
                                         width="50">
                        </el-table-column>
                        <el-table-column prop="Ljgx1003" label="关系">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.Ljgx1003" placeholder="请选择" :disabled="disable.saveDisabled">
                                    <el-option v-for="item in ywlx1003"
                                               :key="item.id"
                                               :label="item.text"
                                               :value="item.id">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="Value1" label="左区间">
                            <template slot-scope="scope">
                                <ifp-input v-model="scope.row.Value1" :disabled="disable.saveDisabled">
                                </ifp-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="Qjfh1" label="运算符">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.Qjfh1" placeholder="请选择" :disabled="disable.saveDisabled">
                                    <el-option label="＜" :value="10070003"></el-option>
                                    <el-option label="≤" :value="10070004"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="Hyzj1004" label="指标">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.Hyzj1004" placeholder="请选择" :disabled="disable.saveDisabled">
                                    <el-option v-for="item in ywlx1004"
                                               :key="item.id"
                                               :label="item.text"
                                               :value="item.id">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="Qjfh2" label="运算符2">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.Qjfh2" placeholder="请选择" :disabled="disable.saveDisabled">
                                    <el-option label="＜" :value="10070003"></el-option>
                                    <el-option label="≤" :value="10070004"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="Value2" label="右区间">
                            <template slot-scope="scope">
                                <ifp-input v-model="scope.row.Value2" :disabled="disable.saveDisabled">
                                </ifp-input>
                            </template>
                        </el-table-column>
                    </el-table>
                </ifp-panel-table>
            </div>
        </div>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>