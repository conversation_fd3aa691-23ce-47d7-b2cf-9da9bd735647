<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>矿点信息</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item" id="app">
        <div class="toolbar">
            <el-button icon="el-icon-refresh">刷新</el-button>
            <ifp-button-lxsz table="table1"></ifp-button-lxsz>
            <ifp-button-close></ifp-button-close>
        </div>
        <div class="flex flex-item padding">
            <div class="flex-item">
                <ifp-table
                    ref="table1"
                    :data="data"
                    style="width:100%;"
                    row-key="id"
                    height="100%"
                    border
                
                    highlight-current-row>
                    <el-table-column type="index"></el-table-column>
                    <el-table-column type="selection"></el-table-column>
                    <el-table-column prop="date" label="日期" width="100"></el-table-column>
                    <el-table-column label="配送信息">
                        <el-table-column label="地址" width="100">
                            <el-table-column prop="province" label="省份" width="100"></el-table-column>
                            <el-table-column prop="city" label="城市" width="100">
                                <template slot-scope="scope">
                                    <ifp-select-ywlx ywlx="1003" v-model="scope.row.city"></ifp-select-ywlx>
                                </template>
                            </el-table-column>
                            <el-table-column prop="zip" label="邮编"></el-table-column>
                            <el-table-column prop="address" label="地址" width="100"></el-table-column>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </div>
            <div>
                <el-pagination @size-change="updateTable"
                    @current-change="updateTable"
                    :current-page="viewer.paging.page"
                    :page-sizes="[10,15,20,50,100]"
                    :page-size="viewer.paging.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="viewer.paging.records">
                </el-pagination>
            </div>
        </div>
    </div>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>