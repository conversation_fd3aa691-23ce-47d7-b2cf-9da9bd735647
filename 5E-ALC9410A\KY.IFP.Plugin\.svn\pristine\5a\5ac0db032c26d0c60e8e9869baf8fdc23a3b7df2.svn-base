//F.controls.textbox
define(["./picker","jquery","daterangepicker"],function(base,$,Picker){
	
	var defaultOption = {
		width:230,
		onlyselect:false,
		field_start:null,
		field_end:null,
		weekfirstday:false //是否取系统参数配置（周报周期起始日：ZBZQQSR）的 firstDay
	}
	
	var dpDefaultOption = {
	    "showWeekNumbers": true,
	    "showISOWeekNumbers": true,
	    "weekDatePicker": true,
	    "useEmpty":true,
	    "locale": {
	        "weekLabel": "周"
	        ,"direction": "ltr"
	        ,"format": "YYYY-MM-DD"
	        ,"separator": " 至 "
	        ,"AM": "上午"
	        ,"PM": "下午"
	        ,"applyLabel": "确定"
	        ,"cancelLabel": "取消"
	        ,"clearLabel": "清空"
	        ,"fromLabel": "从"
	        ,"toLabel": "到"
	        ,"customRangeLabel": "自定义"
	        ,"daysOfWeek": ["日","一","二","三","四","五","六"]
	        ,"monthNames": ["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]
			,"firstDay":1
	    }
	    ,"autoUpdateInput": true
	    ,"showCustomRangeLabel": false
	    ,"alwaysShowCalendars": true
	}
	
	return F.class(base,{
		name:"control-daterangepicker",
		getFormData:function(data){
			if(this.option.field_start){
				data[this.option.field_start]=this.startDate();
			}else{
				data[this.id]=this.startDate();
			}
			if(this.option.field_start){
				data[this.option.field_end]=this.endDate();
			}
		},
		setFormData:function(data){
			if(this.option.field_start){
				this.startDate(data[this.option.field_start])
			}else{
				this.startDate(data[this.id])
			}
			if(this.option.field_start){
				this.endDate(data[this.option.field_end]);
			}
		},
		option:null,
		pickerOption:null,
		disable:function(state){
			this.base.apply(this,arguments);
			if(arguments.length==0 || state ){
				this.input.prop("disabled",true);
			}else{
				this.input.prop("disabled",false);
			}
		},
		focus:function(){
			this.input.click()
		},
		value:function(v){
			return this.startDate.apply(this,arguments);
		},
		setWeekDate:function(v){
			this.daterangepicker.setWeekDate(v);
		},
		startDate:function(v){
			if(arguments.length>0){
				if(this.option.weekDatePicker){
					this.setWeekDate(v);
				}else{
					this.daterangepicker.setStartDate(v); 
				}
			}else{
				if(this.daterangepicker.isEmpty){return null}
				return this.daterangepicker&&this.daterangepicker.startDate.format(this.option.locale.format)
			}
		},
		endDate:function(v){
			if(arguments.length>0){
				this.daterangepicker.setEndDate(v);
				if(this.option.weekDatePicker){
					this.daterangepicker.setWeekDate();
				}
			}else{
				if(this.daterangepicker.isEmpty){return null}
				return this.daterangepicker&&this.daterangepicker.endDate.format(this.option.locale.format)
			}
		},
		clear:function(){
			this.daterangepicker.clickClear();
			this.daterangepicker.updateElement();
			//this.startDate(null);
		},
		text:function(v){
			return this.input.val();
		},
		createDefaultOption:function(container,option){
			return $.extend(true,this.base.apply(this,arguments),defaultOption,dpDefaultOption);
		},
		createContainer:function(container){
			var rev = this.base.apply(this,arguments);
			this.pickerbox.addClass("date");
			this.addon.append($('<span class="glyphicon glyphicon-calendar"></span>'))
			return rev;
		},
		setOption:function(option){
			this.datetimepicker.options(option);
			//this.$container.datetimepicker(option);
		},
		minDate:function(v){
			this.datetimepicker.minDate(v);
		},
		maxDate:function(v){
			this.datetimepicker.maxDate(v);
		},
		getPickerOption:function(){
			var rev = {};
			var opt = $.extend(true,{},dpDefaultOption,this.option);
			for(var a in dpDefaultOption){
				if(opt[a]===null){continue;}
				rev[a] = opt[a];
			}
			rev.widgetParent = $("body");
			return rev;
		},
		destroy:function(){
			this.daterangepicker.remove();
		},
		render:function(){
			var _this = this;
			//取系统参数配置（周报周期起始日：ZBZQQSR）
			var sysFirstDay = F.common.ywUtil.getXtcsByKey("ZBZQQSR");
			if(sysFirstDay&&this.option.weekfirstday){
				this.option.locale.firstDay = F.common.parseFloat(sysFirstDay);
			};
			this.input = this.$container.find("input");
			this.input.prop("readonly",true);
			this.input.removeAttr("control");
			
			this.input.daterangepicker(this.option,function(start, end, label) { 
				_this.trigger("change",start.format(
					_this.option.locale.format), 
					end.format(_this.option.locale.format)
				);
	        });
			this.input.on("show.daterangepicker",function(){
				_this.trigger("beforeDialog");
				_this.trigger("show");
			})
			this.input.on("hide.daterangepicker",function(){
				_this.trigger("hide");
				_this.trigger("afterDialog");
			})
			
			
			
			
			this.daterangepicker = this.input.data("daterangepicker");
			this.option.value&&this.value(this.option.value);
			if(this.option.width){
				this.$container.width(this.option.width);
			}
			if(this.option.disabled){
				this.disable();
			}
			this.addon.click(function(){
				if(_this.input.prop("disabled")){return;}
				_this.input.click();
			});
			this.afterRender();
			this.base.apply(this,arguments);
		},
		afterRender:function(){
			if(this.option.required){
				this.$container.find("input.form-control").attr("placeholder","必填");
			}else{
				this.$container.find("input.form-control").attr("placeholder","");
			}
		}
	});
});