﻿using System;
using System.Buffers;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;

namespace KY.IFP.Runtime
{
    /// <summary>
    /// Json对象创建器
    /// </summary>
    public static class JsonFactory
    {
        /// <summary>
        /// 创建JsonElement对象
        /// </summary>
        /// <returns>JsonElement对象</returns>
        public static JsonElement CreateElement()
        {
            return new JsonElement();
        }

        /// <summary>
        /// 创建JsonElement对象
        /// </summary>
        /// <param name="content">源对象</param>
        /// <returns>JsonElement对象</returns>
        public static JsonElement CreateElement(object content)
        {
            return JsonDocument.Parse(JsonSerializer.SerializeToUtf8Bytes(content)).RootElement;
        }

        #region JsonElement扩展
        /// <summary>
        /// 获取指定类型的值
        /// </summary>
        /// <param name="json">扩展对象</param>
        /// <param name="type">指定类型</param>
        /// <returns>输出数值</returns>
        public static object GetValue(this JsonElement json, Type type)
        {
            object turn()
            {
                if (json.ValueKind == JsonValueKind.Object || (json.ValueKind == JsonValueKind.Array && type.IsAssignableFrom(typeof(IEnumerable))))
                {
                    var data = type.IsValueType ? Activator.CreateInstance(type) : null;
                    var buff = new ArrayBufferWriter<byte>();
                    using (var utf8 = new Utf8JsonWriter(buff))
                    {
                        json.WriteTo(utf8);
                        data = JsonSerializer.Deserialize(buff.WrittenSpan, type);
                    }
                    return data;
                }
                else
                {
                    throw new Exception("无法将 " + json.GetRawText() + " 转换为目标类型 " + type + " 。");
                }
            }

            if (type == typeof(JsonElement))
            {
                return json;
            }
            else if (type == typeof(bool))
            {
                return (json.ValueKind == JsonValueKind.True || json.ValueKind == JsonValueKind.False) ? json.GetBoolean() : turn();
            }
            else if (type == typeof(byte))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetByte() : turn();
            }
            else if (type == typeof(byte[]))
            {
                return (json.ValueKind == JsonValueKind.String) ? json.GetBytesFromBase64() : turn();
            }
            else if (type == typeof(DateTime))
            {
                return (json.ValueKind == JsonValueKind.String) ? json.GetDateTime() : turn();
            }
            else if (type == typeof(DateTimeOffset))
            {
                return (json.ValueKind == JsonValueKind.String) ? json.GetDateTimeOffset() : turn();
            }
            else if (type == typeof(decimal))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetDecimal() : turn();
            }
            else if (type == typeof(double))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetDouble() : turn();
            }
            else if (type == typeof(Guid))
            {
                return (json.ValueKind == JsonValueKind.String) ? json.GetGuid() : turn();
            }
            else if (type == typeof(short))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetInt16() : turn();
            }
            else if (type == typeof(int))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetInt32() : turn();
            }
            else if (type == typeof(long))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetInt64() : turn();
            }
            else if (type == typeof(sbyte))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetSByte() : turn();
            }
            else if (type == typeof(float))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetSingle() : turn();
            }
            else if (type == typeof(string))
            {
                return (json.ValueKind == JsonValueKind.String) ? json.GetString() : turn();
            }
            else if (type == typeof(ushort))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetUInt16() : turn();
            }
            else if (type == typeof(uint))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetUInt32() : turn();
            }
            else if (type == typeof(ulong))
            {
                return (json.ValueKind == JsonValueKind.Number) ? json.GetUInt64() : turn();
            }
            else
            {
                return turn();
            }
        }
        /// <summary>
        /// 尝试获取指定类型的值
        /// </summary>
        /// <param name="json">扩展对象</param>
        /// <param name="type">指定类型</param>
        /// <param name="data">输出数值</param>
        /// <returns>True获取成功，False获取失败</returns>
        public static bool GetValue(this JsonElement json, Type type, out object data)
        {
            data = default;
            try
            {
                data = json.GetValue(type);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取指定泛型的值
        /// </summary>
        /// <typeparam name="T">指定泛型</typeparam>
        /// <param name="json">扩展对象</param>
        /// <returns>输出数值</returns>
        public static T GetValue<T>(this JsonElement json)
        {
            return (T)json.GetValue(typeof(T));
        }
        /// <summary>
        /// 尝试获取指定泛型的值
        /// </summary>
        /// <typeparam name="T">指定泛型</typeparam>
        /// <param name="json">扩展对象</param>
        /// <param name="data">输出数值</param>
        /// <returns>True获取成功，False获取失败</returns>
        public static bool GetValue<T>(this JsonElement json, out T data)
        {
            data = default;
            try
            {
                data = (T)json.GetValue(typeof(T));
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取指定属性的指定类型的值
        /// </summary>
        /// <param name="json">扩展对象</param>
        /// <param name="name">属性名称</param>
        /// <param name="type">指定类型</param>
        /// <returns>输出数值</returns>
        public static object GetValue(this JsonElement json, string name, Type type)
        {
            var data = type.IsValueType ? Activator.CreateInstance(type) : null;
            if (json.ValueKind == JsonValueKind.Object)
            {
                var item = json.EnumerateObject().FirstOrDefault(x => string.Equals(x.Name, name, StringComparison.OrdinalIgnoreCase));
                if (item.Value.ValueKind != JsonValueKind.Undefined)
                {
                    return item.Value.GetValue(type);
                }
                else
                {
                    throw new Exception("无法找到对应的属性" + name + "或属性值未定义。");
                }
            }
            else
            {
                throw new Exception("无法获取非对象类型的属性值。");
            }
        }
        /// <summary>
        /// 尝试获取指定属性的指定类型的值
        /// </summary>
        /// <param name="json">扩展对象</param>
        /// <param name="name">属性名称</param>
        /// <param name="type">执行类型</param>
        /// <param name="data">输出数值</param>
        /// <returns>True获取成功，False获取失败</returns>
        public static bool GetValue(this JsonElement json, string name, Type type, out object data)
        {
            data = default;
            try
            {
                data = json.GetValue(name, type);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取执行属性的指定泛型的值
        /// </summary>
        /// <typeparam name="T">输出泛型</typeparam>
        /// <param name="json">扩展对象</param>
        /// <param name="name">属性名称</param>
        /// <returns>输出数值</returns>
        public static T GetValue<T>(this JsonElement json, string name)
        {
            return (T)json.GetValue(name, typeof(T));
        }
        /// <summary>
        /// 尝试获取执行属性的指定泛型的值
        /// </summary>
        /// <typeparam name="T">输出泛型</typeparam>
        /// <param name="json">扩展对象</param>
        /// <param name="name">属性名称</param>
        /// <param name="data">输出数值</param>
        /// <returns>True获取成功，False获取失败</returns>
        public static bool GetValue<T>(this JsonElement json, string name, out T data)
        {
            data = default;
            try
            {
                data = (T)json.GetValue(name, typeof(T));
                return true;
            }
            catch
            {
                return false;
            }
        }


        /// <summary>
        /// 根据表达式读取json数据
        /// </summary>
        /// <param name="json">Json对象</param>
        /// <param name="func">表达式，func必须为返回New匿名对象的表达式，对象成员的引用即为对应json数据的存储变量</param>
        public static void GetValue(this JsonElement json, Expression<Func<object>> func)
        {
            json.GetValue(func.Body as NewExpression);
        }
        private static void GetValue(this JsonElement json, NewExpression func)
        {
            if (json.ValueKind == JsonValueKind.Object && func != null)
            {
                var dict = json.EnumerateObject().ToDictionary(x => x.Name, x => x.Value, StringComparer.OrdinalIgnoreCase);
                for (int i = 0; i < func.Arguments.Count; i++)
                {
                    if (dict.TryGetValue(func.Members[i].Name, out JsonElement data))
                    {
                        if (func.Arguments[i] is NewExpression)
                        {
                            GetValue(data, func.Arguments[i] as NewExpression);
                        }
                        else if (func.Arguments[i] is MemberExpression)
                        {
                            var linq = func.Arguments[i] as MemberExpression;
                            var from = default(object);
                            if (linq.Expression != null)
                            {
                                //非静态变量
                                from = Expression.Lambda(linq.Expression).Compile().DynamicInvoke();
                            }
                            var info = linq.Member;
                            if (info != null)
                            {
                                try
                                {
                                    if (info is PropertyInfo)
                                    {
                                        var item = (PropertyInfo)info;
                                        item.SetValue(from, data.GetValue(item.PropertyType), null);
                                    }
                                    else if (info is FieldInfo)
                                    {
                                        var item = (FieldInfo)info;
                                        item.SetValue(from, data.GetValue(item.FieldType));
                                    }
                                }
                                catch
                                {
                                    //单个键值报错可以不影响其它
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 设置属性值，注意：需要将原对象赋值为修改后新对象
        /// </summary>
        /// <param name="json">扩展对象</param>
        /// <param name="name">属性名称，不区分大小写</param>
        /// <param name="data">属性数值</param>
        /// <returns>修改后新对象</returns>
        public static JsonElement SetValue(this JsonElement json, string name, object data)
        {
            if (json.ValueKind == JsonValueKind.Object)
            {
                var buff = new ArrayBufferWriter<byte>();
                using (var utf8 = new Utf8JsonWriter(buff))
                {
                    utf8.WriteStartObject();
                    foreach (var item in json.EnumerateObject())
                    {
                        if (string.Equals(item.Name, name, StringComparison.OrdinalIgnoreCase)) continue;
                        item.WriteTo(utf8);
                    }
                    utf8.WritePropertyName(name);
                    JsonDocument.Parse(JsonSerializer.SerializeToUtf8Bytes(data)).RootElement.WriteTo(utf8);
                    utf8.WriteEndObject();
                }
                json = JsonDocument.Parse(buff.WrittenMemory).RootElement;
            }
            return json;
        }
        /// <summary>
        /// 设置多个属性，注意：需要将原对象赋值为修改后新对象
        /// </summary>
        /// <param name="json">扩展对象</param>
        /// <param name="data">属性字典，键值不区分大小写</param>
        /// <returns>修改后新对象</returns>
        public static JsonElement SetValue(this JsonElement json, IDictionary<string, object> data)
        {
            if (json.ValueKind == JsonValueKind.Object)
            {
                var buff = new ArrayBufferWriter<byte>();
                using (var utf8 = new Utf8JsonWriter(buff))
                {
                    utf8.WriteStartObject();
                    foreach (var item in json.EnumerateObject())
                    {
                        if (data.ContainsKey(item.Name)) continue;
                        item.WriteTo(utf8);
                    }
                    foreach (var pair in data)
                    {
                        utf8.WritePropertyName(pair.Key);
                        JsonDocument.Parse(JsonSerializer.SerializeToUtf8Bytes(pair.Value)).RootElement.WriteTo(utf8);
                    }
                    utf8.WriteEndObject();
                }
                json = JsonDocument.Parse(buff.WrittenMemory).RootElement;
            }
            return json;
        }
        #endregion
    }
}