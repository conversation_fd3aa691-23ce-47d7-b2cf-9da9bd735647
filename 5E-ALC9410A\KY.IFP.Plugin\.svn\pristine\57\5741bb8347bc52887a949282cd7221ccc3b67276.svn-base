﻿using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP.www.DbModel.SM
{
    //未用到

    /// <summary>
    /// 组织机构  
    /// </summary>
    [SugarTable("IFP_SM_ORG")]
    public class IFP_SM_ORG
    {
        [SugarColumn(IsPrimaryKey = true, ColumnName = "GID", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(36)")]
        public Field<string> Gid { get; set; }

        [SugarColumn(ColumnName = "PGID", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(36)")]
        public Field<string> Pgid { get; set; }

        [SugarColumn(ColumnName = "BNAME", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(200)")]
        public Field<string> Bname { get; set; }

        [SugarColumn(ColumnName = "SNAME", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(200)")]
        public Field<string> Sname { get; set; }

        [SugarColumn(ColumnName = "DWBM", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(100)")]
        public Field<string> Dwbm { get; set; }

        [SugarColumn(ColumnName = "DWXZ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(100)")]
        public Field<string> Dwxz { get; set; }

        [SugarColumn(ColumnName = "JZGC", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(100)")]
        public Field<string> Jzgc { get; set; }

        [SugarColumn(ColumnName = "BEIZHU", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(200)")]
        public Field<string> Beizhu { get; set; }

        [SugarColumn(ColumnName = "COMPID", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(100)")]
        public Field<string> Compid { get; set; }

        [SugarColumn(ColumnName = "ZFBZ", SqlParameterDbType = typeof(FieldTypeConverter))]
        public Field<int> Zfbz { get; set; }
    }

    //[Table(Name = "IFP_SM_ORG")]
    //public class IFP_SM_ORG
    //{
    //    [Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.NVarChar, DbType = "nvarchar(36)")]
    //    public Field<string> Gid { get; set; }

    //    [Column(Name = "PGID", DataType = DataType.NVarChar, DbType = "nvarchar(36)")]
    //    public Field<string> Pgid { get; set; }

    //    [Column(Name = "BNAME", DataType = DataType.NVarChar, DbType = "nvarchar(200)")]
    //    public Field<string> Bname { get; set; }

    //    [Column(Name = "SNAME", DataType = DataType.NVarChar, DbType = "nvarchar(200)")]
    //    public Field<string> Sname { get; set; }

    //    [Column(Name = "DWBM", DataType = DataType.NVarChar, DbType = "nvarchar(100)")]
    //    public Field<string> Dwbm { get; set; }

    //    [Column(Name = "DWXZ", DataType = DataType.NVarChar, DbType = "nvarchar(100)")]
    //    public Field<string> Dwxz { get; set; }

    //    [Column(Name = "JZGC", DataType = DataType.NVarChar, DbType = "nvarchar(100)")]
    //    public Field<string> Jzgc { get; set; }

    //    [Column(Name = "BEIZHU", DataType = DataType.NVarChar, DbType = "nvarchar(200)")]
    //    public Field<string> Beizhu { get; set; }

    //    [Column(Name = "COMPID", DataType = DataType.NVarChar, DbType = "nvarchar(100)")]
    //    public Field<string> Compid { get; set; }

    //    [Column(Name = "ZFBZ", DataType = DataType.Int32, DbType = "int")]
    //    public Field<int> Zfbz { get; set; }
    //}
}
