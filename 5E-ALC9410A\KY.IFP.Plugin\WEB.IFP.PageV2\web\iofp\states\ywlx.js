define(["vuex","vue","iofp/vue-plugins/ifp-api","kyadmin/utils/util"],function(Vuex,Vue,api,util){
    Vue.use(Vuex);

    function getName(opt){
        if(typeof opt=="number" || typeof opt=="string"){
            return opt
        }else if( typeof opt==="object"){
            return opt.name || opt.url || opt.ywlx
        }else if(typeof opt=="function"){
            return opt.name || opt.toString()
        }else{
            throw "获取name失败"
        }
    }

    /**
     * 创建配置文件
     * @param {number|string|object} opt 4002 {ywlx:4002} {():Promise<any>}
     * @returns {name:string,option:Object|Function}
     * @example
     * 
     * 4002
     * 
     * {ywlx:4002}
     * 
     * {name?:4002,ywlx:4002,option?:{post?:{}}}
     * {name?:key,ywlx:url,option?:{post?:{}}}
     * 
     */
     function createItem(opt){
        let name = getName(opt)
        ,option,type;
        if(typeof opt=="number" || typeof opt=="string"){
            option = {ywlx:opt};
        } else if(typeof opt=="object"){
            option=opt;
        } else if(typeof opt=="function"){
            if(!opt.name){
                throw "$option.data 中 function 必须是命名函数"
            }
            option = {fn:opt}
        } else if(typeof opt==="object" && opt.name && opt.option && (opt.option.ywlx || opt.option.fn || opt.option.url)){
            return opt;
        } else {
            throw "$store 参数错误"
            //return null;
        }
        return {name,option}
    }
    
    const moduleYWLX = {
        namespaced: true,
        state: {
            // yw
            items:[]
        },
        mutations: {
            update(state,{name,data,clear=true}){
              const item = state.items.find(item=>item.name===name);
              if(clear){
                item.data.splice(0,item.data.length,...data)
              }else{
                item.data.splice(item.data.length-1,0,...data)
              }
            },
            addItem (state,{name,option}) {
              if(state.items.find(item=>item.name===name)){
                // console.log(`$store addItem error:has ${name}`);
                return;
              }
              state.items.push({
                name,
                time:Date.now(),
                option:option,
                data:[]
              })
            },
            updateTime(state,name){
                const item = state.items.find(item=>item.name===name);
                item.time = Date.now();
            }
        },
        getters: {
            map(state){
                return Object.fromEntries(state.items.map(item=>[
                    isNaN(Number(item.name))?item.name:('$'+item.name),item.data
                ]))
            },
            // ...
            getItem: (state) => (name) => {
                return state.items.find(item=>item.name===name)
            }
        },
        // 行为
        actions:{
            refresh:({commit,getters,dispatch},opt)=>{
                const item = getters.getItem(opt.name);
                let pro;
                if(item){
                    if(item.option.ywlx){
                        pro = api.ywlx(item.option.ywlx,item.option.post,opt.option.cache)
                    }else if(item.option.url){
                        pro = api.post(item.option.url,item.option.post,opt.option.cache)
                    }else if(item.option.fn){
                        pro = Promise.resolve(item.fn(item.option))
                    }else{
                        pro = Promise.reject("$store actions update 错误")
                        // 不可能运行到这个位置
                    }
                    pro.then(d=>{
                        return commit("update",{name:item.name,data:d})
                    })
                }else{
                    console.error("xxxxxx");
                }
                return pro
            },
            update({commit,getters,dispatch},option){
                if(!option){return Promise.resolve();}
                
                const name = getName(option);
                let dataitem = getters.getItem(name);
                if(!dataitem){
                    dataitem = createItem(option)
                    commit('addItem',dataitem)
                    dataitem = getters.getItem(name);
                }else{
                    if((Date.now()-dataitem.time)<2000){
                        return Promise.resolve();
                    }else{
                        commit('updateTime',name)
                    }
                }
                dataitem = getters.getItem(name);
                console.log("请求："+`[${Date.now()-dataitem.time}]` + atob(name))
                return dispatch('refresh',dataitem)
            }
        }
    }

    return moduleYWLX
})