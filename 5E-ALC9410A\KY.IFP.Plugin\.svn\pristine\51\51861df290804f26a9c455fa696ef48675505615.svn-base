; define([
    "controllers/base",
    "jquery",
    "jclass", "iofp/common",
    "iofp/components/iofpsocket",
    "controls/number",
    "controls/select2",
    "controls/tabs"
],
    function (base, $, jclass, common, iofpsocket) {
        return jclass(base, {
            name: "cssz",
            //判断websocket报文命令执行成功
            done: false,
            csList: null,
            csDict: new Array(),   //字典 通过参数gid对应一个数组
            //管理分组
            xtcsGroupObj: function () {
                var _this = this;
                var opt = {
                    url: "/pages/cssz/csfz.html",
                    full: false,
                    width: 600,
                    height: 500,
                    title: "管理参数分组",
                    //回调，退出页面时刷新
                    onhidden: function () {
                        //location.reload(true)

                        setTimeout(() => {
                            location.reload(true)
                        }, 1000)

                    },
                    parameter: {
                    }
                };
                var win = $.showIframe(opt);
            },
            //管理组中的参数 参数选择
            csxzObj: function () {
                var _this = this;
                //var fenzuGid = _this.controls.tabPage1.select().id; //暂时用不了
                var fenZuGid = $("li.active").children("a").attr("href").substr(1);
                var fenZuName = $("li.active").children("a")[0].innerHTML;
                var opt = {
                    url: "/pages/cssz/csxz.html",
                    full: true,
                    title: "参数选择",
                    parameter: {
                        FenZuGid: fenZuGid,
                        FenZuName: fenZuName
                    },
                    //回调，退出页面时刷新
                    onhidden: function () {
                        location.reload(true);
                    },
                };
                var win = $.showIframe(opt);
            },
            bindEvent: function () {
                var _this = this;

                //todo
                //this.controls.saveBtn.disable(false);
                //this.controls.syncBtn.disable(false);
                //this.controls.xtcsGroupBtn.disable(false);

                _this.bind("xtcsGroupBtn", "click", _this.xtcsGroupObj, _this);

                for (let i = 0; i < $(".csxz").length; ++i)
                    _this.bind($(".csxz")[i], "click", _this.csxzObj, _this);
                //保存
                _this.controls["saveBtn"].bind("click", function () {
                    _this.saveFun();
                });
                _this.controls["syncBtn"].bind("click", function () {
                    _this.done = false;
                    _this.sendCommand('UPDATE_PARAM_TO_DB');
                });

                iofpsocket.onCmdMsg("RControl", data => {
                    var result = JSON.parse(data.C);
                    //本页发送2个websocket指令收到的回复如果出现前后颠倒
                    //就无法判断属于哪个的结果
                    if (result.CMD == "UPDATE_PARAM_TO_DB") {
                        if (result.Result == 1) {
                            _this.getDataSource();
                            _this.refreshData();
                            $.bootoast.success("获取成功");
                        }
                        else {
                            $.bootoast.danger("获取失败");
                            $.bootoast.danger(result.Msg);
                        }
                    }
                    if (result.CMD == "UPDATE_PARAM_TO_PLC") {
                        if (result.Result == 1) {
                            //location.reload(true);
                            $.bootoast.success("保存成功。");
                        }
                        else {
                            $.bootoast.danger("保存失败。");
                            $.bootoast.danger(result.Msg);
                        }
                    }
                    console.log(JSON.stringify(data), "接收");
                });
                iofpsocket.onCmdMsg("Open", data => {
                    var result = JSON.parse(data.C);
                    //todo
                    if (result.Auth == 1) {
                        this.controls.saveBtn.disable(false);
                        this.controls.syncBtn.disable(false);
                        this.controls.xtcsGroupBtn.disable(false);
                    }
                    else {
                        this.controls.saveBtn.disable(true);
                        this.controls.syncBtn.disable(true);
                        this.controls.xtcsGroupBtn.disable(true);
                    }
                });
            },
            //发送IOFP命令
            sendCommand: function (CMD) {
                //var  CMD = 'UPDATEPARMFROMDB';
                Parameter = {};
                var content = common.createCommandArgs({ CMD: CMD, Parameter: Parameter });

                console.log("SControl", content)
                iofpsocket.sendCommand("SControl", content, function (data) {
                    var result = JSON.parse(data.C);
                    if (result.Result == "0") {
                        $.bootoast.danger(result.Msg);
                    }
                    $.bootoast.success(result.Msg);
                });
            },
            saveFun: function () {
                var _this = this;
                var list = [];  //需要保存的参数列表
                var fenZuGid = $("li.active").children("a").attr("href").substr(1);  //拿到当前分组的Gid
                for (var i = 0; i < _this.csList.length; i++) {
                    //_this.csList[i].CsValue = _this.controls["cs" + _this.csList[i].Gid].value();
                    var cs = _this.csList[i];
                    if (cs.FenZuGid == fenZuGid) {
                        cs.CsValue = _this.controls[cs.CSGid + "_" + i].value();
                        list.push(cs);
                    }
                }
                F.util.showWait();
                F.ajax({
                    url: "/API/IFP/BaseInfo/Xtcs/UpdateXtcsByCode",  //通过编码改的
                    data: JSON.stringify(list),
                    success: function (resp) {
                        F.util.hideWait();
                        if (resp && resp["success"]) {
                            _this.sendCommand('UPDATE_PARAM_TO_PLC');
                            $.bootoast.success("保存成功。");
                            //location.reload(true);
                        } else {
                            $.bootoast.danger("保存失败。");
                        }
                    }
                })
            },
            //更新绑定数据
            refreshData: function () {
                var _this = this;
                for (var i = 0; i < _this.csList.length; i++) {
                    if (_this.csList[i].Gid == undefined)  //分组无参数时跳过填充项的赋值
                        continue;
                    var cs = _this.csList[i];

                    if (_this.csDict[cs.CSGid] == undefined) //不存在这个参数
                        continue;
                    for (let j = 0; j < _this.csDict[cs.CSGid].length; ++j)
                        _this.controls[_this.csDict[cs.CSGid][j]].value(cs.CsValue);
                }
            },
            //获得数据源
            getDataSource: function () {
                var _this = this;
                return F.ajax({
                    url: "/API/IFP/BaseInfo/XtcsFenZu/FenZuListAll",
                    data: JSON.stringify({ flag: true }),
                    //不用异步
                    async: false,
                    success: function (resp) {
                        _this.csList = resp;
                    }
                });
            },
            //添加html元素
            addTags: function (source) {
                var resp = source;
                var _this = this;
                if (resp.length == 0) //没有任何分组 有分组的话 就算没参数后台也会补一个空对象过来
                    return;
                let fenzuGid = _this.csList[0].FenZuGid;
                var html = "<div id=\"tabPage1\" control=\"controls/tabs\" option=\"{height:'100%',width:'100%'}\">";
                html = html + "<div id=\"" + resp[0].FenZuGid + "\" option=\"{title:'" + resp[0].FenZuName + "',id:'" + resp[0].FenZuGid + "',active:true}\">";
                //html = html + "<div option=\"{title:'" + resp[0].FenZuName + "',id:'" + resp[0].FenZuGid + "',active:true}\">";
                html = html + "<div class=\"flex-item text-right\"> <a control=\"controls/button\" option=\"{icon:'glyphicons-plus'}\" class ='csxz'>选择</a> </div>"
                for (var i = 0; i < resp.length; i++) {
                    if (fenzuGid != resp[i].FenZuGid)  //说明要切下一个页签了
                    {
                        html = html + "</div>";
                        html = html + "<div option=\"{title:'" + resp[i].FenZuName + "',id:'" + resp[i].FenZuGid + "'}\">";
                        html = html + "<div class=\"flex-item text-right\"> <a control=\"controls/button\" option=\"{icon:'glyphicons-plus'}\" class ='csxz'>选择</a></div>"
                        fenzuGid = resp[i].FenZuGid
                    }
                    if (resp[i].Gid == undefined)
                        continue;
                    var input = "";
                    var idstr = resp[i].CSGid + "_" + i;  //由分组id和数组序号构成唯一标识
                    //number控件
                    if (resp[i].LeiXing == 1) {
                        input = '<input id="' + idstr + '" control="controls/number" option="{width:\'100%\',required:true,showName:\'' + resp[i].CsName + '\'}" />';
                    }
                    //textbox控件
                    if (resp[i].LeiXing == 2) {
                        input = '<input id="' + idstr + '" control="controls/textbox" option="{width:\'100%\',required:true,showName:\'' + resp[i].CsName + '\'}" />';
                    }
                    //select2控件
                    if (resp[i].LeiXing == 3) {
                        input = '<input id="' + idstr + '" control="controls/select2" option="{width:\'100%\',data:' + resp[i].Data + ',required:true,showName:\'' + resp[i].CsName + '\'}" />';
                    }
                    if (_this.csDict[resp[i].CSGid] == undefined) {  //加到字典里面 为了后序的赋值
                        _this.csDict[resp[i].CSGid] = [idstr];
                    }
                    else {
                        _this.csDict[resp[i].CSGid].push(idstr);
                    }
                    html = html + ""
                        + '<div style="width:400px; height: 40px; border: 0px solid #ccc; float: left;padding-top:5px">'
                        + '	 <div style="text-align: right; line-height:35px; width:280px; height: 40px; border: 0px solid #ccc; float: left">'
                        + resp[i].CsName + '：'
                        + '	 </div>'
                        + '	 <div style="text-align: left; width:90px; height: 40px; border: 0px solid #ccc; float: left">'
                        + input
                        + '	 </div>'
                        + '	 <div style="text-align: center; line-height:35px; width:30px; height: 40px; border: 0px solid #ccc; float: left">'
                        + resp[i].DanWei
                        + '	 </div>'
                        + '</div>';
                }
                html = html + "</div></div>";
                $("#csdiv").append(html);
            },
            onLoad: function () {
                this.bindEvent();
                this.refreshData();
            },
            beforeRender: function () {
                var _this = this;
                //查询参数信息，渲染界面
                _this.getDataSource();
                _this.addTags(_this.csList);
            },
        })
    });