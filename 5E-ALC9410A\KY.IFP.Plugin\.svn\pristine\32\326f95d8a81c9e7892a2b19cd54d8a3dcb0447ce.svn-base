﻿using COM.IFP.Common;
using ORM.IFP.DbModel;
using System;
using System.Collections.Generic;
using System.Threading;

namespace DAL.IFP.www.Device
{
    /// <summary>
    /// daiabin
    /// </summary>
    public class Reader
    {
        readonly Lazy<ReadConfig> _readConfig = Entity.Create<ReadConfig>();

        COM.IFP.Device.Reader read = new COM.IFP.Device.Reader();
        public PFActionResult ReadCard(string ip, int port, int count)
        {
            //ReturnMessage result = new ReturnMessage();
            //result.Type = EnumMsgType.执行失败;
            Dictionary<string, object> data = new Dictionary<string, object>();
            PFActionResult result = new PFActionResult();
            result.success = false;
            try
            {
                read.Connect(ip, port);
            }
            catch (Exception e)
            {
                result.success = false;
                result.msg = "连接读卡器失败。";
                //result.Type = EnumMsgType.连接失败;
                //result.Message = "连接读卡器失败。";
                return result;
            }

            for (int i = 1; i <= count; ++i)
            {
                string CardNo = "";
                string CardContent = "";
                try
                {
                    read.ReadIDContent(out CardNo, out CardContent);
                    if (!string.IsNullOrEmpty(CardNo))
                    {
                        result.success = true;
                        data["CardNo"] = CardNo;
                        data["CardContent"] = CardContent;
                        data["Count"] = i;
                        result.data = data;
                        //result.Type = EnumMsgType.执行成功;
                        //result.Params["CardNo"] = CardNo;
                        //result.Params["CardContent"] = CardContent;
                        ////取最大读卡的次数
                        //result.Params["Count"] = i;
                        read.Close();
                        return result;
                    }
                }
                catch { }
                Thread.Sleep(100);
            }
            read.Beep(2);
            read.Close();
            result.success = false;
            result.msg = "读卡失败，请检查卡是否放置到位";
            //result.Type = EnumMsgType.执行失败;
            //result.Message = "读卡失败，请检查卡是否存放正确";
            return result;
        }

        public PFActionResult WriteCard(string ip, int port, int count, string content)
        {
            //ReturnMessage result = new ReturnMessage();
            //result.Type = EnumMsgType.执行失败;
            Dictionary<string, object> data = new Dictionary<string, object>();
            PFActionResult result = new PFActionResult();
            result.success = false;
            try
            {
                read.Connect(ip, port);
            }
            catch (Exception e)
            {
                result.success = false;
                result.msg = "连接读卡器失败。";

                //result.Type = EnumMsgType.连接失败;
                //result.Message = "连接读卡器失败。";
                return result;
            }
            for (int i = 1; i <= count; i++)
            {
                try
                {
                    read.WriteContent(content);

                    result.success = true;
                    result.msg = "写入成功";
                    data["Count"] = i;
                    result.data = data;

                    //result.Type = EnumMsgType.执行成功;
                    //result.Message = "写入成功";
                    //result.Params["Count"] = i;
                    read.Close();
                    return result;
                }
                catch { }
                Thread.Sleep(100);
            }
            read.Beep(2);
            read.Close();
            result.success = false;
            result.msg = "写卡失败，请检查卡是否放置到位";
            //result.Type = EnumMsgType.执行失败;
            //result.Message = "写卡失败，请检查卡是否存放正确";
            return result;
        }


        public PFActionResult ReadCard(IFP_SM_READCONFIG jobj)
        {
            PFActionResult result = new PFActionResult();
            result.success = false;
            if (jobj == null)
            {
                result.success = false;
                result.msg = "找不到对应的读卡器";
                return result;
            }

            bool checkIp = jobj.CheckIp;
            if (checkIp && string.IsNullOrEmpty(jobj.Gid.Value)) //如果需要校验IP是否在读卡器白名单但是读卡器对象又不是从数据库取值
            {
                result.success = false;
                result.msg = "找不到对应的读卡器";
                return result;
            }
            //默认读取10次
            if (!jobj.Count.HasValue)
            {
                jobj.Count = 10;
            }
            string IPaddr = jobj.IPaddr.Value;
            int Port = int.Parse(jobj.Port.Value);
            PFActionResult res = ReadCard(IPaddr, Port, (int)jobj.Count);

            return res;
        }

        public PFActionResult WriteCard(IFP_SM_READCONFIG jobj)
        {

            PFActionResult result = new PFActionResult();
            if (jobj == null)
            {
                result.success = false;
                result.msg = "找不到对应的读卡器";
                return result;
            }

            if (string.IsNullOrEmpty(jobj.CardContent))
            {
                result.success = false;
                result.msg = "写入内容不能为空。";
                return result;
            }
            //默认写10次
            if (!jobj.Count.HasValue)
            {
                jobj.Count = 10;
            }
            string IPaddr = jobj.IPaddr.Value;
            int Port = int.Parse(jobj.Port.Value);

            PFActionResult res = WriteCard(IPaddr, Port, (int)jobj.Count, jobj.CardContent);
            return res;

            //if (res.Type == EnumMsgType.执行成功)
            //{
            //    result.success = true;
            //    result.msg = res.Message;
            //    result.data = res.Params["Count"];
            //    return result;
            //}
            //else
            //{
            //    result.success = false;
            //    result.msg = res.Message;
            //    return result;
            //}
        }

        public PFActionResult ReadCardDefault()
        {
            var config = _readConfig.Value.ClientReadConfig();
            return ReadCard(config);
        }
    }
}
