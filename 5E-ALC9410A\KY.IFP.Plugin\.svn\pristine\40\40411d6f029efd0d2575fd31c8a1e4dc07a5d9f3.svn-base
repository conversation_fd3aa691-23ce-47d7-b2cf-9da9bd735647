﻿//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;
using System;

namespace ORM.IFP.DbModel
{
    /// <summary>
	/// 基础资料
	/// </summary>
	[SugarTable("IFP_BS_BASEINFO")]
    public partial class IFP_BS_BASEINFO
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "BIGINT")]
        public Field<long> Gid { get; set; }

        /// <summary>
        /// 上级对象GID
        /// </summary>
        [SugarColumn(ColumnName = "PGID", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "BIGINT")]
        public Field<long> Pgid { get; set; }

        /// <summary>
        /// 对象名称
        /// </summary>
        [SugarColumn(ColumnName = "BNAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(128)")]
        public Field<string> Bname { get; set; }

        /// <summary>
        /// 对象简称
        /// </summary>
        [SugarColumn(ColumnName = "SNAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(128)")]
        public Field<string> Sname { get; set; }

        /// <summary>
        /// 业务类型 
        /// </summary>
        [SugarColumn(ColumnName = "YWLX", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(4)")]
        public Field<string> YWLX { get; set; }

        /// <summary>
        /// 所属单位
        /// </summary>
        [SugarColumn(ColumnName = "COMPID", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(8)")]
        public Field<string> ComPid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "ADDTIME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> AddTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnName = "LASTTIME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> LastTime { get; set; }

        /// <summary>
        /// 作废标志（1作废  0正常）
        /// </summary>
        [SugarColumn(ColumnName = "ZFBZ", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> ZFBZ { get; set; }

        /// <summary>
        /// 业务编码
        /// </summary>
        [SugarColumn(ColumnName = "YWBM", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> YWBM { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "BEIZHU", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> BeiZhu { get; set; }

        /// <summary>
        /// 树结构编码，也可用于排序
        /// </summary>
        [SugarColumn(ColumnName = "TREECODE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(128)")]
        public Field<string> TreeCode { get; set; }

    }


    /* 以下为对应的Json对象
	{
		GID: null,
		PGID: null,		//上级对象GID
		BNAME: null,		//对象名称
		SNAME: null,		//对象简称
		YWLX: null,		//业务类型 
		COMPID: null,		//所属单位
		ADDTIME: null,
		LASTTIME: null,		//修改时间
		ZFBZ: null,		//作废标志（1作废  0正常）
		YWBM: null,		//业务编码
		BEIZHU: null,		//备注
		TREECODE: null		//树结构编码，也可用于排序
	}
	*/

    ////基础资料
    //[SugarTable("IFP_BS_BASEINFO")]
    //public partial class IFP_BS_BASEINFO : PageParam
    //{
    //    [SugarColumn(IsPrimaryKey = true)]
    //    public long? Gid { set; get; }

    //    /// <summary>
    //    /// 上级对象GID
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Pgid")]
    //    public long? Pgid { set; get; }

    //    /// <summary>
    //    /// 对象名称
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Bname", ColumnDataType = "nvarchar(128)")]
    //    public string Bname { set; get; }

    //    /// <summary>
    //    /// 对象简称
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Sname", ColumnDataType = "nvarchar(128)")]
    //    public string Sname { set; get; }

    //    /// <summary>
    //    /// 业务类型
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Ywlx", ColumnDataType = "nvarchar(4)")]
    //    public string Ywlx { set; get; }

    //    /// <summary>
    //    /// 所属单位
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Compid", ColumnDataType = "nvarchar(8)")]
    //    public string Compid { set; get; }

    //    /// <summary>
    //    /// Addtime
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Addtime")]
    //    public DateTime? Addtime { set; get; }

    //    /// <summary>
    //    /// 修改时间
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Lasttime")]
    //    public DateTime? Lasttime { set; get; }

    //    /// <summary>
    //    /// 作废标志（1作废  0正常）
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Zfbz")]
    //    public int? Zfbz { set; get; }

    //    /// <summary>
    //    /// 业务编码
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Ywbm", ColumnDataType = "nvarchar(255)")]
    //    public string Ywbm { set; get; }

    //    /// <summary>
    //    /// 树结构编码，也可用于排序
    //    /// </summary>
    //    [SugarColumn(ColumnName = "TREECODE", ColumnDataType = "nvarchar(255)")]
    //    public string TreeCode { get; set; }

    //    /// <summary>
    //    /// 备注
    //    /// </summary>
    //    [SugarColumn(ColumnName = "Beizhu", ColumnDataType = "nvarchar(255)")]
    //    public string Beizhu { set; get; }

    //    /// <summary>
    //    /// 业务对象扩展信息
    //    /// </summary>
    //    //public string Ywdxinfo { set; get; }
    //}
    public partial class IFP_BS_BASEINFO
    {
        //[SugarColumn(IsIgnore = true)]
        //public string Gid_IN { set; get; }

        //[SugarColumn(IsIgnore = true)]
        //public string Bname_LIKE { set; get; }
        /// <summary>
        /// 业务对象扩展信息，接口同步用到
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string Ywdxinfo { set; get; }
    }
}
