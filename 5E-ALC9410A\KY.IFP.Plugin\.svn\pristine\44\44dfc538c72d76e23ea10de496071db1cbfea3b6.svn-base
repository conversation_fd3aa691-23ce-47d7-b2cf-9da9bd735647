/*! URI.js v1.19.6 http://medialize.github.io/URI.js/ */
/* build contains: jquery.URI.js */
(function(d,e){"object"===typeof module&&module.exports?module.exports=e(require("jquery"),require("./URI")):"function"===typeof define&&define.amd?define(["jquery","./URI"],e):e(d.jQuery,d.URI)})(this,function(d,e){function k(a){return a.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(a){var b=a.nodeName.toLowerCase();if("input"!==b||"image"===a.type)return e.domAttributes[b]}function p(a){return{get:function(b){return d(b).uri()[a]()},set:function(b,c){d(b).uri()[a](c);return c}}}function m(a,
b){if(!l(a)||!b)return!1;var c=b.match(q);if(!c||!c[5]&&":"!==c[2]&&!h[c[2]])return!1;var g=d(a).uri();if(c[5])return g.is(c[5]);if(":"===c[2]){var f=c[1].toLowerCase()+":";return h[f]?h[f](g,c[4]):!1}f=c[1].toLowerCase();return n[f]?h[c[2]](g[f](),c[4],f):!1}var n={},h={"=":function(a,b){return a===b},"^=":function(a,b){return!!(a+"").match(new RegExp("^"+k(b),"i"))},"$=":function(a,b){return!!(a+"").match(new RegExp(k(b)+"$","i"))},"*=":function(a,b,c){"directory"===c&&(a+="/");return!!(a+"").match(new RegExp(k(b),
"i"))},"equals:":function(a,b){return a.equals(b)},"is:":function(a,b){return a.is(b)}};d.each("origin authority directory domain filename fragment hash host hostname href password path pathname port protocol query resource scheme search subdomain suffix tld username".split(" "),function(a,b){n[b]=!0;d.attrHooks["uri:"+b]=p(b)});var r=function(a,b){return d(a).uri().href(b).toString()};d.each(["src","href","action","uri","cite"],function(a,b){d.attrHooks[b]={set:r}});d.attrHooks.uri.get=function(a){return d(a).uri()};
d.fn.uri=function(a){var b=this.first(),c=b.get(0),g=l(c);if(!g)throw Error('Element "'+c.nodeName+'" does not have either property: href, src, action, cite');if(void 0!==a){var f=b.data("uri");if(f)return f.href(a);a instanceof e||(a=e(a||""))}else{if(a=b.data("uri"))return a;a=e(b.attr(g)||"")}a._dom_element=c;a._dom_attribute=g;a.normalize();b.data("uri",a);return a};e.prototype.build=function(a){if(this._dom_element)this._string=e.build(this._parts),this._deferred_build=!1,this._dom_element.setAttribute(this._dom_attribute,
this._string),this._dom_element[this._dom_attribute]=this._string;else if(!0===a)this._deferred_build=!0;else if(void 0===a||this._deferred_build)this._string=e.build(this._parts),this._deferred_build=!1;return this};var q=/^([a-zA-Z]+)\s*([\^\$*]?=|:)\s*(['"]?)(.+)\3|^\s*([a-zA-Z0-9]+)\s*$/;var t=d.expr.createPseudo?d.expr.createPseudo(function(a){return function(b){return m(b,a)}}):function(a,b,c){return m(a,c[3])};d.expr[":"].uri=t;return d});
