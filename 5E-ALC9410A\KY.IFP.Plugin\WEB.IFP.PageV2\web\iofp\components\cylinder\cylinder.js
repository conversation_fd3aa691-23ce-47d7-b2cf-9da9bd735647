/**
 * 圆柱形容器 vue 组件
 * zq 2021-09-01
 */
define(["TweenLite"],function(TweenLite){
    return {
        props:{
            inteval:{default:0.2},//动画时长
            value:{default:0},//液面高度
            maxValue:{default:150},//页面最大值，超过这个值，按这个值渲染
            minValue:{default:0},// 液面最小值，小于这个值，按这个值渲染
            boxWidth:{default:100},// 容器宽度
            boxHeight:{default:150},// 容器高度
            colorOutside:{default:'#0001'}, // 容器颜色
            colorInside:{default:'#cccf'}, // 液体颜色
            colorStroke:{default:'#0006'}, // 边线颜色
            showOutside:{default:true}, // 显示容器
            showInside:{default:true}, // 显示液体
            arcHeight:{default:20}//圆弧高度，短直径
        },
        data(){
            return {
                strokeWidth:1,
                //maxWidth:100,
                //maxHeight:150,
                //arcHeight:20,
                //height:50,
                height:0,
            }
        },
        watch:{
            value(){
                let v = this.height;
                if(this.value<this.minValue){
                    v = this.minValue
                }else if(this.value > this.maxValue){
                    v = this.maxValue
                }else {
                    v = this.value; 
                }
                TweenLite.to(this,this.inteval,{height:v})
            }
        },
        mounted(){
            TweenLite.to(this,this.inteval,{height:this.value})
        },
        computed:{
            maxWidth(){ return this.boxWidth; },
            maxHeight(){ return this.boxHeight; },
            pathArcTop(){
                return `A${this.maxWidth/2} ${this.arcHeight/2} 0 1 1 `
            }
        },
        template:`
<svg :preserve-aspect-ratio.camel="'xMidYMid meet'" :view-box.camel="\`\${-strokeWidth} \${-arcHeight/2-strokeWidth} \${maxWidth+strokeWidth*2} \${maxHeight+arcHeight+strokeWidth*2}\`">
    
    <defs>
        <linearGradient id="metal1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" :style="'stop-color:'+colorOutside" style="stop-opacity: 1;"></stop>
            <stop offset="50%" :style="'stop-color:'+colorOutside" style="stop-opacity: 0.5;"></stop>
            <stop offset="100%" :style="'stop-color:'+colorOutside" style="stop-opacity: 1;"></stop>
        </linearGradient>
        <linearGradient id="metal2" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" :style="'stop-color:'+colorInside" style="stop-opacity: 1;"></stop>
            <stop offset="50%" :style="'stop-color:'+colorInside" style="stop-opacity: 1;"></stop>
            <stop offset="100%" :style="'stop-color:'+colorInside" style="stop-opacity: 1;"></stop>
        </linearGradient>
    </defs>

    <g v-if="showOutside" :stroke-width="strokeWidth" :stroke="colorStroke" :style="{fill:colorOutside}">
        <!--外层 后圆柱-->
        <path :d="
        \`M0 0 
        \${pathArcTop} \${maxWidth} 0
        L\${maxWidth} \${maxHeight}
        A\${maxWidth/2} \${arcHeight/2} 0 1 0 0 \${maxHeight}
        Z\`"></path>
        
        <!--外层 底-->
        <path :d="
        \`M0 \${maxHeight} 
        A\${maxWidth/2} \${arcHeight/2} 0 1 1 \${maxWidth} \${maxHeight}
        A\${maxWidth/2} \${arcHeight/2} 0 1 1 0 \${maxHeight}
        Z\`"></path>
    </g>

    <g v-if="showInside" :stroke="colorStroke" :style="{fill:colorInside}" :transform="\`translate(0 \${maxHeight-height})\`" :stroke-width="strokeWidth">
        <!--内层 后圆柱-->
        <path :d="
        \`M0 0 
        A\${maxWidth/2} \${arcHeight/2} 0 1 1 \${maxWidth} 0
        L\${maxWidth} \${height}
        A\${maxWidth/2} \${arcHeight/2} 0 1 0 0 \${height}
        Z\`"></path>
        
        <!--内层 底-->
        <path :d="
        \`M0 \${height} 
        A\${maxWidth/2} \${arcHeight/2} 0 1 1 \${maxWidth} \${height}
        A\${maxWidth/2} \${arcHeight/2} 0 1 1 0 \${height}
        Z\`"></path>
    </g>
    <g v-if="showInside" :stroke="colorStroke" :style="{fill:colorInside}" :transform="\`translate(0 \${maxHeight-height})\`" :stroke-width="strokeWidth">

        <!--内层 前圆柱-->
        <path :d="
        \`M0 0 
        A\${maxWidth/2} \${arcHeight/2} 0 1 0 \${maxWidth} 0
        L\${maxWidth} \${height}
        A\${maxWidth/2} \${arcHeight/2} 0 1 1 0 \${height}
        Z\`"></path>

        <!--内层 顶-->
        <path :d="
        \`M0 0 
        A\${maxWidth/2} \${arcHeight/2} 0 1 1 \${maxWidth} 0
        A\${maxWidth/2} \${arcHeight/2} 0 1 1 0 0
        Z\`"></path>
    </g>
    <g v-if="showOutside" :stroke="colorStroke" transform="translate(0 0)" :style="{fill:colorOutside}" :stroke-width="strokeWidth">
        <!--外层 前圆柱-->
        <path :d="
        \`M0 0 
        A\${maxWidth/2} \${arcHeight/2} 0 0 0 \${maxWidth} 0
        L\${maxWidth} \${maxHeight}
        A\${maxWidth/2} \${arcHeight/2} 0 0 1 0 \${maxHeight}
        Z\`"></path>

        <!--外层 顶
        <path :d="
        \`M0 0 
        A\${maxWidth/2} \${arcHeight/2} 0 0 1 \${maxWidth} 0
        A\${maxWidth/2} \${arcHeight/2} 0 0 1 0 0
        Z\`"></path>
        -->
    </g>
</svg>
        `
    }
})