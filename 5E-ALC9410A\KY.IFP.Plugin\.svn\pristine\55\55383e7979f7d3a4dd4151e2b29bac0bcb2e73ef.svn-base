﻿using Newtonsoft.Json.Linq;

namespace COM.IFP.Common
{
    public class JObjectHelper
    {
        public static string GetStringValue(JObject jobject, string key)
        {
            JToken token = jobject.GetValue(key);
            if (token == null)
            {
                return string.Empty;
            }
            return token.ToString();
        }

        public static int? GetIntValue(JObject jobject, string key)
        {
            JToken token = jobject.GetValue(key);
            if (token == null)
            {
                return null;
            }
            string str = token.ToString();
            if (string.IsNullOrEmpty(str))
            {
                return null;
            }
            return int.Parse(str);
        }

        public static long? GetLongValue(JObject jobject, string key)
        {
            JToken token = jobject.GetValue(key);
            if (token == null)
            {
                return null;
            }
            string str = token.ToString();
            if (string.IsNullOrEmpty(str))
            {
                return null;
            }
            return long.Parse(str);
        }

        public static decimal? GetDecimalValue(JObject jobject, string key)
        {
            JToken token = jobject.GetValue(key);
            if (token == null)
            {
                return null;
            }
            string str = token.ToString();
            if (string.IsNullOrEmpty(str))
            {
                return null;
            }
            return decimal.Parse(str);
        }
    }
}
