define("styles",[
    "css!bootstrap-css",
    "css!kjlib/bootstrap-theme-kykj/dist/css/bootstrap-theme-kykj",
    "css!kjlib/bootstrap-theme-kykj/dist/css/dicb",
    "css!kjlib/bootstrap-theme-kykj/iconfont/dicb2/iconfont",
    "css!kjlib/bootstrap-theme-kykj/iconfont/kjicon/iconfont",
    "css!kjlib/bootstrap-theme-kykj/iconfont/glyphions/css/glyphions"
],function(){
    return "platform-styles load success!";
})

// $.bootoast
define("jquery.bootoast",["bootoast"],function(bootoast){
    $.bootoast = function(msg,option){
        bootoast.toast($.extend(true,{
            message: msg,position:"top",timeout:2
        },option));
    }
    $.each(["success","warning","danger"],function(i,item){
        $.bootoast[item]=function(msg){
            this(msg,{type:item});
        }
    });
})

define([
    "require",
    "zutil",
    "jquery",
    "configs/config.service",
    "renderer",
    "styles"
],function(require,sysUtil,$,urlConfig,renderer){

    return function(){
        return new Promise(function(resolve,reject){

            //将url配置挂在到 window 上
            $.extend(true,window,urlConfig);

            requirejs([
                "model","util","common","cookie","ajax",
                "enum","jquery.layout","dialog","jquery.ky.dialog"
            ],function(
                model,util,common,cookie,ajax,tenum,layout,dialog
            ){

                //初始化model
                model.init(false);

                //构建F对象 所有全局变量及配置都在这里定义
                window.F = window.F || {
                    model:model,
                    util:util,
                    common:common,
                    cookie:cookie,
                    ajax:ajax.ajax,
                    post:ajax.post,
                    model:model,
                    layout:layout,
                    enum:tenum,
                    dialog:dialog
                }
                
                //按需加载资源
                renderer.rquirePageControlPlus()

                //渲染页面
                .then(renderer.controllers).then(function(){
                    sysUtil.showBody();
                    $.layout(null,true);//立即执行布局
                    //$.layout(null,true);//立即执行布局
                    ///setTimeout(function(){ $.layout(null,true); })//执行队列后执行布局

                    resolve();
                }).catch(resolve);
            })
        })
    }
})