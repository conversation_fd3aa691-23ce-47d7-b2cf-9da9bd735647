﻿using System;
using System.IO;
using System.Reflection;

namespace COM.IFP.Common.Reflex
{
    public class ReflexCustom
    {
        /// <summary>
        /// DLL所在地址
        /// </summary>
        public string DLLPath { set; get; }
        /// <summary>
        /// 方法所在类的路径
        /// </summary>
        public string MethodPath { set; get; }
        /// <summary>
        /// 方法名称
        /// </summary>
        public string MethodName { set; get; }
        /// <summary>
        /// 说明
        /// </summary>
        public string Explain { set; get; }

        public static Type GetReflexType(ReflexCustom ctrfx)
        {
            //获取程序集所在的目录
            var basePath = new FileInfo(Assembly.GetEntryAssembly().Location).DirectoryName;
            Assembly ass = null;
            //调DLL
            try
            {
                ass = Assembly.LoadFrom(basePath + $"\\{ctrfx.DLLPath}.dll");
            }
            catch (Exception ex)
            {
                throw new Exception(ctrfx.DLLPath + "路径并不存在");
            }
            Type type = ass.GetType(ctrfx.MethodPath);
            if (type == null)
            {
                throw new Exception(ctrfx.MethodPath + "方法名并不存在");
            }
            //调方法名。
            return type;
        }
    }
}