define([
    "jclass",
    "controller",
    "lib/bower/mockjs/mock-min"
],function(jclass,base,Mock){

    // 自定义
    Mock.Random.extend({
        cstore() {
            return this.pick([
                "男",
                "女",
                "未说明"
            ]);
        }
    })

    var template = {
        "test":"p-test",
        "list|1-100":[{
            "gid":"@guid",
            "name":"@cname()",
            "email":'@email',
            "img":"<img src=\"@image(60x20,@color,@color,@cname)\"/>",
            "borthday":"@date('yyyy-MM-dd')",
            "city":"@county(true)",
            "cid":"@id()",
            "gender|1":[
                "男",
                "女",
                "未说明"
            ],
            cuser:"@user",
            "gender2":"@cstore"
        }]
    }
    //

    var msg = Mock.valid(template, {
        list:[
            {gid:"333",name:"33",email:"3.com",cid:"412702"}
        ]
    })
    console.log(msg);

    // 生成jsonschema
    var schema = Mock.toJSONSchema(template);
    console.log(schema);


    return jclass(base,{
        initControl:function(){
            console.time("load grid data")
            this.controls.grid33.value(Mock.mock(template).list)
            console.timeEnd("load grid data")
        },

        bindEvent:function(){
            var ts = this;
            ts.controls.selBtn.bind("click",function(){
                var inputData = controller.formData();
                alert(JSON.stringify(inputData,null,4));
            })

            ts.controls.t_addBtn.bind("click",function(){
                ts.controls.grid33.addRowData({});
            })
            
            ts.controls.t_delBtn.bind("click",function(){
                var selId = ts.controls.grid33.getSelectRowId();
                if(!selId){$.bootoast.warning("请先选择一行")}
                else{
                    ts.controls.grid33.delRowData(selId);
                }
            })
            ts.controls.t_delCheckBtn.bind("click",function(){
                var selId = ts.controls.grid33.getCheckRowId();
                if(!selId){$.bootoast.warning("请先选择一行")}
                else{
                    selId.forEach(function(id){
                        ts.controls.grid33.delRowData(id);
                    })
                }
            })
        },

        onLoad:function(){
            this.initControl();
            this.bindEvent();
        }
    });
})