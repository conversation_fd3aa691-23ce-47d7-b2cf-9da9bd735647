﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace COM.IFP.Common.Secure
{
    public class DESHelper
    {
        #region DES加密解密
        /// <summary>
        /// 8位字符的密钥字符串(需要和加密时相同)
        /// </summary>
        private const string DESKey = "";
        /// <summary>
        /// DES加密
        /// </summary>
        /// <param name="data">加密数据</param>
        /// <param name="iv">8位字符的初始化向量字符串</param>
        /// <returns></returns>
        public static string DESEncrypt(string data, string iv)
        {
            byte[] byKey = System.Text.ASCIIEncoding.ASCII.GetBytes(DESKey);
            byte[] byIV = System.Text.ASCIIEncoding.ASCII.GetBytes(iv);

            DESCryptoServiceProvider cryptoProvider = new DESCryptoServiceProvider();
            int i = cryptoProvider.KeySize;
            MemoryStream ms = new MemoryStream();
            CryptoStream cst = new CryptoStream(ms, cryptoProvider.CreateEncryptor(byKey, byIV), CryptoStreamMode.Write);

            StreamWriter sw = new StreamWriter(cst);
            sw.Write(data);
            sw.Flush();
            cst.FlushFinalBlock();
            sw.Flush();
            return Convert.ToBase64String(ms.GetBuffer(), 0, (int)ms.Length);
        }

        /// <summary>
        /// DES解密
        /// </summary>
        /// <param name="data">解密数据</param>
        /// <param name="iv">8位字符的初始化向量字符串(需要和加密时相同)</param>
        /// <returns></returns>
        public static string DESDecrypt(string data, string iv)
        {
            byte[] byKey = System.Text.ASCIIEncoding.ASCII.GetBytes(DESKey);
            byte[] byIV = System.Text.ASCIIEncoding.ASCII.GetBytes(iv);

            byte[] byEnc;
            try
            {
                byEnc = Convert.FromBase64String(data);
            }
            catch
            {
                return null;
            }

            DESCryptoServiceProvider cryptoProvider = new DESCryptoServiceProvider();
            MemoryStream ms = new MemoryStream(byEnc);
            CryptoStream cst = new CryptoStream(ms, cryptoProvider.CreateDecryptor(byKey, byIV), CryptoStreamMode.Read);
            StreamReader sr = new StreamReader(cst);
            return sr.ReadToEnd();
        }
        #endregion

        #region 3DES 加密解密
        /// <summary>
        /// 同各体系间约定的秘钥
        /// </summary>
        private const string DES3Key = "792a48477c02775d";
        /// <summary>
        /// DES3加密
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string DES3Encrypt(string data)
        {
            //TripleDESCryptoServiceProvider DES = new TripleDESCryptoServiceProvider();
            TripleDES DES = TripleDES.Create();

            DES.Key = ASCIIEncoding.UTF8.GetBytes(DES3Key);     //约定UTF8格式进行加密，IFP设置的是128（16*8）位密码，一般默认为192（24*8）位密码，此处要注意。
            DES.Mode = CipherMode.ECB;                          //约定为ECB
            DES.Padding = PaddingMode.PKCS7;                    //约定为PKCS7

            ICryptoTransform DESEncrypt = DES.CreateEncryptor();

            byte[] Buffer = ASCIIEncoding.UTF8.GetBytes(data);  //约定UTF8格式进行加密
            return Convert.ToBase64String(DESEncrypt.TransformFinalBlock(Buffer, 0, Buffer.Length));
        }
        /// <summary>
        /// DES3解密
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string DES3Decrypt(string data)
        {
            //TripleDESCryptoServiceProvider DES = new TripleDESCryptoServiceProvider();
            TripleDES DES = TripleDES.Create();

            DES.Key = ASCIIEncoding.UTF8.GetBytes(DES3Key); //约定UTF8格式进行加密
            DES.Mode = CipherMode.ECB;                      //约定为ECB
            DES.Padding = PaddingMode.PKCS7;                //约定为PKCS7

            ICryptoTransform DESDecrypt = DES.CreateDecryptor();

            string result = "";
            try
            {
                byte[] Buffer = Convert.FromBase64String(data);
                result = ASCIIEncoding.UTF8.GetString(DESDecrypt.TransformFinalBlock(Buffer, 0, Buffer.Length));    //约定UTF8格式进行加密
            }
            catch (Exception e)
            {

            }
            return result;
        }

        #endregion
    }
}
