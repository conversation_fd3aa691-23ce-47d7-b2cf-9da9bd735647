﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理I/O点分组</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" v-loading="loading" style="height:100%">
        <ifp-toolbar close>
            <ifp-button @click="save" log>保存</ifp-button>
        </ifp-toolbar>

        <ifp-panel-table class="flex-item padding">
            <template v-slot:header>
                <ifp-button code="B1" role="新增" @click="addMx">新增行</ifp-button>
                <ifp-button code="B3" role="删除" @click="deleteMx">删除行</ifp-button>
            </template>
            <ifp-table ref="tableRef"
                       :data="data"
                       row-key="Gid"
                       :border="true"
                       :highlight-current-row="true"
                       @row-click="rowClick"
                       style="height:500px">
                <el-table-column type="index"
                                 width="50">
                </el-table-column>
                <el-table-column prop="Name"
                                 label="分组名称"
                                 header-align="center"
                                 align="left"
                                 width="200">
                    <template slot-scope="scope">
                        <ifp-input v-model="scope.row.Name">
                        </ifp-input>
                    </template>
                </el-table-column>
                <el-table-column prop="Sxh"
                                 label="顺序号"
                                 align="center"
                                 width="100">
                    <template slot-scope="scope">
                        <ifp-input v-model="scope.row.Sxh">
                        </ifp-input>
                    </template>
                </el-table-column>
                <el-table-column prop="StopState"
                                 label="是否停用"
                                 align="center"
                                 width="120">
                    <template slot-scope="scope">
                        <ifp-select v-model="scope.row.StopState"
                                    :items="sfList"
                                    value-key="id"
                                    label-key="text">
                        </ifp-select>
                    </template>
                </el-table-column>
                <el-table-column prop="TabName"
                                 label="页签名称"
                                 header-align="center"
                                 align="left"
                                 width="200">
                    <template slot-scope="scope">
                        <ifp-input v-model="scope.row.TabName">
                        </ifp-input>
                    </template>
                </el-table-column>
            </ifp-table>
        </ifp-panel-table>

        <!--<ifp-dialog :visible.sync="editor.show" :title="editor.title" width="800px">
            <ifp-detail @cancel="editor.show=false;"
                        @submit="editor.show=false,onSelect()"
                        :data="editor.data"
                        :list="ioShowList"></ifp-detail>
        </ifp-dialog>-->
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>