﻿define(["iofp/api", "platform/vue", "iofp/util"], function (API, pVue, util) {

    return {
        //初始化显示列配置
        initXslsz: function (_this, tablerefs) {
            //默认所有列都显示
            var xslsz = { show: false, tableref: "" };
            for (var i = 0; i < tablerefs.length; i++) {
                var refobj = {};
                for (var j = 1; j <= 1000; j++) {
                    refobj["C" + j] = true;
                }
                xslsz[tablerefs[i]] = refobj;
            }
            _this.$set(_this, "xslsz", xslsz);

            //根据数据库的设置，动态控制列显
            for (var i = 0; i < tablerefs.length; i++) {
                this.setColumnShow(_this, tablerefs[i]);
            }
        },

        //读取后台数据库存储的显示列配置，并刷新界面
        setColumnShow: function (_this, tableref) {
            //页面地址，例如：/pages/sys/liexian/index.html
            var pathname = document.location.pathname;
            //URL参数，例如：?ysfs1001=10010001&ghdw4002=4000581
            var search = document.location.search;

            //页面地址
            var pageurl = util.urlGetPageID() || (pathname + search);

            //需要控制列显的表格
            API.GetAction("API/IFP/www/sys/XslszAPI/Select", { Pageurl: pageurl, Tableid: tableref }).then(x => {
                var columns = (x.Columns == null || x.Columns == "") ? [] : x.Columns.split(",");
                var table = _this.xslsz[tableref];
                var keys = Object.keys(table);
                for (var i = 0; i < keys.length; i++) {
                    table[keys[i]] = (columns.length == 0 ? true : columns.indexOf(keys[i]) > -1);
                }
                _this.$nextTick(() => {
                    _this.$refs[tableref].doLayout();
                });
            }).catch(e => {
                _this.$message.error(e);
            });
        },

        //列显设置按钮点击后事件
        onXslBtnClick: function (_this, tableref) {
            _this.xslsz.show = true;
            _this.xslsz.tableref = tableref;
        },

        //列显设置界面保存后返回事件
        onSuccess: function (_this) {
            _this.xslsz.show = false;
            this.setColumnShow(_this, _this.xslsz.tableref);
        }
    }
});