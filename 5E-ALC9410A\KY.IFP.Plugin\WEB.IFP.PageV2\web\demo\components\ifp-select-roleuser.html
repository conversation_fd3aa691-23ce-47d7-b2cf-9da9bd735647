<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="业务类型,控件,下拉">
    <title>角色用户</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" class="padding">
        <ifp-select-roleuser v-model="custom" code="ICS_HYY"></ifp-select-roleuser>
        code 为空 查询全部用户
        <ifp-select-roleuser v-model="custom" code=""></ifp-select-roleuser>
        <ifp-select-roleuser :canedit="false" v-model="custom" code="ICS_HYY"></ifp-select-roleuser>
    </ifp-page>

    <script src="/iofp/starter.js"></script> 
</body>
</html>