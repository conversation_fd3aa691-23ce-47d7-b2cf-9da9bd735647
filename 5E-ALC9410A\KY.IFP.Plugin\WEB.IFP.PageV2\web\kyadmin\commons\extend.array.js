;define(["require","./extend"], function(require,extend) {
	function cacl(arr, callback) {
	    var ret;
	    for (var i=0; i<arr.length;i++) {
	        ret = callback(arr[i], ret);
	    }
	    return ret;
	}
	
	function Numberdata(prop){
		if (!isNaN(Number(prop))){
			prop = Number(prop);
		}
		return prop;
	}
	
	extend(Array,{
		test:function(){
			
		},
		max : function () {
		    return cacl(this, function (item, max) {
		        if (!(Numberdata(max) > Numberdata(item))) {
		            return item;
		        }
		        else {
		            return max;
		        }
		    });
		},
		min : function () {
		    return cacl(this, function (item, min) {
		        if (!(Numberdata(min) < Numberdata(item))) {
		            return item;
		        }
		        else {
		            return min;
		        }
		    });
		},
		sum : function () {
		    return cacl(this, function (item, sum) {
		        if (typeof (sum) == 'undefined') {
		            return item;
		        }
		        else {
		        	if (typeof item == 'string') {
		        		item = parseFloat(item);
		        	}
		        	if (typeof sum == 'string') {
		        		sum = parseFloat(sum);
		        	}
		            return sum += item;
		        }
		    });
		},
		avg : function () {
		    if (this.length == 0) {
		        return 0;
		    }
		    return this.sum(parseFloat(this)) / this.length;
		},
		
		 /**
         * 数组去除重复的(根据对象来)
         * @param {Object} ary
         */
        unique2: function(ary) {
            var result = [],
                hash = {};
            for(var i = 0, elem;
                (elem = arr[i]) != null; i++) {
                if(!hash[elem]) {
                    result.push(elem);
                    hash[elem] = true;
                }
            }
            return result;
        },
		/**
         * 数组的去重
         * @param arr
         * @returns {Array}
         */
        unique: function(arr) {
            var ra = new Array();
            for(var i = 0; i < arr.length; i++) {
                if(!ra.contains(arr[i])) {
                    ra.push(arr[i]);
                }
            }
            return ra;
        },
        
     

	});
});