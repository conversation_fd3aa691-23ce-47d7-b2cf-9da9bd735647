﻿define([
    "ELEMENT",
    "util",
    "iofp/api",
    "platform/vue",
    "ramda",
    "css!/kyadmin/css/layout.css"
], function (ELEMENT, util, API, pVue,R) {

    return {
        el: '#app',
        data: {
            datalx: [{ value: 1, label: '数值' }, { value: 2, label: '文本' }, { value: 3, label: '下拉框' }],
            //子窗口属性
            iconDialog: {
                //是否显示子窗口
                show: false,
            },
            //菜单树
            treeData: [{
                Gid: null,
                pid: null,
                Pxh: null,
                CsName: null,
                CsCode: null,
                CsValue: null,
                DanWei: null,
                LeiXing: null,
                Data: null,
                REMARK: null,
                children: []
            }],
            //煤种树属性对应关系
            treeProps: {
                children: 'children',
                label: 'CsName'
            },
            currentIndex: -1,
            //当前选择信息
            currentXtcs: {
                Gid: null,
                pid: null,
                Pxh: null,
                CsName: null,
                CsCode: null,
                CsValue: null,
                DanWei: null,
                LeiXing: null,
                Data:null,
                REMARK: null
            },
            disable: {
                saveDisabled: true,
                updateDisabled: true,
            },
            treeSortChange: false
            //menuIcon: 'el-icon el-icon-search'
        },
        methods: {
            //加载煤种树信息
            LoadTree: function (val) {
                var _this = this;
                API.GetAction("API/IFP/BaseInfo/Xtcs/GetAllXtcsListAndJsonFz", {}).then(x => {
                    _this.treeData = util.getTreeData(x, { id: "CsCode", pid: "pid" });
                });
            },
            //保存
            onSave: function () {
                var _this = this;
                _this.currentXtcs = R.omit(["parent"])(_this.currentXtcs);
                _this.currentXtcs = R.omit(["children"])(_this.currentXtcs);
                API.GetAction("API/IFP/BaseInfo/Xtcs/UpdateXtcs", _this.currentXtcs).then(x => {
                    if (x.success) {
                        _this.$message.success("保存成功。");
                        _this.disable.saveDisabled = true;
                        _this.disable.updateDisabled = false;
                        _this.LoadTree();
                    } else {
                        _this.$message.error(x.msg);
                    }
                }).catch(e => {
                    _this.$message.error(e);
                });
            },
            //修改
            onUpdate: function () {
                var _this = this;
                _this.disable.saveDisabled = false;
                _this.disable.updateDisabled = true;
            },
            //退出
            onQuit: function () { },
            //树选择
            onMenuClick: function (data) {
                var _this = this;
                if (data.CsValue == null) {
                    return;
                }
                _this.currentXtcs = R.omit(["parent"])(data);//R.omit(["parent"]表示去掉parent属性
                //_this.currentXtcs = R.omit(["children"])(data);//R.omit(["children"]表示去掉children属性
                _this.disable.saveDisabled = true;
                _this.disable.updateDisabled = false;
            },
           
            onClosed: function () {

            },
            //过滤节点
            filterNode(value, data) {
                if (!value || data.menuName == null) return true;
                return data.menuName.indexOf(this.$data.sreachName) !== -1 && data.Delt == this.$data.searchState;
            },
            //节点拖动
            handleDrop: function (draggingNode, dropNode, dropType, ev) {
               // reSetSort(this.treeData[0]);
               // this.treeSortChange = true;
            },
            //节点是否能被拖动
            allowDrag(node) {
                return false;
            },
            //节点是否能被放置
            allowDrop(draggingNode, dropNode, type) {
                return false;
            },
            //保存菜单顺序
            saveTreeSort() {
                var list = treeToList(this.treeData);
                var _this = this;
                API.GetAction("API/IFP/Rights/Menu/SaveMenu", list).then(x => {
                    if (x.success) {
                        _this.$message.success("保存成功，" + x.msg);
                        _this.treeSortChange = false;
                    } else {
                        _this.$message.error(x.msg);
                    }
                }).catch(e => {
                    _this.$message.error(e);
                });
            }
        },
        watch: {
            sreachName: function (val) {
                var _this = this;
                _this.$refs.tree.filter(_this.sreachName);
            },
            searchState: function (val) {
                var _this = this;
                _this.$refs.tree.filter(_this.searchState);
            }

        },
        //计算属性
        computed: {
            menuIcon: function (val) {
                var _this = this;
                if (this.currentXtcs.menuImg == null) {
                    return 'el-icon el-icon-search';
                } else {
                    var index = this.currentXtcs.menuImg.lastIndexOf("-")
                    var s = this.currentXtcs.menuImg.substr(0, index);
                    return s + " " + this.currentXtcs.menuImg;
                }
            }
        },
        created: function () {
            var _this = this;
            _this.LoadTree();
        }
    }
});