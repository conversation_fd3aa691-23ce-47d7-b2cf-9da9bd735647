# CSS 编码规范

## Vue 组件页面

### 组件添加样式文件

若需要便携样式，则需要在 `#app`元素上添加全局唯一标识，建议将路径`/`替换为`-`作为唯一标识。

例如：

`/pages/user/list.html`

```html
<body controller option="{platform:'element'}">
    <div id="#app" pages-user-list>
        <!--页面内容-->
        <div class="custom-header">页面私有class演示</div>
    </div>
</body>
```

`/pages/user/list.js`
```js
define(["css!./index.css"],function(){

})
```

`/pages/user/list.css`
```css
[pages-user-list] .custom-header{
    color:#f00;
}
```

## 共用样式文件

### 全局样式

公用样式文件，可以定义在启动器 `starter.js` 中。

`starter.js`
```js
window.kycore = {
    callback:function(){
        // 全局样式
        requirejs(["css!/commons/golbal.css"])
    }
}
```

### 模块化引入样式

动态引入样式

```js
define([
    "css!/commons/css/theme1.css"
],function(){

})
```