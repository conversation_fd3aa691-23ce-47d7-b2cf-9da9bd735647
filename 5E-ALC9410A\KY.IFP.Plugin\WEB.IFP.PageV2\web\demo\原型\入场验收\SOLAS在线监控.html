<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="示例页面,图表,运行状态">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>079 SOLAS在线监控</title>
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button type="primary" icon="el-icon-download">下发采样方案</ifp-button>
            <ifp-button type="danger" icon="glyphicons glyphicons-power">结束采样</ifp-button>
        </ifp-toolbar>

        <div class="margin flex flex-row" style="justify-content: space-between;
        align-items: center;padding-bottom:0;">
            <div class="padding-left">
                <span class="margin-right">运行状态</span>
                <ifp-legend code="runstate" :value="runState"></ifp-legend>
            </div>
            <div>
                <ifp-button circle type="success">启动</ifp-button>
                <ifp-button circle type="danger">停止</ifp-button>
                <ifp-button circle type="warning">暂停</ifp-button>
            </div>
        </div>

        <div style="min-height:300px;" class="margin flex flex-item flex-row">
            <ifp-panel flex title="来煤明细" body-class="flex"
            style="white-space:nowrap" height="100%">
                <ifp-searchbar 
                @search="$message('查询')" 
                @reset="$message('reset')">
                    <ifp-searchbar-item label="日期">
                        <ifp-date-picker style="width:120px" type="date" placeholder="选择日期"></ifp-date-picker>
                        至
                        <ifp-date-picker style="width:120px" type="date" placeholder="选择日期"></ifp-date-picker>
                    </ifp-searchbar-item>
                </ifp-searchbar>

                <ifp-panel-table class="flex-item margin" style="margin-top:0;">
                    <ifp-table>
                        <ifp-table-column label="采样状态"></ifp-table-column>
                        <ifp-table-column label="采样编码"></ifp-table-column>
                    </ifp-table>
                    <template v-slot:pagination>
                        <ifp-pagination 
                            @size-change="table1SizeChange" 
                            @current-change="table1PageChange" 
                            :current-page="paging.page"
                            :page-size="paging.size" 
                            :total="paging.records">
                        </ifp-pagination>
                    </template>
                </ifp-panel-table>
            </ifp-panel>
            <ifp-panel class="flex-item margin-left" 
                theme="style1" border flex 
                class="margin-left" 
                title="运行消息"
                >
                <div class="padding-left padding-right" v-for="item in 5">xxxxx xxxxxx xxx xxxx xxxxxx</div>
            </ifp-panel>
        </div>

        <ifp-panel flex class="flex-item margin" body-class="flex flex-row"
            title="批次信息">
            <template v-slot:header>
                <span>当前入炉批次：xxxxxx</span>
                <span class="margin-left">共检测次数：xxx</span>
            </template>

            <ifp-chart auto-format class="flex-item padding" :option="option">

            </ifp-chart>

            <ifp-chart auto-format class="flex-item padding" :option="option">

            </ifp-chart>

        </ifp-panel>
    </ifp-page>
    <script src="/iofp/starter.js"></script>
</body>
</html>