define(['iofp/util'],function(ifpUtil){

    var urls = [
        "http://www.baidu.com/aaa.html?test=2222&page=333#home",
        "/demo/index.html?_m=1234",
        "",
        undefined
    ]

    return function(QUnit){
        QUnit.module('iofp/util', function() {
            QUnit.test("ifpUtil.urlGetPageID 获取参数", function(assert) {
                let result = ifpUtil.urlGetPageID();
                assert.equal(true,true,`当前页面PK ${result}'`);
            });


            urls.forEach(url=>{
                QUnit.test("url操作"+url, function(assert) {
                    assert.equal(true,true,`let result = ifpUtil.urlGetParam('${url}','id');`);
                    url = ifpUtil.urlAddParam(url,'id',"testid")
                    assert.equal(true,true,`url = ifpUtil.urlAddParam(url,'id',"testid")`);
                    assert.equal(true,true,`url == ${url}`);
                    let result = ifpUtil.urlGetParam(url,'id');
                    if(ifpUtil.isEmpty(url)){
                        assert.equal(result,null,`url 为 null 或 undefined`);
                    }else{
                        assert.equal(result,'testid',`result = 'testid'`);
                    }

                    let encodeUrl = ifpUtil.urlAddBase64Param(url,'pk','/demo/index.html?pk=1');
                    if(ifpUtil.isEmpty(url)){
                        assert.equal(result,null,`url 为 null 或 undefined`);
                    }else{
                        result = ifpUtil.urlGetBase64Param(encodeUrl,"pk");
                        assert.equal(true,true,`encodeUrl == ${encodeUrl}`);
                        assert.equal(result,'/demo/index.html?pk=1',`result == '/demo/index.html?pk=1'`);
                    }
    
                });
            })
        });
    }
})