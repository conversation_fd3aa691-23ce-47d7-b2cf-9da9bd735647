<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>element 控件</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="/kyadmin//css/element-theme-ky/theme/index.css">
    <!-- 
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2/lib/theme-chalk/index.css">
     -->
</head>
<body>
    <div id="app">
        <el-date-picker v-model="date1" placeholder=""></el-date-picker>
    </div>
    <script src="/kyadmin/lib/bower/vue/vue.js"></script>
    <!-- 引入组件库 -->
    <!--
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2/lib/index.js"></script>
    -->
    <script src="/kyadmin/lib/kjlib/element/lib/index.js"></script>
    
    <script>
        new Vue({
            el:"#app",
            data(){
                return {
                date1:new Date()
                }
            }
        });
    </script>
</body>
</html>