.zxcsypj {
  display: flex;
  flex-direction: row;
}
.zxcsypj .zxcsypj-row {
  border-bottom: 4px solid #ccc;
}
.zxcsypj .zxcsypj-col {
  margin: 3px;
}
/*
* 无瓶
* 待化验
* 化验中
* 待回传
* 待复检
* 异常
*/
.zxcsypj-state-null,
.zxcsypj-state-empty,
.zxcsypj-state-dhy,
.zxcsypj-state-hyz,
.zxcsypj-state-dhc,
.zxcsypj-state-dfj,
.zxcsypj-state-error {
  line-height: 1.1;
  box-sizing: border-box;
  width: 48px;
  height: 48px;
  display: inline-block;
  border-width: 4px;
  border-style: solid;
  border-color: transparent;
  background-color: #bbb;
  border-radius: 50%;
  vertical-align: middle;
  opacity: inherit;
  text-align: center;
  position: relative;
  color: #fff;
}
.zxcsypj-state-null::before,
.zxcsypj-state-empty::before,
.zxcsypj-state-dhy::before,
.zxcsypj-state-hyz::before,
.zxcsypj-state-dhc::before,
.zxcsypj-state-dfj::before,
.zxcsypj-state-error::before {
  font-family: element-icons !important;
  position: absolute;
  left: 50%;
  top: 50%;
  font-size: 36px;
  margin-left: -18px;
  margin-top: -20px;
}
.zxcsypj-state-null.canselect,
.zxcsypj-state-empty.canselect,
.zxcsypj-state-dhy.canselect,
.zxcsypj-state-hyz.canselect,
.zxcsypj-state-dhc.canselect,
.zxcsypj-state-dfj.canselect,
.zxcsypj-state-error.canselect {
  cursor: pointer;
  border-color: #fff;
  border-style: dotted;
  border-color: #00f;
}
.zxcsypj-state-null.canselect:not(.selected),
.zxcsypj-state-empty.canselect:not(.selected),
.zxcsypj-state-dhy.canselect:not(.selected),
.zxcsypj-state-hyz.canselect:not(.selected),
.zxcsypj-state-dhc.canselect:not(.selected),
.zxcsypj-state-dfj.canselect:not(.selected),
.zxcsypj-state-error.canselect:not(.selected) {
  -webkit-animation: Tada 1s both infinite;
  -moz-animation: Tada 1s both infinite;
  -ms-animation: Tada 1s both infinite;
  animation: Tada 1s both infinite;
}
.zxcsypj-state-null.selected,
.zxcsypj-state-empty.selected,
.zxcsypj-state-dhy.selected,
.zxcsypj-state-hyz.selected,
.zxcsypj-state-dhc.selected,
.zxcsypj-state-dfj.selected,
.zxcsypj-state-error.selected,
.zxcsypj-state-null.canselect:hover,
.zxcsypj-state-empty.canselect:hover,
.zxcsypj-state-dhy.canselect:hover,
.zxcsypj-state-hyz.canselect:hover,
.zxcsypj-state-dhc.canselect:hover,
.zxcsypj-state-dfj.canselect:hover,
.zxcsypj-state-error.canselect:hover {
  border-color: #00f;
  border-style: solid;
}
.zxcsypj-state-null {
  background-color: transparent;
}
.zxcsypj-state-empty {
  background-color: #ddd;
}
.zxcsypj-state-dhc::before {
  content: "\e7ba";
}
.zxcsypj-state-dhy::before {
  content: "";
}
.zxcsypj-state-dfj::before {
  content: "";
}
.zxcsypj-state-hyz {
  background-color: #0f0;
}
.zxcsypj-state-error {
  background-color: #f00;
}
.zxcsypj-legend {
  margin-left: 24px;
}
.zxcsypj-legend > div + div {
  margin-top: 16px;
}
.zxcsypj-legend .zxcsypj-state-empty,
.zxcsypj-legend .zxcsypj-state-dhy,
.zxcsypj-legend .zxcsypj-state-hyz,
.zxcsypj-legend .zxcsypj-state-dhc,
.zxcsypj-legend .zxcsypj-state-dfj,
.zxcsypj-legend .zxcsypj-state-error {
  width: 20px;
  height: 20px;
  margin-right: 3px;
}
.zxcsypj-legend .zxcsypj-state-empty::before,
.zxcsypj-legend .zxcsypj-state-dhy::before,
.zxcsypj-legend .zxcsypj-state-hyz::before,
.zxcsypj-legend .zxcsypj-state-dhc::before,
.zxcsypj-legend .zxcsypj-state-dfj::before,
.zxcsypj-legend .zxcsypj-state-error::before {
  font-size: 16px;
  margin-left: -8px;
  margin-top: -8px;
}
@keyframes Tada {
  0%,
  100% {
    border-color: #00ff;
  }
  50% {
    border-color: #00f0;
  }
}
