﻿using COM.IFP.Common;
//using COM.IFP.LinqDB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using COM.IFP.SqlSugarN;

namespace API.ICS.BaseData
{
    public class Material
    {
        readonly Lazy<DAL.IFP.BaseData> bd = Entity.Create<DAL.IFP.BaseData>();
        readonly Lazy<DAL.IFP.Material> my = Entity.Create<DAL.IFP.Material>();

        public object Select(JsonElement json)
        {
            int? zfbz = json.GetValue<int?>("zfbz");
            List<ORM.IFP.YWDX4010> result = new();
            List<ORM.IFP.YWDX4010> list = bd.Value.Select<ORM.IFP.YWDX4010>(x => x.Ywlx.Value.Equals("4010"));
            if (zfbz!=null)
            {
                foreach (var item1 in list.Where(x => x.Zfbz.Value == zfbz).ToList())
                {
                    foreach(var item2 in GetParentItem(item1,list))
                    {
                        item2.Pgid = new Field<long>();
                        if(!result.Contains(item2))
                        {
                            result.Add(item2);
                        }
                    }
                }
            }
            else
            {
                result = list;
            }

            return result;

            //var list = bd.Value.Select(x=>x.Ywlx=="4010");
            //return list;

            //var list = bd.Value.Select<ORM.IFP.YWDX4010>(x=>x.Ywlx=="4010");
            //return list;

        }

        /// <summary>
        /// 获取煤种节点列表
        /// </summary>
        /// <param name="item">当前煤种</param>
        /// <param name="list">所有煤种列表</param>
        /// <returns></returns>
        private List<ORM.IFP.YWDX4010> GetParentItem(ORM.IFP.YWDX4010 item, List<ORM.IFP.YWDX4010> list)
        {
            List<ORM.IFP.YWDX4010> result = new();
            result.Add(item);
            if (item.Pgid.HasValue)
            {
                var parentItem = list.Where(x => x.Gid.Value == item.Pgid.Value).FirstOrDefault();
                if(parentItem!=null&&parentItem.Pgid.HasValue)
                {
                    result.AddRange(GetParentItem(parentItem, list));
                }
                if(parentItem != null&&!result.Contains(parentItem))
                {
                    result.Add(parentItem);
                }
            }

            return result;
        }

        public object SelectQj(JsonElement json)
        {
            var list = my.Value.SelectQj(json.GetValue<long>("mz4010"));
            return list;
        }
        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="json"></param>
        public void Update(JsonElement json)
        {
            var list = json.GetValue<ORM.IFP.BaseData[]>();
            foreach (var ywdx in list)
            {
                if (ywdx.Pgid.Value == (long)-999)
                {
                    ywdx.Pgid = new Field<long>();
                }
            }

            bd.Value.Update(list);
        }

        public void Submit(JsonElement json)
        {
            var ywdx = json.GetValue<ORM.IFP.YWDX4010>("ywdx");
            if(ywdx.Pgid.Value==(long)-999)
            {
                ywdx.Pgid =new Field<long>();
            }
            List<ORM.IFP.YWDX4010_QJ> qj = json.GetValue<List<ORM.IFP.YWDX4010_QJ>>("qj");
            my.Value.Submit(ywdx, qj);
        }

        public void Delete(JsonElement json)
        {
            var item = json.GetValue<ORM.IFP.YWDX4010>();
            my.Value.Delete(item);
        }
    }
}
