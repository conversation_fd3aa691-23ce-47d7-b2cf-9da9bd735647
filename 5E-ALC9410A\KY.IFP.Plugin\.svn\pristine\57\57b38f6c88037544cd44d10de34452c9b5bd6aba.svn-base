﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="index.css">
</head>
<body>
    <div class="flex borderstyle" style="position:fixed;top:8px;left:8px;bottom:8px;right:8px;border-width:1px;">
        <div class="flex-column flex-item padding">
            <div>
                <label>ws：</label>
                <input />
            </div>
            <label>发送内容：</label>
            <textarea id="txtSend" style="width:100%;height: 200px;"></textarea>
            <button id="btnSend">发送</button>
            <label>接收内容：</label>
            <textarea id="txtMessage" class="flex-item"></textarea>
        </div>
        <div class="borderstyle flex-column" style="width:300px;border-left-width:1px;padding:8px;">
            <div class="borderstyle" style="border-bottom-width:1px;padding-bottom:8px;">
                操作日志：
            </div>
            <div id="logbox" class="flex-item" style="overflow: auto;"></div>
        </div>
    </div>

    <!-- websocket 组件 -->
    <script src="util.js"></script>
    <script src="/kyadmin/lib/kjlib/websocket/websocket.js"></script>
    <script>


        var log = Log(document.getElementById("logbox"));

        // 配置 （开发中）
        var option = {
            autoOpen: true, // 自动登陆
            url: "",
            login: {
                name: "",
                pwd: ""
            },
            then: function (rev) {
                return rev.data;
            }
        }

        var socket = kywebsocket("ws://127.0.0.1:8080");

        socket.on("open", function () {
            log("已连接");
        })
        socket.on("close", function () {
            log("连接已断开");
        })
        socket.on("message", function (rev) {
            log("接收到数据");
            document.getElementById("txtMessage").value = JSON.stringify(rev.data, null, 4)
            ///console.log();
        })
        socket.open().then(function () {
            ready()
        }).catch(function () {

        })

        var ready = function () {
            document.getElementById("btnSend").onclick = function () {
                log.success("发送成功");
                socket.send(document.getElementById("txtSend").value);
            }
        }
    </script>
</body>
</html>
