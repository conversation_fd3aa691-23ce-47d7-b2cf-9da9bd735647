﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data;

namespace COM.IFP.SqlSugarN
{
    /// <summary>
    /// 数据引擎，适用达梦数据库，默认采用DM8。
    /// </summary>
    public class Dm
    {
        /// <summary>
        /// 创建引擎
        /// </summary>
        /// <param name="source">连接字符串</param>
        /// <param name="manage">托管事务</param>
        /// <returns>数据引擎</returns>
        public static DConnection Create(string source, bool manage = true)
        {
            var config = new ConnectionConfig
            {
                ConnectionString = source,
                DbType = SqlSugar.DbType.Dm,            // 指定数据库类型为 Dm
                IsAutoCloseConnection = true,         // 自动关闭连接
                InitKeyType = InitKeyType.Attribute,  // 初始化方式，使用特性标记
                /*ConfigureExternalServices = new ConfigureExternalServices
                {
                    // 1、配置 Lambda 表达式支持
                    SqlFuncServices = SqlSugarSetup.GetLambdaExpressions(),
                    //2、配置 Field<> 类型支持
                    EntityService = (property, column) =>
                    {
                        if (property.PropertyType.IsGenericType &&
                            property.PropertyType.GetGenericTypeDefinition() == typeof(Field<>))
                        {
                            column.IsNullable = true;
                            var underlyingType = property.PropertyType.GetGenericArguments()[0];
                            column.UnderType = underlyingType;

                        }
                    }
                },*/

                /*MoreSettings = new ConnMoreSettings
                {
                    IsWithNoLockQuery = true,         // 默认查询使用 NOLOCK
                    IsAutoRemoveDataCache = true      // 自动移除缓存
                },
                AopEvents = new AopEvents
                {
                    OnError = (exp) =>
                    {
                        // 错误日志处理
                        var errorMessage = exp.Message;
                        if (exp.InnerException != null)
                        {
                            errorMessage = $"{errorMessage} | {exp.InnerException.Message}";
                        }
                        throw new Exception($"数据库操作异常：{errorMessage}");
                    },
                    */
                /*OnExecutingChangeSql = (sql, pars) =>
                    {
                        */
                /*// 可以在这里处理 SQL 执行前的逻辑
                        return new KeyValuePair<string, SugarParameter[]>(sql, pars);*/
                /*
                        // 这里可以访问当前的 SqlSugarClient 实例
                        var db = pars.FirstOrDefault()?.Context;
                        if (db != null)
                        {
                            // 异步等待连接打开
                            OnOpen(db).Wait();
                        }
                        return new KeyValuePair<string, SugarParameter[]>(sql, pars);
                    }
                    OnExecutingChangeSql = (sql, pars) =>
                    {
                        // 在执行SQL前检查并确保连接是打开的
                        var connection = pars?.FirstOrDefault()?.Value as SqlSugarClient;
                        */
                /*var connection = connectionParams*//*
                        if (connection != null)
                        {
                            OnOpen(connection).Wait();
                        }
                        return new KeyValuePair<string, SugarParameter[]>(sql, pars);
                    }
                }*/
            };

            return DConnection.Create(config, manage);
        }

        /// <summary>
        /// 配置 SqlSugar 扩展功能
        /// </summary>
        private static class SqlSugarSetup
        {
            /// <summary>
            /// 获取自定义 Lambda 表达式解析配置
            /// </summary>
            public static List<SqlFuncExternal> GetLambdaExpressions()
            {
                var expMethods = new List<SqlFuncExternal>();

                // 这里可以添加自定义函数映射
                expMethods.Add(new SqlFuncExternal
                {
                    UniqueMethodName = "ToDate",
                    MethodValue = (expInfo, _, _) =>
                    {
                        return $"CONVERT(DATE,{expInfo.Args[0].MemberName})";
                    }
                });

                // 可以继续添加其他自定义函数映射...

                return expMethods;
            }
        }

        //删除了原 public static DConnection CreateSqlserver2008(string source, bool manage = true)方法。

        /// <summary>
        /// 处理数据库连接打开事件
        /// </summary>
        /// <param name="db">SqlSugar 客户端实例</param>
        //private static async Task OnOpen(SqlSugarClient db)
        private static void OnOpen(SqlSugarClient db)
        {
            var connection = db.Ado.Connection;
            if (connection == null) return;

            var cancel = new System.Threading.CancellationTokenSource();
            // 从连接字符串或默认值获取超时时间（秒），转换为毫秒
            var timeout = connection.ConnectionTimeout * 1000;
            cancel.CancelAfter(timeout);

            try
            {
                // 如果连接已经打开，则不需要重复打开
                if (connection.State == ConnectionState.Open)
                    return;

                //connection.OpenAsync(cancel.Token);
                connection.Open();
            }
            catch (Exception ex)
            {
                var errorMessage = cancel.IsCancellationRequested
                    ? $"打开数据库连接超时（{timeout / 1000}秒）。数据源：{connection.Database}，数据库：{connection.Database}"
                    : $"打开数据库连接失败。数据源：{connection.Database}，数据库：{connection.Database}。错误：{ex.Message}";

                // 记录详细错误信息
                var detailedException = new Exception(errorMessage, ex);

                // 可以在这里添加日志记录
                // Logger.Error(detailedException);

                throw detailedException;
            }
        }

        /*/// <summary>
        /// 配置连接打开的处理方法
        /// </summary>
        /// <param name="config">数据库连接配置</param>
        private static void ConfigureConnectionOpen(ConnectionConfig config)
        {
            config.AopEvents ??= new AopEvents();

            // 在执行SQL前检查并确保连接打开
            config.AopEvents.OnExecutingChangeSql = (sql, pars) =>
            {
                // 异步等待连接打开
                OnOpen((SqlSugarClient)config.Context).Wait();

                return new KeyValuePair<string, SugarParameter[]>(sql, pars);
            };
        }*/
    }
}
