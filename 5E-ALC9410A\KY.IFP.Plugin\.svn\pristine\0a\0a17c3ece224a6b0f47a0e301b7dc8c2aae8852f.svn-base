﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点表配置</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" v-loading="loading">
        <ifp-toolbar close>
            <ifp-button @click="add" log>增加点位</ifp-button>
            <ifp-button @click="modify" log>修改点位</ifp-button>
            <ifp-button @click="remove" log>删除点位</ifp-button>
            <div style="float:right;">
                <span class="dicb dicb-tuxing" style="font-size:16px;color:#0075D3"></span>
                <span style="font-size: 14px; font-weight: bold;">
                    点表配置
                </span>
            </div>
        </ifp-toolbar>
        <ifp-searchbar>
            <ifp-form-item label="标签名称">
                <ifp-input v-model="filter.name" placeholder="支持模糊查询" clear>
                </ifp-input>
            </ifp-form-item>
            <ifp-form-item label="组名">
                <ifp-select v-model="filter.groupCode" :items="zmList"
                            value-key="id"
                            label-key="text">
                </ifp-select>

            </ifp-form-item>
            <ifp-form-item label="标签块号">
                <ifp-input v-model="filter.block" placeholder="支持模糊查询" clear>
                </ifp-input>
            </ifp-form-item>

            <ifp-button role="查询" @click="onSelect">查询</ifp-button>
            <ifp-button role="重置" @click="onReset">重置</ifp-button>
        </ifp-searchbar>
        <ifp-panel-table class="flex-item padding">
            <ifp-table ref="tableRef"
                       :data="data"
                       row-key="GID"
                       :border="true"
                       :highlight-current-row="true"
                       @row-click="rowClick">
                <el-table-column type="index"
                                 :index="indexMethod"
                                 width="50">
                </el-table-column>
                <el-table-column prop="Name"
                                 label="标签名称"
                                 header-align="center"
                                 align="left"
                                 width="200">
                </el-table-column>
                <el-table-column prop="GroupCode"
                                 label="组名"
                                 align="center"
                                 width="80">
                    <template slot-scope="scope">
                        <ifp-select v-model="scope.row.GroupCode" :items="zmList"
                                    value-key="id"
                                    label-key="text"
                                    :canedit="false">
                        </ifp-select>
                    </template>
                </el-table-column>
                <el-table-column prop="DataType"
                                 label="数据类型"
                                 align="center"
                                 width="80">
                    <template slot-scope="scope">
                        <ifp-select v-model="scope.row.DataType"
                                    :items="sjlxList"
                                    value-key="id"
                                    label-key="text"
                                    :canedit="false">
                        </ifp-select>
                    </template>
                </el-table-column>
                <el-table-column prop="Block"
                                 label="标签块号"
                                 header-align="center"
                                 align="right"
                                 width="70">
                </el-table-column>
                <el-table-column prop="ByteOffset"
                                 label="字节偏移"
                                 header-align="center"
                                 align="right"
                                 width="70">
                </el-table-column>
                <el-table-column prop="BitOffset"
                                 label="位偏移"
                                 header-align="center"
                                 align="right"
                                 width="70">

                </el-table-column>
                <el-table-column prop="IOShow"
                                 label="可I/O展示"
                                 align="center"
                                 width="80">
                    <template slot-scope="scope">
                        <ifp-select v-model="scope.row.IOShow"
                                    :items="ioShowList"
                                    value-key="id"
                                    label-key="text"
                                    :canedit="false">

                        </ifp-select>
                    </template>
                </el-table-column>
                <el-table-column prop="Description"
                                 label="说明"
                                 header-align="center"
                                 align="left"
                                 width="280">

                </el-table-column>

            </ifp-table>
            <template v-slot:pagination>
                <ifp-pagination @size-change="sizeChange"
                                @current-change="pageChange"
                                :current-page="paging.page"
                                :page-sizes="[10,20,100,10000]"
                                :page-size="paging.size"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="paging.records">
                </ifp-pagination>
            </template>
        </ifp-panel-table>

        <ifp-dialog :visible.sync="editor.show" :title="editor.title" width="800px">
            <ifp-detail @cancel="editor.show=false;"
                            @submit="editor.show=false,onSelect()"
                            :data="editor.data"
                            :list="ioShowList"></ifp-detail>
        </ifp-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>