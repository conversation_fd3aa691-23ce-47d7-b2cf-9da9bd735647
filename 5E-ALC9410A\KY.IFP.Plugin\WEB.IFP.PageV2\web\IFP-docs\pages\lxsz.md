# 列显设置

继承于 ifp-button

依赖：```ifp-table```

## 示例代码

### 在 ifp-toolbar 中

详见 [工具栏](pages/toolbar#列显设置)

### 单独使用

```html
<ifp-button-lxsz table="table1"></ifp-button-lxsz>

<ifp-table ref="table1">
    <el-table-column></el-table-column>
</ifp-table>
```

步骤

1. 使用 `ifp-table` 替换 `el-table`
1. `ifp-table` 添加 `ref="table1"` 属性,这里的 `table1` 是自定义的
1. 添加列显设置按钮，并配置`table`属性 `<ifp-button-lxsz table="table1"></ifp-button-lxsz>`