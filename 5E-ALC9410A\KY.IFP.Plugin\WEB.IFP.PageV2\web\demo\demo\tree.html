﻿<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>tree</title>
	</head>
	<body controller="tree.js">
		<div class="layout-h" form="forms/toolbar">
			<a id="returnBtn" control="controls/button" option="{command:'close'}">退出</a>
		</div>
		<div form="forms/lefttree" class="layout-v padding">
			<ul id="tree1" control="controls/tree" option="{
			idfield:'id',pidfield:'pid',textfield:'text',
				url:'/com.kysoft.service/html/baseinfo/queryMzfllist.action',postData:{zfbz:0}}"></ul>
		</div>
		<div class="layout-v padding" style="padding-right:0;">
			<div class="layout-h">
				服务端异步加载
			</div>
			<div class="layout-c" style="background:#ccc; width:200px;">
				<ul id="tree2" control="controls/tree" option="{
					idfield:'id',pidfield:'pid',textfield:'text',
					async:true,
					treeoption:{
						async:{
							enable:true,
							url:'treedata.json',
							type:'get'
						}
					}
				}"></ul>
			</div>
		</div>
		<div class="layout-v padding" style="padding-right:0;">
			<div class="layout-h">
				手动加载
			</div>
			<div class="layout-c" style="background:#ccc; width:200px;">
				<ul id="tree3" control="controls/tree" option="{
				idfield:'id',pidfield:'pid',textfield:'text',
				async:true,asyncurl:'data/treedata.json',
					treeoption:{
						async:{
							enable:true,
							url:'treedata.json',
							type:'get'
						}
					}
				}"></ul>
			</div>
		</div>
		<div class="layout-c padding">
			<div class="layout-c" style="background:#ccc;"></div>
		</div>
	</body>
	<script src="/kyadmin/starter.js"></script>
</html>
