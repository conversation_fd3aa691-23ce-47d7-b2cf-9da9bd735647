﻿using System;

namespace COM.IFP.Log
{
    /// <summary>
    /// 定义好的错误列表
    /// </summary>
    public class ErrorList
    {
        #region IOFP常见错误
        /// <summary>
        /// 通信异常,一般是接口调用地址错误
        /// </summary>
        public static ErrorItem E0001 { get { return new ErrorItem("E0001", "通信异常", "一般是接口调用地址错误"); } }
        /// <summary>
        /// 请求超时
        /// </summary>
        public static ErrorItem E0002 { get { return new ErrorItem("E0002", "请求超时"); } }
        /// <summary>
        /// 报文不全
        /// </summary>
        public static ErrorItem E0003 { get { return new ErrorItem("E0003", "报文不全"); } }
        /// <summary>
        /// 鉴权错误
        /// </summary>
        public static ErrorItem E0004 { get { return new ErrorItem("E0004", "鉴权错误"); } }
        /// <summary>
        /// 参数错误
        /// </summary>
        public static ErrorItem E0005 { get { return new ErrorItem("E0005", "参数错误"); } }
        /// <summary>
        /// 运行时错误，应答方运行报错
        /// </summary>
        public static ErrorItem E0006 { get { return new ErrorItem("E0006", "运行时错误", "应答方运行报错"); } }
        #endregion IOFP常见错误

        #region 通用层的错误
        /// <summary>
        /// 登录鉴权失败
        /// </summary>
        public static ErrorItem E1001 { get { return new ErrorItem("E1001", "登录鉴权失败"); } }
        #endregion 通用层的错误

        #region 产品层错误
        /// <summary>
        /// 设备后台程序启动失败
        /// </summary>
        public static ErrorItem E2001 { get { return new ErrorItem("E2001", "设备后台程序启动失败"); } }
        /// <summary>
        /// WebSocket模式启动失败
        /// </summary>
        public static ErrorItem E2002 { get { return new ErrorItem("E2002", "WebSocket模式启动失败"); } }
        /// <summary>
        /// 程序在运行时的异常
        /// </summary>
        public static ErrorItem E2003 { get { return new ErrorItem("E2003", "运行时异常"); } }
        /// <summary>
        /// 必要数据为空的异常
        /// </summary>
        public static ErrorItem E2004 { get { return new ErrorItem("E2004", "必要数据为空"); } }
        #endregion 产品层错误

        /// <summary>
        /// 未知错误
        /// </summary>
        public static ErrorItem E9999 { get { return new ErrorItem("E9999", "未知错误"); } }

        /// <summary>
        /// 通过代码号获取错误实例，如果代码号找不到就返回E9999的错误实例
        /// </summary>
        /// <param name="errorCode">错误代号</param>
        /// <returns>返回错误实例</returns>
        public static ErrorItem GetErrorItem(string errorCode)
        {
            Type t = typeof(ErrorList);
            ErrorItem ei = new ErrorItem();
            var v_pi = t.GetProperties();
            foreach (var v_pi_item in v_pi)
            {
                if (v_pi_item.Name.ToUpper() == errorCode.ToUpper())
                {
                    return v_pi_item.GetValue(ei) as ErrorItem;
                }
            }
            return E9999;
        }
    }

    /// <summary>
    /// 对象:错误
    /// </summary>
    public class ErrorItem
    {
        public ErrorItem()
        {

        }
        /// <summary>
        /// 单项错误的对象
        /// </summary>
        /// <param name="Code">错误代码</param>
        /// <param name="Name">错误名称</param>
        public ErrorItem(string Code, string Name)
        {
            errorCode = Code;
            errorName = Name;
        }
        /// <summary>
        /// 单项错误的对象
        /// </summary>
        /// <param name="Code">错误代码</param>
        /// <param name="Name">错误名称</param>
        /// <param name="Info">错误内容</param>
        public ErrorItem(string Code, string Name, string Info)
        {
            errorCode = Code;
            errorName = Name;
            errorInfo = Info;
        }
        /// <summary>
        /// 错误代码
        /// </summary>
        public string errorCode { set; get; }
        /// <summary>
        /// 错误名称
        /// </summary>
        public string errorName { set; get; }
        /// <summary>
        /// 错误内容
        /// </summary>
        public string errorInfo { set; get; }
    }
}
