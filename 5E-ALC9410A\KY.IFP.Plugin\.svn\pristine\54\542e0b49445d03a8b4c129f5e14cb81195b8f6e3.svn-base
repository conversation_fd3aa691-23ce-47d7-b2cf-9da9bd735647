﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>基础资料</title>
</head>
<body controller="baseinfo.js">
    <div form="forms/toolbar" class="layout-h">
        <a id="tbBtn" control="controls/button" option="{icon:'glyphicon-arrow-down'}" log>同步</a>
        <a id="modifyBtn" control="controls/button" option="{}" log>修改</a>
        <a id="exitBtn" control="controls/button" option="{command:'close'}">退出</a>
        <div style="float:right;">
            <!--<span class="kjicon kjicon-wenhao" style="font-size:16px;color:#0075D3"></span>
            <span style="font-size: 14px; font-weight: bold;padding-right:5px;">
                帮助
            </span>-->
            <span class="dicb dicb-tuxing" style="font-size:16px;color:#0075D3"></span>
            <span style="font-size: 14px; font-weight: bold;">
                基础资料
            </span>
        </div>
    </div>
    <div class="searchbar layout-h">
        <table>
            <tr>
                <td>对象类型：</td>
                <td>
                    <input id="Ywlx" control="controls/select2" option="{showName:'对象类型',controlsUses:2,minimumResultsForSearch:10,
                               idfield:'Ywlx',textfield:'Basename', url:'/API/IFP/Baseinfo/Baseinfo/GetBaseinfoTypeList'}" />
                </td>
                <td class="pg-l-20">对象名称：</td>
                <td>
                    <input id="Bname_LIKE" control="controls/textbox" option="{showName:'名称对象',controlsUses:2}" />
                </td>
                <td class="pg-l-20">作废标志：</td>
                <td>
                    <input id="Zfbz" control="controls/select2" option="{showName:'作废标志',controlsUses:2, data:[{id:'0',text:'正常'},{id:'1',text:'作废'}]}" />
                </td>
                <td class="seartchbar-buttons">
                    <a id="searchBtn" control="controls/button" option="{}">查询</a>
                    <a id="resetBtn" control="controls/button" option="{}">重置</a>
                </td>
            </tr>
        </table>
    </div>
    <div class="layout-c padding">
        <table id="grid" control="controls/grid" option='{"autoresize":true,shrinkToFit:true,pager:"#pager",sortname:"Bname",sortorder:"asc"}'>
            <thead>
                <tr>
                    <th>对象ID</th>
                    <th>Pgid</th>
                    <th>对象名称</th>
                    <th>对象简称</th>
                    <th>对象类型</th>
                    <th>新增时间</th>
                    <th>修改时间</th>
                    <th>作废标志</th>
                    <th>业务编码<!--对应编码末2位--></th>
                    <th>所属单位</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td option="{name:'Gid', align :'center', width:80, key:true}"></td>
                    <td option="{name:'Pgid', hidden:true}"></td>
                    <td option="{name:'Bname', align :'left', width:300}"></td>
                    <td option="{name:'Sname', align :'left', width:150}"></td>
                    <td option="{name:'Ywlx',  align :'left', control:'controls/select2',width:120,
                                        controloption:{idfield:'Ywlx',textfield:'Basename', url:'/API/IFP/BaseInfo/Baseinfo/GetBaseinfoTypeList'}}"></td>
                    <td option="{name:'Addtime', align:'center', width:160}"></td>
                    <td option="{name:'Lasttime', align:'center', width:160}"></td>
                    <td option="{name:'Zfbz', align:'center', control:'controls/select2', width:80, data:[{id:'0',text:'正常'},{id:'1',text:'作废'}]}"></td>
                    <td option="{name:'Ywbm', align:'center', control:'controls/select2', width:80}"></td>
                    <td option="{name:'Compid',hidden:true}"></td>
                </tr>
            </tbody>
        </table>
        <div id="pager"></div>
    </div>
</body>
<script src="/iofp/starter.js"></script>
</html>
