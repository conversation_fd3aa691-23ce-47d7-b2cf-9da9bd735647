﻿namespace COM.IFP.Common
{
    public class Global
    {
        //弃用
        public static class Json
        {
            //private static class DateTimeConverter
            //{
            //    public class NonNullConverter : JsonConverter<DateTime>
            //    {
            //        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
            //        {
            //            if (reader.TokenType == JsonTokenType.String)
            //            {
            //                DateTime.TryParse(reader.GetString(), out DateTime result);
            //                return result;
            //            }
            //            return reader.GetDateTime();
            //        }

            //        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
            //        {
            //            writer.WriteStringValue(value.ToString("yyyy-MM-dd HH:mm:ss"));   //ISO8601
            //        }
            //    }

            //    public class CanNullConverter : JsonConverter<DateTime?>
            //    {
            //        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
            //        {
            //            if (reader.TokenType == JsonTokenType.String)
            //            {
            //                if (DateTime.TryParse(reader.GetString(), out DateTime result))
            //                {
            //                    return result;
            //                }
            //                else
            //                {
            //                    return null;
            //                }
            //            }
            //            return reader.GetDateTime();
            //        }

            //        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
            //        {
            //            writer.WriteStringValue(value?.ToString("yyyy-MM-dd HH:mm:ss"));
            //        }
            //    }

            //    public static NonNullConverter NonNull { get; }
            //    public static CanNullConverter CanNull { get; }
            //    static DateTimeConverter()
            //    {
            //        NonNull = new NonNullConverter();
            //        CanNull = new CanNullConverter();
            //    }
            //}


            //public class StringConverter : JsonConverter<string>
            //{
            //    public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
            //    {
            //        if (reader.TokenType == JsonTokenType.Null)
            //        {
            //            return null;
            //        }
            //        else if (reader.TokenType == JsonTokenType.Number)
            //        {
            //            return reader.GetDecimal().ToString();
            //        }
            //        else
            //        {
            //            return reader.GetString();
            //        }
            //    }

            //    public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
            //    {
            //        writer.WriteStringValue(value);
            //    }
            //}


            //public static JsonSerializerOptions Simple { get; }
            //public static JsonSerializerOptions Indent { get; }

            //static Json()
            //{
            //    Simple = new JsonSerializerOptions()
            //    {
            //        PropertyNameCaseInsensitive = true, //忽略大小写
            //        IgnoreNullValues = true, //忽略null值
            //        NumberHandling = JsonNumberHandling.AllowReadingFromString,
            //        //DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull, //与IgnoreNullValues不能同时设置
            //        Converters = { new JsonStringEnumConverter(), DateTimeConverter.NonNull, DateTimeConverter.CanNull, new StringConverter() }, //自定义转换器
            //        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All) //支持所有字符集（不进行编码转换）
            //    };
            //    Indent = new JsonSerializerOptions()
            //    {
            //        WriteIndented = true,
            //        PropertyNameCaseInsensitive = true, //忽略大小写
            //        IgnoreNullValues = true, //忽略null值
            //        Converters = { new JsonStringEnumConverter(), DateTimeConverter.NonNull, DateTimeConverter.CanNull, new StringConverter() }, //自定义转换器
            //        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All) //支持所有字符集（不进行编码转换）
            //    };
            //}

            /// <summary>
            /// 必须在所有序列化和反序列化操作之前执行，且只能执行一次。
            /// </summary>
            //public static void Init()
            //{
            //    //var option = (JsonSerializerOptions)typeof(JsonSerializerOptions).GetField("s_defaultOptions", BindingFlags.Static | BindingFlags.NonPublic).GetValue(null);
            //    //option.PropertyNameCaseInsensitive = true;    //忽略大小写
            //    //option.IgnoreNullValues = true;
            //    ////option.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull; //与IgnoreNullValues不能同时设置

            //    //option.Converters.Add(new JsonStringEnumConverter()); //枚举类型使用枚举名称而不是枚举值
            //    //option.Converters.Add(DateTimeConverter.NonNull); //自定义日期类型转换
            //    //option.Converters.Add(DateTimeConverter.CanNull); //自定义日期类型转换
            //    //option.Converters.Add(new StringConverter()); //自定义字符类型转换

            //    //option.Encoder = System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All);   //支持所有字符集（不进行编码转换）
            //    Newtonsoft.Json.JsonConvert.DefaultSettings = () =>
            //    {
            //        var settings = new Newtonsoft.Json.JsonSerializerSettings();
            //        settings.DateFormatString = "yyyy-MM-dd HH:mm:ss";  //时间格式化
            //        settings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;  //不包含空值，可节省空间
            //        settings.Converters.Add(new Newtonsoft.Json.Converters.StringEnumConverter() { AllowIntegerValues = true });    //枚举序列成名称而非枚举值

            //        //不使用驼峰样式的key
            //        //options.SerializerSettings.ContractResolver =new LowercaseContractResolver();
            //        settings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();
            //        return settings;
            //    };
            //}
        }
    }
}
