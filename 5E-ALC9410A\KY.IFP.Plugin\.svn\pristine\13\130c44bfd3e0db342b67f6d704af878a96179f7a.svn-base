
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>dicb控件</title>
</head>
<body controller="controls.js">
    <div form="forms/toolbar" class="layout-h">
        <a id="returnBtn" control="controls/button" option="{command:'close'}">退出</a>
        <a id="selBtn" control="controls/button" option="{icon:'保存'}">按钮</a>
        <a id="selBtn2" control="controls/button" option="{icon:'保存',command:'clearsel'}">清空下来选项</a>
    </div>
    <div class="layout-c padding">
        <div id="tabsPages1" control="controls/tabs" option="{autoresize:true}">
            <div option="{title:'输入控件',active:true}" class="padding" style="border:1px solid #ccc;border-top: 0;">
                <div class="form-inline">
                    <div class="form-group">
                        自动补全：

                        <span>
                            <input id="sel3" control="controls/select2" option="{
                                minimumResultsForSearch:0,
                                ajax:{ url: '/xxxx/select2', delay: 0, dataType: 'json' }
                            }" />
                        </span>
                    </div>
                </div>
            </div>
            <div option="{title:'表格'}">
                <table id="grid33" control="controls/grid" option='{
                    url:"data/base.json",
                    cellEdit:true,
                    postData : {},
                    pager:"#pager1"
                }'>
                    <thead>
                        <tr>
                            <th option="{start:'col1',cols:2}">middle1</th>
                            <th option="{start:'col3',cols:1}">middle2</th>
                        </tr>
                        <tr>
                            <th>操作</th>
                            <th>关系</th>
                            <th>区间</th>
                            <th>运算符</th>
                            <th>指标</th>
                            <th>运算符</th>
                            <th>区间2</th>
                            <th>颜色</th>
                            <th>日期</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td option="{name:'gid',hidden:true,key: true}"></td>
                            <td option="{name:'jobName',editable:false}"></td>
                            <td option="{name:'col1',editable:true,control:'controls/select2',controloption:{
                                minimumResultsForSearch:10,
                                ajax:{ url: '/xxxx/select2', delay: 0, dataType: 'json' }
                            }}"></td>
                            <td option="{name:'col2',editable:false}"></td>
                            <td option="{name:'col3',editable:false}"></td>
                            <td option="{name:'col4',editable:false}"></td>
                            <td option="{name:'col5',editable:false}"></td>
                            <td option="{name:'col6',editable:false}"></td>
                            <td option="{name:'col7',editable:false}"></td>
                        </tr>
                    </tbody>
                </table>
                <div id="pager1"></div>
            </div>
            <div option="{title:'页签3'}">
                <p>333333333。</p>
            </div>
        </div>
    </div>
    <script src="../../kyadmin/starter.js"></script> 
</body>
</html>