/**
*
* @license Guriddo jqGrid JS - v5.4.0 
* Copyright(c) 2008, <PERSON>, <EMAIL>
* 
* License: http://guriddo.net/?page_id=103334
*/
!function(a){"use strict";"function"==typeof define&&define.amd?define(["jquery","./grid.base","./jqModal","./jqDnR"],a):a(jQuery)}(function(a){"use strict";a.extend(a.jgrid,{showModal:function(a){a.w.show()},closeModal:function(a){a.w.hide().attr("aria-hidden","true"),a.o&&a.o.remove()},hideModal:function(b,c){c=a.extend({jqm:!0,gb:"",removemodal:!1,formprop:!1,form:""},c||{});var d=!(!c.gb||"string"!=typeof c.gb||"#gbox_"!==c.gb.substr(0,6))&&a("#"+c.gb.substr(6))[0];if(c.onClose){var e=d?c.onClose.call(d,b):c.onClose(b);if("boolean"==typeof e&&!e)return}if(c.formprop&&d&&c.form){var f=a(b)[0].style.height,g=a(b)[0].style.width;f.indexOf("px")>-1&&(f=parseFloat(f)),g.indexOf("px")>-1&&(g=parseFloat(g));var h,i;"edit"===c.form?(h="#"+a.jgrid.jqID("FrmGrid_"+c.gb.substr(6)),i="formProp"):"view"===c.form&&(h="#"+a.jgrid.jqID("ViewGrid_"+c.gb.substr(6)),i="viewProp"),a(d).data(i,{top:parseFloat(a(b).css("top")),left:parseFloat(a(b).css("left")),width:g,height:f,dataheight:a(h).height(),datawidth:a(h).width()})}if(a.fn.jqm&&!0===c.jqm)a(b).attr("aria-hidden","true").jqmHide();else{if(""!==c.gb)try{a(".jqgrid-overlay:first",c.gb).hide()}catch(a){}try{a(".jqgrid-overlay-modal").hide()}catch(a){}a(b).hide().attr("aria-hidden","true")}c.removemodal&&a(b).remove()},findPos:function(b){var c=a(b).offset();return[c.left,c.top]},createModal:function(b,c,d,e,f,g,h){d=a.extend(!0,{},a.jgrid.jqModal||{},d);var i=this,j="rtl"===a(d.gbox).attr("dir"),k=a.jgrid.styleUI[d.styleUI||"jQueryUI"].modal,l=a.jgrid.styleUI[d.styleUI||"jQueryUI"].common,m=document.createElement("div");h=a.extend({},h||{}),m.className="ui-jqdialog "+k.modal,m.id=b.themodal;var n=document.createElement("div");n.className="ui-jqdialog-titlebar "+k.header,n.id=b.modalhead,a(n).append("<span class='ui-jqdialog-title'>"+d.caption+"</span>");var o=a("<a class='ui-jqdialog-titlebar-close "+l.cornerall+"'></a>").hover(function(){o.addClass(l.hover)},function(){o.removeClass(l.hover)}).append("<span class='"+l.icon_base+" "+k.icon_close+"'></span>");a(n).append(o),j?(m.dir="rtl",a(".ui-jqdialog-title",n).css("float","right"),a(".ui-jqdialog-titlebar-close",n).css("left","0.3em")):(m.dir="ltr",a(".ui-jqdialog-title",n).css("float","left"),a(".ui-jqdialog-titlebar-close",n).css("right","0.3em"));var p=document.createElement("div");a(p).addClass("ui-jqdialog-content "+k.content).attr("id",b.modalcontent),a(p).append(c),m.appendChild(p),a(m).prepend(n),!0===g?a("body").append(m):"string"==typeof g?a(g).append(m):a(m).insertBefore(e),a(m).css(h),void 0===d.jqModal&&(d.jqModal=!0);var q={};if(a.fn.jqm&&!0===d.jqModal){if(0===d.left&&0===d.top&&d.overlay){var r=[];r=a.jgrid.findPos(f),d.left=r[0]+4,d.top=r[1]+4}q.top=d.top+"px",q.left=d.left}else 0===d.left&&0===d.top||(q.left=d.left,q.top=d.top+"px");if(a("a.ui-jqdialog-titlebar-close",n).click(function(){var c=a("#"+a.jgrid.jqID(b.themodal)).data("onClose")||d.onClose,e=a("#"+a.jgrid.jqID(b.themodal)).data("gbox")||d.gbox;return i.hideModal("#"+a.jgrid.jqID(b.themodal),{gb:e,jqm:d.jqModal,onClose:c,removemodal:d.removemodal||!1,formprop:!d.recreateForm||!1,form:d.form||""}),!1}),0!==d.width&&d.width||(d.width=300),0!==d.height&&d.height||(d.height=200),!d.zIndex){var s=a(e).parents("*[role=dialog]").filter(":first").css("z-index");d.zIndex=s?parseInt(s,10)+2:950}var t=0;if(j&&q.left&&!g&&(t=a(d.gbox).width()-(isNaN(d.width)?0:parseInt(d.width,10))-8,q.left=parseInt(q.left,10)+parseInt(t,10)),q.left&&(q.left+="px"),a(m).css(a.extend({width:isNaN(d.width)?"auto":d.width+"px",height:isNaN(d.height)?"auto":d.height+"px",zIndex:d.zIndex,overflow:"hidden"},q)).attr({tabIndex:"-1",role:"dialog","aria-labelledby":b.modalhead,"aria-hidden":"true"}),void 0===d.drag&&(d.drag=!0),void 0===d.resize&&(d.resize=!0),d.drag)if(a(n).css("cursor","move"),a.fn.tinyDraggable)a(m).tinyDraggable({handle:"#"+a.jgrid.jqID(n.id)});else try{a(m).draggable({handle:a("#"+a.jgrid.jqID(n.id))})}catch(a){}if(d.resize)if(a.fn.jqResize)a(m).append("<div class='jqResize "+k.resizable+" "+l.icon_base+" "+k.icon_resizable+"'></div>"),a("#"+a.jgrid.jqID(b.themodal)).jqResize(".jqResize",!!b.scrollelm&&"#"+a.jgrid.jqID(b.scrollelm));else try{a(m).resizable({handles:"se, sw",alsoResize:!!b.scrollelm&&"#"+a.jgrid.jqID(b.scrollelm)})}catch(a){}!0===d.closeOnEscape&&a(m).keydown(function(c){if(27===c.which){var e=a("#"+a.jgrid.jqID(b.themodal)).data("onClose")||d.onClose;i.hideModal("#"+a.jgrid.jqID(b.themodal),{gb:d.gbox,jqm:d.jqModal,onClose:e,removemodal:d.removemodal||!1,formprop:!d.recreateForm||!1,form:d.form||""})}})},viewModal:function(b,c){c=a.extend({toTop:!0,overlay:10,modal:!1,overlayClass:"ui-widget-overlay",onShow:a.jgrid.showModal,onHide:a.jgrid.closeModal,gbox:"",jqm:!0,jqM:!0},c||{});var d="";if(c.gbox){var e=a("#"+c.gbox.substring(6))[0];try{d=a(e).jqGrid("getStyleUI",e.p.styleUI+".common","overlay",!1,"jqgrid-overlay-modal"),c.overlayClass=a(e).jqGrid("getStyleUI",e.p.styleUI+".common","overlay",!0)}catch(a){}}if(void 0===c.focusField&&(c.focusField=0),"number"==typeof c.focusField&&c.focusField>=0?c.focusField=parseInt(c.focusField,10):"boolean"!=typeof c.focusField||c.focusField?c.focusField=0:c.focusField=!1,a.fn.jqm&&!0===c.jqm)c.jqM?a(b).attr("aria-hidden","false").jqm(c).jqmShow():a(b).attr("aria-hidden","false").jqmShow();else{if(""!==c.gbox){var f=parseInt(a(b).css("z-index"))-1;c.modal?(a(".jqgrid-overlay-modal")[0]||a("body").prepend("<div "+d+"></div>"),a(".jqgrid-overlay-modal").css("z-index",f).show()):(a(".jqgrid-overlay:first",c.gbox).css("z-index",f).show(),a(b).data("gbox",c.gbox))}if(a(b).show().attr("aria-hidden","false"),c.focusField>=0)try{a(":input:visible",b)[c.focusField].focus()}catch(a){}}},info_dialog:function(b,c,d,e){var f={width:290,height:"auto",dataheight:"auto",drag:!0,resize:!1,left:250,top:170,zIndex:1e3,jqModal:!0,modal:!1,closeOnEscape:!0,align:"center",buttonalign:"center",buttons:[]};a.extend(!0,f,a.jgrid.jqModal||{},{caption:"<b>"+b+"</b>"},e||{});var g=f.jqModal,h=this,i=a.jgrid.styleUI[f.styleUI||"jQueryUI"].modal,j=a.jgrid.styleUI[f.styleUI||"jQueryUI"].common;a.fn.jqm&&!g&&(g=!1);var k,l="";if(f.buttons.length>0)for(k=0;k<f.buttons.length;k++)void 0===f.buttons[k].id&&(f.buttons[k].id="info_button_"+k),l+="<a id='"+f.buttons[k].id+"' class='fm-button "+j.button+"'>"+f.buttons[k].text+"</a>";var m=isNaN(f.dataheight)?f.dataheight:f.dataheight+"px",n="text-align:"+f.align+";",o="<div id='info_id'>";o+="<div id='infocnt' style='margin:0px;padding-bottom:1em;width:100%;overflow:auto;position:relative;height:"+m+";"+n+"'>"+c+"</div>",o+=d?"<div class='"+i.content+"' style='text-align:"+f.buttonalign+";padding-bottom:0.8em;padding-top:0.5em;background-image: none;border-width: 1px 0 0 0;'><a id='closedialog' class='fm-button "+j.button+"'>"+d+"</a>"+l+"</div>":""!==l?"<div class='"+i.content+"' style='text-align:"+f.buttonalign+";padding-bottom:0.8em;padding-top:0.5em;background-image: none;border-width: 1px 0 0 0;'>"+l+"</div>":"",o+="</div>";try{"false"===a("#info_dialog").attr("aria-hidden")&&a.jgrid.hideModal("#info_dialog",{jqm:g}),a("#info_dialog").remove()}catch(a){}var p=a(".ui-jqgrid").css("font-size")||"11px";a.jgrid.createModal({themodal:"info_dialog",modalhead:"info_head",modalcontent:"info_content",scrollelm:"infocnt"},o,f,"","",!0,{"font-size":p}),l&&a.each(f.buttons,function(b){a("#"+a.jgrid.jqID(this.id),"#info_id").on("click",function(){return f.buttons[b].onClick.call(a("#info_dialog")),!1})}),a("#closedialog","#info_id").on("click",function(){return h.hideModal("#info_dialog",{jqm:g,onClose:a("#info_dialog").data("onClose")||f.onClose,gb:a("#info_dialog").data("gbox")||f.gbox}),!1}),a(".fm-button","#info_dialog").hover(function(){a(this).addClass(j.hover)},function(){a(this).removeClass(j.hover)}),a.isFunction(f.beforeOpen)&&f.beforeOpen(),a.jgrid.viewModal("#info_dialog",{onHide:function(a){a.w.hide().remove(),a.o&&a.o.remove()},modal:f.modal,jqm:g}),a.isFunction(f.afterOpen)&&f.afterOpen();try{a("#info_dialog").focus()}catch(a){}},bindEv:function(b,c){var d=this;a.isFunction(c.dataInit)&&c.dataInit.call(d,b,c),c.dataEvents&&a.each(c.dataEvents,function(){void 0!==this.data?a(b).on(this.type,this.data,this.fn):a(b).on(this.type,this.fn)})},createEl:function(b,c,d,e,f){function g(b,c,d){var e=["dataInit","dataEvents","dataUrl","buildSelect","sopt","searchhidden","defaultValue","attr","custom_element","custom_value","oper"];e=e.concat(["cacheUrlData","delimiter","separator"]),void 0!==d&&a.isArray(d)&&a.merge(e,d),a.each(c,function(c,d){-1===a.inArray(c,e)&&a(b).attr(c,d)}),c.hasOwnProperty("id")||a(b).attr("id",a.jgrid.randId())}var h="",i=this;switch(b){case"textarea":h=document.createElement("textarea"),e?c.cols||a(h).css({width:"98%"}):c.cols||(c.cols=20),c.rows||(c.rows=2),("&nbsp;"===d||"&#160;"===d||1===d.length&&160===d.charCodeAt(0))&&(d=""),h.value=d,a(h).attr({role:"textbox",multiline:"true"}),g(h,c);break;case"checkbox":if(h=document.createElement("input"),h.type="checkbox",c.value){var j=c.value.split(":");d===j[0]&&(h.checked=!0,h.defaultChecked=!0),h.value=j[0],a(h).attr("offval",j[1])}else{var k=(d+"").toLowerCase();k.search(/(false|f|0|no|n|off|undefined)/i)<0&&""!==k?(h.checked=!0,h.defaultChecked=!0,h.value=d):h.value="on",a(h).attr("offval","off")}a(h).attr("role","checkbox"),g(h,c,["value"]);break;case"select":h=document.createElement("select"),h.setAttribute("role","select");var l,m=[];if(!0===c.multiple?(l=!0,h.multiple="multiple",a(h).attr("aria-multiselectable","true")):l=!1,null!=c.dataUrl){var n=null,o=c.postData||f.postData;try{n=c.rowId}catch(a){}i.p&&i.p.idPrefix&&(n=a.jgrid.stripPref(i.p.idPrefix,n)),a.ajax(a.extend({url:a.isFunction(c.dataUrl)?c.dataUrl.call(i,n,d,String(c.name)):c.dataUrl,type:"GET",dataType:"html",data:a.isFunction(o)?o.call(i,n,d,String(c.name)):o,context:{elem:h,options:c,vl:d},success:function(b){var c,d=[],e=this.elem,f=this.vl,h=a.extend({},this.options),j=!0===h.multiple,k=!0===h.cacheUrlData,l="",m=[],n=a.isFunction(h.buildSelect)?h.buildSelect.call(i,b):b;if("string"==typeof n&&(n=a(a.trim(n)).html()),n){if(a(e).append(n),g(e,h,o?["postData"]:void 0),void 0===h.size&&(h.size=j?3:1),j?(d=f.split(","),d=a.map(d,function(b){return a.trim(b)})):d[0]=a.trim(f),a("option",e).each(function(b){c=a(this).text(),f=a(this).val(),k&&(l+=(0!==b?";":"")+f+":"+c),0===b&&e.multiple&&(this.selected=!1),a(this).attr("role","option"),(a.inArray(a.trim(c),d)>-1||a.inArray(a.trim(f),d)>-1)&&(this.selected="selected",m.push(f))}),h.hasOwnProperty("checkUpdate")&&h.checkUpdate&&(i.p.savedData[h.name]=m.join(",")),k)if("edit"===h.oper)a(i).jqGrid("setColProp",h.name,{editoptions:{buildSelect:null,dataUrl:null,value:l}});else if("search"===h.oper)a(i).jqGrid("setColProp",h.name,{searchoptions:{dataUrl:null,value:l}});else if("filter"===h.oper&&a("#fbox_"+i.p.id)[0].p){var p,q=a("#fbox_"+i.p.id)[0].p.columns;a.each(q,function(a){if(p=this.index||this.name,h.name===p)return this.searchoptions.dataUrl=null,this.searchoptions.value=l,!1})}a(i).triggerHandler("jqGridAddEditAfterSelectUrlComplete",[e])}}},f||{}))}else if(c.value){var p;void 0===c.size&&(c.size=l?3:1),l&&(m=d.split(","),m=a.map(m,function(b){return a.trim(b)})),"function"==typeof c.value&&(c.value=c.value());var q,r,s,t,u,v,w=void 0===c.separator?":":c.separator,x=void 0===c.delimiter?";":c.delimiter;if("string"==typeof c.value)for(q=c.value.split(x),p=0;p<q.length;p++)r=q[p].split(w),r.length>2&&(r[1]=a.map(r,function(a,b){if(b>0)return a}).join(w)),s=document.createElement("option"),s.setAttribute("role","option"),s.value=r[0],s.innerHTML=r[1],h.appendChild(s),l||a.trim(r[0])!==a.trim(d)&&a.trim(r[1])!==a.trim(d)||(s.selected="selected"),l&&(a.inArray(a.trim(r[1]),m)>-1||a.inArray(a.trim(r[0]),m)>-1)&&(s.selected="selected");else if("[object Array]"===Object.prototype.toString.call(c.value))for(t=c.value,p=0;p<t.length;p++)2===t[p].length&&(u=t[p][0],v=t[p][1],s=document.createElement("option"),s.setAttribute("role","option"),s.value=u,s.innerHTML=v,h.appendChild(s),l||a.trim(u)!==a.trim(d)&&a.trim(v)!==a.trim(d)||(s.selected="selected"),l&&(a.inArray(a.trim(v),m)>-1||a.inArray(a.trim(u),m)>-1)&&(s.selected="selected"));else if("object"==typeof c.value){t=c.value;for(u in t)t.hasOwnProperty(u)&&(s=document.createElement("option"),s.setAttribute("role","option"),s.value=u,s.innerHTML=t[u],h.appendChild(s),l||a.trim(u)!==a.trim(d)&&a.trim(t[u])!==a.trim(d)||(s.selected="selected"),l&&(a.inArray(a.trim(t[u]),m)>-1||a.inArray(a.trim(u),m)>-1)&&(s.selected="selected"))}g(h,c,["value"])}else g(h,c);break;case"image":case"file":h=document.createElement("input"),h.type=b,g(h,c);break;case"custom":h=document.createElement("span");try{if(!a.isFunction(c.custom_element))throw"e1";var y=c.custom_element.call(i,d,c);if(!y)throw"e2";y=a(y).addClass("customelement").attr({id:c.id,name:c.name}),a(h).empty().append(y)}catch(b){var z=a.jgrid.getRegional(i,"errors"),A=a.jgrid.getRegional(i,"edit");"e1"===b?a.jgrid.info_dialog(z.errcap,"function 'custom_element' "+A.msg.nodefined,A.bClose,{styleUI:i.p.styleUI}):"e2"===b?a.jgrid.info_dialog(z.errcap,"function 'custom_element' "+A.msg.novalue,A.bClose,{styleUI:i.p.styleUI}):a.jgrid.info_dialog(z.errcap,"string"==typeof b?b:b.message,A.bClose,{styleUI:i.p.styleUI})}break;default:var B;B="button"===b?"button":"textbox",h=document.createElement("input"),h.type=b,h.value=d,"button"!==b&&(e?c.size||a(h).css({width:"96%"}):c.size||(c.size=20)),a(h).attr("role",B),g(h,c)}return h},checkDate:function(a,b){var c,d=function(a){return a%4!=0||a%100==0&&a%400!=0?28:29},e={};if(a=a.toLowerCase(),c=-1!==a.indexOf("/")?"/":-1!==a.indexOf("-")?"-":-1!==a.indexOf(".")?".":"/",a=a.split(c),b=b.split(c),3!==b.length)return!1;var f,g,h=-1,i=-1,j=-1;for(g=0;g<a.length;g++){var k=isNaN(b[g])?0:parseInt(b[g],10);e[a[g]]=k,f=a[g],-1!==f.indexOf("y")&&(h=g),-1!==f.indexOf("m")&&(j=g),-1!==f.indexOf("d")&&(i=g)}f="y"===a[h]||"yyyy"===a[h]?4:"yy"===a[h]?2:-1;var l,m=[0,31,29,31,30,31,30,31,31,30,31,30,31];return-1!==h&&(l=e[a[h]].toString(),2===f&&1===l.length&&(f=1),l.length===f&&(0!==e[a[h]]||"00"===b[h])&&(-1!==j&&(l=e[a[j]].toString(),!(l.length<1||e[a[j]]<1||e[a[j]]>12)&&(-1!==i&&(l=e[a[i]].toString(),!(l.length<1||e[a[i]]<1||e[a[i]]>31||2===e[a[j]]&&e[a[i]]>d(e[a[h]])||e[a[i]]>m[e[a[j]]]))))))},isEmpty:function(a){return!(void 0!==a&&!a.match(/^\s+$/)&&""!==a)},checkTime:function(b){var c,d=/^(\d{1,2}):(\d{2})([apAP][Mm])?$/;if(!a.jgrid.isEmpty(b)){if(!(c=b.match(d)))return!1;if(c[3]){if(c[1]<1||c[1]>12)return!1}else if(c[1]>23)return!1;if(c[2]>59)return!1}return!0},checkValues:function(b,c,d,e){var f,g,h,i,j,k,l=this,m=l.p.colModel,n=a.jgrid.getRegional(this,"edit.msg"),o=function(a){var a=a.toString();if(a.length>=2){var b,c;if("-"===a[0]?(b=a[1],a[2]&&(c=a[2])):(b=a[0],a[1]&&(c=a[1])),"0"===b&&"."!==c)return!1}return"number"==typeof parseFloat(a)&&isFinite(a)};if(void 0===d)if("string"==typeof c){for(g=0,j=m.length;g<j;g++)if(m[g].name===c){f=m[g].editrules,c=g,null!=m[g].formoptions&&(h=m[g].formoptions.label);break}}else c>=0&&(f=m[c].editrules);else f=d,h=void 0===e?"_":e;if(f){if(h||(h=null!=l.p.colNames?l.p.colNames[c]:m[c].label),!0===f.required&&a.jgrid.isEmpty(b))return[!1,h+": "+n.required,""];var p=!1!==f.required;if(!0===f.number&&!(!1===p&&a.jgrid.isEmpty(b)||o(b)))return[!1,h+": "+n.number,""];if(void 0!==f.minValue&&!isNaN(f.minValue)&&parseFloat(b)<parseFloat(f.minValue))return[!1,h+": "+n.minValue+" "+f.minValue,""];if(void 0!==f.maxValue&&!isNaN(f.maxValue)&&parseFloat(b)>parseFloat(f.maxValue))return[!1,h+": "+n.maxValue+" "+f.maxValue,""];var q;if(!0===f.email&&!(!1===p&&a.jgrid.isEmpty(b)||(q=/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?$/i,q.test(b))))return[!1,h+": "+n.email,""];if(!0===f.integer&&(!1!==p||!a.jgrid.isEmpty(b))){if(!o(b))return[!1,h+": "+n.integer,""];if(b%1!=0||-1!==b.indexOf("."))return[!1,h+": "+n.integer,""]}if(!0===f.date&&!(!1===p&&a.jgrid.isEmpty(b)||(m[c].formatoptions&&m[c].formatoptions.newformat?(i=m[c].formatoptions.newformat,(k=a.jgrid.getRegional(l,"formatter.date.masks"))&&k.hasOwnProperty(i)&&(i=k[i])):i=m[c].datefmt||"Y-m-d",a.jgrid.checkDate(i,b))))return[!1,h+": "+n.date+" - "+i,""];if(!0===f.time&&!(!1===p&&a.jgrid.isEmpty(b)||a.jgrid.checkTime(b)))return[!1,h+": "+n.date+" - hh:mm (am/pm)",""];if(!0===f.url&&!(!1===p&&a.jgrid.isEmpty(b)||(q=/^(((https?)|(ftp)):\/\/([\-\w]+\.)+\w{2,3}(\/[%\-\w]+(\.\w{2,})?)*(([\w\-\.\?\\\/+@&#;`~=%!]*)(\.\w{2,})?)*\/?)/i,q.test(b))))return[!1,h+": "+n.url,""];if(!0===f.custom&&(!1!==p||!a.jgrid.isEmpty(b))){if(a.isFunction(f.custom_func)){var r=f.custom_func.call(l,b,h,c);return a.isArray(r)?r:[!1,n.customarray,""]}return[!1,n.customfcheck,""]}}return[!0,"",""]},validateForm:function(b){var c,d,e=!0;for(c=0;c<b.elements.length;c++)if(d=b.elements[c],("INPUT"===d.nodeName||"TEXTAREA"===d.nodeName||"SELECT"===d.nodeName)&&(void 0!==d.willValidate?("INPUT"===d.nodeName&&d.type!==d.getAttribute("type")&&d.setCustomValidity(a.jgrid.LegacyValidation(d)?"":"error"),d.reportValidity()):(d.validity=d.validity||{},d.validity.valid=a.jgrid.LegacyValidation(d)),!d.validity.valid)){e=!1;break}return e},LegacyValidation:function(a){var b=!0,c=a.value,d=a.getAttribute("type"),e="checkbox"===d||"radio"===d,f=a.getAttribute("required"),g=a.getAttribute("minlength"),h=a.getAttribute("maxlength"),i=a.getAttribute("pattern");return a.disabled?b:(b=b&&(!f||e&&a.checked||!e&&""!==c),b=b&&(e||(!g||c.length>=g)&&(!h||c.length<=h)),b&&i&&(i=new RegExp(i),b=i.test(c)),b)},buildButtons:function(b,c,d){var e,f;return a.each(b,function(b,g){g.id||(g.id=a.jgrid.randId()),g.position||(g.position="last"),g.side||(g.side="left"),e=g.icon?" fm-button-icon-"+g.side+"'><span class='"+d.icon_base+" "+g.icon+"'></span>":"'>",f="<a  data-index='"+b+"' id='"+g.id+"' class='fm-button "+d.button+e+g.text+"</a>","last"===g.position?c+=f:c=f+c}),c},setSelNavIndex:function(b,c){var d=a(".ui-pg-button",b.p.pager);a.each(d,function(a,d){if(c===d)return b.p.navIndex=a,!1}),a(c).attr("tabindex","0")},getFirstVisibleCol:function(a){for(var b=-1,c=0;c<a.p.colModel.length;c++)if(!0!==a.p.colModel[c].hidden){b=c;break}return b}})});