﻿define(["iofp/api", "iofp/util"], function (API, util) {

    return {
        //按钮权限
        initBtnRight: function (_this) {
            this.setButtonShow(_this);
        },
        //读取后台数据库存储的显示列配置，并刷新界面
        setButtonShow: function (_this) {
            //页面地址，例如：/pages/sys/liexian/index.html
            var basepage = util.urlGetPageID();
            //弹出页或子页面的URL
            var pageurl = _this.$getPageInfo().menuurl;
            if (pageurl == null) {//pageurl == null 说明是子页面
                pageurl = _this.$getPageInfo().url;
            }

            //需要控制列显的表格
            API.GetAction("API/IFP/Rights/User/GetHiddenBtnByUser", { basepage: basepage, pageurl: pageurl }).then(x => {
                var btn = {};
                x.forEach((one) => {
                    btn[one] = true
                });
                _this.btnRight = btn;
            }).catch(e => {
                _this.$message.error(e);
            });
        }

    }
});