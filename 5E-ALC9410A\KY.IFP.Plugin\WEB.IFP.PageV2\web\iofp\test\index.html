<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>createApi 测试</title>
    <style>
        li.success .state{
            color:#0e0
        }
        li.error .state,
        li.error span{
            color:#e00
        }
    </style>
</head>
<body controller option="{platform:'element'}">
    <div id="app" class="padding">
        <h2>IFP 测试</h2>
        <ul v-for="group in message">
            <h3>{{group.name}}</h3>
            <li :class="item.type" v-for="item in group.message">
                <label class="state">{{item.type|stateName}}</label>
                <label>{{item.name}}</label>
                <span>{{item.content}}</span>
            </li>
        </ul>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>