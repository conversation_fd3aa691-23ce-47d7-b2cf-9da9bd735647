define(["iofp/api","zutil","iofp/services/service.ywlx"],function(API,zutil,ywlxService){
    
    function create(args){
        return zutil.extend(true,{
            Gid: "",		//GID
            Pgid: null,		//上级对象GID
            Bname: null,		//对象名称
            Sname: null,		//对象简称
            Ywlx: '4001',		//业务类型
            Compid: null,		//所属单位
            Addtime: null,		//新增时间
            Lasttime: null,		//修改时间
            Zfbz: 0,		//作废标志（1作废  0正常）
            Ywbm: null,		//业务编码
            Ghdw4002: 0,		//供货单位
            Fz4006: null,		//发展
            Ncl: null,		//年产量（万吨）
            Zbwz: null,		//坐标位置
            Ssdq1015: null,		//所属地区枚举 --1015
            Kdbm: null,		//存储矿点对应的编码
            Ssqy2010: null		//所属区域
        },args);
    }


    var btntexts = {
        view:"查看",
        delete:"删除",
        edit:"修改",
        add:"新增"
    }


    return {
        name:"demoMineralDetail",
        el:"#app",
        props:{
            state:{default:"view"}, //add update delete view
            id:{default:""},
            source:{
                default:function(){
                    return create();
                }
            }
        },
        computed:{
            submittext(){
                return btntexts[this.state]
            }
        },
        data(){
            return {
                dialog:false,
                editor:{
                    header:"header",
                    source: create(this.source)
                }
            }
        },
        created(){

        },
        methods:{
            create: function () {
            },
            onSubmit(){
                API.GetAction("API/ICS/BaseData/Mineral/Submit", this.editor.source)
                .then(x => {
                  this.$message.success(btntexts[this.state]+'成功');
                  this.$emit("success")
                }).catch(e => {
                  this.$message.error(e);
                });
            },
            onCancel(){
                this.$emit("cancel")
            }
        }
    }
})