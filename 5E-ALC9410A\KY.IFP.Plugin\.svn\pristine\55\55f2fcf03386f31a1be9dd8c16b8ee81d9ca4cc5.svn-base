parcelRequire=function(e,r,t,n){var i,o="function"==typeof parcelRequire&&parcelRequire,u="function"==typeof require&&require;function f(t,n){if(!r[t]){if(!e[t]){var i="function"==typeof parcelRequire&&parcelRequire;if(!n&&i)return i(t,!0);if(o)return o(t,!0);if(u&&"string"==typeof t)return u(t);var c=new Error("Cannot find module '"+t+"'");throw c.code="MODULE_NOT_FOUND",c}p.resolve=function(r){return e[t][1][r]||r},p.cache={};var l=r[t]=new f.Module(t);e[t][0].call(l.exports,p,l,l.exports,this)}return r[t].exports;function p(e){return f(p.resolve(e))}}f.isParcelRequire=!0,f.Module=function(e){this.id=e,this.bundle=f,this.exports={}},f.modules=e,f.cache=r,f.parent=o,f.register=function(r,t){e[r]=[function(e,r){r.exports=t},{}]};for(var c=0;c<t.length;c++)try{f(t[c])}catch(e){i||(i=e)}if(t.length){var l=f(t[t.length-1]);"object"==typeof exports&&"undefined"!=typeof module?module.exports=l:"function"==typeof define&&define.amd?define(function(){return l}):n&&(this[n]=l)}if(parcelRequire=f,i)throw i;return f}({"mgj5":[function(require,module,exports) {
module.exports=[{code:1,label:"无瓶",name:"empty"},{code:2,label:"待化验",name:"dhy"},{code:3,label:"检测中",name:"hyz"},{code:4,label:"待回传",name:"dhc"},{code:5,label:"待复检",name:"dfj"},{code:6,label:"异常",name:"error"}];
},{}],"r2ni":[function(require,module,exports) {

},{}],"Focm":[function(require,module,exports) {
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.Control=void 0,Object.defineProperty(exports,"states",{enumerable:!0,get:function(){return t.default}});var t=e(require("./states.json"));function e(t){return t&&t.__esModule?t:{default:t}}require("./index.less");var i={props:{tooltip:{type:[Function,null],default:null},enabled:{type:Boolean,default:!0},selectState:{type:Array,default:function(){return[5]}},rows:{type:Number,default:5},cols:{type:Number,default:4},states:{type:Array,default:function(){return t.default}},items:{type:Array,default:function(){return[]}}},template:'\n    <div class="zxcsypj">\n        <div class="zxcsypj-chart">\n            <div v-for="rowitems in groups" class="zxcsypj-row">\n                <el-tooltip :key="item.xh" v-for="item in rowitems" :disabled="item.tooltip==null">\n                    <div v-if="item.tooltip!==null" slot="content" v-html="item.tooltip"></div>\n                    <div\n                        @click="item_click(item)"\n                        class="zxcsypj-col" \n                        :class="{\n                            [\'zxcsypj-state-\'+item.name]:true,\n                            canselect:item.canselect && enabled,\n                            selected:selectXH===item.xh\n                        }"\n                    ></div>\n                </el-tooltip>\n            </div>\n        </div>\n        <div class="zxcsypj-legend">\n            <div v-for="item in states"><div :class="\'zxcsypj-state-\'+item.name"></div>{{item.label}}</div>\n        </div>\n    </div>\n    ',computed:{stateMap:function(){return Object.fromEntries(this.states.map(function(t){return[t.code,t.name]}))},groups:function(){var t=this,e=Array(this.rows).fill().map(function(){return Array(t.cols).fill().map(function(){return{name:"null",label:"",state:null}})});return this.items.forEach(function(i){if(!i)throw"items 中元素必须是 object";if(void 0===i.Pos)throw"未指定 xh ".concat(JSON.stringify(i));var n=i.Pos-1,s=Math.floor(n/t.cols),l=n%t.cols;if(s>=t.rows)throw"zxcsypj 数据量多于最大配置,行数 ".concat(t.rows," 列数 ").concat(t.cols,",总数据行数 ").concat(t.items.length);if(void 0===i.Status)throw"未指定 state ".concat(JSON.stringify(i));if(!t.stateMap[i.Status])throw"未知 state ".concat(JSON.stringify(i));var o=null;i.tooltip?o=i.tooltip:t.tooltip&&(o=t.tooltip(i)),o&&(o=o.replace(/\n/g,"<br/>")),e[s][l]={xh:i.Pos,state:i.Status,canselect:t.selectState.includes(i.Status),name:t.stateMap[i.Status],tooltip:o,raw:i}}),e}},data:function(){return{selectXH:null}},methods:{item_click:function(t){t.canselect&&this.enabled&&(this.selectXH===t.xh?(this.selectXH="",this.$emit("item-select",null)):(this.selectXH=t.xh,this.$emit("item-select",t.raw))),this.$emit("item-click",t.raw)},getSelect:function(){var t=this;return this.items.find(function(e){return e.xh===t.selectXH})}}};exports.Control=i;
},{"./states.json":"mgj5","./index.less":"r2ni"}]},{},["Focm"], "zxcsypj")
//# sourceMappingURL=/index.js.map