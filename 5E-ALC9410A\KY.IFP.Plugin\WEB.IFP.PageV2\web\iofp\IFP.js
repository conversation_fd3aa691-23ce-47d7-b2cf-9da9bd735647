﻿//Author: 刘慧
//Update: 2021/10/18
//WeChat: 47343931
//E-mail: <EMAIL>

/** 接口操作 */
var API = new function () {

    // 私有属性
    let SocketMap = new Map();  //已有socket通讯字典

    /** 心跳间隔，用于Socket通讯时的心跳检测，默认10000（毫秒） */
    this.Heartbeat = 10000;

    /**
     * 调用接口
     * @param {String} url 接口地址，http://localfile/ 表示本地接口
     * @param {any} arg 接口参数，任意可JSON化的对象
     * @returns {Promise} Promise，resolve调用结果或者reject错误信息
     */
    this.GetAction = function (url, arg, pak) {
        let location = new URL(url, document.location.origin);  //以当前域地址自动补全网址，如果url为绝对地址则忽略当前域地址
        return new Promise(function (resolve, reject) {
            let request = new XMLHttpRequest();
            request.open("POST", location.toString());
            request.setRequestHeader("Content-Packet", pak === false ? false : true);   //是否需要包装内容
            if (document.location.origin != location.origin) {
                request.withCredentials = true;
            }
            let segment = document.location.pathname.split('/').filter(x => x != '');
            if (segment[0] == "identity" && segment[1].length == 32) request.setRequestHeader("identity", segment[1]);
            request.setRequestHeader("Content-Type", "application/json");
            request.timeout = 0;  //毫秒，0表示永不超时
            request.onload = function () {
                if (request.status == 200) {
                    let result = JSON.parse(request.response);
                    //if (result.Success != undefined && (result.Message != undefined || result.Content != undefined)) {
                    if (result.Success != undefined) {
                        if (result.Success) {
                            resolve(result.Content ?? null);
                        }
                        else {
                            reject(result.Message ?? null);
                        }
                    }
                    else {
                        resolve(result);
                    }
                }
                else {
                    reject("请求" + url + "出错，状态码" + request.status.toString());
                }
            }
            request.onabort = function () {
                reject("请求" + url + "中断");
            };
            request.onerror = function () {
                reject("请求" + url + "出错");
            };
            request.ontimeout = function () {
                reject("请求" + url + "超时");
            };
            request.onloadend = function () {
                request = null;
            }
            request.send(JSON.stringify(arg));
        });
    };

    /**
     * 获取通讯
     * @param {String} url 通讯地址，绝对地址或相对地址，相对地址等同于当前地址
     */
    this.GetSocket = function (url) {
        return SocketMap.get(new URL(url, document.location.origin).host);
    };

    /**
     * 新建通讯
     * @param {String} url 通讯地址，绝对地址或相对地址，相对地址等同于当前地址
     */
    this.NewSocket = function (url) {
        return new function (url) {
            url = new URL(url, document.location.origin).host;

            let socket = null;
            let onopen = null;
            let detect = null;
            let action = null;
            let failed = null;
            let active = new Map(); //事件回调字典

            let create = function () {
                let self = this;
                socket = new WebSocket("ws://" + url + "/socket");
                socket.onopen = function (event) {
                    if (onopen != null) onopen(event);
                    //1、浏览器一般有pong指令（即应答服务端的ping）。
                    //2、很少有浏览器会主动发送ping指令。
                    //3、网络问题引起异常时，一般不会触发onerror和onclose。
                    if (self.Heartbeat > 0 && active.size > 0 && detect == null) detect = setTimeout(() => socket.send("{\"operator\":\"detect\"}"), self.Heartbeat);
                };
                socket.onmessage = function (event) {
                    let data = JSON.parse(event.data);
                    if (data.Origin == "Action") {
                        if (action != null) action(data.Result);
                    }
                    else if (data.Origin == "Active") {
                        let target = active.get(data.Active);
                        if (target != undefined) {
                            target.fun(data.Result);
                        }
                    }
                    else if (data.Origin == "Detect") {
                        if (self.Heartbeat > 0 && active.size > 0 && detect > 0) detect = setTimeout(() => socket.send("{\"operator\":\"detect\"}"), self.Heartbeat);
                    }
                };
                socket.onerror = function (event) {
                    //onerror不能捕获网络和安全相关的错误，可在onclse中通过code判断
                    if (event.hasOwnProperty("data")) {
                        if (failed != null) failed(event.data);
                        closed.bind(self)(event.data);
                    }
                };
                socket.onclose = function (event) {
                    //https://tools.ietf.org/html/rfc6455#section-7.4.1
                    //https://tools.ietf.org/html/rfc6455#section-11.7
                    let data = null;
                    switch (event.code) {
                        case 1000:
                            //1000 indicates a normal closure, meaning that the purpose for which the connection was established has been fulfilled.
                            data = "正常关闭";
                            break;
                        case 1001:
                            //1001 indicates that an endpoint is "going away", such as a server going down or a browser having navigated away from a page.
                            data = "连接失效";
                            break;
                        case 1002:
                            //1002 indicates that an endpoint is terminating the connection due to a protocol error.
                            data = "协议错误";
                            break;
                        case 1003:
                            //1003 indicates that an endpoint is terminating the connection because it has received a type of data it cannot accept (e.g., an endpoint that understands only text data MAY send this if it receives a binary message).
                            detail = "数据类型错误";
                            break;
                        case 1005:
                            //1005 is a reserved value and MUST NOT be set as a status code in a Close control frame by an endpoint.  It is designated for use in applications expecting a status code to indicate that no status code was actually present.
                            data = "未知原因";
                            break;
                        case 1006:
                            //1006 is a reserved value and MUST NOT be set as a status code in a Close control frame by an endpoint.  It is designated for use in applications expecting a status code to indicate that the connection was closed abnormally, e.g., without sending or receiving a Close control frame.
                            data = "连接异常";
                            break;
                        case 1007:
                            //1007 indicates that an endpoint is terminating the connection because it has received data within a message that was not consistent with the type of the message (e.g., non-UTF-8 [RFC3629] data within a text message).
                            data = "数据编码错误";
                            break;
                        case 1008:
                            //1008 indicates that an endpoint is terminating the connection because it has received a message that violates its policy.  This is a generic status code that can be returned when there is no other more suitable status code (e.g., 1003 or 1009) or if there is a need to hide specific details about the policy.
                            data = "连接受限";
                            break;
                        case 1009:
                            //1009 indicates that an endpoint is terminating the connection because it has received a message that is too big for it to process.
                            data = "数据溢出";
                            break;
                        case 1010:
                            //1010 indicates that an endpoint (client) is terminating the connection because it has expected the server to negotiate one or more extension, but the server didn't return them in the response message of the WebSocket handshake.  The list of extensions that are needed SHOULD appear in the /reason/ part of the Close frame. Note that this status code is not used by the server, because it can fail the WebSocket handshake instead.
                            data = "客户端不支持要求的扩展";
                            if (event.reason != null) detail += "，" + event.reason;
                            break;
                        case 1011:
                            //1011 indicates that a server is terminating the connection because it encountered an unexpected condition that prevented it from fulfilling the request.
                            data = "服务端意外终止";
                            break;
                        case 1015:
                            //1015 is a reserved value and MUST NOT be set as a status code in a Close control frame by an endpoint.  It is designated for use in applications expecting a status code to indicate that the connection was closed due to a failure to perform a TLS handshake (e.g., the server certificate can't be verified).
                            data = "启用安全连接失败";
                            break;
                    }
                    if (event.code > 1000) {
                        if (failed != null) failed(data);
                        closed.bind(self)(data);
                    }

                    //清理资源
                    if (detect) clearTimeout(detect);
                    socket = null;
                    onopen = null;
                    detect = null;
                    action = null;
                    failed = null;
                    //active.clear();  //不清理，以使断线重连后回调依然有效。
                }
            };
            let closed = function (msg) {
                this.ReConnect();
                if (this.OnCrashed != null) {
                    this.OnCrashed(msg);
                }
            };
            /** 心跳间隔，默认取API的Heartbeat */
            this.Heartbeat = API.Heartbeat;
            /** 异常回调 */
            this.OnCrashed = null;
            /** 重新连接 */
            this.ReConnect = function () {
                let self = this;
                setTimeout(() => {
                    create.bind(self)();
                    onopen = function () {
                        let fun = null;
                        for (let [uid, obj] of active) {
                            if (fun == null) {
                                fun = self.SetActive(obj.url, obj.arg, obj.fun, uid);
                            }
                            else {
                                fun = Promise.all(fun).then(self.SetActive(obj.url, obj.arg, obj.fun, uid));
                            }
                        }
                    }
                }, 1000);
            };
            /**
             * 调用接口
             * @param {String} url 接口地址，http://localfile/ 表示本地接口
             * @param {any} arg 接口参数，任意可JSON化的对象
             * @returns {Promise} Promise，resolve调用结果或者reject错误信息
             */
            this.GetActive = function (url, arg) {
                return this.SetActive(url, arg);
            };
            /**
             * 设置回调
             * @param {String} url 接口地址
             * @param {any} arg 接口参数
             * @param {Function} fun 回调函数
             * @param {string} uid 回调标识，为空将自动生成guid标识
             * @returns {Promise} Promise，resolve设置结果或者reject错误信息
             */
            this.SetActive = function (url, arg, fun, uid) {
                return new Promise((resolve, reject) => {
                    if (typeof (arg) == "function") {
                        //省略arg参数
                        uid = fun;
                        fun = arg;
                        arg = undefined;
                    }
                    if (!socket) create.bind(this)();
                    if (socket.readyState < 2) {
                        let upload = {
                            operator: "action",
                            location: url
                        };
                        if (typeof (arg) != "undefined") upload.argument = arg;
                        if (typeof (fun) == "function") {
                            //有回调函数才视为订阅
                            if (uid == void (0)) uid = Guid.NewGuid();
                            upload.identity = uid;
                            active.set(uid, { url: url, arg: arg, fun: fun });
                        }
                        action = function (data) {
                            if (data.Success) {
                                resolve(data.Content);
                            }
                            else {
                                reject(data.Message);
                            }
                        };
                        failed = function (data) {
                            reject(data);
                        };
                        if (socket.readyState == 0) {
                            onopen = function () {
                                socket.send(JSON.stringify(upload));
                            }
                        }
                        else if (socket.readyState == 1) {
                            socket.send(JSON.stringify(upload));
                        }
                    }
                    else {
                        switch (socket.readyState) {
                            case 2:
                                reject("链接正在关闭。");
                            case 3:
                                reject("链接已经关闭。");
                            default:
                                reject("链接没有打开。");
                        }
                    }
                });
            };
            SocketMap.set(new URL(url, document.location.origin).host, this);
        }(url);
    };

    /**
     * 调用接口
     * @param {String} url 接口地址，http://localfile/ 表示本地接口
     * @param {any} arg 接口参数，任意可JSON化的对象
     * @returns {Promise} Promise，resolve调用结果或者reject错误信息
     */
    this.GetActive = function (url, arg) {
        let socket = this.GetSocket(url);
        if (socket == void (0)) socket = this.NewSocket(url);
        return socket.GetActive(url, arg);
    }

    /**
     * 设置回调
     * @param {String} url 接口地址
     * @param {any} arg 接口参数
     * @param {Function} fun 回调函数
     * @param {string} uid 回调标识，为空将自动生成guid标识
     * @returns {Promise} Promise，resolve设置结果或者reject错误信息
     */
    this.SetActive = function (url, arg, fun, uid) {
        let socket = this.GetSocket(url);
        if (socket == void (0)) socket = this.NewSocket(url);
        return socket.SetActive(url, arg, fun, uid);
    }
}();

/** 窗体操作 */
var APP = new function () {

    if (navigator.userAgent.indexOf("Cefly") == -1) return undefined;

    /** 私有变量 */
    let App = undefined;
    let Any = (typeof (Var$App) == "undefined") ? CefSharp.BindObjectAsync("Var$App").then(() => { App = Var$App }) : Promise.resolve();

    /** 环境检测 */
    this.IsCefly = navigator.userAgent.indexOf("Cefly") != -1;

    /**
     * 获取配置
     * @param {String} url json配置文件地址
     * @param {String} str 要读取的配置项路径，如aaa.bbb.ccc
     * @returns {any} 配置项对象
     */
    this.GetConfig = function (url, str) {
        let address = new URL(url, document.location.origin).toString();  //以当前域地址自动补全网址，如果url为绝对地址则忽略当前域地址
        let request = new XMLHttpRequest();
        request.open("GET", address, false);
        request.send(null);
        if (request.status == 200) {
            let obj = JSON.parse(request.response);
            if (str == void (0)) {
                return obj;
            }
            else {
                return eval("obj." + str);
            }
        };
    };

    /**
     * 保存配置
     * @param {String} key 配置项路径，如aaa.bbb.ccc
     * @param {any} val 配置项数据，可JSON.stringify的对象
     * @param {Boolean} cyp 是否加密保存，仅当val为String时才可以使用加密保存，主要用于保存密码等敏感数据
     */
    this.SetConfig = function (key, val, cyp) {
        return Any.then(() => { App.SetConfig(key, JSON.stringify(val), cyp); });
    };

    /**
     * 加密字符
     * @param {String} str 原始字符串
     * @returns {String} 加密后的字符串
     */
    this.Encrypt = function (str) {
        return Any.then(() => { App.Encrypt(str); });
    };

    /**
     * 解密字符
     * @param {String} obj 密文字符串
     * @returns {String} 解密后的字符串
     */
    this.Decrypt = function (str) {
        return Any.then(() => { App.Decrypt(str); });
    };

    /**
     * 创建窗体
     * @param {String|Object} arg 窗体URL地址 或 包含窗体URL地址及其它窗体参数的对象
     * @param {String} arg.address 窗体URL地址
     * @param {String} [arg.captionBackground] 窗体标题背景颜色，如#FFFFFFFF（ARGB）、#FFFFFF（RGB）、Red等，默认#F5F5F5
     * @param {Number} [arg.captionHeight] 标题栏高度，默认26
     * @param {String} [arg.handle] 窗体标识，如果不传，将自动生成一个guid作为窗体标识，后续对窗体的操作需要用到
     * @param {Number} [arg.height] 窗体高度
     * @param {String} [arg.icon] 窗体及任务栏图标，如果窗体网页设置了rel='shortcut icon'，则网页图标加载后将覆盖窗体图标
     * @param {String} [arg.iconMargin] 图标边距，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.thickness?view=netframework-4.0
     * @param {String} [arg.iconVisibility] 图标可见性，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.visibility?view=netframework-4.0
     * @param {Boolean} [arg.isActive] 窗体是否为激活（前台）窗口，isActive可以获取，也可以设置为 true 来达到激活窗体的目的，但是设置为 false 并不会导致窗体取消激活
     * @param {Number} [arg.left] 窗体位置的左侧值
     * @param {Number} [arg.maxHeight] 窗体最大高度
     * @param {Number} [arg.maxWidth] 窗体最大宽度
     * @param {Number} [arg.minHeight] 窗体最小高度
     * @param {Number} [arg.minWidth] 窗体最小宽度
     * @param {String|Number} [arg.owner] 窗体所有者，窗体标识（String） 或 任意窗体的句柄（Number）
     * @param {Boolean} [arg.resizable] 窗体是否可以通过四边调整大小
     * @param {String} [arg.title] 窗体标题，如果窗体网页设置了title标签，则网页title将覆盖窗体标题
     * @param {String} [arg.titleFontFamily] 标题字体
     * @param {Number} [arg.titleFontSize] 标题大小
     * @param {String} [arg.titleFontWeight] 标题粗细，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.fontweights?view=netframework-4.0
     * @param {String} [arg.titleForeground] 标题颜色，如#FFFFFFFF（ARGB）、#FFFFFF（RGB）、Red等，默认#F5F5F5
     * @param {String} [arg.titleHorizontalAlignment] 标题水平对齐，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.horizontalalignment?view=netframework-4.0
     * @param {String} [arg.titleMargin] 标题边距，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.thickness?view=netframework-4.0
     * @param {String} [arg.titleVerticalAlignment] 标题垂直对齐，https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.verticalalignment?view=netframework-4.0
     * @param {String} [arg.visibility] 窗体可见性，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.visibility?view=netframework-4.0
     * @param {Number} [arg.width] 窗体宽度
     * @param {String} [arg.windowStartupLocation] 窗体启动时位置，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.windowstartuplocation?view=netframework-4.0
     * @param {String} [arg.windowState] 窗体状态，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.windowstate?view=netframework-4.0
     */
    this.CreateWindow = function ({
        address,
        captionBackground,
        captionHeight,
        handle,
        height,
        icon,
        iconMargin,
        iconVisibility,
        isActive,
        left,
        maxHeight,
        maxWidth,
        minHeight,
        minWidth,
        owner,
        resizable,
        title,
        titleFontFamily,
        titleFontSize,
        titleFontWeight,
        titleForeground,
        titleHorizontalAlignment,
        titleMargin,
        titleVerticalAlignment,
        visibility,
        width,
        windowStartupLocation,
        windowState
    }) {
        let arg = arguments[0];
        if (arg == void (0)) throw new Error("参数不能为空");
        if (typeof (arg) == 'string') {
            arg = new URL(arg, document.location.origin).toString();
        }
        else if (address != void (0)) {
            address = new URL(address, document.location.origin).toString(); //以当前域地址自动补全网址，如果url为绝对地址则忽略当前域地址
        }
        return Any.then(() => App.CreateWindow(JSON.stringify(arg)));
    };

    /**
     * 查询窗体
     * @param {String} [str] 窗体标识，如果不传，则表示当前窗体
     * @returns {Object} 窗体参数，结构参见CreateWindow的输入参数
     */
    this.SelectWindow = function (str) {
        return Any.then(() => App.SelectWindow(str)).then(x => JSON.parse(x));
    };

    /**
     * 更新窗体
     * @param {Object} arg 包含窗体参数的对象
     * @param {String} [arg.address] 窗体URL地址
     * @param {String} [arg.captionBackground] 窗体标题背景颜色，如#FFFFFFFF（ARGB）、#FFFFFF（RGB）、Red等，默认#F5F5F5
     * @param {Number} [arg.captionHeight] 标题栏高度，默认26
     * @param {String} [arg.handle] 要更新的窗体标识，如果不传，表示更新当前窗体
     * @param {Number} [arg.height] 窗体高度
     * @param {String} [arg.icon] 窗体及任务栏图标，如果窗体网页设置了rel='shortcut icon'，则网页图标加载后将覆盖窗体图标
     * @param {String} [arg.iconMargin] 图标边距，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.thickness?view=netframework-4.0
     * @param {String} [arg.iconVisibility] 图标可见性，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.visibility?view=netframework-4.0
     * @param {Boolean} [arg.isActive] 窗体是否为激活（前台）窗口，isActive可以获取，也可以设置为 true 来达到激活窗体的目的，但是设置为 false 并不会导致窗体取消激活
     * @param {Number} [arg.left] 窗体位置的左侧值
     * @param {Number} [arg.maxHeight] 窗体最大高度
     * @param {Number} [arg.maxWidth] 窗体最大宽度
     * @param {Number} [arg.minHeight] 窗体最小高度
     * @param {Number} [arg.minWidth] 窗体最小宽度
     * @param {String|Number} [arg.owner] 窗体所有者，窗体标识（String） 或 任意窗体的句柄（Number）
     * @param {Boolean} [arg.resizable] 窗体是否可以通过四边调整大小
     * @param {String} [arg.title] 窗体标题，如果窗体网页设置了title标签，则网页title将覆盖窗体标题
     * @param {String} [arg.titleFontFamily] 标题字体
     * @param {Number} [arg.titleFontSize] 标题大小
     * @param {String} [arg.titleFontWeight] 标题粗细，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.fontweights?view=netframework-4.0
     * @param {String} [arg.titleForeground] 标题颜色，如#FFFFFFFF（ARGB）、#FFFFFF（RGB）、Red等，默认#F5F5F5
     * @param {String} [arg.titleHorizontalAlignment] 标题水平对齐，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.horizontalalignment?view=netframework-4.0
     * @param {String} [arg.titleMargin] 标题边距，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.thickness?view=netframework-4.0
     * @param {String} [arg.titleVerticalAlignment] 标题垂直对齐，https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.verticalalignment?view=netframework-4.0
     * @param {String} [arg.visibility] 窗体可见性，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.visibility?view=netframework-4.0
     * @param {Number} [arg.width] 窗体宽度
     * @param {String} [arg.windowStartupLocation] 窗体启动时位置，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.windowstartuplocation?view=netframework-4.0
     * @param {String} [arg.windowState] 窗体状态，参考 https://docs.microsoft.com/zh-cn/dotnet/api/system.windows.windowstate?view=netframework-4.0
     */
    this.UpdateWindow = function ({
        address,
        captionBackground,
        captionHeight,
        handle,
        height,
        icon,
        iconMargin,
        iconVisibility,
        isActive,
        left,
        maxHeight,
        maxWidth,
        minHeight,
        minWidth,
        owner,
        resizable,
        title,
        titleFontFamily,
        titleFontSize,
        titleFontWeight,
        titleForeground,
        titleHorizontalAlignment,
        titleMargin,
        titleVerticalAlignment,
        visibility,
        width,
        windowStartupLocation,
        windowState
    }) {
        let arg = arguments[0];
        if (arg == void (0)) throw new Error("参数不能为空");
        if (address != void (0)) address = new URL(address, document.location.origin).toString();  //以当前域地址自动补全网址，如果url为绝对地址则忽略当前域地址
        Any.then(() => App.UpdateWindow(JSON.stringify(arg)));
    };

    /**
     * 删除窗体
     * @param {String} [str] 删除的窗体句柄，如果不传表示删除当前窗体
     */
    this.DeleteWindow = function (str) {
        Any.then(() => App.DeleteWindow(str));
    };

    /**
     * 接管关闭
     * @param {Function} fun 窗体关闭事件触发时要执行的形如function(){...}的方法，一旦窗体关闭事件被接管，窗体关闭事件将立即终止，窗体也将不会关闭，除非执行删除窗体（DeleteWindow）操作
     */
    this.CustomClosed = function (fun) {
        Any.then(() => App.CustomClosed(fun));
    }

    /**
     * 创建托盘
     * @param {Object} arg 托盘参数对象
     * @param {String} arg.address 托盘右键窗体展示的页面URL地址
     * @param {String} arg.handle 托盘右键窗体展示窗体的标识
     * @param {Number} [arg.height] 托盘右键窗体展示窗体的高度，默认400
     * @param {String} arg.icon 托盘图标文件，必须为.ico的文件路径，相当于与程序运行目录，如 Cefly.App.ico ，表示托盘图标为程序目录下的 Cefly.App.ico 文件
     * @param {String} arg.tooltip 托盘悬停时的提示文字
     * @param {Number} [arg.width] 托盘右键窗体展示窗体的宽度，默认200
     */
    this.CreateNotify = function ({
        address,
        handle,
        height,
        icon,
        tooltip,
        width
    }) {
        let arg = arguments[0];
        if (arg == void (0)) throw new Error("参数不能为空");
        if (arg.address != void (0)) arg.address = new URL(arg.address, document.location.origin).toString(); //以当前域地址自动补全网址，如果url为绝对地址则忽略当前域地址
        Cefly.App.CreateNotify(JSON.stringify(arg));
    };

    /**
     * 查询托盘
     * @returns {Object} 托盘参数，结构参见CreateNotify的输入参数
     */
    this.SelectNotify = function () {
        return JSON.parse(Cefly.App.SelectNotify());
    };

    /**
     * 更新托盘
     * @param {Object} arg 托盘参数对象
     * @param {String} [arg.address] 托盘右键窗体展示的页面URL地址
     * @param {Number} [arg.height] 托盘右键窗体展示窗体的高度，默认400
     * @param {String} [arg.icon] 托盘图标文件，必须为.ico的文件路径，相当于与程序运行目录，如 Cefly.App.ico ，表示托盘图标为程序目录下的 Cefly.App.ico 文件
     * @param {String} [arg.tooltip] 托盘悬停时的提示文字
     * @param {Number} [arg.width] 托盘右键窗体展示窗体的宽度，默认200
     */
    this.UpdateNotify = function ({
        address,
        height,
        icon,
        tooltip,
        width
    }) {
        let arg = arguments[0];
        if (arg.address != void (0)) arg.address = new URL(arg.address, document.location.origin).toString(); //以当前域地址自动补全网址，如果url为绝对地址则忽略当前域地址
        Cefly.App.UpdateNotify(JSON.stringify(arg));
    };

    /** 删除托盘 */
    this.DeleteNotify = function () {
        Cefly.App.DeleteNotify();
    };


    /**
     * 执行脚本
     * @param {Object} arg 托盘参数对象
     * @param {String} arg.handle 窗体标识，要执行脚本的窗体
     * @param {String} [arg.iframe] 执行脚本的内嵌iframe名称，如果没有找到对应的iframe，则在窗体的当前文档执行
     * @param {String} [arg.script] 脚本字符
     */
    this.InvokeScript = function (arg) {
        return JSON.parse(Cefly.App.InvokeScript(JSON.stringify(arg)));
    };

    /**
     * 监听事件
     * @callback fun 形如 function(arg){...} 的回调方法，注意：任何通过 InvokeActive 发布的事件都会无差别通知所有订阅者，订阅者应该根据收到的 arg.active 参数自行过滤自己需要处理的事件
     */
    this.ListenActive = function (fun) {
        if (fun == void (0)) return;
        Cefly.App.ListenActive(function (arg) { fun(JSON.parse(arg)) });
    };

    /**
     * 发布事件
     * @param {Object} arg 托盘参数对象
     * @param {String} arg.active 事件名称，可以订阅方和发布方自行约定，wakeup已经被系统占用
     * @param {String} arg.handle 窗体标识，指示事件发布方的窗体名称或指定订阅方的窗体名称等
     */
    this.InvokeActive = function (arg) {
        Cefly.App.InvokeActive(JSON.stringify(arg));
    };

    /**
     * 提示消息
     * @param {String} str 提示消息
     */
    this.Alert = function (str) {
        Cefly.App.Alert(String(str));
    };

    /**
     * 确认消息
     * @param {String} str 确认消息
     * @returns {Boolean} true(确认)，false(取消)
     */
    this.Confirm = function (str) {
        return Cefly.App.Confirm(String(str));
    };

    /** 嵌入控件 */
    this.Viewer = new function () {
        //私有变量
        let mapped = new Map();
        let thread = null;
        //私有方法
        let locate = function (obj) {
            let arg = obj.getBoundingClientRect();  //只读
            arg = { left: arg.left, top: arg.top, width: arg.width, height: arg.height };
            let win = window;
            let ele = win.frameElement;
            while (ele != null) {
                let tmp = ele.getBoundingClientRect();
                arg.left = arg.left + tmp.left + ele.clientLeft;
                arg.top = arg.top + tmp.top + ele.clientTop;
                win = win.parent;
                ele = win.frameElement;
            }
            if (mapped.has(obj)) {
                let old = mapped.get(obj);
                if (old.left !== arg.left || old.top !== arg.top || old.width !== arg.width || old.height !== arg.height) {
                    return arg;
                }
            }
            return arg;
        };
        let update = function () {
            clearTimeout(thread); //避免内存泄露
            for (let obj of mapped.keys()) {
                try {
                    APP.Viewer.Update(obj);
                }
                catch (e) {
                    console.error(e);
                }
            }
            thread = setTimeout(update, 30);
        };
        /**
         * 创建控件
         * @param {Element} obj 影随元素
         */
        this.Create = function (obj) {
            let arg = locate(obj);
            var ptr = Cefly.App.CreateViewer(JSON.stringify(arg));
            mapped.set(obj, { handle: ptr, left: arg.left, top: arg.top, width: arg.width, height: arg.height });
            if (mapped.size === 1) update();
        };
        this.Update = function (obj) {
            if (mapped.has(obj)) {
                let old = mapped.get(obj);
                let arg = locate(obj);
                if (old.left !== arg.left || old.top !== arg.top || old.width !== arg.width || old.height !== arg.height) {
                    arg.handle = old.handle;
                    Cefly.App.UpdateViewer(JSON.stringify(arg));
                    mapped.set(obj, arg);
                }
            }
        };
        this.Delete = function (obj) {
            if (mapped.has(obj)) {
                Cefly.App.Viewer.Delete(JSON.stringify(mapped.get(obj)));
                mapped.delete(obj);
                if (mapped.size === 0) {
                    clearTimeout(thread);
                }
            }
        };
    }();

    /**
     * 输入消息
     * @param {String} str 提示消息
     * @param {String} val 默认输入
     * @returns {String} 返回输入字符(确定) 或 null(取消)
     */
    this.Prompt = function (str, val) {
        return Cefly.App.Prompt(String(str), String(val));
    }

    /**
     * 加载插件
     * @param {String|Array|Object} arg 单个插件文件路径 或 多个插件文件路径 或 插件目录和过滤字符
     * @param {String} arg.path 插件目录
     * @param {String} arg.part 目录下的文件过滤字符，如 Cefly.Api.*.dll
     */
    this.ImportPlugin = function (arg) {
        Cefly.App.ImportPlugin(JSON.stringify(arg));
    };

    /**
     * 获取插件
     * @returns {Array} 返回已加载的插件列表
     */
    this.SelectPlugin = function () {
        return JSON.parse(Cefly.App.SelectPlugin());
    }

    /**
     * 启动服务
     * @param {Number} num 监听端口
     */
    this.StartServer = function (num) {
        Cefly.App.StartServer(JSON.stringify(num));
    };

    /** 停止服务 */
    this.StopServer = function () {
        Cefly.App.StopServer();
    };

    /**
     * 设置缓存
     * @param {Number} num 缓存时效，分钟数（滑动窗口模式）
     */
    this.UpdateSession = function (num) {
        Cefly.App.UpdateSession(JSON.stringify(num));
    };

    /**
     * 记录日志
     * @param {String} str 日志信息
     */
    this.Log = function (str) {
        Cefly.App.Log(JSON.stringify(str));
    };

    /** 退出程序 */
    this.Exit = function () {
        Any.then(() => App.Exit());
    };
    this.ImportSource = function (handle, source, origin) {
        Cefly.App.ImportSource(handle, source, origin);
    }
}();

/** 唯一键值 */
var Guid = new function () {
    //参考 https://github.com/kelektiv/node-uuid 采用的是RFC4122 v4版本（理论上应该用v1版本，但v1版本要求获取机器码，js无法做到，v3和v5版本为局部唯一算法，不适合）
    let rng;
    let crypto = window.crypto || window.msCrypto;
    if (crypto && crypto.getRandomValues) {
        let rnds8 = new Uint8Array(16);
        rng = function whatwgRNG() {
            crypto.getRandomValues(rnds8);
            return rnds8;
        };
    }
    if (!rng) {
        let rnds = new Array(16);
        rng = function () {
            for (let i = 0, r; i < 16; i++) {
                if ((i & 0x03) === 0) r = Math.random() * 0x100000000;
                rnds[i] = r >>> ((i & 0x03) << 3) & 0xff;
            }
            return rnds;
        };
    }
    //格式化方法
    let byteToHex = [];
    for (let i = 0; i < 256; ++i) {
        byteToHex[i] = (i + 0x100).toString(16).substr(1);
    }
    let bytesToUuid = function (buf, offset) {
        let i = offset || 0;
        let bth = byteToHex;
        return bth[buf[i++]] + bth[buf[i++]] +
            bth[buf[i++]] + bth[buf[i++]] + '-' +
            bth[buf[i++]] + bth[buf[i++]] + '-' +
            bth[buf[i++]] + bth[buf[i++]] + '-' +
            bth[buf[i++]] + bth[buf[i++]] + '-' +
            bth[buf[i++]] + bth[buf[i++]] +
            bth[buf[i++]] + bth[buf[i++]] +
            bth[buf[i++]] + bth[buf[i++]];
    }
    //唯一键值
    this.NewGuid = function (options, buf, offset) {
        let i = buf && offset || 0;

        if (typeof (options) == 'string') {
            buf = options == 'binary' ? new Array(16) : null;
            options = null;
        }
        options = options || {};

        let rnds = options.random || (options.rng || rng)();

        rnds[6] = (rnds[6] & 0x0f) | 0x40;
        rnds[8] = (rnds[8] & 0x3f) | 0x80;

        if (buf) {
            for (let ii = 0; ii < 16; ++ii) {
                buf[i + ii] = rnds[ii];
            }
        }

        return buf || bytesToUuid(rnds);
    }
}();