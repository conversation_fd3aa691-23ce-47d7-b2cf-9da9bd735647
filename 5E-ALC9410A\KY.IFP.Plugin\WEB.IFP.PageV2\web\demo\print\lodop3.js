define(["lodop"],function(LodopFuncs){

    // 生成打印 html
    async function print(htmls){
        const LODOP = await LodopFuncs.loadLodop()

        // 初始化打印对象
        LODOP.PRINT_INIT("打印控件功能演示_Lodop功能_分页打印综合表格");

        // 将多个html片段输出为多个页面
        htmls.forEach((html,i)=>{
            // 第二页前加分页符
            if(i>0){
                LODOP.NewPageA();
            }

            // 设置内外边距
            LODOP.ADD_PRINT_HTM('1cm',"1cm","RightMargin:1cm","BottomMargin:1cm",html);
            //LODOP.SET_PRINT_STYLEA(0,"LinkedItem",0); // 关联对象
    
            // 设置 table 打印样式 数字型，0--上边距锁定 1--下边距锁定 2--垂直方向居中 3--上边距和下边距同时锁定（中间拉伸），缺省值是0。
            LODOP.SET_PRINT_STYLEA(0,"Vorient",3);	
        })

        // 输出页号
        LODOP.ADD_PRINT_HTM(1,'90%','RightMargin:10mm',100,"<font color='#000'><span tdata='pageNO'>#</span>/<span tdata='pageCount'>#</span></font>");
        LODOP.SET_PRINT_STYLEA(0,"ItemType",1);		// 0--普通项 1--页眉页脚 2--页号项 3--页数项 4--多页项

        LODOP.PREVIEW();
    }

    
    /**
     * 生成 单个talbe html 代码
     * @param {Array<String>} pagelist html 片段列表
     * @returns 
     */
    function createTableHtml(pagelist){
        return `
        <TABLE border=1 cellSpacing=0 cellPadding=1 width="100%" style="border-collapse:collapse" bordercolor="#333333">
            <thead>
                <TR>
                    <TD width="10%"><DIV align=center><b>表格页眉</b></DIV></TD>
                    <TD width="25%"><DIV align=center><b>品名</b></DIV></TD>
                    <TD width="10%"><DIV align=center><b>颜色</b></DIV></TD>
                    <TD width="10%"><DIV align=center><b>规格</b></DIV></TD>
                    <TD width="10%"><DIV align=center><b>数量</b></DIV></TD>
                    <TD width="15%"><DIV align=center><b>单价</b></DIV></TD>
                    <TD width="20%"><DIV align=center><b>金额</b></DIV></TD>
                </TR>
            </thead>
            <TBODY>
                ${
                    pagelist.map(
                        item=>`<TR><TD>&nbsp;${item.code}</TD><TD>${item.name}</TD><TD>${item.color}</TD><TD>${item.type}</TD><TD>${item.count}</TD><TD>${item.price}</TD><TD>${item.sel}</TD></TR>`
                    ).join('')
                }      
            </TBODY>
        </TABLE>
        `
    }

    /**
     * 将数据列表生成过个html片段
     * @param {Array} list 数据列表
     * @param {Number} firstCount 首页条数，由于首页要输出标题，所以条数会少一些
     * @param {Number} count 其他页面条数
     * @returns {Array<String>} html 列表
     */
    function createPrintHtml(list,firstCount,count){
        let pages = [];

        // 标题，仅首页
        let title = `
        <DIV style="LINE-HEIGHT: 30px;text-align: center; font-weight: bold;font-size:18px;">销售发货单-01</DIV>        
        <TABLE border=0 cellSpacing=0 cellPadding=0 width="100%">
            <TBODY>
                <TR>
                    <TD style="width:33.3%">收 件 人：王斌</TD> 
                    <TD style="text-align: center;">xxxxx</TD>
                    <TD style="width:33.3%;text-align: right;">发货日期：2011-5-10</TD>
                </TR>
            </TBODY>
        </TABLE>`

        let firstlist = list.splice(0,firstCount);
        let firstpage = createTableHtml(firstlist);

        pages.push(title+firstpage);

        while(list.length){
            let pagelist = list.splice(0,count);
            pagehtml = createTableHtml(pagelist);
            pages.push(pagehtml);
        }

        return pages;
    }


    return {
        el:"#app",
        data(){
            return {
                list:[
                    {gid:"111",name:"张三"}
                ]
            }
        },
        methods:{
            print(){
                // 生成150条模拟数据
                let list = Array(150).fill().map((n,i)=>({
                    code:`code-${i+1}`,
                    name:`设备${i+1}`,
                    color:`蓝色`,
                    type:`T2021/${i}`,
                    count:Math.round(Math.random()*10),
                    price:Math.round(Math.random()*1000)/10+200,
                    sel:Math.round(Math.random()*1000)/10+300
                }))

                // 将数据列表 按首页 35条，其他页面每页40条 生成 多个html片段
                let htmls = createPrintHtml(list,35,40);

                // 将多个 html 片段打印成多个页面
                print(htmls);
            }
        }
    }
})