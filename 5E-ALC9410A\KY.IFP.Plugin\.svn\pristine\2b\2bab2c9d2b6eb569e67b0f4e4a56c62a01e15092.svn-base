//F.config
"use strict";
define(["util","topwindow"],function(util,topwindow){
	var _sysGlobal = null;
	try{
		topwindow["_dicb_sys_global"];
	}catch(ex){
		
	}
	if(!_sysGlobal){
		_sysGlobal = util.topns("_dicb_sys_global");
		_sysGlobal.grid_id_field = "gid";
		_sysGlobal.decimal_digits = 3;
	}
	var config = function(key,value){
		if(arguments.length==0 || !key){
			return _sysGlobal;
		}
		var arr = key.split(".");
		var lastKey = arr[arr.length-1];
		var path = arr.reverse().slice(1).reverse().join(".");
		
		var target = (arr.length==1?_sysGlobal:util.ns(_sysGlobal,path));
		
		if(arguments.length==1){
			return target[lastKey];
		}
		else{
			target[lastKey] = value;
			return target[lastKey];
		}
	};
	config.accesser = function(path,defaultValue){
		this.path = path;
		this.updateMap();
		if(typeof defaultValue!="undefined"){
			this.set(defaultValue);
		}
	}
	config.accesser.prototype.get = function(key){
		return config(this.path+(key?("."+key):""));
	}
	config.accesser.prototype.updateMap=function(){
		this.map = config(this.path);
	}
	config.accesser.prototype.set = function(key,value){
		var rev;
		if(typeof key == "object"){
			rev = config(this.path,key);
		}else{
			rev = config(this.path+"."+key,value);
		}
		this.updateMap();
		return rev;
	}
	config.control = function(){
		if(argument.length>0){
			var arr = [].slice.call(arguments,0);
			if(arr[0]!=""){
				arr[0] = "control."+ arr[0];
			}
			return config.apply(this,arr);
		}
		return config.apply(this,arguments);
	}
	
	return config;
});
