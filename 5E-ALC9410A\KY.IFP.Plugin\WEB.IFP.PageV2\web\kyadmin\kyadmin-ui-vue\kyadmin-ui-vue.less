@import "../lib/kjlib/bootstrap-theme-kykj/iconfont/fonts.less";



// @fonts: dicb kjicon glyphions;//字体列表
/**
  * kyadmin-ui-vue 
  * 基于 element-ui 的样式
  */
body.kyadmin-ui.kyadmin-ui-vue{
    label.el-radio{
        margin-bottom:unset;
        margin-right:20px;
    }

    .el-dialog__wrapper.subpage > .el-dialog,
    .el-dialog__wrapper > .el-dialog.subpage {
        > .el-dialog__header{
            background:var(--bg-main);
            padding:8px;
            > .el-dialog__title{
                color:var(--color-main);
                font-size: 16px;
            }
        }
        /*
        .el-dialog__body{
            padding:0;
        }
        */
    }

    // 字体处理，所有字体都需要在这里注册
    .el-button{
        each(@fonts, {
            .@{value}{
                font-weight: 400;
                line-height: 0;
                font-size:unset;
                vertical-align: baseline;

                &+span{
                    margin-left:5px;
                }
            }
        });
    }

    .toolbar .el-button{
        +.el-button{margin-left:0;}
    }

        /*
    .el-table {
        th{
            background:#E6EAED;
            color:#333;
        }
        .el-table__header{
        }
    }
        */
}


body.kyadmin-ui.kyadmin-ui-vue{

    /*
    .toolbar .el-input,
    .searchbar .el-input{
        width: 180px;
    }
    .toolbar,.searchbar{
        flex-shrink: 0;
    }
    .toolbar .el-input,
    .searchbar .el-input{
        width: 180px;
    }
    */

    .el-main {
        padding:1rem;
    }

    .subpage .el-dialog__body{
        padding:0;
    }

    /*
    .el-form-item{
        display:flex;
        .el-form-item__content{
            flex:1;
        }
    }
    */

    .kya-searchbar{
        padding:0;
        
        position:relative;
        .kya-searchbar-folder{
            text-align:right;
            position:absolute;
            height:1.2rem;
            width:100%;
            padding-right:1rem;
            color:#999;
            > span{
                line-height:1rem;
                top:-3px;
            }
            &:hover{
                background-color:var(--bg-primary);
                color:var(--color-primary);
            }
        }
        + * {
            margin-top:0;
        }
    }
}