;
define([ "require"], function(require) {

	return {
		
		getMzflByHyz : function(hyz){
			var retonj;
			F.ajax({
				url:"/com.kysoft.service/html/util/ywlx/getMzflByHyz.action",
				data:hyz,
				async:false,
				success:function(resp){
					retonj = resp;
				}
			});	
			return retonj;
		},
		
		getMzByHyz : function(hyz){
			var retonj;
			F.ajax({
				url:"/com.kysoft.service/html/util/ywlx/getMzByHyz.action",
				data:hyz,
				async:false,
				success:function(resp){
					retonj = resp;
				}
			});	
			return retonj;
		},
       
		/**
		 * 获取全部化验值设置信息
		 */
        getAllhyzsz: function() {
        	var retonj;
        	F.ajax({
				url:"/com.kysoft.service/html/settings/queryHyzlxszAllList.action",
				data:{},
				async:false,
				success:function(resp){
					retonj = eval('(' + resp + ')');
				}
			});	
        	return retonj;
        },
        
        /**
         * 根据系统参数KEY获取对应参数值
         */
        getXtcsByKey: function(key) {
        	var csvalue = "";
        	F.ajax({
				url:"/com.kysoft.service/html/settings/getXtcsValueByKey.action",
				data:key,
				async:false,
				success:function(resp){
					csvalue = resp;
				}
			});	
        	return csvalue;
        },
        
        /**
         * 根据指标类型获取化验值设置信息
         */
        getHyzszByZblx: function(zblx) {
        	var retonj;
        	F.ajax({
				url:"/com.kysoft.service/html/settings/queryHyzlxszAllList.action",
				data:{},
				async:false,
				success:function(resp){
					var	resultObj = eval('(' + resp + ')');
					for(var i = 0 ; i < resultObj.length ; i++){
						if(resultObj[i].zblx1004 === zblx){
							retonj = resultObj[i];
						}
					}
				}
			});	
        	return retonj;
        },
        
        /**
         * 根据运输方式获取对应的入厂批次规则
         */
        getRcpcgz: function(ysfs) {
        	var retonj = [];
        	F.ajax({
				url:"/com.kysoft.service/html/util/CommonUtil/getRcpcgz.action",
				async:false,
				data:ysfs,
				success:function(resp){
					var	resultObj = eval('(' + resp + ')');
					retonj = resultObj;
				}
			});	
        	return retonj;
        },
        
        getRcpcgzStr: function(ysfs) {
        	var retonj = "";
        	F.ajax({
				url:"/com.kysoft.service/html/util/CommonUtil/getRcpcgz.action",
				async:false,
				data:ysfs,
				success:function(resp){
					var	resultObj = eval('(' + resp + ')');
					//cmhc,ghdw,kuangdian,meizhong,mzfl,qb,shrq,ysdw,zhandian  
					if(resultObj.length > 0){
						if(resultObj[0].shrq === 1){
							retonj += "日期," 
						}
						if(resultObj[0].kuangdian === 1){
							retonj += "矿点," 
						}
						if(resultObj[0].ghdw === 1){
							retonj += "供货单位," 
						}
						if(resultObj[0].qb === 1){
							retonj += "期别," 
						}
						if(resultObj[0].ysdw === 1){
							retonj += "运输单位," 
						}
						if(resultObj[0].zhandian === 1){
							retonj += "站点," 
						}
						if(resultObj[0].meizhong === 1){
							retonj += "煤种," 
						}
						if(resultObj[0].mzfl === 1){
							retonj += "煤质分类," 
						}
						if(resultObj[0].cmhc === 1){
							retonj += "船名航次," 
						}
						retonj = retonj.substring(0,retonj.length-1);
					}
				}
			});	
        	return retonj;
        },
        
        /**
         * 根据ywlx获取业务类型.
         */
        getYwlx:function(ywlx){
        	var retonj;
        	F.ajax({
				url:"/com.kysoft.service/html/util/ywlx/getYwlxCombox.action",
				async:false,
				data:{ywlx:ywlx},
				success:function(resp){
					var	resultObj = eval('(' + resp + ')');
					retonj = resultObj;
				}
			});	
        	return retonj;
        },
        
        getYwlxMap:function(ywlx){
        	var retonj,map;
        	F.ajax({
				url:"/com.kysoft.service/html/util/ywlx/getYwlxCombox.action",
				async:false,
				data:{ywlx:ywlx},
				success:function(resp){
					var	resultObj = eval('(' + resp + ')');
					retonj = resultObj;
					map = new F.common.Map();
					if(retonj&&retonj.length>0){
						for(var i=0;i<retonj.length;i++){
							map.put(retonj[i].id,retonj[i].text);
						}
					}
					
				}
			});	
        	return map;
        },
        
    	/**根据条件获取指定表格数据.
    	 * @param 查询map(表名tabname、查询条件wheresql、排序条件ordersql)
         */
        getDataBySql:function(map){
        	var retonj;
        	F.ajax({
				url:"/com.kysoft.service/html/util/CommonUtil/getDataBySql.action",
				async:false,
				data:map,
				success:function(resp){
					var	resultObj = eval('(' + resp + ')');
					retonj = resultObj;
				}
			});	
        	return retonj;
        },
    
		//加载化验指标（qddw为true表示要去掉后面的单位%）
		loadHyzb : function(grid,qddw){
			var _this = this;
			F.ajax({
				url:"/com.kysoft.service/html/util/CommonUtil/getHyzbsz.action",
				data:{},
				success: function (resp) {
					var hyzzb = JSON.parse(resp);
					for(var i=0; i<hyzzb.length; i++){
						//热值
						if(hyzzb[i].zblx1004=='10040001'){
							$("#jqgh_"+grid+"_syRz").html(_this.qddwFunc(hyzzb[i].displayText,qddw));
						}
						//全水
						if(hyzzb[i].zblx1004=='10040005'){
							$("#jqgh_"+grid+"_sySf").html(_this.qddwFunc(hyzzb[i].displayText,qddw));
						}
						//挥发分
						if(hyzzb[i].zblx1004=='10040002'){
							$("#jqgh_"+grid+"_syHff").html(_this.qddwFunc(hyzzb[i].displayText,qddw));
						}
						//灰分
						if(hyzzb[i].zblx1004=='10040004'){
							$("#jqgh_"+grid+"_syHf").html(_this.qddwFunc(hyzzb[i].displayText,qddw));
						}
						//硫分
						if(hyzzb[i].zblx1004=='10040003'){
							$("#jqgh_"+grid+"_syLf").html(_this.qddwFunc(hyzzb[i].displayText,qddw));
						}
						//灰熔点
						if(hyzzb[i].zblx1004=='10040006'){
							$("#jqgh_"+grid+"_syHrd").html(_this.qddwFunc(hyzzb[i].displayText,qddw));
						}
					}
	            }
			});
		},
		qddwFunc:function(text,qddw){
			if(qddw){
				var idx = text.indexOf("（");
				return text.substring(0,idx);
			}
			return text;
		},
		
		//煤堆构成要素
		loadMdgc : function(grid, _this){
			F.ajax({
				url:"/com.kysoft.service/html/coalyard/mcgl/mcsyt/getMdgc.action",
				data:{type:1},
				success: function (resp) {
					var mdgc = JSON.parse(resp);
					for(var i=0; i<mdgc.length; i++){
						if(mdgc[i].id.indexOf("MZ4010")!=-1){
							_this.controls[grid].grid("showCol","mz4010");
							_this.controls[grid].grid("showCol","mz");
						}
						if(mdgc[i].id.indexOf("MZFL4011")!=-1){
							_this.controls[grid].grid("showCol","mzfl4011");
							_this.controls[grid].grid("showCol","mzfl");
						}
						if(mdgc[i].id.indexOf("KD4001")!=-1){
							_this.controls[grid].grid("showCol","kd4001");
							_this.controls[grid].grid("showCol","kd");
						}
						if(mdgc[i].id.indexOf("GHDW4002")!=-1){
							_this.controls[grid].grid("showCol","ghdw4002");
							_this.controls[grid].grid("showCol","ghdw");
						}
						if(mdgc[i].id.indexOf("CCH")!=-1){
							_this.controls[grid].grid("showCol","cch");
						}
						if(mdgc[i].id.indexOf("PCBM")!=-1){
							_this.controls[grid].grid("showCol","rcpc");
						}
					}
	            }
			});
		},
		
		getAllUser:function(){
			var rows = [];
			F.ajax({
				url:'/com.kysoft.service/html/util/CommonUtil/getUser.action',
				data:{
					compid:"nofilter",
					page : 1,
					rows : 10000,
					sidx : "id",
					sord : "desc"
				},
				async:false,
				success:function(resp){
					rows = JSON.parse(resp).rows;
				}
			});
			var result = {};
			for(var i=0; i<rows.length; i++){
				result[rows[i].id] = rows[i].text;
			}
			return result;
		}
	}
});