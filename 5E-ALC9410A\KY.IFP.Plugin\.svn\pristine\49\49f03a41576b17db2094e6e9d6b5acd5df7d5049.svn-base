﻿using COM.IFP.Common;
using ORM.IFP.www.DbModel.SM;
using System;
using System.Text.Json;

namespace API.IFP.www.sys
{
    public class XslszAPI
    {
        readonly Lazy<DAL.IFP.www.sys.XslszDAL> lazy = Entity.Create<DAL.IFP.www.sys.XslszDAL>();

        public IFP_SM_XSLSZ Select(JsonElement json)
        {
            IFP_SM_XSLSZ entity = json.GetValue<IFP_SM_XSLSZ>();
            return lazy.Value.Select(entity);
        }

        public void Save(JsonElement json)
        {
            IFP_SM_XSLSZ entity = json.GetValue<IFP_SM_XSLSZ>();
            lazy.Value.Save(entity);
        }
    }
}
