define(["lodop","iofp/util","text!./eltable.css"],function(LodopFuncs,iofpUtil,eltablecss){
    function html2Table(tableHtml){
        let fragment = document.createElement("div")
        fragment.innerHTML = tableHtml;
        return fragment.children[0];
    }

    function printELTable(vm){
        let tableHeader = vm.$refs.headerWrapper.innerHTML;
        let tableBody = vm.$refs.bodyWrapper.innerHTML;
        let head = html2Table(tableHeader)
        let body = html2Table(tableBody)
        head.appendChild(body.children[1]);
        console.log(head);
        printTable({tableId:vm.tableId,table:`<style>${eltablecss}</style>${head.outerHTML}`});
    }

    /**
     * 打印 table html
     */
    function printTable({tableId,table}){
        tableId = tableId || "";
        LodopFuncs.loadLodop().then(function(LODOP){
            LODOP.PRINT_INIT(iofpUtil.urlGetPageID()+"-"+tableId||"");
            LODOP.ADD_PRINT_TABLE("10mm","10mm","RightMargin:10mm","BottomMargin:10mm",table);	
            LODOP.PREVIEW();
        })
    }
    return {
        printELTable,

        
        printTable
    }
})