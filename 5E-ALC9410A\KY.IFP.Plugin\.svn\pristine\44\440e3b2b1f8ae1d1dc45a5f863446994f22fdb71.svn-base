define(function(){

    return {
        el:"#app",
        data(){
            return {
                option:{
                    title: {
                        text: 'ECharts 示例'
                    },
                    tooltip: {},
                    legend: {
                        data:['销量','销量2','销量3']
                    },
                    xAxis: {
                        data: ["衬衫","羊毛衫","雪纺衫","裤子","高跟鞋","袜子"]
                    },
                    yAxis: {},
                    series: [
                        { name: '销量', type: 'bar', data: [] },
                        { name: '销量2', type: 'bar', data: [] },
                        { name: '销量3', type: 'bar', data: [] }
                    ]
                }
            }
        },
        mounted(){
            // 模拟延时
            setTimeout(()=>{
                this.$api.get("./ifp-echart.json").then(data=>{
                    this.option.series[0].data = data;
                    this.option.series[1].data = data;
                    this.option.series[2].data = data;
                })
            },1000)
        }
    }
})