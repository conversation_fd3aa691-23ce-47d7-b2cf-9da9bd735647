﻿using LinqToDB.Configuration;
using LinqToDB.Data;
using LinqToDB.DataProvider.SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace COM.IFP.LinqDB
{
    public class SQLite
    {
        /// <summary>
        /// 创建引擎
        /// </summary>
        /// <param name="source">连接字符</param>
        /// <returns>数据引擎</returns>
        public static DConnection Create(string source)
        {
            return DConnection.Create(SQLiteTools.GetDataProvider(), source, false);
        }
    }
}
