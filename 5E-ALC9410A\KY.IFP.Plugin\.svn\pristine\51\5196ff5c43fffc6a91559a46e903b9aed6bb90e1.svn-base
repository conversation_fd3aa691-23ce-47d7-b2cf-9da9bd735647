﻿<!DOCTYPE html >
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>列显设置</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item" id="app">
        <div class="toolbar">
            <el-button icon="el-icon-c-scale-to-original" @click="onXslBtnClick">列显设置</el-button>
            <el-button icon="kjicon kjicon-tuichu" @click="onExit">退出</el-button>
        </div>
        <div class="searchbar">
            运输方式：<ifp-select-ywlx v-model="filter.Ysfs1001" :ywlx="1001"></ifp-select-ywlx>
            <span style="padding-left:20px">
                <el-button icon="el-icon-search" @click="onSelect">查询</el-button>
            </span>
        </div>
        <div class="flex flex-item padding">
            <el-table ref="tableLmtj"
                      :data="tableData"
                      row-key="Gid"
                      size="medium"
                      :border="true"
                      :highlight-current-row="true">
                <el-table-column type="index"
                                 label="序号"
                                 
                                 width="50">
                </el-table-column>
                <el-table-column type="selection"
                                 
                                 width="50">
                </el-table-column>
                <el-table-column prop="Ysfs1001"
                                 align="center"
                                 label="运输方式"
                                 
                                 v-if="xslsz.tableLmtj['C1']">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.Ysfs1001" :ywlx="1001" :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                    </template>
                </el-table-column>
                <el-table-column prop="Pcbm"
                                 label="批次编码"
                                 
                                 v-if="xslsz.tableLmtj['C2']">
                </el-table-column>
                <el-table-column prop="Shrq"
                                 label="收货日期"
                                 
                                 v-if="xslsz.tableLmtj['C3']">
                </el-table-column>
                <el-table-column prop="Ghdw4002"
                                 label="供货单位"
                                 
                                 v-if="xslsz.tableLmtj['C4']">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.Ghdw4002" :ywlx="4002" :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                    </template>
                </el-table-column>
                <el-table-column prop="Kd4001"
                                 label="矿点"
                                 
                                 v-if="xslsz.tableLmtj['C5']">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.Kd4001" :ywlx="4001" :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                    </template>
                </el-table-column>
                <el-table-column prop="Mz4010"
                                 label="煤种"
                                 
                                 v-if="xslsz.tableLmtj['C6']">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.Mz4010" :ywlx="4010" :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                    </template>
                </el-table-column>
                <el-table-column label="数量信息"
                                 align="center"
                                 
                                 v-if="xslsz.tableLmtj['C7']">
                    <el-table-column label="入厂量"
                                     align="center"
                                     
                                     v-if="xslsz.tableLmtj['C8']">
                        <el-table-column prop="Maozhong"
                                         label="毛重"
                                         
                                         v-if="xslsz.tableLmtj['C9']">
                        </el-table-column>
                        <el-table-column prop="Pizhong"
                                         label="皮重"
                                         
                                         v-if="xslsz.tableLmtj['C10']">
                        </el-table-column>
                        <el-table-column prop="Jingzhong"
                                         label="净重"
                                         
                                         v-if="xslsz.tableLmtj['C11']">
                        </el-table-column>
                        <el-table-column prop="Ysl"
                                         label="验收量"
                                         
                                         v-if="xslsz.tableLmtj['C12']">
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="矿发量"
                                     align="center"
                                     
                                     v-if="xslsz.tableLmtj['C13']">
                        <el-table-column prop="Kfmz"
                                         label="矿发毛重"
                                         
                                         v-if="xslsz.tableLmtj['C14']">
                        </el-table-column>
                        <el-table-column prop="Kfpz"
                                         label="矿发皮重"
                                         
                                         v-if="xslsz.tableLmtj['C15']">
                        </el-table-column>
                        <el-table-column prop="Piaozhong"
                                         label="矿发净重"
                                         
                                         v-if="xslsz.tableLmtj['C16']">
                        </el-table-column>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="质量信息"
                                 align="center"
                                 
                                 v-if="xslsz.tableLmtj['C17']">
                    <el-table-column prop="Rz"
                                     label="热值"
                                     
                                     v-if="xslsz.tableLmtj['C18']">
                    </el-table-column>
                    <el-table-column prop="Sf"
                                     label="水份"
                                     
                                     v-if="xslsz.tableLmtj['C19']">
                    </el-table-column>
                    <el-table-column prop="Lf"
                                     label="硫份"
                                     
                                     v-if="xslsz.tableLmtj['C20']">
                    </el-table-column>
                </el-table-column>
                <el-table-column prop="Mztime"
                                 label="重车时间"
                                 
                                 v-if="xslsz.tableLmtj['C21']">
                </el-table-column>
                <el-table-column prop="Pztime"
                                 label="轻车时间"
                                 
                                 v-if="xslsz.tableLmtj['C22']">
                </el-table-column>
            </el-table>
        </div>
        <el-dialog class="subpage" title="显示列设置"
                   :visible.sync="xslsz.show"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   width="600px">
            <ifp-table-column-show @cancel="xslsz.show=false"
                                   @success="onSuccess"
                                   :tableref="xslsz.tableref"
                                   :show="xslsz.show">
            </ifp-table-column-show>
        </el-dialog>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>