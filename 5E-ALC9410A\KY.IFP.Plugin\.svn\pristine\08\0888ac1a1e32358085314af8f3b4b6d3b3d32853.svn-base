﻿using COM.IFP.Common;
using ORM.IFP.DbModel;
using ORM.IFP.www.DTO;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace API.IFP.Rights
{
    public class UserLog
    {
        private Lazy<DAL.IFP.Rights.UserLog> userLog = Entity.Create<DAL.IFP.Rights.UserLog>();
        //保存日志.
        public void SaveLog(JsonElement json)
        {
            userLog.Value.SaveLog(json);
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public object GetLogList(JsonElement json)
        {
            //IFP_SM_USERLOG obj = JsonConvert.DeserializeObject<IFP_SM_USERLOG>(json.ToString());
            IList<IFP_SM_USERLOG> filter = json.GetValue<IList<IFP_SM_USERLOG>>("filter");
            PageModel paging = null;
            var tmp = new JsonElement();
            if (json.TryGetProperty("paging", out tmp) == true)
                paging = json.GetValue<PageModel>("paging");

            return userLog.Value.GetLogList(filter, paging);
        }

        /// <summary>
        /// 查询页面名列表
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public List<IdTextModel> GetPageNameList(JsonElement json)
        {
            return userLog.Value.GetPageNameList();
        }

        /// <summary>
        /// 查询按钮名列表
        /// </summary>
        /// <param name="obj">根据页名进行查询</param>
        /// <returns></returns>
        public List<IdTextModel> GetBtnNameList(JsonElement json)
        {
                        // 使用自定义设置进行反序列化
            IFP_SM_USERLOG obj = JsonSerializer.Deserialize<IFP_SM_USERLOG>(json.ToString());
            return userLog.Value.GetBtnNameList(obj);
        }
    }
}