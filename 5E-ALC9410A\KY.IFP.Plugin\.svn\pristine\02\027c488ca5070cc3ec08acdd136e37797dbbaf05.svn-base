; define([
    "require",
    "controllers/base",
    "jquery",
    "jclass"],
    function (require, base, $, jclass) {

        return jclass(base, {
            name: "csxz",
            win: null,
            openWin: function () {
                this.log.info("打开弹出页");
            },
            hasSelect: function (list, id) {
                return list.find(function (item) {
                    return item.Gid == id
                });
            },
            bindEvent: function () {
                var _this = this;
                //更新事件
                this.bind("updateBtn", "click", this.updateObj, this);
                //查询事件			
                this.bind("searchBtn", "click", this.searchObj, this);
                //重置事件			
                this.bind("resetBtn", "click", this.resetObj, this);
                this.bind("grid", "onSelectAll", function (list, selected) {  //全选 顺序号用默认顺序号
                    if (selected) {
                        let data = _this.controls.grid2.value();  //先拿到之前整体的数据 顺序号
                        _this.controls.grid2.clear();
                        _this.controls.grid2.addRowData(_this.controls.grid.value());
                        let datanow = _this.controls.grid2.value();
                        for (let i = 0; i < datanow.length; ++i) {  //把顺序号替换回来
                            for (let j = 0; j < data.length; ++j) {
                                if (datanow[i].CsName == data[j].CsName) {  //不能比较gid 因为俩张表gid的意思是不一样的
                                    datanow[i].Sxh = data[j].Sxh;
                                    break;
                                }
                                if (j == data.length - 1)  //说明是新选的 顺序号直接走默认
                                {
                                    datanow[i].Sxh = datanow[i].Pxh;
                                }
                            }
                        }
                        _this.controls.grid2.clear();
                        datanow.sort((a, b) => {   //排序
                            return a.Sxh - b.Sxh;
                        });
                        _this.controls.grid2.addRowData(datanow);	//重新插入数据
                    } else {
                        _this.controls.grid2.clear();
                    }
                });

                //选中行事件 顺序号用自增顺序号
                this.bind("grid", "onSelectRow", function (rowid) {
                    let data = _this.controls.grid2.value();  //先拿到整体的数据 顺序号
                    if (_this.hasSelect(_this.controls.grid2.value(), rowid)) {
                        _this.controls.grid2.delRowData(rowid);
                    } else {
                        _this.controls.grid2.addRowData(_this.controls.grid.getRowData(rowid));
                    }
                    let datanow = _this.controls.grid2.value();
                    for (let i = 0; i < datanow.length; ++i) {  //把顺序号替换回来
                        if (data.length == 0) //说明是新选的 顺序号直接走默认
                        {
                            datanow[i].Sxh = 10;  //空列表就取10
                            continue;
                        }
                        for (let j = 0; j < data.length; ++j) {
                            if (datanow[i].CsName == data[j].CsName) { //不能比较gid 因为俩张表gid的意思是不一样的
                                datanow[i].Sxh = data[j].Sxh;
                                break;
                            }
                            if (j == data.length - 1)  //说明是新选的 顺序号直接走默认
                            {
                                datanow[i].Sxh = parseInt(datanow[datanow.length - 2].Sxh / 10) * 10 + 10;  //比最大顺序号大10，且舍弃个位数
                            }
                        }
                    }
                    _this.controls.grid2.clear();
                    _this.controls.grid2.addRowData(datanow);
                });

                //数据修改后刷新排序顺序
                this.bind("grid2", "afterSaveCell", function (rowid, name, val, iRow, iCol) {
                    let data = _this.controls.grid2.value();  //先拿到整体的数据 顺序号
                    _this.controls.grid2.clear();
                    data.sort((a, b) => {   //排序
                        return a.Sxh - b.Sxh;
                    });
                    _this.controls.grid2.addRowData(data);	//重新插入数据
                });

            },
            //重置方法
            resetObj: function () {
                var _this = this;
                //清空控件内容
                _this.controls.CsName_LIKE.clear();
                _this.loadForm();
            },
            updateObj: function () {
                var _this = this;
                var data = _this.controls.grid2.value();
                if (data && data.length <= 0) {
                    $.bootoast.warning("请选择参数");
                    return;
                }
                var arr = _this.controls.grid2.value();

                F.ajax({
                    url: "/API/IFP/BaseInfo/XtcsFenZu/XtcsMapFenZuSave",
                    data: {
                        SaveGroup: arr,
                        Gid: _this.controls.FenZuGid.value()
                    },
                    success: function (resultObj) {
                        window.parent.$.alert("保存成功。");
                        win.close();
                    },
                    error: function () {
                        $.alert("保存失败");
                    }
                });
            },
            //查询方法
            searchObj: function () {
                this.loadForm();
            },
            //初始化页面
            initData: function () {
                //this.loadForm();
            },
            //加载GIRD
            loadForm: function () {
                var _this = this;
                F.util.showWait();
                F.ajax({
                    url: "/API/IFP/BaseInfo/Xtcs/GetAllXtcsListFenZu",
                    data: {
                        CsName: { Value: _this.controls.CsName_LIKE.value(), Match: "HAS" }
                    },
                    success: function (resultObj) {
                        _this.controls.grid.value(resultObj);
                        //var selectlist = R.filter(R.propEq('IOFenZuGid', _this.controls.FenZuGid.value()))(resultObj);
                        //_this.controls.grid2.value(selectlist);
                        var rowdata2 = _this.controls.grid2.value();
                        for (var i = 0; i < rowdata2.length; i++) {
                            _this.controls.grid.$container.jqGrid('setSelection', rowdata2[i].Gid, false);
                        }
                        F.util.hideWait();
                    }
                });
            },

            //加载GIRD
            loadForm2: function () {
                var _this = this;
                F.util.showWait();
                F.ajax({
                    url: "/API/IFP/BaseInfo/XtcsFenZu/FenZuListAll",
                    data: {
                        Gid: _this.controls.FenZuGid.value(),
                        flag: false
                    },
                    success: function (resultObj) {
                        if (resultObj.length == 1 && resultObj[0].Gid == undefined)  //如果是空分组，会返回一个填充项，去掉这个
                            resultObj = [];
                        _this.controls.grid2.value(resultObj);

                        _this.loadForm();
                        //F.util.hideWait();
                    }
                });
            },

            onLoad: function () {
                var _this = this;
                win = this.getDialogWindow();
                var params = this.win && this.win.parameter || {};
                this.controls.FenZuGid.value(params.FenZuGid);
                this.initData();
                this.loadForm2();
                this.bindEvent();
            }
        })
    });