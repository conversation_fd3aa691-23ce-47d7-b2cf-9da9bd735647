﻿using System;
using System.Collections.Generic;

namespace API.IFP.WebSocket
{
    public class WebSocketClient
    {
        #region 字段属性
        /// <summary>
        /// 本WebSocket客户端在后端订阅的事件委托
        /// </summary>
        public Action<object> Action { get; set; }
        /// <summary>
        /// 客户端名称
        /// </summary>
        public string ClientName { set; get; }
        /// <summary>
        /// 总结构里面的I，控制器会带过来
        /// </summary>
        public string I { set; get; } = "8888888";
        /// <summary>
        /// 本客户端需要的发送间隔,ms毫秒
        /// </summary>
        public int Interval { get; set; } = 500;
        /// <summary>
        /// 下次推送的时刻
        /// </summary>
        public DateTime NextMoment { get; set; }

        /// <summary>
        /// 是否具备大权限
        /// </summary>
        public bool b_auth = false;
        /// <summary>
        /// 是否已关闭连接
        /// </summary>
        public bool b_close = false;
        /// <summary>
        /// 需要显示的点位表
        /// </summary>
        public List<string> lplcs = new List<string>();

        #endregion
        #region 构造
        public WebSocketClient()
        {
            SetNextMoment();
        }
        #endregion
        #region
        /// <summary>
        /// 计算并设置下一个推送的时刻
        /// </summary>
        /// <returns></returns>
        public void SetNextMoment()
        {
            long nextTick = DateTime.Now.Ticks + TimeSpan.TicksPerMillisecond * Interval;
            NextMoment = new DateTime(nextTick);
        }
        #endregion
    }
}
