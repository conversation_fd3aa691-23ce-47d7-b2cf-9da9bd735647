﻿using COM.IFP.SqlSugarN;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ORM.IFP.DbModel
{
    [SugarTable("IFP_BS_BASEINFO_TYPE_DICTIONARY")]
    public class IFP_BS_BASEINFO_TYPE_DICTIONARY
    {
        /// <summary>
        /// 类型编码
        /// </summary>
        [SugarColumn(ColumnName = "Ywlx", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(16)")]
        public Field<string> Ywlx { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        [SugarColumn(ColumnName = "Basename", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(400)")]
        public Field<string> Basename { get; set; }

        /// <summary>
        /// 扩展表名
        /// </summary>
        [SugarColumn(ColumnName = "TableName", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(1000)")]
        public Field<string> TableName { get; set; }

        /// <summary>
        /// 新建日期
        /// </summary>
        [SugarColumn(ColumnName = "CreateDate", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "datetime")]
        public Field<DateTime> CreateDate { get; set; }

        /// <summary>
        /// 新建来源（如电厂）
        /// </summary>
        [SugarColumn(ColumnName = "CreateSource", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(100)")]
        public Field<string> CreateSource { get; set; }

        /// <summary>
        /// 添加人
        /// </summary>
        [SugarColumn(ColumnName = "CreateUser", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(50)")]
        public Field<string> CreateUser { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "Remark", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(500)")]
        public Field<string> Remark { get; set; }

    }
}