/**
 * 播放器
 * src 对应的服务，应返回当前时间的图片
 */
define(function(){

    return {
        props:{
            /**
             * 资源地址
             */
            src:{default:''},

            /**
             * 自动播放
             */
            autoPlay:{default:true},

            /**
             * 缩放样式，默认 完整等比居中展示
             * object-fit: contain|cover|fill|none|scale-down;
             * @see https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit#css
             */
            fit:{default:'contain'}
        },
        data(){
            return {
                imgurl:'',
                message:"正在连接",
                showFPS:true,//输出当前帧率
                runing:false,
                fps:'',
                toolbar:false,
                toolbarTimoutid:null
            }
        },
        watch:{
            src(){
                this.controller.updateSrc(this.src);
            }
        },
        mounted(){
            // 使用 canvas 利用 GPU 进行绘图

            let canvas = this.$el.querySelector("canvas");
			let ctx = canvas.getContext('2d', { alpha: false });

            let printFPS = createPrintFPS();

            this.controller = create({
                src:this.src,
                timeout:60000,
                ondata:img=>{
                    this.message = '';
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img,0,0,img.width,img.height);
                    if(this.showFPS){
                        this.fps = printFPS()
                        console.log(this.fps + " FPS");
                    }
                },
                onerror:err=>this.message=err
            })

            this.autoPlay && this.play();
        },
        template:`
        <div @mouseover="mouseover" @mousemove="mousemove" @mouseleave="mouseleave" class="ifp-camera" style="position: relative; text-align: center;font-size:0;">
            <canvas :style="{
                objectFit:fit
            }" style="object-fit: contain;background:#000;width:100%;height:100%;"></canvas>

            <div v-if="runing && message" style="border-radius: 5px;font-size:14px;
            left:50%;top:50%;margin-left:-50px;width:100px;margin-top:-26px;
            position: absolute;text-align:center;padding:6px;background:#fff9">
                <div>
                    <span class="el-icon el-icon-loading" style="font-size:24px;"></span>
                </div>
                <div v-if="message">{{message}}</div>
            </div>

            <div v-if="!runing" style="width:100%;height:100%;background:#0006;position:absolute;top:0;left:0;">
                <div style="position:absolute;top:50%;margin-top:-30px;left:50%;margin-left:-30px;">
                    <span @click="play" class="ifp-camera-btn ifp-camera-btn-play el-icon el-icon-video-play" style="font-size:60px;"></span>
                </div>
            </div>

            <div v-if="toolbar" style="position:absolute;bottom:0;left:0;right:0;background-color:#0006;padding:8px;text-align:left;">
                <span v-if="runing" @click="stop" class="ifp-camera-btn ifp-camera-btn-play el-icon el-icon-video-pause" style="font-size:24px;"></span>
                <span v-else @click="play" class="ifp-camera-btn ifp-camera-btn-play el-icon el-icon-video-play" style="font-size:24px;"></span>
            </div>
        </div>
        `,
        methods:{
            stop(){
                this.controller.stop();
                this.runing = this.controller.isRunning();
            },
            play(){
                this.controller.play();
                this.runing = this.controller.isRunning();
            },
            showToolbar(time=1000){
                this.toolbar = true;
                if(this.toolbarTimoutid){
                    window.clearTimeout(this.toolbarTimoutid);
                    this.toolbarTimoutid = null;
                }
                this.toolbarTimoutid = setTimeout(()=>this.hideToolbar(),time)
            },
            hideToolbar(){
                this.toolbar = false;
                if(this.toolbarTimoutid){
                    window.clearTimeout(this.toolbarTimoutid);
                    this.toolbarTimoutid = null;
                }
            },
            mouseover(){
                this.showToolbar()
            },
            mousemove(){
                this.showToolbar()
            },
            mouseleave(){
                this.hideToolbar()
            }
        }
    }

    function createQuery(option,ondata,onerror){
        let xhr = new XMLHttpRequest();
        xhr.withCredentials = true; //跨域允许附带自定义数据
        xhr.responseType = "blob";   
        xhr.timeout = 60000;  //毫秒，0表示永不超时
        xhr.onload = function () {
            if (xhr.status == 200) {
                ondata(xhr.response)
            }
        }
        xhr.onerror=()=>onerror&&onerror("正在连接")
        return ()=>{
            xhr.open("GET", option.src);
            xhr.send();
        }
    }


    function create(option){
        let timeoutid = null;
        let img = new Image();
        let runing = false;

        let MyOptions = {
            src:option.src,
        }

        let queryBlob = createQuery(MyOptions,function(blob){
            img.src && window.URL.revokeObjectURL(img.src);
            img.src = window.URL.createObjectURL(blob);
        },(err)=>{
            if(!runing){ return; }
            option.onerror(err);
            timeoutid = requestAnimationFrame(query);
        });

        let query = ()=>{
            timeoutid = null;
            queryBlob();
        }

        img.onload = ()=>{
            if(!runing){ return; }
            option.ondata && option.ondata(img);
            timeoutid = requestAnimationFrame(query);
        }

        return {
            updateSrc(src){
                MyOptions.src = src;
            },

            isRunning(){ return runing; },
            
            play:function(){
                if(runing){ return; }
                runing = true;
                query();
            },
            
            stop:function(){
                if(timeoutid){
                    window.cancelAnimationFrame(timeoutid);
                    timeoutid = null;
                }
                runing = false;
                option.onerror&&option.onerror(null);
            }
        }
    }

    // 输出帧率
    function createPrintFPS(){
        let lastTime = Date.now();
        let curTime;
        let fps;
        return function printFPS(){
            // 显示当前帧率
            curTime = Date.now();
            fps = curTime===lastTime?0:1000/(curTime-lastTime);
            lastTime = curTime
            return Math.round(fps);
        }
    }
})