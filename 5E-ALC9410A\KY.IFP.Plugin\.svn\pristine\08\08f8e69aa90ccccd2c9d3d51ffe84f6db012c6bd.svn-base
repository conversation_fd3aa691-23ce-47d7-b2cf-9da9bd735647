﻿using System;
using System.Collections.Generic;
using System.IO;

namespace COM.IFP.ComputerInfo
{
    /// <summary>
    /// 获得硬盘信息
    /// </summary>
    public class DiskInfo
    {
        public static Dictionary<string, long> AllDiskInfo()
        {
            long gb = 1024 * 1024 * 1024;


            Dictionary<string, long> diskMap = new Dictionary<string, long>();
            String[] diskNames = Environment.GetLogicalDrives();

            DriveInfo[] allDirves = DriveInfo.GetDrives();
            //检索计算机上的所有逻辑驱动器名称
            foreach (DriveInfo drive in allDirves)
            {
                //Fixed 硬盘
                //Removable 可移动存储设备，如软盘驱动器或USB闪存驱动器。
                //Console.Write(item.Name + "---" + item.DriveType);
                //判断是否是固定磁盘
                if (drive.DriveType == DriveType.Fixed)
                {
                    long totalSize = drive.TotalSize / gb;
                    long freeSize = drive.TotalFreeSpace / gb;
                    //long useSize = totalSize - freeSize;
                    diskMap.Add($"diskFree_{drive.Name.ToUpper().Replace(":\\", "")}", freeSize);
                    diskMap.Add($"diskTotal_{drive.Name.ToUpper().Replace(":\\", "")}", totalSize);
                }
            }
            return diskMap;
        }
    }
}
