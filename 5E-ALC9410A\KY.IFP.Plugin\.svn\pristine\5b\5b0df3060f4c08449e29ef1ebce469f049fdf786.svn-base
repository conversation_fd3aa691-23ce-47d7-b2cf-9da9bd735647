define(["require", "./base", "jquery","renderer","jclass"],function(require, base, $,renderer,jclass) {
	var defaultOption = {
		url: "",
		hidden:false
	}
	
	//获取绝对路径
	var toAbsURL = function(){
	    var directlink = function(url){
	        var a = document.createElement('a');
	        a.href = url;
	        return a.href;
	    };
	    return directlink('') === '' ? function(url){
	        var div = document.createElement('div');
	        div.innerHTML = '<a href="' + url.replace(/"/g, '%22') + '"/>';
	        return div.firstChild.href;
	    } : directlink;
	}();
	

	// 动画效果：class：fade

	return jclass(base, {
		name:"controlPage",
		pagecontroller:null,
		option:{

		},
		show:function(v){
			this.option.hidden=false;
			this.$container.show(v);
			if(!this.loaded){
				this.loadPage();
			}
		},
		hide:function(){
			this.$container.hide();
			this.option.hidden=true;
		},
		render:function() {
			this.controller.bindControllerEvent("afterOnLoad",this.alterOnLoad,this)
			this.base.apply();
		},
		loaded:false,
		loadPage:function(url,option){
			var _this = this;
			var url = this.option.url = url || this.option.url;
			if(url) {
				this.loaded = true;
				if(this.pagecontroller){
					this.pagecontroller.destroy();
					this.pagecontroller = null;
				}
				require(["text!" + toAbsURL(url)],function(html){
					var REG_BODY = /<body([^>]*)>([\s\S]*?)<\/body>/;
					var REG_style = /<style[^>]*>([\S\s]*?)<\/style>/g;
			        function getBody(content){
			            var result = REG_BODY.exec(content);
			            if(result && result.length === 3){
				            var html = result[2];
				            var styles = content.match(REG_style);
				            if(styles&&styles.length){
				            	html+="\n"+styles.join("\n");
				            }
			                return {
			                	attrs:result[1],
			                	content:html
			                };
			            }
			            return content;
			        }
			        function getStyle(content){
			            var result = REG_BODY.exec(content);
			            if(result && result.length === 3)
			                return {
			                	attrs:result[1],
			                	content:result[2]
			                };
			            return content;
			        }
			        var body = getBody(html);
			        _this.$container.empty().html(body.content);
					
					var atts = (/controller=["']{1}([^'"]*)["']{1}/).exec(body.attrs);
					var controller = "controller";
			        if(atts&&atts.length==2){
			        	var pathArry = atts[1].split("/");
			        	var jspath = atts[1];
			        	controller = jspath;
			        }
			        if(url.indexOf("/")>-1){
			        	var arr = url.split("/");
			        	var path = arr.slice(0,arr.length-1).join("/");
			        	if(path){path+="/"}
			        	controller = path+controller;
			        }
			        renderer.rquirePageControlPlus().then(function(){
						require([controller],function(Controller){
							_this.pagecontroller = new Controller(_this.$container,$.extend(true,{
								url:F.util.parseURL(url),
								type:"ajax"
							},option));
						})
			        });
				});
			}
		},
		alterOnLoad:function(){
			if(this.option.hidden){
				this.$container.hide();
				return;
			}
			this.loadPage();
		}
	})
});