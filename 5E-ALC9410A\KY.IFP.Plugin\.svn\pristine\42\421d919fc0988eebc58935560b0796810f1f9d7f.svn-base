# ifp-tabs

继承于：el-tabs

<a href="/demo/components/ifp-tabs.html">示例</a>

> 必须使用 ifp-tab-pane 替换 el-tab-pane

## 扩展属性

### flex

flex 用于 内容适应 tabs 的场景

例如

```html

<!--1. 高度适应 flex 容器-->
<!--ifp-tabs flex v-model="activeName" class="flex-item"-->

<!--2. 定高300px-->
<ifp-tabs flex v-model="activeName" style="height:300px;">
    <ifp-tab-pane label="用户管理" name="first" class="flex">
        <h2>用户管理内容xxx</h2>
        <div class="flex-item" style="background-color:#000;">
            <!--内容会占据剩余空间-->
        </div>
    </ifp-tab-pane>
    <ifp-tab-pane label="系统设置" name="second" class="flex">
        <div class="flex-item">
            <!--内容会占据剩余空间-->
        </div>
    </ifp-tab-pane>
</ifp-tabs>
```

```js
{
    data(){
        return {
            activeName:'first'
        }
    }
}
```

## ifp-tab-pane

继承于：el-tab-pane

配合 `ifp-tabs` 使用
