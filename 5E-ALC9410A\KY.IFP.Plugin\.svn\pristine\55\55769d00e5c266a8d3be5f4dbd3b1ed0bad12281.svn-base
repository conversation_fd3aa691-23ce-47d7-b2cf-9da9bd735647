# 通用下拉控件 | ifp-select

__继承于 [el-select](https://element.eleme.cn/#/zh-CN/component/select)__

<a href="/demo/components/ifp-select.html">示例页面</a>

## 功能

* 使用对象数组加载选项列表
* 远程加载
* 动态过滤选项
* 仅显示文本
* 多选 支持“,”分割字符串 例如'id2,id2'
* 始终显示包含当前值选项

## 属性

|属性|类型|默认值|说明|
|-|-|-|-|
|value-key|string|''|items,url 依赖参数[必选]，用来配置 value 属性名|
|label-key|string|''|items,url 依赖参数[必选]，用来配置 label 属性名|
|disabled-key|string|''|items,url 依赖参数[可选选]，用来配置 disabled 属性名|
|items|Array|[]|绑定一个数组|
|url|string|''|api，需要返回一个数组|
|post|Object|unddefind|请求 url 传递的参数，undefined 或 {}|
|filter|Function|function(list,arg){return list}|过滤函数|
|filter-arg|string|''|过滤函数依赖值，当该值改变，将触发 配置的 filter 函数|
|canedit|bool|true|false 不显示 输入框|
|alwaysShowCurrentValue|bool|true|如果控件值在选项中已被过滤，仍会显示|
|valueIsNumber|bool|false|","分割的字符串默认为字符串型，此配置指定为number|

## 事件

* data-load
* data-load-items
* data-load-url

### data-load
更新 items、url、post 导致的下拉列表刷新，均会触发此事件，参数：list
### data-load-items
更新 items 导致的下拉列表刷新，会触发此事件，参数：list
### data-load
更新 url、post 导致的下拉列表刷新，均会触发此事件，参数：list

## 代码示例

### 仅显示文本

一般用在table中

```html
  <ifp-select v-model="value" :canedit="false" placeholder="请选择">
    <ifp-option label="选项1" value="001"></ifp-option>
    <ifp-option label="选项2" value="002"></ifp-option>
  </ifp-select>
```

### 加载数组 
```html
<ifp-select v-model="value1" :items="list1" value-key="id" label-key="text"></ifp-select>
```

```js
{
    data(){
        return {
            value1:'',
            items:[
                {id:1,text:'选项1'}
            ]
        }
    }
}
```

### 加载URL

相关参数
* post 参数可选
* url

```html
<!--加载 url-->
<ifp-select v-model="value1" url="/API/XXXX" value-key="id" label-key="text"></ifp-select>
<!--加载 url ，并传参-->
<ifp-select v-model="value1" url="/API/XXXX" :post="{arg1:'xxx'}" value-key="id" label-key="text"></ifp-select>
```

```js
{
    data(){
        return {
            value1:''
        }
    }
}
```

url 返回

注意：元素的属性要与 valueKey 和 labelKey 保持一致
```json
[
    {"id":"001","text":"选项1"}
]
```

### 筛选

使用 url 或 items 时，根据条件动态过滤

* filter 过滤函数，接收两个参数：列表、filger-arg
* filter-arg 会传入过滤函数，只有这个值改变了，才会触发筛选函数

```html
<!--筛选条件-->
<ifp-input v-model="filterText"></ifp-input>

<!--使用过滤函数-->
<ifp-select v-model="value1" :items="list1" value-key="id" label-key="text"
    :filter="filterHandle" :filter-arg="filterText"
></ifp-select>

<!--使用过滤函数-->
<ifp-select v-model="value2" url="/API/XXXX" :post="{arg1:'xxx'}"
    :filter="filterHandle" :filter-arg="filterText"
></ifp-select>
```

```js
{
    data(){
        return {
            value1:'',
            value2:'',
            filterText:'',
            items:[
                {id:11,text:'选项11'},
                {id:12,text:'选项12'},
                {id:21,text:'选项21'}
            ]
        }
    },
    methods:{
        // 返回过滤后数组
        filterHandle(list,arg){
            return list.filter(item=>item.text.indexOf(arg)>-1);
        }
    }
}
```

### 配置可选项 
```html
<ifp-select v-model="value1" :items="list1" disabled-key="disabled" value-key="id" label-key="text"></ifp-select>
```

```js
{
    data(){
        return {
            value1:'',
            items:[
                {id:1,text:'选项1'},
                {id:2,text:'选项2',disabled:true},
                {id:3,text:'选项3'}
            ]
        }
    }
}
```