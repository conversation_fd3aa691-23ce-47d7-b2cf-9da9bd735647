﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统授权</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button code="B1" @click="add()">新增</ifp-button>
            <ifp-button code="B2" @click="view()" :disabled="viewer.select == null">查看</ifp-button>
            <ifp-button code="B3" @click="zf()" :disabled="viewer.select == null">作废</ifp-button>
        </ifp-toolbar>
        <ifp-searchbar @search="updateList" @reset="onSelect">
            <ifp-form-item label="授权日期">
                <ifp-date-picker v-model="viewer.filter[0].CreateTime.Value" type="date" placeholder="选择日期"></ifp-date-picker>
            </ifp-form-item>
            <ifp-form-item label="至" label-width="68px">
                <ifp-date-picker v-model="CreateTime1" type="date" placeholder="选择日期"></ifp-date-picker>
            </ifp-form-item>
        </ifp-searchbar>
        <ifp-panel-table class="flex-item padding">
            <ifp-table :data="viewer.source" style="width:100%;" height="100%"
                       border ref="table" highlight-current-row
                       @sort-change="sortChange" :default-sort="{prop: 'CreateTime', order: 'descending'}" @current-change="onChange">
                <el-table-column type="index" :index="indexMethod"
                                 width="50">
                </el-table-column>
                <el-table-column prop="QxCode"
                                 label="权限编码"
                                 header-align="center"
                                 align="center">
                </el-table-column>
                <el-table-column prop="QxName"
                                 label="权限名称"
                                 header-align="center"
                                 align="center">
                </el-table-column>
                <el-table-column prop="SQR"
                                 label="授权人登录名"
                                 header-align="center"
                                 align="center">
                </el-table-column>
                <el-table-column prop="SQRName"
                                 label="授权人名"
                                 header-align="center"
                                 align="center">
                </el-table-column>
                <el-table-column prop="BSQR"
                                 label="被授权人登录名"
                                 header-align="center"
                                 align="center">
                </el-table-column>
                <el-table-column prop="BSQRName"
                                 label="被授权人名"
                                 header-align="center"
                                 align="center">
                </el-table-column>
                <el-table-column prop="CreateTime"
                                 label="授权时间"
                                 header-align="center"
                                 width="150"
                                 align="center"
                                 :formatter="$TableFormatter.datetime">
                </el-table-column>
                <el-table-column prop="KSSJ"
                                 label="授权开始时间"
                                 header-align="center"
                                 width="150"
                                 align="center"
                                 :formatter="$TableFormatter.datetime">
                </el-table-column>
                <el-table-column prop="JSSJ"
                                 label="授权结束时间"
                                 header-align="center"
                                 width="150"
                                 align="center"
                                 :formatter="$TableFormatter.datetime">
                </el-table-column>
                <el-table-column prop="ZFBZ"
                                 label="状态"
                                 header-align="center"
                                 align="center">
                    <template slot-scope="scope">
                        <ifp-select-ywlx v-model="scope.row.MZ4010" :ywlx="4010" :canedit="false" selectstyle="width: 100%"></ifp-select-ywlx>
                        {{scope.row.ZFBZ == 0 ? "启用" : "作废"}}
                    </template>
                </el-table-column>
                <el-table-column prop="ZFR"
                                 label="作废人"
                                 header-align="center"
                                 align="center">
                </el-table-column>
                <el-table-column prop="ZFSJ"
                                 label="作废时间"
                                 width="150"
                                 align="center"
                                 :formatter="$TableFormatter.datetime">
                </el-table-column>
                <el-table-column prop="Remarks"
                                 label="备注"
                                 header-align="center"
                                 align="center">
                </el-table-column>
            </ifp-table>
            <template v-slot:pagination>
                <ifp-pagination @size-change="sizeChange" @current-change="pageChange" :current-page="viewer.paging.Page"
                                :page-sizes="[10,15,20,50,100]" :page-size="viewer.paging.Size" layout="total, sizes, prev, pager, next, jumper"
                                :total="viewer.paging.Sums"><detail></detail></ifp-pagination>
            </template>
        </ifp-panel-table>
        <ifp-dialog :title="editor.title" :visible.sync="editor.dialog" width="1200px">
            <ifp-sqjlxq-detail v-if="editor.dialog" @success="onSelect();editor.dialog = false" :source="editor.source" :qxgroup="editor.qxGroup" :action="editor.action"></ifp-sqjlxq-detail>
        </ifp-dialog>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>