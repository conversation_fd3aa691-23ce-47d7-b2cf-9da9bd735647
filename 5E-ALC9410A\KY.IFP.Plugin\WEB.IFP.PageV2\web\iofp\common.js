﻿define([
    "cookie", 
    "topwindow", 
    "crypto-js/md5",
    "iofp/services/service.user"
], function (cookie, topwindow, MD5,userService) {
    topwindow.authKey = topwindow.authKey || ""; //"Mw4qdSAmYn+Jlys5bpn/eCMA0KMQCQ9qmdd1WX0Nqo5GMYaRQBuXmg==";
    var common = {
        //设置全局鉴权密钥
        setAuthKey: function (value) {
            topwindow.authKey = value;
        },

        //获取全局鉴权密钥
        getAuthKey: function () {
            return topwindow.authKey;
        },

        //当前登录用户对象
        getLoginUser: function () {
            var cookieName = "kjsoftUserCookie"; //window.location.host.replace(new RegExp("\\.", "gm"), "_").replace(new RegExp(":"), "_") +

            var cookieValue = cookie.get(cookieName);
            return Promise.resolve(cookieValue && JSON.parse(window.decodeURIComponent(cookieValue)) || null);
        },

        //当前登录用户对象
        getLoginUserSync: function () {
            var cookieName = "kjsoftUserCookie"; //window.location.host.replace(new RegExp("\\.", "gm"), "_").replace(new RegExp(":"), "_") +

            var cookieValue = cookie.get(cookieName);
            return cookieValue && JSON.parse(window.decodeURIComponent(cookieValue)) || null;
        },

        //判断当前用户是否拥有某个权限
        checkRole: function (roleid) {
            var roleids = common.getLoginUserSync().RoleIds;
            if (roleids.indexOf("role0000") != -1) return true; //管理员权限

            if (roleids.indexOf(roleid) != -1) return true;

        },
        //绑定用户名密码到传入对象
        createCommandArgs: function (dataobj) {
            dataobj = dataobj || {};
            var userObj = common.getLoginUserSync();
            dataobj.User = userObj.UsiLoginName;
            dataobj.Pwd = MD5(common.getAuthKey() + userObj.UsiLoginName).toString();
            return dataobj;
        },

        loginOut:function(){
            userService.loginOut({}).then(function (data) {
                try {
                    //data = JSON.parse(data);
                    if (data._data) {
                        data = JSON.parse(data._data);
                    }
                }
                catch (ex) {
                    error("服务器异常");
                    console && console.error("请求失败，获取到的数据为：" + data);
                }
                if (data.code == "0") {
                    window.location = data.url;
                } else {
    
                }
            })
        }
    }
    //$("#a").click(common.checkRole)
    return common;
})
