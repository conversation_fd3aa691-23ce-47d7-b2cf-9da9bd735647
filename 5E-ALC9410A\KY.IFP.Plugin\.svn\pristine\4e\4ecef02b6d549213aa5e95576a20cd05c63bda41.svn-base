﻿using KY.IFP.Runtime;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace KY.IFP.Service.Agency
{

    internal class Request : ApiRequest
    {
        public override string Method { get; }

        public override NameValueCollection Headers { get; }

        public override NameValueCollection Querys { get; }

        public override Stream Stream { get; }

        public Request(HttpRequest request)
        {
            Method = request.Method;

            Headers = new NameValueCollection();
            foreach ((string key, StringValues values) in request.Headers)
            {
                foreach (var value in values)
                {
                    Headers.Add(key, value);
                }
            }

            Querys = new NameValueCollection();
            foreach ((string key, StringValues values) in request.Query)
            {
                foreach (var value in values)
                {
                    Querys.Add(key, value);
                }
            }

            Stream = request.Body;
        }
    }

    internal class Response : ApiResponse
    {
        private HttpResponse response;

        public override Stream Stream { get; }

        public override void Redirect(string target)
        {
            response.Redirect(target);
        }

        public Response(HttpResponse response)
        {
            this.response = response;

            Stream = response.Body;
        }
    }

    internal class Content : ApiContent
    {
        public override Request Request { get; }

        public override Response Response { get; }

        public Content(HttpContext content)
        {
            Request = new Request(content.Request);
            Response = new Response(content.Response);
        }
    }
}
