define(["./echart.meichang","jquery","services/meichang"],function(base,jquery,mcservice){
	
	var defaultOption = {
		datasetting:{selectType:"MD"}
	}

	
	return F.class(base,{
		name:"control-chart-meichang-show",
		mdgc:null,//煤堆构成
		hyzb:null,//化验指标
		load:function(mcid,option,noCache,callback){
			var baseFn = this.base;
			var _this = this;
			mcservice.getMeiDuiChartData(mcid,noCache,option)
			.then(function(data){
				_this.mdgc = data.mdgc;
				_this.hyzb = data.hyzb;
				//设置煤场高度煤场高度
				if(data.mc && data.mc.height){
					$.extend(option,{
						yAxis:{
							max:data.mc.height
						}
					});
				}
				//调用父控件load方法
				baseFn.call(_this,data,option);
				callback && callback(data);
			})["catch"](function(error){
				$.bootoast.danger(error);
				_this.log.error(error);
			});
		},

		createDefaultOption:function(container,option){
			return $.extend(true,this.base.apply(this,arguments),defaultOption);
		},
		
		//读取指定化验指标的名称
		getHyzbText : function(zblx, defaulText){
			var _this = this;
			for(var i=0; i<_this.hyzb.length; i++){
				if(_this.hyzb[i].zblx1004==zblx){
					return _this.hyzb[i].displayText;
				}
			}
			return defaulText;
		},
		
		render:function(){
			var _this = this;
			this.base.apply(this,arguments);
			this.bind("mdtooltip",function(type,data){
				var conf = {};
				for(var i=0; i<_this.mdgc.length; i++){
					if(_this.mdgc[i].id.indexOf("MZ4010")!=-1){
						conf["mz"] = "煤种";
					}
					if(_this.mdgc[i].id.indexOf("MZFL4011")!=-1){
						conf["mzfl"] = "煤质分类";
					}
					if(_this.mdgc[i].id.indexOf("KD4001")!=-1){
						if(data.kuanghao){
							conf["kuanghao"] = "矿号";
						}else{
							conf["kd"] = "矿点";
						}
					}
					if(_this.mdgc[i].id.indexOf("GHDW4002")!=-1){
						conf["ghdw"] = "供货单位";
					}
					if(_this.mdgc[i].id.indexOf("CCH")!=-1){
						conf["cch"] = "船名航次";
					}
					if(_this.mdgc[i].id.indexOf("PCBM")!=-1){
						conf["pcbm"] = "入厂批次";
					}
				}
				conf["cmts"] = "存煤天数";
				conf["syml"] = "存煤量（吨）";
				conf["syRz"] = _this.getHyzbText("10040001", "热值");
				conf["sySf"] = _this.getHyzbText("10040005", "水分");
				conf["syLf"] = _this.getHyzbText("10040003", "硫分");
				conf["syHff"] = _this.getHyzbText("10040002", "挥发分");
				conf["syHf"] = _this.getHyzbText("10040004", "灰分");
				conf["syHrd"] = _this.getHyzbText("10040006", "灰熔点");
				conf["trmj"] = "天然煤价";
				conf["bmdj"] = "标煤单价";
				return _this.getTooltipContent(data,data.mdmc,conf);
			});
			this.bind("fqtooltip",function(type,data){
				return _this.getTooltipContent(data,data.mcfqmc,{
					dtfqmc:"动态分区",
					syml:"存煤量（吨）"
				});
			});
		},
		
		init:function(){
			this.base.apply(this,arguments);
		}
	});
});