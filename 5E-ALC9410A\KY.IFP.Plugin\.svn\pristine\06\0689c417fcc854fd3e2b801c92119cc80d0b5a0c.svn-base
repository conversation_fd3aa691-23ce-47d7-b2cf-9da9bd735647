﻿using COM.IFP.Common;

namespace ORM.IFP.ViewModel
{
    /// <summary>
    /// 共用模块层
    /// </summary>
    public class AutoCompleteParam
    {
        public string _type { set; get; }

        public string term { set; get; }
        public int page { set; get; }
        public int rows { set; get; }
        public string idfield { set; get; }
        public string textfield { set; get; }
        public string searchfield { set; get; }


        public AutoCompleteParam(string idField, string textField, string searchField)
        {
            this._type = WebHelper.getRequestParam("_type");
            this.page = string.IsNullOrEmpty(WebHelper.getRequestParam("page")) ? 1 : int.Parse(WebHelper.getRequestParam("page"));
            this.term = WebHelper.getRequestParam("term");
            this.rows = 6;// 前端控件展示数据为6条,使用默认值
            this.idfield = idField;
            this.textfield = textField;
            this.searchfield = searchField;
        }

    }
}
