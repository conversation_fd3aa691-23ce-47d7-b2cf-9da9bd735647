
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>dicb,grid2</title>
</head>
<body controller style="position: static;">
    <div form="forms/toolbar" class="layout-h">
        <a id="returnBtn" control="controls/button" option="{command:'close'}">退出</a>
        <a id="selBtn" control="controls/button" option="{}">查看输入内容</a>
    </div>
    <div class="layout-h padding" style="padding-bottom:0;">
        <div class="form-header">
            <a id="t_addBtn" control="controls/button" option="{}">新增</a>
            <a id="t_delBtn" control="controls/button" option="{}">删除光标行</a>
            <a id="t_delCheckBtn" control="controls/button" option="{}">删除勾选行</a>
        </div>
    </div>
    <div class="layout-c padding" style="padding-top:0;">
        <table id="grid33" control="controls/grid" option='{
            cellEdit:true,
            frozen:true,
            multiselect:true,
            multiboxonly:true,
            pager:"#pager1"
        }'>
            <thead>
                <tr>
                    <th>操作</th>
                    <th>图片</th>
                    <th>姓名</th>
                    <th>邮箱</th>
                    <th>出生日期</th>
                    <th>城市</th>
                    <th>证件号</th>
                    <th>性别</th>
                    <th>性别2</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td option="{name:'gid',hidden:true,key: true,frozen: true}"></td>
                    <td option="{name:'img',sortable:true,editable:false,frozen: true,align:'center'}"></td>
                    <td option="{name:'name',frozen: true,editable:false,align:'left',width:100}"></td>
                    <td option="{name:'email',frozen: true,editable:false,width:200,align:'left'}"></td>
                    <td option="{name:'borthday',editable:false,width:200}"></td>
                    <td option="{name:'city',editable:false,align:'left',width:300}"></td>
                    <td option="{name:'cid',editable:false,width:200}"></td>
                    <td option="{name:'gender',editable:false}"></td>
                    <td option="{name:'gender2',editable:false}"></td>
                </tr>
            </tbody>
        </table>
        <div id="pager1"></div>
    </div>
    <script src="/iofp/starter.js"></script> 
</body>
</html>