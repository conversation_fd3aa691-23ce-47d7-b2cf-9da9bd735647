﻿define(["iofp/api", "zutil"], function (API, zutil) {
    return {
        el: "#app",
        props: {
            action: {},  //操作类型
            source: {}
        },
        data() {
            return {
                role: zutil.extend(true,
                    { Gid: null, RoleName: null, RoleCode: null, RoleName: null, RoleDoc: null },
                    this.source)
            }
        },
        created() {
        },
        watch: {
            source: function () {
                this.$set(this, "role", zutil.extend(true,
                    { Gid: null, RoleName: null, RoleCode: null, RoleName: null, RoleDoc: null },
                    this.source))
            }
        },
        methods: {
            onSubmit() {
                var _this = this;
                _this.$refs.form.validate((valid) => {
                    if (valid) {
                        API.GetAction("API/IFP/Rights/Role/SaveRole", _this.role).then(x => {
                            if (x.success) {
                                this.$message.success(_this.action + '成功');
                                this.$emit("submit")
                            } else {
                                this.$message.error("失败。" + x.msg);
                            }
                        }).catch(e => {
                            this.$message.error(e);
                        });
                    } else {

                    }
                })
            },
            onCancel() {
                this.$emit("cancel")
            }
        }
    }
})