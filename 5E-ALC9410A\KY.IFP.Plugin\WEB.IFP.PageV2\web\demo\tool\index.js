;define([
"sysSetting",
"css!./index.css"
],
function(sysSetting){
	var glyphicon = ['glyphicon-asterisk','glyphicon-plus','glyphicon-euro','glyphicon-eur','glyphicon-minus','glyphicon-cloud','glyphicon-envelope','glyphicon-pencil','glyphicon-glass','glyphicon-music','glyphicon-search','glyphicon-heart','glyphicon-star','glyphicon-star-empty','glyphicon-user','glyphicon-film','glyphicon-th-large','glyphicon-th','glyphicon-th-list','glyphicon-ok','glyphicon-remove','glyphicon-zoom-in','glyphicon-zoom-out','glyphicon-off','glyphicon-signal','glyphicon-cog','glyphicon-trash','glyphicon-home','glyphicon-file','glyphicon-time','glyphicon-road','glyphicon-download-alt','glyphicon-download','glyphicon-upload','glyphicon-inbox','glyphicon-play-circle','glyphicon-repeat','glyphicon-refresh','glyphicon-list-alt','glyphicon-lock','glyphicon-flag','glyphicon-headphones','glyphicon-volume-off','glyphicon-volume-down','glyphicon-volume-up','glyphicon-qrcode','glyphicon-barcode','glyphicon-tag','glyphicon-tags','glyphicon-book','glyphicon-bookmark','glyphicon-print','glyphicon-camera','glyphicon-font','glyphicon-bold','glyphicon-italic','glyphicon-text-height','glyphicon-text-width','glyphicon-align-left','glyphicon-align-center','glyphicon-align-right','glyphicon-align-justify','glyphicon-list','glyphicon-indent-left','glyphicon-indent-right','glyphicon-facetime-video','glyphicon-picture','glyphicon-map-marker','glyphicon-adjust','glyphicon-tint','glyphicon-edit','glyphicon-share','glyphicon-check','glyphicon-move','glyphicon-step-backward','glyphicon-fast-backward','glyphicon-backward','glyphicon-play','glyphicon-pause','glyphicon-stop','glyphicon-forward','glyphicon-fast-forward','glyphicon-step-forward','glyphicon-eject','glyphicon-chevron-left','glyphicon-chevron-right','glyphicon-plus-sign','glyphicon-minus-sign','glyphicon-remove-sign','glyphicon-ok-sign','glyphicon-question-sign','glyphicon-info-sign','glyphicon-screenshot','glyphicon-remove-circle','glyphicon-ok-circle','glyphicon-ban-circle','glyphicon-arrow-left','glyphicon-arrow-right','glyphicon-arrow-up','glyphicon-arrow-down','glyphicon-share-alt','glyphicon-resize-full','glyphicon-resize-small','glyphicon-exclamation-sign','glyphicon-gift','glyphicon-leaf','glyphicon-fire','glyphicon-eye-open','glyphicon-eye-close','glyphicon-warning-sign','glyphicon-plane','glyphicon-calendar','glyphicon-random','glyphicon-comment','glyphicon-magnet','glyphicon-chevron-up','glyphicon-chevron-down','glyphicon-retweet','glyphicon-shopping-cart','glyphicon-folder-close','glyphicon-folder-open','glyphicon-resize-vertical','glyphicon-resize-horizontal','glyphicon-hdd','glyphicon-bullhorn','glyphicon-bell','glyphicon-certificate','glyphicon-thumbs-up','glyphicon-thumbs-down','glyphicon-hand-right','glyphicon-hand-left','glyphicon-hand-up','glyphicon-hand-down','glyphicon-circle-arrow-right','glyphicon-circle-arrow-left','glyphicon-circle-arrow-up','glyphicon-circle-arrow-down','glyphicon-globe','glyphicon-wrench','glyphicon-tasks','glyphicon-filter','glyphicon-briefcase','glyphicon-fullscreen','glyphicon-dashboard','glyphicon-paperclip','glyphicon-heart-empty','glyphicon-link','glyphicon-phone','glyphicon-pushpin','glyphicon-usd','glyphicon-gbp','glyphicon-sort','glyphicon-sort-by-alphabet','glyphicon-sort-by-alphabet-alt','glyphicon-sort-by-order','glyphicon-sort-by-order-alt','glyphicon-sort-by-attributes','glyphicon-sort-by-attributes-alt','glyphicon-unchecked','glyphicon-expand','glyphicon-collapse-down','glyphicon-collapse-up','glyphicon-log-in','glyphicon-flash','glyphicon-log-out','glyphicon-new-window','glyphicon-record','glyphicon-save','glyphicon-open','glyphicon-saved','glyphicon-import','glyphicon-export','glyphicon-send','glyphicon-floppy-disk','glyphicon-floppy-saved','glyphicon-floppy-remove','glyphicon-floppy-save','glyphicon-floppy-open','glyphicon-credit-card','glyphicon-transfer','glyphicon-cutlery','glyphicon-header','glyphicon-compressed','glyphicon-earphone','glyphicon-phone-alt','glyphicon-tower','glyphicon-stats','glyphicon-sd-video','glyphicon-hd-video','glyphicon-subtitles','glyphicon-sound-stereo','glyphicon-sound-dolby','glyphicon-sound-5-1','glyphicon-sound-6-1','glyphicon-sound-7-1','glyphicon-copyright-mark','glyphicon-registration-mark','glyphicon-cloud-download','glyphicon-cloud-upload','glyphicon-tree-conifer','glyphicon-tree-deciduous','glyphicon-cd','glyphicon-save-file','glyphicon-open-file','glyphicon-level-up','glyphicon-copy','glyphicon-paste','glyphicon-alert','glyphicon-equalizer','glyphicon-king','glyphicon-queen','glyphicon-pawn','glyphicon-bishop','glyphicon-knight','glyphicon-baby-formula','glyphicon-tent','glyphicon-blackboard','glyphicon-bed','glyphicon-apple','glyphicon-erase','glyphicon-hourglass','glyphicon-lamp','glyphicon-duplicate','glyphicon-piggy-bank','glyphicon-scissors','glyphicon-bitcoin','glyphicon-btc','glyphicon-xbt','glyphicon-yen','glyphicon-jpy','glyphicon-ruble','glyphicon-rub','glyphicon-scale','glyphicon-ice-lolly','glyphicon-ice-lolly-tasted','glyphicon-education','glyphicon-option-horizontal','glyphicon-option-vertical','glyphicon-menu-hamburger','glyphicon-modal-window','glyphicon-oil','glyphicon-grain','glyphicon-sunglasses','glyphicon-text-size','glyphicon-text-color','glyphicon-text-background','glyphicon-object-align-top','glyphicon-object-align-bottom','glyphicon-object-align-horizontal','glyphicon-object-align-left','glyphicon-object-align-vertical','glyphicon-object-align-right','glyphicon-triangle-right','glyphicon-triangle-left','glyphicon-triangle-bottom','glyphicon-triangle-top','glyphicon-console','glyphicon-superscript','glyphicon-subscript','glyphicon-menu-left','glyphicon-menu-right','glyphicon-menu-down','glyphicon-menu-up']
	var kjicon = ["kjicon-baocun,保存","kjicon-chaxunzhongzhi,查询重置","kjicon-chexiao,撤销","kjicon-chakan,查看","kjicon-caigouyinqing,采购引擎","kjicon-chanshaoyinqing,掺烧引擎","kjicon-chukuguanli,出库管理","kjicon-dayin,打印","kjicon-chuandi,传递","kjicon-chushihua,初始化","kjicon-daochu,导出","kjicon-daoru,导入","kjicon-tiaozheng,调整","kjicon-doulunjijiankong,斗轮机监控","kjicon-duifang,堆放","kjicon-duihao,对号","kjicon-fangdajing,放大镜","kjicon-fuzhi,复制","kjicon-fuzhixinzeng,复制新增","kjicon-fenxi,分析","kjicon-fuhequxian,负荷曲线","kjicon-guanbi,关闭","kjicon-gengxin,更新","kjicon-hunpeiyinqingsvg,混配引擎svg","kjicon-jichuxinxi,基础信息","kjicon-jisuan,计算","kjicon-jiantou,箭头","kjicon-jiazai,加载","kjicon-lajitong,垃圾桶","kjicon-kucunguanli,库存管理","kjicon-liexianshezhi,列显设置","kjicon-liuchengguiji,流程轨迹","kjicon-kucunyinqing,库存引擎","kjicon-mobanxiazai,模板下载","kjicon-paibanbiao,排班表","kjicon-piliangtianchong,批量填充","kjicon-piliangxinzeng,批量新增","kjicon-pingjunfentan,平均分摊","kjicon-qiyong,启用","kjicon-quanpingshousuo,全屏收缩","kjicon-quanping,全屏","kjicon-queren,确认","kjicon-rukuguanli,入库管理","kjicon-rili,日历","kjicon-shanchu,删除","kjicon-shanchuhang,删除行","kjicon-shangchuanfujian,上传附件","kjicon-shangcang,上仓","kjicon-shijian,时间","kjicon-shezhi,设置","kjicon-shuaxin,刷新","kjicon-tongjibaobiao,统计报表","kjicon-tingyong,停用","kjicon-tongjichaxun,统计查询","kjicon-tuichu,退出","kjicon-xitongpeizhi,系统配置","kjicon-xiangshangshousuo,向上收缩","kjicon-xitongshouye,系统首页","kjicon-xinzeng,新增","kjicon-xinzenghang,新增行","kjicon-xiugai,修改","kjicon-xuanzecheliang,选择车辆","kjicon-xuanzepici,选择批次","kjicon-yewuzongtu,业务总图","kjicon-zhinengfenxi,智能分析","kjicon-wenhao,问号","kjicon-shangjiantou,上箭头","kjicon-xiajiantou,下箭头","kjicon-loading,loading","kjicon-jinggao,警告"];

	var maps = {};

	(function(){
		maps.kjicon = {};
		let arr;
		kjicon.forEach(function(item){
			arr = item.split(",");
			maps.kjicon[arr[0]]=arr[1]
		})
	})();

	var reg = /([\w|-]+)\:before/g;
	function getFontClass(css){
		return fetch(css).then(d=>d.text()).then(function(css){
			return css.match(reg).map(function(item){
				return item.split(":")[0];
			})
		});
	}
	
	function names2list(names,type){
		return names.filter(name=>type?name.indexOf(type)===0:name.indexOf("-")>-1).map(name=>{
			var arr = name.split("-");
			let itemType = type||arr[0]
			return {
				type:itemType,
				name:name,
				title:maps[itemType]&&maps[itemType][name]||name
			}
		})
	}

	var getAllFontClass = function(){
		return Promise.all([
			getFontClass(sysSetting.corepath+"/lib/kjlib/bootstrap-theme-kykj/iconfont/dicb2/iconfont.css"),
			getFontClass(sysSetting.corepath+"/lib/kjlib/bootstrap-theme-kykj/iconfont/glyphions/css/glyphions.css"),
			getFontClass(sysSetting.corepath+"/lib/kjlib/bootstrap-theme-kykj/iconfont/kjicon/iconfont.css"),
			getFontClass(sysSetting.corepath+"/lib/kjlib/element/lib/theme-chalk/index.css","el-icon")
		]).then(function(data){
			return [
				{name:"el-icon",title:"Element-UI 内置",list:names2list(data[3],"el-icon")},
				{name:"kjicon",title:"阿里ICONFONT开源图标",list:names2list(data[2])},
				{name:"dicb",title:"DICB 图标（坤佳版权）",list:names2list(data[0])},
				{name:"glyphicons",title:"Bootstrap3 内置",list:names2list(data[1])},
				{name:"glyphicon",title:"glyphicon 官方",list:names2list(glyphicon)}
			]
		});
	}
	
	return {
		inject:{
			ifpDialog:{default:null}
		},
		props:{
			className:{default:''}
		},
		el:"#app",
		data(){
			return {
				// {name:string,list:{name:string,type:string,title:string}[]}
				searchText:"",
				onlyfilter:false,
				current:"glyphicon-align-left",
				font:'glyphicon',
				groups:[
					//{name:"glyphicon",list:glyphicon}
				]
			}
		},
		created(){
			getAllFontClass()
			.then(groups=>{
				return this.groups.splice(this.groups.length,0,...groups)
			}).then(function(){
				if(this.className){
					let list = this.className.split(',');
					if(list.length>1){
						if(list[0].length>list[1].length){
							this.font=list[1];
							this.current=list[0];
						}else{
							this.font=list[0];
							this.current=list[1];
						}
					}else{
						let group = this.groups.find(item=>this.className.indexOf(item.name)===0);
						this.current = this.className
						if(group){
							this.font= group.name;
						}
					}
				}
			})
		},
		computed:{
			showlist(){
				if(!this.searchText){
					return this.groups;
				}
				if(this.onlyfilter){
					return this.groups.map(group=>({
						name:group.name,
						title:group.title,
						list:group.list.filter(item=>this.check(item))
					}))
				} else {
					return this.groups.map(group=>({
						name:group.name,
						title:group.title,
						list:group.list.map(item=>{
							return {
								selected:this.check(item),
								...item
							}
						})
					}))
				}
			}
		},
		methods:{
			returnValue(){
				this.$emit('success',[this.font,this.current].filter(item=>item).join(' '))
				this.$emit('close');
			},
			check(item){
				return item.name.indexOf(this.searchText)>-1
				||item.title.indexOf(this.searchText)>-1
				||item.type.indexOf(this.searchText)>-1
			},
			getselectclass(item){
				if(this.searchText && this.check(item)){
					return " selected"
				}else{
					return ""
				}
			}
		}
	};
	return F.class(base,{
		name:"icon",
		onLoad:function(){
			var cn = $("#txtClassName");
			var fs = $("#txtFontSize");
			var sn = $("#spnIcon");
			
			$("#btnCreate").click(function(){
				sn.removeClass();
				sn.addClass(cn.val().split("-")[0]).addClass(cn.val());
				sn.css("fontSize",fs.val()*1);
			}).click();
			var box = $("#box");
			
			var loadIcon = function(type,data){
				box.append($('<div>'+ type +'</div>'));
				$.each(data,function(i,itemi){
					var arr = itemi.split(",");
					var item = arr[0];
					var title = arr[1]&&(arr[1]+":"+arr[0])||arr[0];
					var content = arr[1]||arr[0];
					box.append($("<span></span>").append($('<span class="'+ type +' '+ item 
							+'" data-class="'+ item +'" title="'+ title +'"></span>').click(function(){
								$("#btnTest").attr("title",content)
								.html('<span class="'+type+' '+ item +'" aria-hidden="true" "></span> '+content)
						cn.val($(this).attr("class").split(" ")[1]);
						$("#btnCreate").click();
					})).append("<div style='display:none;'>"+ title +"</div>"));
				});
			}
			
			var st = $("#txtSearch");
			$("#btnSearch").click(function(){
				var text = st.val();
				box.find("span").removeClass("selected").filter(function(){
					var txt  =  $(this).children().data("class");
					return txt&&txt.indexOf(text)>-1;
				}).addClass("selected");
			});
			getAllFontClass().then(function(data){
				for(var a in data){
					loadIcon(a,data[a]);
				}
			});
		}
	})
});