﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查看明细</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page class="flex flex-item padding" id="app" pages-rc-rcmsltz-detail>
        <ifp-toolbar close>
            <ifp-button v-if="isShowSpBtn&&!$btnRight.B1" icon="glyphicon glyphicon-share-alt" @click="onSp"
                        code="B1">审核通过</ifp-button>
            <ifp-button code="B2">导出</ifp-button>
            <div v-show="isShowSpBtn" class="padding" style="float: right;">
                审核人：{{formdata.SPR}}
                审核时间：{{formdata.SPSJ}}
            </div>
        </ifp-toolbar>
        <div class="flex-item flex padding">
            <ifp-panel border title="批次信息">
                <ifp-form ref="form" size="mini" :model="formdata"
                          label-width="110px" style="height:auto;" :disabled="disable.viewDisabled" class="padding">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="来煤批次编码" prop="PCBM">
                                <ifp-input v-model="formdata.PCBM"></ifp-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="来煤计划" prop="LMJH">
                                <ifp-input v-model="formdata.LMJH"></ifp-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="收货日期" prop="SHRQ">
                                <ifp-date-picker v-model="formdata.SHRQ" type="date" formatt="yyyy-DD-dd"
                                                 align="right"></ifp-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="供货单位" prop="GHDW4002">
                                <ifp-select-ywlx v-model="formdata.GHDW4002" :ywlx="4002"></ifp-select-ywlx>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="矿点" prop="KD4001">
                                <ifp-select-ywlx v-model="formdata.KD4001" :ywlx="4001"></ifp-select-ywlx>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="煤种" prop="MZ4010">
                                <ifp-select-ywlx v-model="formdata.MZ4010" :ywlx="4010"></ifp-select-ywlx>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="运输方式" prop="YSFS1001">
                                <ifp-select-ywlx v-model="formdata.YSFS1001" :ywlx="1001"></ifp-select-ywlx>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="发站" prop="FZ4006">
                                <ifp-select-ywlx v-model="formdata.FZ4006" :ywlx="4006"></ifp-select-ywlx>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="发货日期" prop="FHRQ">
                                <ifp-date-picker v-model="formdata.FHRQ" type="date" formatt="yyyy-DD-dd"
                                                 align="right"></ifp-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="运输单位" prop="YSDW4013">
                                <ifp-select-ywlx v-model="formdata.YSDW4013" :ywlx="4013"></ifp-select-ywlx>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="收货单位" prop="SHDW">
                                <ifp-input v-model="formdata.SHDW"></ifp-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="班次" prop="BC4005">
                                <ifp-select-ywlx v-model="formdata.BC4005" :ywlx="4005"></ifp-select-ywlx>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </ifp-form>
            </ifp-panel>
            <ifp-panel-table class="flex-item margin-top" title="车辆明细记录">
                <ifp-table :data="mxlist" style="width:100%;" row-key="GID"
                           default-expand-all
                           :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                           border highlight-current-row height="100%">
                    <ifp-table-column type="index" label="序号" align="center"
                                      width="50"></ifp-table-column>
                    <ifp-table-column prop="CYBM" width="150" align="center"
                                      label="采样编码"></ifp-table-column>
                    <ifp-table-column prop="CH" width="80" align="center"
                                      label="车号"></ifp-table-column>
                    <ifp-table-column prop="HCCX4017" align="center" width="110"
                                      label="火车车型">
                        <template slot-scope="scope">
                            <ifp-select-ywlx v-model="scope.row.HCCX4017" :ywlx="4017" :canedit="false"
                                             selectstyle="width: 100%"></ifp-select-ywlx>
                        </template>
                    </ifp-table-column>
                    <ifp-table-column prop="PIAOZHONG" align="center" label="票重"></ifp-table-column>
                    <ifp-table-column prop="MAOZHONG" align="center" label="毛重"></ifp-table-column>
                    <ifp-table-column prop="PIZHONG" align="center" label="皮重"></ifp-table-column>
                    <ifp-table-column prop="JINGZHONG" align="center" label="净重"></ifp-table-column>
                    <ifp-table-column prop="KD" align="center" label="扣吨"></ifp-table-column>
                    <ifp-table-column prop="YSL" align="center" label="验收量"></ifp-table-column>
                    <ifp-table-column prop="YS" align="center" label="运损"></ifp-table-column>
                    <ifp-table-column prop="YKD" align="center" label="盈亏吨"></ifp-table-column>
                    <ifp-table-column prop="RCSJ" label="入厂时间" sortable="custom"
                                      :formatter="$TableFormatter.datetime" width="150"></ifp-table-column>
                    <ifp-table-column prop="GZSJ" label="过重时间" sortable="custom"
                                      :formatter="$TableFormatter.datetime" width="150"></ifp-table-column>
                    <ifp-table-column prop="GQSJ" label="过轻时间" sortable="custom"
                                      :formatter="$TableFormatter.datetime" width="150"></ifp-table-column>
                    <ifp-table-column prop="CCSJ" label="出厂时间" sortable="custom"
                                      :formatter="$TableFormatter.datetime" width="150"></ifp-table-column>
                </ifp-table>
            </ifp-panel-table>
        </div>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>
