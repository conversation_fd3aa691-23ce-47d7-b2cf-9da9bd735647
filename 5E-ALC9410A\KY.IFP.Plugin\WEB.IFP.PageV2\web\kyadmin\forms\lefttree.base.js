define(["jclass","form","controls/button","controls/tree"],function(jclass,base){
	return jclass(base,{
		btnid:null,
		input:null,
		treeid:null,
		btn:null,
		render:function(){
			var width = this.$container[0].style&&this.$container[0].style.width;
			var padding = this.$container[0].style&&this.$container[0].style.padding;
			this.$container.css({
				width:width||300,
				padding:padding||"8px 0px 8px 8px"
			});
			var c = this.$container.children();
			this.treeid = c.attr("id");
			
			var btnid = this.btnid = F.util.genid("btnLeftTreeBtn");
			var header = this.header = $([
				'<div class="layout-h" style="border-bottom:0;">',
				    '<div class="input-group">',
				    	'<input type="text" class="form-control" style="border:1px solid #ccc;" placeholder="请输入">',
				    	'<span class="input-group-btn">',
				    		'<a id="'+ btnid +'" control="controls/button" option="{}">定位</a>',
				    	'</span>',
				    '</div>',
				'</div>'
			].join(""));
			this.input = header.find("input");
			this.btn_ele = header.find(".input-group-btn");
			
			
			
			var content = $('<div class="layout-c"></div>')
			c.each(function(){
				$(this).appendTo(content);
			});
			if(this.option.title){
				this.$container.append($([
					'<div class="layout-h form-header">',
					this.option.title,
					'</div>'
				].join("")));
			}
			this.$container.append(header);
			this.$container.append(content);
		},
		afterReader:function(){
			var _this = this;
			this.btn = this.controls[this.btnid];
			
			var oldHights = [];
			var lastSearchText = null;
			var lastDW = null;
			
			//清空最后查询内容
			this.input.bind("change",function(){
				lastSearchText = null;
			})
			//定位
			this.btn.bind("click",function(){
				var txt = _this.input.val();
				var control = _this.controls[_this.treeid];
				var zTree = control.ztree;
				
				//与上次查询内容相同，执行定位到下一个匹配项功能
				if(lastSearchText!=null && lastSearchText==txt){
					//上次查询结果未空，此次认为同样为空，不做任何处理
					if(oldHights.length==0){return;}
					//获取所有选中的项
					if(lastDW === null){return;}
					if(lastDW>=oldHights.length-1){
						lastDW=-1;
					}
					zTree.selectNode(oldHights[++lastDW]);
					return;
				}
				if(!txt){
					$.bootoast.warning("请输入查询内容");
					_this.input.focus();
					return;
				}
				lastSearchText = txt;
				var nodeList = zTree.getNodesByParamFuzzy("name",txt);
				
				if(oldHights.length){
					for(var i=0;i<oldHights.length;i++){
						oldHights[i].highlight = false;
						zTree.updateNode(oldHights[i]);
					}
				}
				for( var i=0, l=nodeList.length; i<l; i++) {
					nodeList[i].highlight = true;
					var parent = nodeList[i].getParentNode();
					while(parent){
						if(!parent.open){
							zTree.expandNode(parent);
						}
						parent = parent.getParentNode();
					}
					zTree.updateNode(nodeList[i]);
				}
				oldHights = nodeList;
				if(oldHights.length>0){
					zTree.selectNode(oldHights[0]);
					lastDW = 0;
				}else{
					lastDW = null;
				}
			})
		},
		init:function(ele,inputer,controler){
			return this.base.apply(this,arguments);
		}
	})
});
