﻿using COM.IFP.Common;
using Newtonsoft.Json;
using ORM.IFP.DbModel;
using ORM.IFP.www.DTO;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace API.IFP.BaseInfo
{
    //基础资料
    public class Baseinfo
    {
        private Lazy<DAL.IFP.Baseinfo.Baseinfo> _service = Entity.Create<DAL.IFP.Baseinfo.Baseinfo>();


        //读取"基础资料"下拉框
        public List<IdTextModel> GetYwlxSelect(JsonElement json)
        {
            return _service.Value.GetYwlxSelect(json);
        }

        //翻页读取"基础资料"
        public PageModel<IFP_BS_BASEINFO> GetBaseinfoPage(JsonElement obj)
        {
            IFP_BS_BASEINFO entity = JsonConvert.DeserializeObject<IFP_BS_BASEINFO>(obj.ToString());
            PageModel<IFP_BS_BASEINFO> result = _service.Value.GetBaseinfoPage(entity);
            return result;
        }

        //读取"基础资料"
        public object GetBaseinfoList(JsonElement json)
        {
            //IFP_BS_BASEINFO entity = JsonConvert.DeserializeObject<IFP_BS_BASEINFO>(obj.ToString());


            var filter = json.GetValue<IList<IFP_BS_BASEINFO>>("filter");
            PageModel paging = null;
            var tmp = new JsonElement();
            if (json.TryGetProperty("paging", out tmp) == true)
                paging = json.GetValue<PageModel>("paging");


            var list = _service.Value.GetBaseinfoList(filter, paging);
            return list;
        }

        /// <summary>
        /// 同步基础资料
        /// </summary>
        /// <param name="obj">例:{'ywlx':'4001,4002'}或{'ywlx':''}</param>
        /// <returns></returns>
        public Dictionary<string, string> SynchroBaseInfo(JsonElement obj)
        {
            Dictionary<string, string> reMap = new Dictionary<string, string>();

            //Dictionary<string, Object> param = JsonElementEdit.Convert2Object(obj);
            JsonNode json = JsonNode.Parse(obj.ToString()).AsObject();
            //string ywlxs = param["ywlx"].ToString();
            string ywlxs = json["filter"].ToString();

            if (string.IsNullOrEmpty(ywlxs))
            {
                reMap.Add("success", "0");
                reMap.Add("count", "0");
                reMap.Add("msg", "没有同步到基础资料。");
            }
            string[] ywlxList = ywlxs.Split(",");
            try
            {
                int count = 0;
                foreach (string ywlx in ywlxList)
                {
                    Dictionary<string, string> res = _service.Value.SynchroBaseInfo(ywlx);
                    count += int.Parse(res["count"]);
                }
                reMap.Add("success", "1");
                reMap.Add("count", count + "");
                reMap.Add("msg", $"同步基础资料{count}条");
            }
            catch (Exception e)
            {
                reMap.Add("success", "0");
                reMap.Add("msg", "系统错误");
            }
            return reMap;
        }

        //保存"基础资料"
        public Dictionary<string, string> SaveBaseinfoList(JsonElement obj)
        {
            List<IFP_BS_BASEINFO> list = JsonConvert.DeserializeObject<List<IFP_BS_BASEINFO>>(obj.ToString());
            Dictionary<string, string> result = new Dictionary<string, string>();
            try
            {
                _service.Value.SaveBaseinfoList(list);
                result["success"] = "OK";
            }
            catch (Exception e)
            {
                result["success"] = "ERROR";
            }
            return result;
        }

        /// <summary>
        /// 更新一条基本资料的
        /// 
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public Dictionary<string, string> UpdateBaseInfo(JsonElement json)
        {
            var filter = json.GetValue<IList<IFP_BS_BASEINFO>>("filter");
            PageModel paging = null;
            var tmp = new JsonElement();
            if (json.TryGetProperty("paging", out tmp) == true)
                paging = json.GetValue<PageModel>("paging");
            Dictionary<string, string> result = new Dictionary<string, string>();
            int flag = _service.Value.UpdateBaseInfo(filter, paging);
            if (flag > 0)
            {
                result["success"] = "OK";
            }
            else
            {
                result["success"] = "ERROR";
            }

            return result;
        }

        //读取"基础资料类型"
        public object GetBaseinfoTypeList(JsonElement obj)
        {
            var list = _service.Value.GetBaseinfoTypeList();
            return list;
        }

        //保存"基础资料类型"
        public Dictionary<string, string> SaveBaseinfoTypeList(JsonElement obj)
        {
            List<IFP_BS_BASEINFO_TYPE> list = JsonConvert.DeserializeObject<List<IFP_BS_BASEINFO_TYPE>>(obj.ToString());
            Dictionary<string, string> result = new Dictionary<string, string>();
            try
            {
                _service.Value.SaveBaseinfoTypeList(list);
                result["success"] = "OK";
            }
            catch
            {
                result["success"] = "ERROR";
            }
            return result;
        }

        public PFActionResult SelectBasDic(JsonElement json)
        {
            var tmp = new JsonElement();
            var filter = json.GetValue<List<IFP_BS_BasicDIC>>("filter");
            COM.IFP.Common.PageModel paging = null;
            if (json.TryGetProperty("paging", out tmp) == true)
                paging = tmp.GetValue<COM.IFP.Common.PageModel>();

            object res = _service.Value.SelectBasDic(filter, paging);
            //object res = GenericPSelectDAL.lazy.Value.SelectPage<BasicDIC>(filter, paging);
            return new PFActionResult(true, "查询成功", res);
        }

    }
}
