define(['../lib/createApi'],({createApi,createApis})=>{

    // 测试用例
    const tests = [
        {
            name:"测试 url 无参数",
            test:()=>createApi({url:'/API/IFP/Rights/User/ProgramStartTime'})().then(d=>true)
        },
        {
            name:"测试 url 有参数",
            test:()=>createApi({url:'/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect',prop:{ywlx:1002}})().then(d=>true)
        },
        {
            name:"测试 url 有动态参数",
            test:()=>createApi({
                url:'/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect',
                prop(ywlx){
                    return {ywlx:ywlx}
                }
            })(1002).then(d=>d.constructor===Array)
        },
        
        // 对于尚未实现api接口，可以仅定义模拟数据
        
        {
            name:"测试 静态模拟数据",
            test:()=>createApi({
                mock:[
                    {name:"test"}
                ]
            })().then(d=>d[0].name==="test")
        },
        
        // 对于尚未实现api接口，可以仅定义模拟数据 动态
        {
            name:"测试 动态模拟数据",
            test:()=>createApi({
                // 这里仅能传递一个参数
                mock(opt){
                    return []
                }
            })().then(d=>true)
        },
        // 可以对接口进行再次封装
        {
            name:"测试 usemock = true",
            test:()=>createApi({
                url:'/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect',
                prop:{ywlx:1002},
                usemock:true,
                mock(){
                    return []
                }
            })().then(d=>true)
        },
        // 可以对接口进行再次封装
        {
            name:"测试 usemock = false",
            test:()=>createApi({
                url:'/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect',
                prop:{ywlx:1002},
                usemock:false,
                mock(){
                    return [{id:1}]
                }
            })().then(d=>d[0].id===1)
        },
        // 可以对接口进行再次封装
        {
            name:"测试 应产生一个异常",
            test:()=>{
                let action = createApi({})
                return action()
                .then(d=>d[0].id===1)
            }
        },
        // 可以对接口进行再次封装
        {
            name:"测试 then",
            test:()=>{
                let action = createApi({
                    mock:{text:1},
                    then(d){
                        return d.text
                    }
                })
                return action()
                .then(d=>d===1)
            }
        }
    ]

    return {
        name:"createApi 测试",
        test(success,error){
            tests.map(item=>{
                item.test()
                .then(msg=>success(item.name,msg))
                .catch(err=>{
                    error(item.name,err)
                })
            })
        }
    }
})