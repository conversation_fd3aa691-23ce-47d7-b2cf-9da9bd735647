define(["iofp/lib/createApi"],function({createApis}){
    let apis = createApis({
        getRunMessages:{
            // 启用url
            url:'/xxx/xxx/xx',
            usemock:true,
            link:"data1",
            mock:[
                {id:'001',time:'2021-01-01 01:01:01 1254',content:"#1 采样机已启动"},
                {id:'002',time:'2021-01-01 01:01:01 1254',content:"#1 采样机已启动"}
            ]
        },
        getDat1:{
            url:'/xxx/xxx/xx',
            usemock:true,
            mock:[
                {id:'001',name:'采样机1'},
                {id:'002',name:'采样机1'}
            ]
        },
        getDat2:{
            url:'/xxx/xxx/xx',
            usemock:true,
            mock:[
                {id:'001',name:'采样机1'},
                {id:'002',name:'采样机1'}
            ]
        }
    });

    
    return {
        el:"#app",
        data(){
            return {
                runMessages:[],
                data1:[],
                data2:[],

                // 运行状态
                runState:1,

                paging:{
                    size:20,
                    page:1,
                    total:0
                },

                
                option:{
                    title: {
                        text: 'ECharts 示例'
                    },
                    tooltip: {},
                    legend: {
                        data:['销量','销量2','销量3']
                    },
                    xAxis: {
                        data: ["衬衫","羊毛衫","雪纺衫","裤子","高跟鞋","袜子"]
                    },
                    yAxis: {},
                    series: [
                        { name: '销量', type: 'bar', data: [] },
                        { name: '销量2', type: 'bar', data: [] },
                        { name: '销量3', type: 'bar', data: [] }
                    ]
                }
            }
        },
        created(){

        },
        methods:{
            table1SizeChange(v){
                console.log(v);
            },
            table1PageChange(v){
                console.log(v);
            }
        }
    }
})