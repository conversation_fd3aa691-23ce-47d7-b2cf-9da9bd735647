﻿using System;
using System.Collections.ObjectModel;

namespace COM.IFP.Common
{
    /// <summary>
    /// 终端信息
    /// </summary>
    public class Basics
    {
        /// <summary>
        /// 缓存标识
        /// </summary>
        public string Unique { get; init; }
        /// <summary>
        /// 窗口时间
        /// </summary>
        public TimeSpan? Period { get; init; }
        /// <summary>
        /// 请求主机
        /// </summary>
        public string Origin { get; init; }
        /// <summary>
        /// 来源结点
        /// </summary>
        public string Source { get; init; }
        /// <summary>
        /// 用户代理
        /// </summary>
        public string Client { get; init; }
        /// <summary>
        /// 通讯标识，通过Socket请求时有效
        /// </summary>
        public string Socket { get; init; }
        /// <summary>
        /// 回调标识，通过Socket设置回调时有效
        /// </summary>
        public string Handle { get; init; }
        /// <summary>
        /// 扩展对象
        /// </summary>
        public object Extend { get; init; }
    }

    public class Caches
    {
        public interface Expect
        {
            TimeSpan? Period { get; set; }
            Basics Basics { get; }
            ReadOnlyDictionary<string, object> Values { get; }

            bool HasName(string name);
            void SetData(string name, object data);
            object GetData(string name);
            T GetData<T>(string name);
            bool GetData(string name, out object data);
            bool GetData<T>(string name, out T data);
        }

        internal static Expect entity;

        /// <summary>
        /// 缓存窗口期
        /// </summary>
        public static TimeSpan? Period { get => entity.Period; set => entity.Period = value; }

        /// <summary>
        /// 缓存集终端
        /// </summary>
        public static Basics Basics { get => entity.Basics; }

        public static bool Inited
        {
            get
            {
                try
                {
                    Basics a = entity.Basics;
                }
                catch
                {
                    return false;
                }
                return true;
            }
        }

        /// <summary>
        /// 缓存项字典
        /// </summary>
        public static ReadOnlyDictionary<string, object> Values { get => entity.Values; }

        /// <summary>
        /// 检索缓存键
        /// </summary>
        /// <param name="name">缓存键名称</param>
        /// <returns>True缓存键存在，False缓存键不存在</returns>
        public static bool HasName(string name)
        {
            return entity.HasName(name);
        }

        /// <summary>
        /// 设置缓存项
        /// </summary>
        /// <param name="name">缓存项名称</param>
        /// <param name="data">缓存项数值</param>
        public static void SetData(string name, object data)
        {
            entity.SetData(name, data);
        }

        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <param name="name">缓存项名称</param>
        /// <returns>缓存项数值</returns>
        public static object GetData(string name)
        {
            return entity.GetData(name);
        }

        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="name">缓存项名称</param>
        /// <returns>缓存项数值</returns>
        public static T GetData<T>(string name)
        {
            return (T)GetData(name);
        }

        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <param name="name">缓存项名称</param>
        /// <param name="data">缓存项数值</param>
        /// <returns>True获取成功，False获取失败</returns>
        public static bool GetData(string name, out object data)
        {
            return entity.GetData(name, out data);
        }

        /// <summary>
        /// 移除
        /// </summary>
        /// <param name="name">缓存项名称</param>
        public static void Remove(string name)
        {
            // TODO:需要改造成真正的移除方法
            entity.SetData(name, null);
        }
        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="name">缓存项名称</param>
        /// <param name="data">缓存项数值</param>
        /// <returns>True获取成功，False获取失败</returns>
        public static bool GetData<T>(string name, out T data)
        {
            return entity.GetData(name, out data);
        }
    }
}
