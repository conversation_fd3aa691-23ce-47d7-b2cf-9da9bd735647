# IFP 前端开发文档

目的：

为IFP项目前端开发提供一个可共同维护的文档

关于 kyadmin 的文档 请访问<a href="/kyadmin/docs/index.html">kyadmin-docs<a>

> 所有文档采用 markdown 格式编写

## 维护文档

文档目录 `KY.IFP.Plugin\WEB.IFP.Page\web\docs` 以下用 `~/docs/` 代替

### 新增

例如现在要再左侧菜单中添加一个描述关于 `ifp api` 的文档

#### 1. 新建文件

新建 `~/docs/pages/api.md`

文件内容

```md
# API
```


#### 2. 添加菜单

修改 `~/docs/_sidebar.md` 文件

```md
* [首页](/)
* [布局](/pages/layout)
```

添加一条

```md
* [首页](/)
* [布局](/pages/layout)
* [API](/pages/api)
```

### 浏览

有以下方式启动本文档

1. 方式1：启动IFP项目访问 `/docs/index.html`,访问 `http://localhost:5009/index.html`
1. 方式2：运行`[项目根目录]/run-web.bat 访问`,访问 `http://localhost:5010/index.html`
1. 方式3：将本文档(README.md)所在目录，直接部署到web服务 访问 `/index.html`

访问 <a href="/docs/index.html">/docs/index.html</a>

## 更新前端 `nuget` 包

1. 更新项目 `KY.IFP.Plugin\WEB.IFP.Page\WEB.IFP.Page.csproj` 的nuget依赖，到最新版。
2. 重新编译

> 更新nuget可以使用命令行：`dotnet restory KY.IFP.Plugin\WEB.IFP.Page`