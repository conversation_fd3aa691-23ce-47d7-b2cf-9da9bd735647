<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title></title>
		<style type="text/css">
		table.gridtable {
			table-layout:fixed;
		    font-family: verdana,arial,sans-serif;
		    font-size:11px;
		    color:#333333;
		    border-width: 1px;
		    border-color: #666666;
		    border-collapse: collapse;
		}
		table.gridtable th {
			text-align:center;
			width:17%;
		    border-width: 1px;
		    padding: 8px;
		    border-style: solid;
		    border-color: #666666;
		    background-color: #dedede;
		}
		table.gridtable td {
			text-align:center;
		    border-width: 1px;
		    padding: 8px;
		    border-style: solid;
		    border-color: #666666;
		    background-color: #ffffff;
		}
		</style>
	</head>
	<body controller="demo.js">
		<div class="layout-h padding"> 
			<a id="btnMergeRow" control="controls/button">合并行</a>
			<a id="btnMergeCol" control="controls/button">和并列</a>
			<a id="btnReset" control="controls/button">重置</a>
			<a id="btnCreate" control="controls/button" option="{}">创建Grid</a>
		</div>
		<div class="layout-c padding">
			<table class="layout-full"></table>
		</div>
		<script src="../../../starter.js"></script>
	</body>
</html>
