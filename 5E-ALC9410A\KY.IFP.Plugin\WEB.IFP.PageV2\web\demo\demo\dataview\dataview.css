#app {
  width: 100%;
  height: 100%;
  background-color: #030409;
  color: #fff;
}
#app #dv-full-screen-container {
  background-image: url('img/bg.png');
  background-size: 100% 100%;
  box-shadow: 0 0 3px blue;
  display: flex;
  flex-direction: column;
}
#app .main-header {
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
#app .main-header .mh-left {
  font-size: 20px;
  color: #0186bb;
}
#app .main-header .mh-left a:visited {
  color: #0186bb;
}
#app .main-header .mh-middle {
  font-size: 30px;
}
#app .main-header .mh-left,
#app .main-header .mh-right {
  width: 450px;
}
#app .main-container {
  height: calc(100vh - 80px);
}
#app .main-container .border-box-content {
  padding: 20px;
  box-sizing: border-box;
  display: flex;
}
#app .left-chart-container {
  width: 22%;
  padding: 10px;
  box-sizing: border-box;
}
#app .left-chart-container .border-box-content {
  flex-direction: column;
}
#app .right-main-container {
  width: 78%;
  padding-left: 5px;
  height: 100%;
  box-sizing: border-box;
}
#app .rmc-top-container {
  height: 65%;
  display: flex;
}
#app .rmctc-left-container {
  width: 65%;
}
#app .rmctc-right-container {
  width: 35%;
}
#app .rmc-bottom-container {
  height: 35%;
}
#app .rmctc-chart-1,
#app .rmctc-chart-2 {
  height: 50%;
}
