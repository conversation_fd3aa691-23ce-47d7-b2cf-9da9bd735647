<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="控件,下拉">
    <title>下拉控件</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" class="padding">
        

        <ifp-panel title="绑定数据">
            <ifp-form inline class="padding">
                <ifp-form-item label="基础用法">
                    <ifp-select v-model="value">
                        <ifp-option label="选项1" value="1">选项1</ifp-option>
                        <ifp-option label="选项2" value="2">选项2</ifp-option>
                        <ifp-option label="选项3" value="3">选项3</ifp-option>
                    </ifp-select>
                </ifp-form-item>
                
                <ifp-form-item label="使用URL">
                    <ifp-select 
                    url="/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect"
                    :post="{ywlx:1047}"
                    value-key="id" label-key="text"
                    v-model="value1"></ifp-select>
                </ifp-form-item>

                <ifp-form-item label="使用对象数组">
                    <ifp-select :items = "items" disabled-key="disabled"
                    value-key="id" label-key="text" v-model="value2"></ifp-select>
                </ifp-form-item>
            </ifp-form>
        </ifp-panel>

        <ifp-panel title="动态过滤选项" class="margin-top">
            <ifp-form inline class="padding">
                <ifp-form-item label="筛选条件">
                    <ifp-input v-model="filterText"></ifp-input>
                </ifp-form-item>
                
                <ifp-form-item label="url">
                    <ifp-select v-model="value1"
                    url="/API/IFP/Baseinfo/Baseinfo/GetYwlxSelect"
                    :post="{ywlx:1047}"

                    value-key="id" label-key="text"
                    :filter="filterHandle" :filter-arg="filterText"
                    ></ifp-select>
                </ifp-form-item>

                <ifp-form-item label="对象数组">
                    <ifp-select :items = "items" 
                    :filter="filterHandle" :filter-arg="filterText"
                    value-key="id" label-key="text" v-model="value2"></ifp-select>
                </ifp-form-item>
            </ifp-form>
        </ifp-panel>

        <ifp-panel title="禁止编辑" class="margin-top">
            <ifp-form inline class="padding">
                <ifp-form-item label="已选择">
                    <ifp-select v-model="value2"
                    :items="items"
                    :canedit="false"
                    value-key="id" label-key="text"
                    ></ifp-select>
                </ifp-form-item>
                <ifp-form-item label="已选择">
                    <ifp-select-ywlx v-model="value2"
                    :ywlx="1047"
                    :canedit="false"
                    v-model="ywlx1047"
                    ></ifp-select-ywlx>
                </ifp-form-item>
            </ifp-form>
        </ifp-panel>
        
        <ifp-panel title="禁止编辑-表格" class="margin-top">
            <template v-slot:header>
                <el-checkbox v-model="canedit">编辑</el-checkbox>
            </template>
            <ifp-table row-key="id" height="100%" :data="[{id:'1',value2:'2'}]">
                <el-table-column type="index" label="序号" width="50"></el-table-column>

                <el-table-column label="列1" prop="mark">
                    <template slot-scope="scope">
                        <ifp-select v-model="value2" :items="items" :canedit="canedit"
                        value-key="id" label-key="text"
                        ></ifp-select>
                    </template>
                </el-table-column>
            </ifp-table>
        </ifp-panel>
        
        <ifp-panel title="业务类型" class="margin-top">
            <template v-slot:header>
                <el-checkbox v-model="canedit">编辑</el-checkbox>
            </template>
            <ifp-form inline class="padding">
                <ifp-form-item label="已选择">
                    <ifp-select-ywlx :canedit="canedit" v-model="value1" ywlx="1047"></ifp-select-ywlx>
                </ifp-form-item>
                <ifp-form-item label="已选择">
                    <ifp-select-ywlx v-model="value1" ywlx="1047"></ifp-select-ywlx>
                </ifp-form-item>
            </ifp-form>
        </ifp-panel>

        <ifp-panel title="联动测试" class="margin-top">
            <ifp-form inline class="padding">
                <ifp-form-item label="业务类型">
                    <ifp-select v-model="ywlx">
                        <ifp-option label="1002" value="1002">1002</ifp-option>
                        <ifp-option label="1003" value="1003">1003</ifp-option>
                        <ifp-option label="4005" value="4005">4005</ifp-option>
                    </ifp-select>
                </ifp-form-item>
                <ifp-form-item label="已选择">
                    <ifp-select-ywlx v-model="custom" :ywlx="ywlx"></ifp-select-ywlx>
                </ifp-form-item>
            </ifp-form>
        </ifp-panel>

        <ifp-panel title="多选" class="margin-top">
            <ifp-form inline class="padding">
                <ifp-form-item label="不可编辑">
                    <ifp-select-ywlx style="width:100px;" :canedit="false" v-model="ywlx1047s" :ywlx="1047" multiple></ifp-select-ywlx>
                </ifp-form-item>
                
                <ifp-form-item label="多选">
                    <ifp-select-ywlx @change="$message('22')" v-model="ywlx1047s" :ywlx="1047" :canedit="true" :value-is-number="false" multiple></ifp-select-ywlx>
                </ifp-form-item>
            </ifp-form>
        </ifp-panel>
    </ifp-page>

    <script src="/iofp/starter.js"></script> 
</body>
</html>