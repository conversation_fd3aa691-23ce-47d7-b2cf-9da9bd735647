﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>参数选择</title>
</head>
<body controller="csxz.js">
    <div class="hidden">
        <input id="FenZuGid" control="controls/textbox" class="hideInput" />
    </div>

    <div class="layout-v padding" style="width:310px;">
        <div class="form-header">已选择参数</div>
        <table id="grid2" control="controls/grid"
               option='{"autoresize":true,cellEdit:true,sortname:"Sxh", sortorder : "asc",
                method:"post",
                queryParams: {
                }
            }'>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>名称</th>
                    <th>参数gid</th>
                    <th>顺序号</th>
                    <th>原顺序号</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td option="{name:'Gid',width : 50,frozen: true,hidden:true, key:true}"></td>
                    <td option="{name:'CsName',width : 200,align : 'left',editable:false}"></td>
                    <td option="{name:'CsGid',width : 200,align : 'left',editable:false,hidden:true}"></td>
                    <td option="{name:'Sxh',width : 50,align : 'left',editable:true}"></td>
                    <td option="{name:'Pxh',width : 50,hidden:true}"></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="layout-c padding">
        <div class="searchbar layout-h">
            <table>
                <tr>
                    <td> 名称：</td>
                    <td>
                        <input id="CsName_LIKE" control="controls/textbox" option="{showName:'名称',controlsUses:2}" />
                    </td>
                    <td class="seartchbar-buttons">
                        <a id="searchBtn" control="controls/button" option="{}">查&nbsp;&nbsp;询</a>
                        <a id="resetBtn" control="controls/button" option="{}">重&nbsp;&nbsp;置</a>
                    </td>
                </tr>

            </table>
        </div>

        <div class="layout-c ">
            <div class="form-header">全部参数</div>
            <table id="grid" control="controls/grid"
                   option='{"autoresize":true,cellEdit:false,sortname:"Gid", sortorder : "asc",
                method:"post",multiselect:true,
                queryParams: {
                }
            }'>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>参数编码</th>
                        <th>参数类型</th>
                        <th>默认顺序号</th>
                        <th>存在于哪些分组</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td option="{name:'Gid',width : 50,frozen: true,hidden:true, key:true}"></td>
                        <td option="{name:'CsName',width : 200,align : 'left',editable:false}"></td>
                        <td option="{name:'CsCode',width : 300,align : 'center',editable:false}"></td>
                        <td option="{name:'LeiXing',width : 80,align : 'center',editable:false, data:[{id:'1',text:'数字'},{id:'2',text:'文本'},{id:'3',text:'选择项'}]}"></td>
                        <td option="{name:'Pxh',width : 100,align : 'center',editable:false}"></td>
                        <td option="{name:'FenZuListStr',width : 300,align : 'center',editable:false, data:[{id:'1',text:'数字'},{id:'2',text:'文本'},{id:'3',text:'选择项'}]}"></td>
                    </tr>
                </tbody>
            </table>
        </div>


    </div>
    <div class="footbar layout-h padding">
        <a id="updateBtn" control="controls/button" option="{icon:'dicb-queding',requiredcheck:true}" log>确定</a>
        <a id="closeBtn" control="controls/button" option="{command:'close'}">取消</a>
    </div>
</body>
<script src="/iofp/starter.js"></script>
</html>
