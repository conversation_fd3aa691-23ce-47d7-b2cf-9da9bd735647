﻿local keys = redis.call('KEYS',@header..'*:Stamp')
local gets={}
table.insert(gets,{'$SYN',redis.call('GET','$SYN')})
for i,k in ipairs(keys) do
	local t=redis.call('GET',k)
	if t>@moment then
		k=string.sub(k,1,#k-6)
		table.insert(gets,{k,redis.call('GET',k..':Value')})
	end
end
keys=redis.call('KEYS','*:$Live')
for i,k in ipairs(keys) do
	table.insert(gets,{k,redis.call('GET',k)})
end
keys=redis.call('KEYS','*:$Time')
for i,k in ipairs(keys) do
	table.insert(gets,{k,redis.call('GET',k)})
end
keys=redis.call('KEYS','*:$Info')
for i,k in ipairs(keys) do
	table.insert(gets,{k,redis.call('GET',k)})
end
return gets