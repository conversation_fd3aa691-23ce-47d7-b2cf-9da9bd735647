﻿<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <title>角色菜单选择</title>
    </head>
    <body controller="menuChoose.js">
        <div class="layout-v padding" style="width: 260px">
            <div class="form-header">已选择菜单</div>
            <table
                id="grid2"
                control="controls/grid"
                option='{"autoresize":true,cellEdit:false,sortname:"Gid", sortorder : "asc",
                method:"post",
                queryParams: {
                }
            }'
            >
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>菜单id</th>
                        <th>名称</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td option="{name:'Gid',width : 50,frozen: true,hidden:true, key:true}"></td>
                        <td option="{name:'Gid',width : 100,align : 'left',editable:false}"></td>
                        <td option="{name:'menuName',width : 100,align : 'left',editable:false}"></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="layout-c padding">
            <div class="searchbar layout-h">
                <table>
                    <tr>
                        <td>角色名：</td>
                        <td>
                            <input id="RoleName" control="controls/textbox" option="{showName:'名称',controlsUses:2,disabled:true}" />
                        </td>
                        <td style="padding-left: 40px">角色编码：</td>
                        <td>
                            <input id="RoleCode" control="controls/textbox" option="{showName:'名称',controlsUses:2,disabled:true}" />
                        </td>
                        <td style="padding-left: 40px"></td>
                        <td>
                            <a id="refreshBtn" control="controls/button" option="{}">刷新</a>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="layout-c">
                <div class="form-header">全部菜单</div>
                <table
                    id="grid"
                    control="controls/grid"
                    option='{"autoresize":true,cellEdit:false,sortname:"Gid", sortorder : "asc",
                method:"post",multiselect:true,
                queryParams: {
                }
            }'
                >
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>页面序号</th>
                            <th>页面名称</th>
                            <th>父菜单名称</th>
                            <th>排序号</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td option="{name:'Gid',width : 50,frozen: true,hidden:true, key:true}"></td>
                            <td option="{name:'Gid',width : 200,align : 'left',editable:false}"></td>
                            <td option="{name:'menuName',width : 200,align : 'left',editable:false}"></td>
                            <td option="{name:'PName',width : 100,align : 'left',editable:false}"></td>
                            <td option="{name:'sort',width : 100,align : 'center',editable:false}"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="footbar layout-h padding">
            <a id="updateBtn" control="controls/button" option="{icon:'dicb-queding',requiredcheck:true}" log>确定</a>
            <a id="closeBtn" control="controls/button" option="{command:'close'}">取消</a>
        </div>
    </body>
    <script src="/iofp/starter.js"></script>
</html>
