﻿using SqlSugar;
using System;
using DbType = SqlSugar.DbType;

namespace COM.IFP.SqlSugarN
{
    public class DB
    {


        /// <summary>
        /// 数据库类型
        /// </summary>
        public static string Driver { get; set; }

        /// <summary>
        /// 连接字符串
        /// </summary>
        public static string Source { get; set; }

        /// <summary>
        /// 使用默认配置创建数据库连接
        /// </summary>
        /// <param name="manage">是否启用事务管理</param>
        /// <returns>数据库连接实例</returns>
        public static DConnection Create(bool manage = true)
        {
            return Create(Driver, Source, manage);
        }

        /// <summary>
        /// 使用指定连接字符串创建SQL Server数据库连接
        /// </summary>
        /// <param name="source">连接字符串</param>
        /// <param name="manage">是否启用事务管理</param>
        /// <returns>数据库连接实例</returns>
        public static DConnection Create(string source, bool manage = true)
        {
            return Create("SQLSERVER", source, manage);
        }

        /// <summary>
        /// 创建指定类型的数据库连接
        /// </summary>
        /// <param name="driver">数据库类型</param>
        /// <param name="source">连接字符串</param>
        /// <param name="manage">是否启用事务管理</param>
        /// <returns>数据库连接实例</returns>
        public static DConnection Create(string driver, string source, bool manage = true)
        {
            if (string.IsNullOrEmpty(driver))
                throw new ArgumentNullException(nameof(driver));
            if (string.IsNullOrEmpty(source))
                throw new ArgumentNullException(nameof(source));

            var config = new ConnectionConfig
            {
                ConnectionString = source,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
            };

            // 根据不同的数据库类型设置对应的配置
            switch (driver.ToUpper())
            {
                case "SQLSERVER":
                    config.DbType = DbType.SqlServer;
                    return SqlServer.Create(source, manage);

                case "SQLSERVER2008":
                    config.DbType = DbType.SqlServer;
                    // SqlSugar 会自动处理 SQL Server 的不同版本
                    return SqlServer.Create(source, manage);
                case "DM":
                    config.DbType = DbType.Dm;
                    return Dm.Create(source, manage);
                case "SQLITE":
                    config.DbType = DbType.Sqlite;
                    return SQLite.Create(source);
                case "POLARDB":
                    config.DbType = DbType.PolarDB;
                    return PolarDB.Create(source);
                case "OPENGAUSS":
                    return OpenGauss.Create(source);
                default:
                    throw new ArgumentException($"不支持的数据库类型: {driver}", nameof(driver));
            }
        }
    }
}