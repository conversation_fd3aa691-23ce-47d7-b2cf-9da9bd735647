;define([
"controller",
"jclass","ajax"],
function(base,jclass,ajax){
	return jclass(base,{
		name:"maintable",
		win:null,
		sdList:[],
		openWin:function(){
			this.log.info("打开弹出页");
		},
		bindEvent:function(){			
			var data = win && win.parameter || {};			
			var _this = this;
			//新增事件			
			this.bind("saveBtn","click",this.saveFun,this);
		
			this.bind("exitBtn","click",function(){
				if(win){
					win.close();
				}
			});	
		},
		//绑定数据
		initData:function(pGid){
			var _this = this;
			F.ajax({
				url:'/www/Gssj/queryGssjById',
				data:{gid:pGid},
				success:function(resultObj){
					//var	resultObj = eval('(' + resp + ')');
					_this.formData(resultObj);				
				}
			});		
		},
	
		loadYangPin:function(){
			var _this = this;
			ajax({
				url:"/com.kysoft.service/html/zk01/baseinfo/4302/queryGSlist.action",
				data:{zfbz:0},
				success:function(resp){
					var	resultObj = eval('(' + resp + ')');
					
					var list = resultObj.filter(function(item){
						return item.id !="4302";
					}).map(function(item,index){
						return {
							gid:F.util.uuid(),
							mz4302:item.gid
						}
					})
					_this.controls.mxlist.value(list);
				}
			});
		},
	
		saveFun:function(){
			var _this = this;
			var formdata = _this.formData();
			//var cookie = F.common.getLoginCookie();
			F.ajax({
				url:'/www/Gssj/saveOrUpdateGssj',
				data: JSON.stringify(formdata),
				success: function (resultObj) {
					
					if(resultObj.success) {
						window.parent.$.alert("保存成功。");
						 if(win){
								win.close();
						}
					} else {
						$.alert(resultObj.data);
					}
	            }
			});
		},		
	
		onLoad:function(){	
			win = this.getDialogWindow();			
			var data = win && win.parameter || {};			
			if(data.pMode=="show") {
				this.initData(data.pGid);
				this.disable($(".layout-c.bill"),true);
				this.controls.saveBtn.disable();
				$("#saveBtn").attr("disabled","disabled");
			
			} else if (data.pMode=="edit") {
				this.initData(data.pGid);
			} else {
				//var cookie = F.common.getLoginCookie();		    	
	    		//this.controls.huayanyuan.value(cookie.userName);
	    		//var dt =F.common.postSyn("/com.kysoft.service/html/util/DateUtil/getDate.action",{});
				this.controls.ywrq.value(new Date());
				
				//this.loadYangPin();
			}			
			this.bindEvent();
		}
	})
});