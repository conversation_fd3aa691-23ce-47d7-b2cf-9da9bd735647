
(function (global, factory) {
    if (typeof define === 'function' && define.amd) {
        define(["EventEmitter","iofp/api"], factory);
    } else if (typeof module !== 'undefined' && module.exports){
        module.exports = factory();
    } else {
        global.ReconnectingWebSocket = factory(global.EventEmitter);
    }
})(this, function (EventEmitter) {
    if (!EventEmitter) {
        console.error("ReconnectingWebSocket need EventEmitter")
        return;
    }
    if (!('WebSocket' in window)) {
        console.error("WebSocket no supper")
        return;
    }


    function ReconnectingWebSocket(url, protocols, options) {
        EventEmitter.mixin(this);
        this.url = Promise.resolve(url);
        // Default settings
        var settings = {
            autoOpen:true
        }
        if (!options) { options = {}; }

        // Overwrite and define settings with options if they exist.
        for (var key in settings) {
            if (typeof options[key] !== 'undefined') {
                this[key] = options[key];
            } else {
                this[key] = settings[key];
            }
        }

        // Whether or not to create a websocket upon instantiation
        if (this.autoOpen == true) {
            this.open(false);
        }
        var self = this;
        this.on("open", function () {
            self.readyState = WebSocket.OPEN;
        })
    }
    ReconnectingWebSocket.prototype.send = function (data) {
        var self = this;
        return Promise.resolve(this.url).then(function(url){
            return API.SetActive(url.get, data).then(data => {
                self.emit("message", data);
                return data;
            }).catch(e => {
                self.emit("error",e);
            });
        })
    }

    ReconnectingWebSocket.prototype.open = function () {
        var self = this;
        return Promise.resolve(this.url).then(function(url){
            return API.SetActive(url.set, function receive(obj) {
                self.emit("message",obj);
            }).then(() => {
                self.emit("open",obj);
            }).catch(e => {
                self.emit("error",e);
            });
        })
    }

    ReconnectingWebSocket.prototype.on = 
    ReconnectingWebSocket.prototype.addEventListener = 
    function(name,callback,context){

        // 指定了上下文
        if(arguments.length>2){
            var _callback = callback;
            callback = function () { 
                _callback.apply(context,arguments)
            } 
        }


        this.events[name]=this.events[name]||[];
        this.events[name].push(callback);
        if(name=="open" && this.readyState()==1){
            callback();
            return ;
        }
    }
    ReconnectingWebSocket.prototype.onopen = function(event) {};
    ReconnectingWebSocket.prototype.onclose = function(event) {};
    ReconnectingWebSocket.prototype.onconnecting = function(event) {};
    ReconnectingWebSocket.prototype.onmessage = function(event) {};
    ReconnectingWebSocket.prototype.onerror = function(event) {};

    ReconnectingWebSocket.CONNECTING = WebSocket.CONNECTING;
    ReconnectingWebSocket.OPEN = WebSocket.OPEN;
    ReconnectingWebSocket.CLOSING = WebSocket.CLOSING;
    ReconnectingWebSocket.CLOSED = WebSocket.CLOSED;

    return ReconnectingWebSocket;
});
