﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示列设置</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item" id="app" style="height:500px;">
        <div class="toolbar">
            <el-button icon="kjicon kjicon-baocun" @click="onSave">保存</el-button>
            <el-button icon="kjicon kjicon-tuichu" @click="onExit">退出</el-button>
        </div>
        <div class="flex-item padding" style="background-color:white">
            <el-tree ref="tree"
                        :data="treeData"
                        node-key="id"
                        :props="defaultProps"
                        :default-expand-all="true"
                        :highlight-current="true"
                        :expand-on-click-node="false"
                        :show-checkbox="true"></el-tree>
        </div>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>