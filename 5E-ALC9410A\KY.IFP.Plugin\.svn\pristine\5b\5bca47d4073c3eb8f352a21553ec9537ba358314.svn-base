table {table-layout: fixed;}
table {border-collapse: collapse; border-spacing: 0; width: 100%}
table > thead > tr > td, 
table > tbody > tr > td, 
table > thead > tr > th, 
table > tbody > tr > th{ 
	padding:0 0; border: 1px solid #ccc; 
	height:34px;
}
table > thead > tr > th, 
table > tbody > tr > th{ 
	padding:0px 4px;background-color: #f6f6f6; 
	font-weight: normal; text-align: center; 
}
table > tbody > tr > td > input,
table > tbody > tr > td > textarea,
table > tbody > tr > td > select ,
table > tbody > tr > td > .input-group > input
{ border-width:0px; width:100%;}
table > tbody > tr > td > .radio,
table > tbody > tr > td > .checkbox{
	padding-left: 0px;
}
table > tbody > tr > td > select {
    padding-left: 0px;
}
table > tbody > tr > td > textarea{
	height:100%;
	background:transparent;
}
table > tbody > tr > td .select2-selection.select2-selection--single
{
	border:0px;
}
table > tbody > tr > th {text-align:left;padding-left:0px;}

tr.jqgfirstrow{
}

iframe{
	border: 0px;
}
ul,li{
	list-style: none;
	margin: 0px;
	padding:0px;
}

li{
	/* text-align: center; */
	position: relative;
}
.padding{
	padding:0px;
}

/* jqgrid */
.ui-jqgrid{
}
.ui-jqgrid .ui-jqgrid-btable tbody tr.jqgrow td,
.ui-jqgrid .ui-jqgrid-htable thead th{
	padding-right:8px;
}
.ui-jqgrid .ui-jqgrid-htable thead th div{
	overflow: visible !important;
}



/* area */
.area-toolbar{
	background: #eee;
	padding:8px;
}


/* modal */
.icon-modal-info{
	font-size:40px;
	padding:0 20px;
}
.modal.full{
	
}

.modal.full .modal-content{
	border:0px;
}

.modal.full > .modal-dialog{
    margin: 0px;
}
div.modal-header{
	background: #ddd;
	padding:8px;
	font-size:14px;
}
div.modal.full.in{
	overflow:hidden;
}

/* form *
 * 
 */
.form-header{
	padding:8px;
	border: 1px solid #ccc;
    border-bottom: 0px;
    padding-left: 10px;
    background: #f1f1f1;
}
.form-content{
	border:1px solid #ccc;
	margin-bottom:8px;
	padding:8px;
}
.form-content > table{
	width:100%;
}
.form-content > table td{
	padding:4px;
}


.bill{
	padding:8px;
	overflow-x:auto;
}
.form-title{
	padding:30px; 
	text-align:center; 
	font-size:20px;
	font-weight: bold;
}

.form-title .form-label{
	font-weight: bold;
}

.ime-disabled{
	/*Chrome Safari*/
	-webkit-ime-mode: disabled; /*auto | active | inactive | disabled*/
	/*Mozilla Firefox*/
	-moz-ime-mode: disabled;
	/*Opera*/
	-o-ime-mode: disabled;
	/*Internet Explorer*/
	-ms-ime-mode: disabled;
	/*CSS3 Standard*/
	ime-mode: disabled;
}


/* searchbar  */
.searchbar > table td {
	padding-top:4px;
	padding-bottom:4px;
	white-space:nowrap;
}

.searchbar{
	padding:8px;
	background:#f1f1f1;
}
.seartchbar-buttons{
	padding-left:20px;text-align:right;
}
.footbar{
	padding:8px;
	background:#f1f1f1;
	text-align:right;
}



table.form-datatable {table-layout: fixed;}
.form-datatable {border-collapse: collapse; border-spacing: 0; width: 100%}
.form-datatable > thead > tr > td, 
.form-datatable > tbody > tr > td, 
.form-datatable > thead > tr > th, 
.form-datatable > tbody > tr > th{ 
	padding:0 0; border: 1px solid #ccc; 
	height:34px;
}
.form-datatable > thead > tr > th, 
.form-datatable > tbody > tr > th{ 
	padding:0px 4px;background-color: #f6f6f6; 
	font-weight: normal; text-align: center; 
}
.form-datatable > tbody > tr > td > input,
.form-datatable > tbody > tr > td > textarea,
.form-datatable > tbody > tr > td > select ,
.form-datatable > tbody > tr > td > .input-group > input
{ border-width:0px; width:100%;}
.form-datatable > tbody > tr > td > .radio,
.form-datatable > tbody > tr > td > .checkbox{
	padding-left: 12px;
}
table > tbody > tr > td > select {
    padding-left: 8px;
}
.form-datatable > tbody > tr > td > textarea{
	height:100%;
	background:transparent;
}
.form-datatable > tbody > tr > td .select2-selection.select2-selection--single
{
	border:0px;
}
.form-datatable > tbody > tr > th {text-align:left;padding-left:4px;}
.autoscroll{
	overflow:auto;
}