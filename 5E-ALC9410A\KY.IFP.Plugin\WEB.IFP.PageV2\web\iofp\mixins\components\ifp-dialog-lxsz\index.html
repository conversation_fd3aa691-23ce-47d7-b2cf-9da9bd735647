<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示列设置</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item" id="app" style="height:500px; text-align:left;">
        <div class="toolbar">
            <el-button type="primary" icon="kjicon kjicon-baocun" @click.stop="onSave">保存</el-button>
            <el-button icon="kjicon kjicon-baocun" @click.stop="selectAll">全选</el-button>
            <el-button icon="kjicon kjicon-baocun" @click.stop="selectEmpty">全部取消</el-button>
            <el-button icon="kjicon kjicon-baocun" @click.stop="selectReverse">反选</el-button>
            <el-button icon="kjicon kjicon-tuichu" @click.stop="onExit">退出</el-button>
        </div>
        <div class="flex-item padding" style="background-color:white">
            <el-tree ref="tree"
                :data="columns"
                node-key="id"
                :props="defaultProps"
                :default-checked="defaultChecked"
                :default-expand-all="true"
                :check-on-click-node="true"
                :highlight-current="true"
                :expand-on-click-node="false"
                :show-checkbox="true"></el-tree>
        </div>
        <el-alert
          title="修改后不要忘记保存"
          show-icon
          :closable = "false"
          type="info">
        </el-alert>
    </div>
    <script src="/iofp/starter.js"></script>
</body>
</html>