define(["lodop"],function(LodopFuncs){

    // 生成打印 html
    async function print(html){

        /**
         * Lodop 打印控件对象
         * @官网 [官方文档](http://www.lodop.net/LodopDemo.html)
         * @API https://docs.qq.com/doc/DSFREekJOZ3RxZWhE
         * @文档 https://docs.qq.com/doc/DSFVIQ1hNUlNUd25w
         **/
        const LODOP = await LodopFuncs.loadLodop()

        // 初始化打印对象
        LODOP.PRINT_INIT("打印控件功能演示_Lodop功能_分页打印综合表格");


        /**
         * 
         * 数字型，0--普通项 1--页眉页脚 2--页号项 3--页数项 4--多页项 
         * 缺省（不调用本函数时）值0。普通项只打印一次；页眉页脚项则每页都在固定位置重复打印；
         * 页号项和页数项是特殊的页眉页脚项，其内容包含当前页号和全部页数；多页项每页都打印，直到把内容打印完毕，打印时在每页上的位置和区域大小固定一样（多页项只对纯文本有效）
         */
         //LODOP.SET_PRINT_STYLEA(0,"ItemType",0);

        // 设置内外边距
        LODOP.ADD_PRINT_HTM('1cm',"1cm","RightMargin:1cm","BottomMargin:1cm",html);
        //LODOP.SET_PRINT_STYLEA(0,"LinkedItem",0); // 关联对象

        // 设置 table 打印样式 数字型，0--上边距锁定 1--下边距锁定 2--垂直方向居中 3--上边距和下边距同时锁定（中间拉伸），缺省值是0。
        LODOP.SET_PRINT_STYLEA(0,"Vorient",3);	


        
        //LODOP.SET_PRINT_STYLEA(0,"ItemType",0);
        //LODOP.SET_PRINT_STYLEA(0,"LinkedItem",1);	
        
        // 打印 页号 format='ChineseNum' 可显示中文
        LODOP.ADD_PRINT_HTM(1,'90%','RightMargin:10mm',100,"<font color='#000'><span tdata='pageNO'>#</span>/<span tdata='pageCount'>#</span></font>");
        // 页号设置为每页展示
        LODOP.SET_PRINT_STYLEA(0,"ItemType",1);		// 0--普通项 1--页眉页脚 2--页号项 3--页数项 4--多页项

        LODOP.PREVIEW();
    }

    // 生成要打印的 html
    function createPrintHtml(list){
        return `
        <DIV style="LINE-HEIGHT: 30px;text-align: center; font-weight: bold;font-size:18px;">销售发货单-01</DIV>        
        <TABLE border=0 cellSpacing=0 cellPadding=0 width="100%">
            <TBODY>
                <TR>
                    <TD style="width:33.3%">收 件 人：王斌</TD> 
                    <TD style="text-align: center;">xxxxx</TD>
                    <TD style="width:33.3%;text-align: right;">发货日期：2011-5-10</TD>
                </TR>
            </TBODY>
        </TABLE>
        <TABLE border=1 cellSpacing=0 cellPadding=1 width="100%" style="border-collapse:collapse" bordercolor="#333333">
            <thead>
                <TR>
                    <TD width="10%"><DIV align=center><b>表格页眉</b></DIV></TD>
                    <TD width="25%"><DIV align=center><b>品名</b></DIV></TD>
                    <TD width="10%"><DIV align=center><b>颜色</b></DIV></TD>
                    <TD width="10%"><DIV align=center><b>规格</b></DIV></TD>
                    <TD width="10%"><DIV align=center><b>数量</b></DIV></TD>
                    <TD width="15%"><DIV align=center><b>单价</b></DIV></TD>
                    <TD width="20%"><DIV align=center><b>金额</b></DIV></TD>
                </TR>
            </thead>      
            <TBODY>
                ${
                    list.map(
                        item=>`<TR><TD>&nbsp;${item.code}</TD><TD>${item.name}</TD><TD>${item.color}</TD><TD>${item.type}</TD><TD>${item.count}</TD><TD>${item.price}</TD><TD>${item.sel}</TD></TR>`
                    ).join('')
                }      
            </TBODY>
        </TABLE>
        `
    }


    return {
        el:"#app",
        data(){
            return {
                list:[
                    {gid:"111",name:"张三"}
                ]
            }
        },
        methods:{
            print(){
                // 35条模拟数据
                let list = Array(35).fill().map((n,i)=>({
                    code:`code-${i+1}`,
                    name:`设备${i+1}`,
                    color:`蓝色`,
                    type:`T2021/${i}`,
                    count:Math.round(Math.random()*10),
                    price:Math.round(Math.random()*1000)/10+200,
                    sel:Math.round(Math.random()*1000)/10+300
                }))
                let html = createPrintHtml(list);
                print(html);
            }
        }
    }
})