﻿using System.Runtime.InteropServices;
using System.Text;

namespace COM.IFP.ComputerInfo
{
    /// <summary>
    /// 作者：戴阿斌
    /// 文档具体参考“关于增加仪器软件注册码的统一规定.doc”
    /// </summary>
    public class KYRegistrationCode
    {
        /// <summary>
        /// 获取本机ID字符串，通过参数反馈。
        /// </summary>
        /// <param name="lpStrOut">必须初始化一个值且长度不小于10，直接设置Capacity是无效的</param>
        /// <returns>成功则返回0，否则返回负值</returns>
        [DllImport("lib/KYRegistrationCode.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        public static extern int GetMyMachineID(StringBuilder lpStrOut);

        /// <summary>
        /// 检查软件是否经过授权。
        /// </summary>
        /// <param name="lpStrRegisterCode">注册码</param>
        /// <param name="nMachineType">仪器类型，含义如下：0—量热仪；
        /// 1—工业分析（含工业分析、水分仪、马弗炉、干燥箱）；
        /// 2—测硫仪（含库仑硫、红外硫）；
        /// 3—元素分析（C、H、N等元素分析）；4—灰熔融；5—胶质层；
        /// 6—以上未覆盖到的其它类型；7~9——预留；
        /// 10以上——平方的软件产品，自行约定型号。</param>
        /// <returns>-1—验证未通过，不能开始试验；
        /// 0—注册码验证通过；
        /// 1—没有注册码，正处于试用期，且距离试用截止日期30天以上；
        /// 2—没有注册码，正处于试用期，但距离试用截止日期不到30天，需要主动提醒用户。</returns>
        [DllImport("lib/KYRegistrationCode.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        public static extern int CheckLicense(StringBuilder lpStrRegisterCode, int nMachineType);

        /// <summary>
        /// 设置软件的试用期限，停用废弃，请直接用授权码蕴含期限信息
        /// </summary>
        /// <param name="lpStrLicenseDate">软件试用期截止时间的字符串指针
        /// （字符串格式形如“2014-08-15 23:59:59”）。</param>
        /// <param name="nMachineType">仪器类型，含义同前</param>
        /// <returns>成功则返回0，否则返回负值</returns>
        [DllImport("lib/KYRegistrationCode.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        public static extern int SetLicenseDate(StringBuilder lpStrLicenseDate, int nMachineType);

        /// <summary>
        /// 获取软件的试用期限，以字符串的形式在参数中反馈
        /// </summary>
        /// <param name="nMachineType">仪器类型，含义同前</param>
        /// <param name="lpStrOut">用于获取软件试用期限返回字符串的指针。
        /// （软件试用期截止时间字符串格式形如“2014-08-15 23:59:59”。
        /// 注意调用程序必须为其开辟足够的空间，dll中仅针对指针进行处理，
        /// 并不会申请新的内存</param>
        /// <returns></returns>
        [DllImport("lib/KYRegistrationCode.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        public static extern int GetLicenseDate(int nMachineType, StringBuilder lpStrOut);

        /// <summary>
        /// 记录软件开始运行的时间（当仪器软件开始运行时主动调用该函数，
        /// 为了防止软件运行期间用户篡改系统时间）
        /// </summary>
        /// <param name="nMachineType">仪器类型</param>
        /// <returns>正常则返回0，当发现用户私自修改系统时间则返回负值</returns>
        [DllImport("lib/KYRegistrationCode.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        public static extern int SetBeginTime(int nMachineType);

        /// <summary>
        /// 记录软件运行结束的时间（当仪器软件运行结束前主动调用该函数，
        /// 为了防止软件关闭后用户篡改系统时间）
        /// </summary>
        /// <param name="nMachineType">仪器类型</param>
        /// <returns>正常则返回0，当发现用户私自修改系统时间则返回负值</returns>
        [DllImport("lib/KYRegistrationCode.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        public static extern int SetEndTime(int nMachineType);

        /// <summary>
        /// 调用程序来指明自身使用的是否Unicode字符集（因为dll用的是Unicode
        /// 字符集，二者可能会不同，dll中需要进行转换）
        /// </summary>
        /// <param name="bUseUnicode">调用程序是否使用的是Unicode字符集</param>
        [DllImport("lib/KYRegistrationCode.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        public static extern void SetUseUnicode(bool bUseUnicode);


    }
}
