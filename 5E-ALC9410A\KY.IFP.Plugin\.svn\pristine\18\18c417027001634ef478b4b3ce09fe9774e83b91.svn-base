/* 首页tabs组件 */
define(["jquery","text!./menutabs/menutabs.html","css!./menutabs/menutabs"],function($,html){
	// body 内容
	var template = html.match(/<body[^>]*>([\s\S]*)<\/body>/)[1];
	var debug = false
	return {
		template:template,
		props:{
			items:{default:function(){return []}},
			idfield:{default:function(){return ""}}
		},
		data: function () {
		  return {
			  boxWidth:0,
			  innerWidth:0,
			  scrollLeft:0,
			  currentTab:null
		  }
		},
		created:function(){
			this.items.forEach(function(item){
				item = Object.assign({
					id:null,
					title:"页签",
					url:null,
					active:false,
					rendered:false,
					noClose:false,
					module:null
				},item)
			});
			
			var actives = this.items.filter(function(item){
				return item.active === true;
			});
			
			if(actives.length>0){
				actives.forEach(function(item){
					item.active = false;
				});
				actives[0].active=true;
				actives[0].rendered = true;
				this.current = actives[0];
			}
		},
		mounted:function(){
			var _this = this;
			this.updateScroll();
		},
		
		computed:{
			disabledLeft:function(){
				return this.scrollLeft == 0;
			},
			disabledRight:function(){
				return this.innerWidth - this.scrollLeft - this.boxWidth <=0;
			},
			hasScroll:function(){
				return this.boxWidth-this.innerWidth<0
			},
			step:function(){
				return this.boxWidth*0.8 || 100;
			}
		},

		methods:{
			showTab:function(){
				debug&&console.log("showTab");
				this.$nextTick(function(){
					var activeEl = $("li.active",this.$refs["listwrap"]);
					if(activeEl.length){
						var l = activeEl.position().left;
						if(l-20<0){
							this.scrollTo(l-20);
						}
						var w = activeEl.width();
						if(l>this.boxWidth-w){
							this.scrollTo(l-this.boxWidth+w-20);
						}
					}
				});
				//this.selectModule = this.current.module||"";
			},
			updateScroll:function(){
				this.$nextTick(function(){
					var wraper = $(this.$refs["listwrap"]);
					var box = wraper.children().eq(0);
					this.boxWidth=wraper.width();
					this.innerWidth=box.width();
					this.scrollLeft = wraper.scrollLeft();
				});
			},
			getTabById:function(id){
				return this.items.filter(function(item){return item.id == id})[0];
			},
			selectTab:function(item){
				debug&&console.log("selectTab");
				if(this.current){
					this.current.active = false
				};
				this.current = this.getTabById(item.id);
				this.current.active = true;
				this.current.rendered = true;
				this.updateScroll();
				this.showTab();
				this.$emit("showtab",this.current.module||"");
			},
			selectLast:function(){
				this.items.length&&(this.items[this.items.length-1].active = true);
			},
			getNextTab:function(id){
				//如果非最后一个，则取后一个，否则取前一个，若只有一个则直接返回空
				
				var rev = null;
				for(var i = 0;i<this.items.length;i++){
					if(this.items[i].id != id) {continue;}
					if(i<this.items.length-1){
						rev = this.items[i+1];
						break;
					}
					if(i==this.items.length-1&&i>0){
						rev = this.items[i-1];
						break;
					}
					return null;
				}
				return rev;
			},
			deleteTab:function(id){
				//找到下一个页签
				var nextTab = this.getNextTab(id);
				
				if(nextTab && this.current && this.current.id == id){
	    			this.selectTab(nextTab);
				}
				var index = -1;
				for(var i =0;i<this.items.length;i++){
					if(this.items[i].id==id){
						index = i;
						break;
					}
				}
				this.$delete(this.items,index);
				this.updateScroll();
				this.showTab();
			},
			addTab:function(name,url,selected,data,hasClose){
				debug&&console.log("addTab");
				var id = data[this.idfield];
				var is = this.getTabById(id);
				if(is){
					this.selectTab(is);
					this.updateScroll();
					this.showTab();
					return;
				}
				if(url){
					url+=url.indexOf("?")==-1?"?":"&";
					url+="_m="+id;
					if(window.currentVersion){
						url+="&ver=" + window.currentVersion;
					}
				}
				var item = Object.assign({
					active:false,
					rendered:false
				},{
					id:id,
					title:name,
					url:url,
					noClose:hasClose===false,
					module:data.module&&data.module[this.idfield]||data[this.idfield]
				});
				
				this.items.push(item);
				this.selectTab(item);
				this.updateScroll();
				this.showTab();
			},
			scrollTo:function(v,useanimate){
				var _this = this;
				//useanimate = useanimate!==false;
				var el = $(this.$refs["listwrap"]);
				el.scrollLeft(this.scrollLeft + v);
				this.updateScroll();
			},
			
			onScroll:function(e){
				this.scrollTo(e.deltaY,false);
			},
			scrollLeftClick:function(){
				this.scrollTo(this.step*-1);
			},
			scrollRightClick:function(){
				this.scrollTo(this.step);
			}
		}
	}
})