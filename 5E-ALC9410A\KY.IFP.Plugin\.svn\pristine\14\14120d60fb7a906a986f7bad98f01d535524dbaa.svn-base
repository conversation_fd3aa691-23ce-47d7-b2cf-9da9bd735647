﻿define(["iofp/api", "platform/vue"], function (API, pVue) {
    const components = pVue.createAsyncComponents({
        IfpYsdwDetail: "./detail.js"  //对应标签ifp-ysdw-detail，VUE会把IfpPcgzDetail 转换成 全小写， 大写字母全部变成 -小写字母
    })

    return {
        el: "#app",

        components: components,

        data() {
            return {
                //查询条件
                filter:{
                    Zfbz: { Value: 0, Match: 'EQU' },
                },

                //表格数据
                tableData: [],
                source: {},
                //子窗口属性
                editor: {
                    //是否显示子窗口
                    showdetail: false,

                    //子窗口标题
                    title: '',

                    //父页面传入的对象
                    source: "",

                    //操作类型(add, view, modify)
                    czlx: ""
                }
            }
        },

        created() {
            this.onReset();
        },

        methods: {
            //查询
            onSelect: function () {
                let obj = JSON.parse(JSON.stringify(this.filter));  //拷贝一份
                if (obj.Zfbz.Value == -1)
                    obj = null;
                API.GetAction("API/ICS/BaseData/Ywdx4005API/Select", { "filter": obj}).then(x => {
                    this.$set(this, "tableData", x);
                    this.editor.source = null;
                    this.$refs.tableBc.setCurrentRow();
                }).catch(e => {
                    this.$message.error(e.message);
                });
            },

            sizeChange(v) {
                this.paging.size = v;
                this.onSelect();
            },

            pageChange(v) {
                this.paging.page = v;
                this.onSelect();
            },

            //重置
            onReset: function () {
                this.filter.Zfbz.Value = 0;
                this.onSelect();
            },

            //新增
            onAdd: function () {
                this.editor.showdetail = true;
                this.editor.title = "新增";
                this.editor.czlx = "add";
                this.editor.source = {};
            },

            //查看
            onView: function () {
                if (this.source != null && this.source.Gid != undefined) {
                    this.editor.source = this.source;
                    this.editor.showdetail = true;
                    this.editor.title = "查看";
                    this.editor.czlx = "view";
                } else {
                    this.$message.warning("请选中一行。");
                }
            },

            //修改
            onUpdate: function () {
                if (this.source != null && this.source.Gid != undefined) {
                    this.editor.source = this.source;
                    this.editor.showdetail = true;
                    this.editor.title = "修改";
                    this.editor.czlx = "modify";
                } else {
                    this.$message.warning("请选中一行。");
                }
            },

            //点“退出”按钮
            onExit: function () {
                this.$emit("cancel");
            },

            //当前行变化事件
            rowClick: function (row) {
                this.source = row;
            }
        }
    }
})