<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="业务类型,控件,下拉">
    <title>业务类型下拉控件</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app" class="padding">
        <ifp-select-ywlx v-model="custom" :ywlx="1003"></ifp-select-ywlx>
        <ifp-select-ywlx v-model="custom" :ywlx="1002"></ifp-select-ywlx>
        <ifp-select-ywlx :canedit="false" v-model="custom" ywlx="1047"></ifp-select-ywlx>

        <div>
            
            页面中直接使用 ifp-select-ywlx 控件<br/>
            4002:
            <ifp-select-ywlx 
            v-model="ywlx4002" 
            :ywlx="4002"
            ></ifp-select-ywlx>
            1003:
            <ifp-select-ywlx v-model="ywlx1003" :ywlx="1003"></ifp-select-ywlx>

            <br>
            绑定事件，与el-select一致
            
            <ifp-select-ywlx 
            v-model="ywlx4002" 
            :ywlx="4002"
            @change="$message(`值变更为：${$event.toString()}`)"
            @focus="$message('获得焦点')"
            @blur="$message('失去焦点')"
            ></ifp-select-ywlx>
            <el-link target="_blank" href="http://127.0.0.1:3000/docs/#/components/%E4%B8%9A%E5%8A%A1%E6%8E%A7%E4%BB%B6">参考文档</el-link>
            
            <ifp-panel padding title="设备">
                <ifp-select-sb v-model="value_sb" sbyt="10090005,10090006" sblx="10020004" value-key="Sbbm" :value-is-number="false" :showid="true">
                </ifp-select-sb>
                <ifp-select-sb multiple v-model="value_sb2" sbyt="10090005,10090006" sblx="10020004">
                </ifp-select-sb>
                <ifp-select-sb v-model="value_sb">
                </ifp-select-sb>
                不可编辑：
                <ifp-select-sb multiple :canedit="false" v-model="value_sb2" sbyt="10090005,10090006" sblx="10020004">
                </ifp-select-sb>
            </ifp-panel>
        </div>
    </ifp-page>

    <script src="/iofp/starter.js"></script> 
</body>
</html>