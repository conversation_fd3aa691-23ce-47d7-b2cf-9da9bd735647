{"version": 3, "sources": ["less\\dicb\\layout.less", "less\\dicb\\grid.less", "less\\dicb\\select2.less", "less\\dicb\\bill.less", "less\\dicb\\ztree.less", "less\\dicb\\form.less", "less\\dicb\\searchbar.less", "less\\dicb\\helper.less", "less\\dicb\\form-datatable.less", "less\\dicb\\bootstrap-dialog.less", "less\\dicb\\dicb.less"], "names": [], "mappings": "AAAA,UACC,SAAA,KCCD,WACI,iBAAA,KAEO,2BACV,WAAA,QAEqC,wDACrC,WAAA,IAE8C,4DAC9C,QAAA,KAG+B,2CAC5B,OAAA,KACA,OAAA,IAAA,EACH,WAAA,OAI4C,gDACV,sCAClC,cAAA,EAE8C,2DAC9C,QAAA,EAEqC,0CACrC,SAAA,kBAGwD,4DACxD,WAAA,IAAA,MAAA,KChCD,mBACC,QAAA,MACA,sCACC,cAAA,gBCLF,MACC,QAAA,IACA,WAAA,KCCD,OACC,WAAA,KACG,OAAA,YACG,YAEC,UAAA,KAEO,mBACP,WAAA,IACI,+BACA,OAAA,KAAA,ICZe,qCAC1B,OAAA,IAAA,MAAA,kBAEsC,0CACtC,WAAA,QAE0C,gDAC1C,WAAA,QACA,MAAA,KAED,gBACC,iBAAA,KACA,8BAAiC,mCAAnB,mCACb,OAAA,EAED,mCACC,YAAA,IAAA,MAAA,KAIF,YAAY,OAAA,EACV,YAAA,IAEF,aACC,QAAA,IACA,OAAA,IAAA,MAAA,KACA,cAAA,EACA,aAAA,KACG,iBAAA,QAEJ,cACC,OAAA,IAAA,MAAA,KACA,cAAA,IACA,QAAA,IAEa,oBACb,MAAA,KAEqB,uBACrB,QAAA,IAED,cACC,QAAA,KAAA,EAGD,SACC,QAAA,IACA,WAAA,QACA,WAAA,MAID,cACC,WAAA,IACA,QAAA,IAUS,iCADV,uBAFA,oBACO,2BAGN,QAAA,aACA,cAAA,EACA,aAAA,KACA,SAAA,SACA,YAAA,IACA,aAAA,EAMyB,+CADH,yCAEtB,QAAA,MACA,aAAA,EAG6B,4CADH,sCAE1B,QAAA,KAG4C,iDADN,2CAEtC,aAAA,MAG2D,iEADN,2DAElD,OAAA,IAAA,MAAA,KAIkE,0EADN,oEAE5D,WAAA,KAG8C,uDADN,iDAE3C,QAAS,IACN,QAAA,aACA,MAAA,IACA,eAAA,OACA,OAAA,IACA,OAAA,IAAA,MAAA,QACA,SAAA,SACA,IAAA,IACA,WAAA,MACA,KAAA,EAG8C,wDADN,kDAE3C,QAAS,IACT,WAAA,IAAA,IACG,aAAA,IACA,QAAA,aACA,SAAA,SAEA,MAAA,EACA,OAAA,EACA,IAAA,IACA,KAAA,KACA,WAAA,EACH,WAAA,QAG2C,iDADA,kDAExC,cAAA,IAGwD,gEADN,0DAElD,MAAA,IACA,OAAA,IACA,IAAA,IACA,KAAA,EACA,WAAA,MACH,WAAA,QAKD,sBACC,WAAA,MC7I+B,6BAC/B,YAAA,IACA,eAAA,IACA,YAAA,OACA,mCACC,YAAA,QAIF,WACC,QAAA,IACA,WAAA,IACA,SAAA,SACA,WAAA,EAED,oBACC,aAAA,KAAkB,WAAA,MAEF,mCAChB,QAAA,aAEiB,oCACjB,QAAA,aACG,QAAA,EACA,WAAA,MAEJ,kBACI,OAAA,EACA,SAAA,QACA,QAAA,EAAA,KACA,WAAA,MACA,QAAA,MACA,MAAA,KACA,WAAA,KAE2B,kCAC3B,OAAA,QACA,MAAA,KACA,QAAA,MACA,WAAA,MACA,cAAA,KACA,SAAA,SACA,IAAA,KAEiC,wCACjC,MAAA,KACA,WAAA,QAEM,qBACT,WAAA,KAES,qBACT,WAAA,MAES,kBACT,OAAA,EACA,SAAA,OACA,QAAA,EAGD,oBACC,QAAA,WACA,QAAA,EAED,yBACC,QAAA,aACA,aAAA,KACA,QAAA,IAAA,EAEwB,8BACxB,QAAA,aAAsB,eAAA,OAGvB,mBACC,QAAA,WACA,QAAA,IACA,YAAA,OACA,eAAA,IAEkB,iCAClB,SAAA,SACC,IAAA,ICjFF,QAAQ,YAAA,IACR,QAAQ,aAAA,IACR,SAAS,YAAA,KACT,SAAS,aAAA,KAET,SAAS,aAAA,KACT,SAAS,cAAA,KAIT,SAAS,QAAA,IACT,aAAa,YAAA,IACb,QAAQ,OAAA,IACR,YAAY,WAAA,IACZ,eAAe,cAAA,ICZV,qBAAiB,aAAA,MACtB,gBAAiB,gBAAA,SAA2B,eAAA,EAAmB,MAAA,KAElC,4BAEA,4BAHA,4BAEA,4BAE5B,QAAA,EAAA,EAAa,OAAA,IAAA,MAAA,KACb,OAAA,KAG4B,4BADA,4BAE5B,QAAA,EAAA,IAAgB,iBAAA,QAChB,YAAA,IAAqB,WAAA,OAK2B,+CAHf,kCAEA,mCADA,qCAGhC,aAAA,EAAkB,MAAA,KAEc,sCADA,mCAEjC,aAAA,KAEiC,qCACjC,OAAA,KACA,WAAA,IAEmD,yEAEnD,OAAA,EAEgC,kCAChC,aAAA,IACA,cAAA,IAG4B,4BAAM,WAAA,KAAgB,aAAA,ICpC3C,8CACI,uBAAA,EACA,wBAAA,EACA,OAAA,EAGP,gCACG,aAAA,YACA,SAAA,OACuC,+EACnC,QAAA,ECAZ,KACC,WAAA,QACA,UAAA,KACA,SAAA,SAGD,OACC,OAAA,EACA,eAAA,IAEE,GAAH,GACC,WAAA,KACA,OAAA,EACA,QAAA,EAGD,GAEC,SAAA,SAGD,SACI,WAAA,KACA,WAAA,OACH,SAAA,KACA,eACC,QAAA,KACA,WAAA,KACA,MAAA,OACA,OAAA,KAAA,KACA,WAAA,KACA,WAAA,KACgB,6BAAhB,6BACC,OAAA,EAAA,MAAA,KACA,iBAAA,KAKH,iBACC,UAAA,KACA,QAAA,EAAA,KAGA,2BACC,OAAA,EAED,0BACC,OAAA,EAIC,iBACF,WAAA,KACA,QAAA,IACA,UAAA,KAEa,kBACb,SAAA,OAED,YACC,QAAA,KACA,WAAA,OACA,UAAA,KACA,YAAA,IAID,cAEC,iBAAA,SAEA,cAAA,SAEA,YAAA,SAEA,aAAA,SAEA,SAAA,SAIuB,yBACpB,aAAA,IAEJ,YACC,SAAA,KAMgE,oFACD,mFAJ1C,6BACtB,gBAI8C,iEAH9B,sBAIZ,aAAA,QACA,mBAAA,MAAA,EAAA,IAAA,IAAA,gBAAA,CAAA,EAAA,EAAA,IAAA,QACA,WAAA,MAAA,EAAA,IAAA,IAAA,gBAAA,CAAA,EAAA,EAAA,IAAA,QAGuD,gFAC1D,WAAA,QAEuE,wFACpE,iBAAA,QACA,MAAA,KAGJ,YACI,QAAA,aACA,eAAA,OAEM,gBACoB,yCAC7B,WAAA,KAED,QACC,QAAA,KACA,SAAA,KAED,cACC,QAAA,KACG,WAAA,OACA,UAAA,KACA,YAAA,IAEU,0BACV,YAAA,IAGW,eAAf,eACC,cAAA,KAMD,sBADA,oBAEA,qBAJA,sBADA,oBAEA,qBAIC,QAAA,WAID,uBADA,uBAEC,QAAA,MACA,MAAA,KACA,aAAA,MAID,oBADA,oBAEI,WAAA,KACH,MAAA,QAGD,qBADA,qBAEC,WAAA,MACA,MAAA,QAGD,sBADA,sBAEI,WAAA,OACH,MAAA,QAI+B,yCACJ,wCAHI,yCACJ,wCAG3B,WAAA,IACG,WAAA,EACA,WAAA,KACA,YAAA,EACA,aAAA,EAEJ,cACI,cAAA,KACA,WAAA,KAEJ,qBACC,OAAA,EAAA,MAAA,KACG,iBAAA,QACA,QAAA,IAKmB,mCADA,mCAEtB,UAAA,MACG,cAAA,IAAA,MAAA,KACA,QAAA,aACA,OAAA,EACA,YAAA,KACA,YAAA,IACA,OAAA,KACA,eAAA,OAGU,oBACb,MAAA,KAEsB,uBACnB,QAAA,IAAA,IAEJ,YACC,SAAA,SAAkB,IAAA,KAAS,MAAA,KAEJ,8BACvB,UAAA,KAII,kBAAkB,mBAAqC,wBAAlB,YAC1C,mBAAA,MAIA,YACI,MAAA", "sourcesContent": [".layout-c{\n\toverflow:auto;\n}", "\n\n.ui-jqgrid{\n    background-color: #fff;\n}\n.ui-jqgrid .ui-jqgrid-hdiv {\n\tbackground: #E6EAED;\n}\n.table>tbody>tr>td.edit-cell.success  .input-group-addon{\n\tbackground:transparent;\n}\n.ui-jqgrid .ui-pager-control .ui-pager-table td[align=\"left\"]{\n\tdisplay:none;\n}\n\n.ui-jqgrid .ui-jqgrid-htable th .ui-th-div {\n    height: auto;\n    margin: 2px 0;\n\ttext-align: center;\n}\n\n/* jqgrid */\n.ui-jqgrid .ui-jqgrid-btable tbody tr.jqgrow td,\n.ui-jqgrid .ui-jqgrid-htable thead th{\n\tpadding-right:0px;\n}\n.ui-jqgrid .ui-jqgrid-btable tbody tr.jqgrow td.hascontrol{\n\tpadding:0px;\n}\n.ui-jqgrid .ui-jqgrid-htable thead th div{\n\toverflow: visible !important;\n}\n/* 多行表头边线丢失 */\n.ui-jqgrid tr.jqg-third-row-header.jqg-second-row-header > th{\n\tborder-top:1px solid #ddd;\n}", "\n\n.select2-container{\n\tdisplay: block;\n\t.select2-selection{\n\t\tborder-radius:unset !important;\n\t}\n}", ".bill{\n\tpadding:8px;\n\toverflow-x:auto;\n}", "\n\n\n.ztree{\n\tbackground:#fff;\n    border:0px !important;\n    li *\n    {\n        font-size:14px;\n    }\n    &[control] li{\n        margin-top:5px;\n        span.button{\n            margin:auto 5px;\n        }\n    }\n}", "/* left tree */\ndiv[form=\"forms/lefttree\"] > .layout-h{\n\tborder:1px solid #E6EAED !important;\n}\ndiv[form=\"forms/lefttree\"] > .layout-h .btn{\n\tbackground:#E6EAED;\n}\ndiv[form=\"forms/lefttree\"] > .layout-h .btn:hover{\n\tbackground:#568EFF;\n\tcolor:#fff;\n}\n.form-datatable{\n\tbackground-color: #fff;\n\t.form-control,.select2-selection,.input-group-addon{\n\t\tborder:0;\n\t}\n\t.input-group-addon{\n\t\tborder-left:1px solid #ccc;\n\t}\n}\n\n.form-label{margin: 0px;\n  font-weight: normal;\n}\n.form-header{\n\tpadding:8px;\n\tborder: 1px solid #ccc;\n\tborder-bottom: 0px;\n\tpadding-left: 10px;\n    background-color: #f1f1f1;\n}\n.form-content{\n\tborder:1px solid #ccc;\n\tmargin-bottom:8px;\n\tpadding:8px;\n}\n.form-content > table{\n\twidth:100%;\n}\n.form-content > table td{\n\tpadding:4px;\n}\n.form-buttons {\n\tpadding:10px 0px;\n}\n\n.footbar{\n\tpadding:8px;\n\tbackground:#f1f1f1;\n\ttext-align:right;\n}\n\n/* area */\n.area-toolbar{\n\tbackground: transparent;\n\tpadding:8px;\n}\n\n\n\n\n/*radio checkbox*/\n.form-control-radio,\n.radio .form-control-radio,\n.form-control-checkbox,\n.checkbox .form-control-checkbox{\n\tdisplay: inline-block;\n\tmargin-bottom:0px;\n\tmargin-right:0.8em;\n\tposition: relative;\n\tfont-weight: normal;\n\tpadding-left:0px;\n}\n\n\n/* checkbox */\n.radio.vertical > label.form-control-radio,\n.checkbox.vertical > label.form-control-checkbox{\n\tdisplay: block;\n\tmargin-right:0px;\n}\n.form-control-radio > input[type=\"radio\"],\n.form-control-checkbox > input[type=\"checkbox\"]{\n\tdisplay: none;\n}\n.form-control-radio>input[type=\"radio\"]+span,\n.form-control-checkbox>input[type=\"checkbox\"]+span{\n\tpadding-left:1.2em;\n}\n.form-control-radio>input[type=\"radio\"][disabled]+span:after,\n.form-control-checkbox>input[type=\"checkbox\"][disabled]+span:after{\n    border: 1px solid #aaa;\n}\n\n.form-control-radio>input[type=\"radio\"][disabled]:checked + span:before,\n.form-control-checkbox>input[type=\"checkbox\"][disabled]:checked + span:before{\n    background: #aaa;\n}\n.form-control-radio>input[type=\"radio\"]+span:after,\n.form-control-checkbox>input[type=\"checkbox\"]+span:after{\n\tcontent: \" \";\n    display: inline-block;\n    width: 1em;\n    vertical-align: middle;\n    height: 1em;\n    border: 1px solid #1E90FF;\n    position: absolute;\n    top: 50%;\n    margin-top: -0.5em;\n    left: 0;\n}\n.form-control-radio>input[type=\"radio\"]+span:before,\n.form-control-checkbox>input[type=\"checkbox\"]+span:before{\n\tcontent: \" \";\n\ttransition: all 0.2s;\n    margin-right: 5px;\n    display: inline-block;\n    position: absolute;\n\tbackground: #1E90FF;\n    width: 0em;\n    height: 0em;\n    top: 50%;\n    left: 0.5em;\n    margin-top: 0em;\n\tbackground: #1E90FF;\n}\n.form-control-radio>input[type=\"radio\"]+span:before,\n.form-control-radio>input[type=\"radio\"]+span:after{\n    border-radius: 50%;\n}\n.form-control-radio>input[type=\"radio\"]:checked + span:before,\n.form-control-checkbox>input[type=\"checkbox\"]:checked + span:before{\n    width: 1em;\n    height: 1em;\n    top: 50%;\n    left: 0em;\n    margin-top: -0.5em;\n\tbackground: #1E90FF;\n}\n\n\n/* nubmer 控件 */\n.control-input-number{\n\ttext-align:right;\n}", "\n/* searchbar  */\n.searchbar > table > tbody > tr > td {\n\tpadding-top:4px;\n\tpadding-bottom:4px;\n\twhite-space:nowrap;\n\ttable{\n\t\twhite-space: initial;\n\t}\n}\n\n.searchbar{\n\tpadding:8px;\n\tbackground:transparent;\n\tposition: relative;\n\tmin-height:0px;\n}\n.seartchbar-buttons{\n\tpadding-left:20px;text-align:right;\n}\n.searchbar:hover .searchbar-folder{\n\tdisplay:inline-block;\n}\n.searchbar.folded .searchbar-folder{\n\tdisplay:inline-block;\n    z-index:2;\n    margin-top: -14px;\n}\n.searchbar-folder{\n    height: 0px;\n    overflow: visible;\n    padding: 0px 10px;\n    text-align: right;\n    display: block;\n    color: #aaa;\n    background: #eee;\n}\n.searchbar + .searchbar-folder > span{\n    cursor: pointer;\n    width: 100%;\n    display: block;\n    text-align: right;\n    padding-right:10px;\n    position: relative;\n    top: -7px;\n}\n.searchbar + .searchbar-folder > span:hover{\n    color:#fff;\n    background:#568EFF;\n}\n.searchbar.singlerow{\n\tmin-height:66px;\n}\n.searchbar.doublerow{\n\tmin-height:116px;\n}\n.searchbar.folded{\n\theight:0px;\n\toverflow:hidden;\n\tpadding: 0px;\n}\n\n.searchbar-inputers{\n\tdisplay: table-cell;\n\tpadding: 0px;\n}\n.searchbar-inputers-item{\n\tdisplay: inline-block;\n\tmargin-right: 10px;\n\tpadding: 8px 0;\n}\n.searchbar-inputers-item > span{\n\tdisplay: inline-block;vertical-align: middle;\n}\n\n.searchbar-buttons{\n\tdisplay: table-cell;\n\tpadding: 4px;\n\twhite-space: nowrap;\n\tvertical-align: top;\n}\n.searchbar-buttons > [type=button]{\n\tposition: relative;\n \ttop: 5px;\n}", "\n\n.mt-l-8{margin-left:8px;}\n.mt-r-8{margin-right:8px;}\n.mt-l-20{margin-left:20px;}\n.mt-r-20{margin-right:20px;}\n\n.pg-l-20{padding-left:20px;}\n.pg-r-20{padding-right:20px;}\n\n\n/* base */\n.padding{padding:8px;}\n.padding-top{padding-top:8px;}\n.margin{margin:8px;}\n.margin-top{margin-top:8px;}\n.margin-bottom{margin-bottom:8px;}", "\n\n\n\ntable.form-datatable {table-layout: fixed;}\n.form-datatable {border-collapse: collapse; border-spacing: 0; width: 100%}\n.form-datatable > thead > tr > td,\n.form-datatable > tbody > tr > td,\n.form-datatable > thead > tr > th,\n.form-datatable > tbody > tr > th{\n\tpadding:0 0; border: 1px solid #ccc;\n\theight:34px;\n}\n.form-datatable > thead > tr > th,\n.form-datatable > tbody > tr > th{\n\tpadding:0px 4px;background-color: #f6f6f6;\n\tfont-weight: normal; text-align: center;\n}\n.form-datatable > tbody > tr > td > input,\n.form-datatable > tbody > tr > td > textarea,\n.form-datatable > tbody > tr > td > select ,\n.form-datatable > tbody > tr > td > .input-group > input\n{ border-width:0px; width:100%;}\n.form-datatable > tbody > tr > td > .radio,\n.form-datatable > tbody > tr > td > .checkbox{\n\tpadding-left: 12px;\n}\n.form-datatable > tbody > tr > td > textarea{\n\theight:100%;\n\tbackground:transparent;\n}\n.form-datatable > tbody > tr > td .select2-selection.select2-selection--single\n{\n\tborder:0px;\n}\n.form-datatable > tbody > tr > td.title{\n\tpadding-left:8px;\n\tpadding-right:8px;\n}\n\n.form-datatable > tbody > tr > th {text-align:left;padding-left:4px;}", "\n/* dialog */\n.bootstrap-dialog{\n    .modal-dialog {\n        .modal-header {\n            border-top-left-radius: 0px;\n            border-top-right-radius: 0px;\n            border:0;\n        }\n    }\n    &.dialog-iframe {\n        padding-left:0px !important;\n        overflow:hidden;\n        > div.modal-dialog > div.modal-content > .modal-body{\n            padding:0px;\n        }\n    }\n}", "\n@import \"../bootstrap/variables-kykj.less\";\n@import \"layout.less\";\n@import \"grid.less\";\n@import \"select2.less\";\n@import \"bill.less\";\n@import \"ztree.less\";\n@import \"form.less\";\n@import \"searchbar.less\";\n@import \"helper.less\";\n@import \"form-datatable.less\";\n@import \"bootstrap-dialog.less\";\n\n/* font-size */\nbody{\n\tbackground:#efefef;\n\tfont-size:14px;\n\tposition: relative;\n}\n\niframe{\n\tborder: 0px;\n\tvertical-align:top;/* 解决iframe height:100% 时，父元素出现滚动条的问题 */\n}\nul,li{\n\tlist-style: none;\n\tmargin: 0px;\n\tpadding:0px;\n}\n\nli{\n\t/* text-align: center; */\n\tposition: relative;\n}\n\n.billbox{\n    background: rgb(102, 102, 102);\n    text-align: center;\n\toverflow: auto;\n\t.bill{\n\t\tpadding: 50px;\n\t\toverflow-x: auto;\n\t\twidth: 1000px;\n\t\tmargin: 20px auto;\n\t\tbackground: #fff;\n\t\ttext-align: left;\n\t\t> .form-content,.form-content{\n\t\t\tborder:0px solid #ccc;\n\t\t\tbackground-color: #fff;\n\t\t}\n\t}\n}\n/* modal */\n.icon-modal-info{\n\tfont-size:40px;\n\tpadding:0 20px;\n}\n.modal.full{\n\t.modal-content{\n\t\tborder:0px;\n\t}\n\t> .modal-dialog{\n\t\tmargin: 0px;\n\t}\n}\n\ndiv.modal-header{\n\tbackground: #ddd;\n\tpadding:8px;\n\tfont-size:14px;\n}\ndiv.modal.full.in{\n\toverflow:hidden;\n}\n.form-title{\n\tpadding:10px;\n\ttext-align:center;\n\tfont-size:20px;\n\tfont-weight: bold;\n}\n\n\n.ime-disabled{\n\t/*Chrome Safari*/\n\t-webkit-ime-mode: disabled; /*auto | active | inactive | disabled*/\n\t/*Mozilla Firefox*/\n\t-moz-ime-mode: disabled;\n\t/*Opera*/\n\t-o-ime-mode: disabled;\n\t/*Internet Explorer*/\n\t-ms-ime-mode: disabled;\n\t/*CSS3 Standard*/\n\time-mode: disabled;\n}\n\n\ntable > tbody > tr > td > select {\n    padding-left: 8px;\n}\n.autoscroll{\n\toverflow:auto;\n}\n\n.required.form-control:focus,\n.state_required,\n.state_required input,\n.required .select2-container--bootstrap.select2-container--focus .select2-selection,\n.required .select2-container--bootstrap.select2-container--open .select2-selection,\n.state_required .select2-container--bootstrap .select2-selection{\n    border-color: #843534;\n    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #ce8483;\n    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #ce8483;\n}\n\nbody .select2-container--bootstrap .select2-results__option[aria-selected=true]{\n\tbackground:#dff0d8;\n}\nbody .select2-container--bootstrap .select2-results__option--highlighted[aria-selected] {\n    background-color: #337ab7;\n    color: #fff;\n}\n\n.pickerwrap{\n    display: inline-block;\n    vertical-align: middle;\n}\n.inputDis input,\n.picker.inputDis .form-control[readonly]{\n\tbackground:#eee;\n}\n.report{\n\tpadding:10px;\n\toverflow:auto;\n}\n.report-title{\n\tpadding: 10px;\n    text-align: center;\n    font-size: 20px;\n    font-weight: bold;\n}\n.report-title .form-label{\n    font-weight: bold;\n}\n\n.report-header,.report-footer{\n\tmargin-bottom:10px;\n}\n.report-header-left,\n.report-header-center,\n.report-header-right,\n.report-footer-left,\n.report-footer-center,\n.report-footer-right{\n\tdisplay: table-cell;\n}\n\n.report-header-content,\n.report-footer-content{\n\tdisplay:table;\n\twidth:100%;\n\ttable-layout: fixed;\n}\n\n.report-header-left,\n.report-footer-left{\n    text-align: left;\n\twidth:33.333%;\n}\n.report-header-right,\n.report-footer-right{\n\ttext-align:right;\n\twidth:33.333%;\n}\n.report-header-center,\n.report-footer-center{\n    text-align: center;\n\twidth:33.333%;\n}\n.report-header-content input:not([type]),\n.report-header-content input[type=text],\n.report-footer-content input:not([type]),\n.report-footer-content input[type=text]{\n\tbackground: transparent;\n    border-top: 0;\n    box-shadow: none;\n    border-left: 0;\n    border-right: 0;\n}\n.report-group{\n   \tmargin-bottom:10px;\n   \tbackground:#fff;\n}\n.report-group-header{\n\tborder: 0px solid #ccc;\n   \tbackground-color: #E6EAED;\n   \tpadding: 8px;\n}\n\n\n.report-header-content .form-label,\n.report-footer-content .form-label{\n\tmin-width: 100px;\n    border-bottom: 1px solid #666;\n    display: inline-block;\n    margin: 0px;\n    line-height: 24px;\n    font-weight: normal;\n    height: 24px;\n    vertical-align: bottom;\n}\n\n.report-group > table{\n\twidth:100%;\n}\n.report-group  > table td {\n    padding: 4px 8px;\n}\n.report-ybz{\n\tposition:absolute;top:90px;right:50px;\n}\n.btn-group.btn-group-xs .dicb {\n\tfont-size:12px;\n}\n\n/* 冲掉iview中的样式 */\nhtml [type=reset],html [type=submit],html button, html body [type=button]{\n-webkit-appearance:unset;\n}\n\n\n.pickerwrap {\n    width: 100%;\n}"]}