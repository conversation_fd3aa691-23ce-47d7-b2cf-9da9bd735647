﻿<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>矸石化验记录表</title>
		<style type="text/css">
        .SelectBG{
           color:blue;
            }
    </style>
	</head>
	<body controller="test.js">
		<div form="forms/toolbar" class="layout-h">		
			<a id="addBtn" control="controls/button" option="{}">test1 </a>
			<a id="showBtn" control="controls/button" option="{}">查看</a>
			<a id="editBtn" log control="controls/button" option="{}">修改</a>
			<a id="delBtn" control="controls/button" option="{}">删除</a>


			<a id="returnBtn" control="controls/button"  option="{command:'close'}">退出</a>

			<input id="btnInput" log type="button" value="input" />		
			<a id="btnInput" log >aButton</a>
			<a log-id="btnInput" log >使用 log-id</a>
			<a id="add2Btn2" log title="保存" control="controls/button" option="{icon:'保存'}"></a>

			<a log log-name="btn2" title="在图标按钮中，默认使用 title 作为 name，也可以通过 log-name 单独设置，就像这个例子" log-name="保存" control="controls/button" option="{icon:'保存'}"></a>

			<!-- 这个图标按钮 既未指定 tilte 也未指定 log-name 那么 btnName 会为空 -->
			<a log log-page-id="pageurl" log-page-name="888" control="controls/button" option="{icon:'保存'}"></a>

			<a id="xxxx" type="button" class="btn btn-default" isrendered="true" log="log">
				<span class="kjicon kjicon-baocun" aria-hidden="true" title="undefined"></span>
			</a>
		</div>
	</body>
	<script src="/iofp/starter.js"></script> 
</html>
