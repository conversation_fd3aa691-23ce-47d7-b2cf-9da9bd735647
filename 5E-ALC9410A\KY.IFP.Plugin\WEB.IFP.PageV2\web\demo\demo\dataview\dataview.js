define(['vue', 'datav'], function (Vue, DataV) {
    var data1 = {
        data: [
            {
                name: '南阳',
                value: 167,
            },
            {
                name: '周口',
                value: 67,
            },
            {
                name: '漯河',
                value: 123,
            },
            {
                name: '郑州',
                value: 55,
            },
            {
                name: '西峡',
                value: 98,
            },
        ],
    };

    var app = new Vue({
        el: '#app',
        data: function () {
            return {
                loading: true,
                data1: data1,
                dv_scroll_ranking_board_config: {
                    data: [
                        { name: '周口', value: 55 },
                        { name: '南阳', value: 120 },
                        { name: '西峡', value: 78 },
                        { name: '驻马店', value: 66 },
                        { name: '新乡', value: 80 },
                        { name: '信阳', value: 45 },
                        { name: '漯河', value: 29 },
                    ],
                    carousel: 'page',
                },
                dv_water_level_pond_config: {
                    data: [66, 45],
                    shape: 'roundRect',
                },
                dv_flyline_chart_config: {
                    centerPoint: [0.48, 0.35],
                    points: [
                        [0.52, 0.23],
                        [0.43, 0.29],
                        [0.59, 0.35],
                        [0.53, 0.47],
                        [0.45, 0.54],
                        [0.36, 0.38],
                        [0.62, 0.55],
                        [0.56, 0.56],
                        [0.37, 0.66],
                        [0.55, 0.81],
                        [0.55, 0.67],
                        [0.37, 0.29],
                        [0.2, 0.36],
                        [0.76, 0.41],
                        [0.59, 0.18],
                        [0.68, 0.17],
                        [0.59, 0.1],
                    ],
                    bgImgUrl: 'img/map.jpg',
                },
                percent_pond_config: {
                    value: 66,
                },
                dv_scroll_board_config: {
                    header: ['列1', '列2', '列3'],
                    data: [
                        ['<span style="color:#37a2da;">行1列1</span>', '行1列2', '行1列3'],
                        ['行2列1', '<span style="color:#32c5e9;">行2列2</span>', '行2列3'],
                        ['行3列1', '行3列2', '<span style="color:#67e0e3;">行3列3</span>'],
                        ['行4列1', '<span style="color:#9fe6b8;">行4列2</span>', '行4列3'],
                        ['<span style="color:#ffdb5c;">行5列1</span>', '行5列2', '行5列3'],
                        ['行6列1', '<span style="color:#ff9f7f;">行6列2</span>', '行6列3'],
                        ['行7列1', '行7列2', '<span style="color:#fb7293;">行7列3</span>'],
                        ['行8列1', '<span style="color:#e062ae;">行8列2</span>', '行8列3'],
                        ['<span style="color:#e690d1;">行9列1</span>', '行9列2', '行9列3'],
                        ['行10列1', '<span style="color:#e7bcf3;">行10列2</span>', '行10列3'],
                    ],
                    index: true,
                    columnWidth: [50],
                    align: ['center'],
                },
                option: {
                    title: {
                        text: '剩余油量表',
                        style: {
                            fill: '#fff',
                        },
                    },
                    series: [
                        {
                            type: 'gauge',
                            data: [{ name: 'itemA', value: 55 }],
                            center: ['50%', '55%'],
                            axisLabel: {
                                formatter: '{value}%',
                                style: {
                                    fill: '#fff',
                                },
                            },
                            axisTick: {
                                style: {
                                    stroke: '#fff',
                                },
                            },
                            animationCurve: 'easeInOutBack',
                        },
                    ],
                },
            };
        },
    });

    setTimeout(() => {
        app.$data.loading = false;
    }, 2000);
});
