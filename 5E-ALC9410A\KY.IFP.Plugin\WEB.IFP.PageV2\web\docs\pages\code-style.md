# 编码规范

ifp 项目基于 Vue2、Element2 开发，因此需要遵守 Vue2、Element2 规范，
目的是为了回避错误、小纠结和反模式

相关资源：

* [Vue 风格指南](https://v2.cn.vuejs.org/v2/style-guide/index.html/)

## 编码规范

* 在js代码或表达式中使用单引号 '
* 在html中使用双引号 "

如果要赋值某段 html 代码到js中，不要再关心引号问题

```html

<div></div>

```

## Vue2

针对项目及开发人员特点，这里重点强调

### 赋值

Data 中对象和数组赋值请使用 $set

示例

```js

{
    data(){
        return {
            info:{
                name:'张三',
                gender:'男'
            }
        }
    },
    created(){
        // 不推荐
        this.info = {
            name:'小丽',
            gender:'女'
        }

        // 推荐
        this.$set(this,'info',{
            name:'小丽',
            gender:'女'
        })
    }
}

```

### watch

慎用 watch

### props

明确定义数据类型

### computed

不要编写复杂的计算属性，如果确有必要，可拆成多个

## Element2

## IFP
