<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="图表,控件,input">
    <title>Input</title>
</head>
<body class="flex" controller="../demo.js" option="{platform:'element'}">

    <ifp-page id="app" class="padding">
        <ifp-input v-model="str1"></ifp-input>
        <ifp-input-number v-model="num1"></ifp-input-number>
        <ifp-input-number v-model="numnull"></ifp-input-number>
        <ifp-date-picker v-model="date1"></ifp-date-picker>
        <el-date-picker v-model="date1"></el-date-picker>
    </ifp-page>

    <script src="/iofp/starter.js"></script> 
</body>
</html>