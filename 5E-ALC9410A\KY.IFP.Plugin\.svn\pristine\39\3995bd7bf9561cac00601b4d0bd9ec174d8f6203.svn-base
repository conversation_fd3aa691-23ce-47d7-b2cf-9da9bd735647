<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="示例页面,入场验收">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>066 管道机采样</title>
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button type="primary" icon="el-icon-download">下发采样方案</ifp-button>
            <ifp-button type="danger" icon="glyphicons glyphicons-power">结束采样</ifp-button>
        </ifp-toolbar>

        <div class="padding flex flex-row" style="justify-content: space-between;">
            <div>
                <span class="margin-right">采样机状态</span>
                <ifp-legend code="cyj" value="2"></ifp-legend>
            </div>
            <div>
                <ifp-button circle type="success">启动</ifp-button>
                <ifp-button circle type="danger">停止</ifp-button>
            </div>
        </div>

        <div class="padding flex flex-item flex-row">
            <div class="flex flex-item">
                <ifp-panel-table title="采样任务列表" title-right="单位：吨、个" >
                    <ifp-table>
                        <ifp-table-column label="采样状态"></ifp-table-column>
                        <ifp-table-column label="采样编码"></ifp-table-column>
                    </ifp-table>
                </ifp-panel-table>
                <ifp-panel-table title="样桶信息" class="flex-item margin-top">
                    
                </ifp-panel-table>
            </div>
            <ifp-panel theme="style1" border flex class="margin-left" title="运行消息" style="max-width:50%">
                <div class="padding-left padding-right" v-for="item in 5">xxxxx xxxxxx xxx xxxx xxxxxx</div>
            </ifp-panel>
        </div>
    </ifp-page>
    <script src="/iofp/starter.js"></script>
</body>
</html>