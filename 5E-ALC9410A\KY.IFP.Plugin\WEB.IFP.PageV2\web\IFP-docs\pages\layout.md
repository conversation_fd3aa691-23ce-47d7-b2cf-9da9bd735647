# 总体布局

左侧为菜单栏，所有业务功能页面都可在其中找到，新增或删除页面可在`系统管理 - 菜单管理`中添加或删除。

上方为tabs导航栏，包含各个打开的页面链接，方便快速切换页面。

中间为主体内容区，显示不同页面的具体内容，根据页面的不同，可能会有查询栏、表格、图片、文本等多种元素。

<!-- ## 列表页

> 实例：<a href="/demo/template/list.html">列表页</a> -->

### index.html

```HTML
<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[标题]</title>
</head>
<body class="flex" controller option="{platform:'element'}">
  <ifp-page class="flex flex-item" id="app" v-loading="loading">
    <!--查询栏-->
    <ifp-toolbar>
      <!--列显设置按钮-->
      <ifp-button-lxsz table="table1"></ifp-button-lxsz>
      <!--查询栏按钮-->
      <ifp-button @click='search'>查询</ifp-button>
    </ifp-toolbar>
    <!--表单-->
    <el-form ref="form">
      <el-form-item label="表单" prop="product">
        <ifp-input v-model="productInfo.product"></ifp-input>
      </el-form-item>
    </el-form>
    <!--表格-->
    <ifp-panel-table title="表格">
      <ifp-table>
        <ifp-table-column label="column1"></ifp-table-column>
        <ifp-table-column label="column2"></ifp-table-column>
      </ifp-table>
    </ifp-panel-table>  
  </ifp-page>
  <script src="/iofp/starter.js"></script>
</body>
</html>
```

### index.js

```Javascript
define(['require', 'controllers/base', 'iofp/api'], function (require, base, API) {
    return {
        el: '#app',
        data() {
            return {
               msg:"Hello World!" 
            };
        },
        // 页面加载前
        created() {
            
        },
        // 页面加载完成
        mounted() {

        },
        // 方法定义    
        methods: {
          search() {

          },
        },
    };
});
```
