<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="示例页面,入场验收">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>076 全自动制样</title>
</head>
<body controller class="flex" option="{platform:'element'}">
    <ifp-page id="app" ifp-pages-qzdzy>
        <ifp-toolbar close>
            <ifp-button type="primary" icon="el-icon-download">分样机卸料</ifp-button>
            <ifp-button icon="glyphicons glyphicons-power">归批机卸料</ifp-button>
            <ifp-button icon="glyphicons glyphicons-power">人工卸料</ifp-button>
        </ifp-toolbar>

        <div class="margin flex flex-row">
            <div v-for="item in stateList" style="margin-right:4rem;">
                <label>{{item.label}}</label>
                <ifp-legend :code="item.stateType" :value="item.state"></ifp-legend>
                <ifp-button circle type="success">启动</ifp-button>
                <ifp-button circle type="danger">停止</ifp-button>
            </div>
        </div>

        <ifp-panel-table class="margin" title="制样任务列表" style="height:30%;">
            <ifp-table height="100%">
                <ifp-table-column label="采样状态"></ifp-table-column>
                <ifp-table-column label="采样编码"></ifp-table-column>
            </ifp-table>
        </ifp-panel-table>

        <div class="margin flex flex-item flex-row">
            <ifp-panel-table title="样桶信息" class="flex-item">
                <ifp-table height="100%">
                    <ifp-table-column label="采样状态"></ifp-table-column>
                    <ifp-table-column label="采样编码"></ifp-table-column>
                </ifp-table>
            </ifp-panel-table>
            <ifp-panel theme="style1" border flex class="margin-left" title="运行消息" style="max-width:50%">
                <div class="padding-left padding-right" v-for="item in 5">xxxxx xxxxxx xxx xxxx xxxxxx</div>
            </ifp-panel>
        </div>
    </ifp-page>
    <script src="/iofp/starter.js"></script>
</body>
</html>