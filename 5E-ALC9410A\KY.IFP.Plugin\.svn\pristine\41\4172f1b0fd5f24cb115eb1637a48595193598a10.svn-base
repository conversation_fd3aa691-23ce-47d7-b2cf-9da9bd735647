﻿using COM.IFP.Common;
using COM.IFP.Common.Reflex;
using COM.IFP.Log;
using Newtonsoft.Json;
using ORM.IFP.DbModel;
using ORM.IFP.www.DbModel.SM;
using Quartz;
using Quartz.Impl;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;

namespace DAL.IFP.Job
{
    //调度任务操作类.

    public class QuartzUtil
    {

        private static ISchedulerFactory _schedulerFactory;
        private static IScheduler _scheduler;
        //不需要通过标签的方式添加job了
        public static void initTagJob()
        {
            Lazy<DAL.IFP.Job.Job> _service = Entity.Create<DAL.IFP.Job.Job>();

            List<Assembly> _defaultAssemblies = AssemblyLoadContext.Default.Assemblies
                    .Where(assembly => !assembly.IsDynamic &&
                    (assembly.FullName.Contains("COM.") ||
                    assembly.FullName.Contains("DAL.") ||
                    assembly.FullName.Contains("API.")))
                    .ToList();
            string rootPath = System.IO.Directory.GetCurrentDirectory() + "\\";
            foreach (var ass in _defaultAssemblies)
            {
                Type[] types = ass.GetTypes();

                foreach (var type in types)
                {
                    MethodInfo[] methods = type.GetMethods();

                    foreach (MethodInfo method in methods)
                    {
                        if (method.GetCustomAttribute(typeof(JobAttribute)) != null)
                        {
                            string dllpath = ass.Location;
                            dllpath = dllpath.Replace(rootPath, "");
                            dllpath = dllpath.Substring(0, dllpath.LastIndexOf("."));
                            string classpath = type.FullName;
                            string methdoName = method.Name;

                            IFP_SM_JOBINFO param = new IFP_SM_JOBINFO();
                            param.DLLPath = dllpath;
                            param.ClassPath = classpath;
                            param.MethodName = methdoName;
                            List<IFP_SM_JOBINFO> joblist = _service.Value.QueryJobList(param);
                            if (joblist != null && joblist.Count > 0)
                            {
                                continue;
                            }
                            JobAttribute obsAttr = (JobAttribute)Attribute.GetCustomAttribute(method, typeof(JobAttribute));
                            IFP_SM_JOBINFO model = new IFP_SM_JOBINFO();
                            model.Gid = Guid.NewGuid().ToString("N");
                            model.JobName = obsAttr.JobName;
                            model.JobDesc = obsAttr.JobDesc;
                            model.DLLPath = dllpath;
                            model.ClassPath = classpath;
                            model.MethodName = methdoName;
                            if (string.IsNullOrEmpty(obsAttr.Corn))
                            {
                                model.Interval = obsAttr.Interval.Value;
                                model.Zxfs = 1;
                            }
                            else
                            {
                                model.Corn = obsAttr.Corn;
                                model.Zxfs = 0;
                            }
                            model.Status = 0;

                            //获取程序集所在的目录
                            ReflexCustom Refcstm = new ReflexCustom();
                            Refcstm.DLLPath = model.DLLPath.Value;
                            Refcstm.MethodPath = model.ClassPath.Value;
                            Refcstm.MethodName = model.MethodName.Value;
                            //调方法名。
                            Type RefT = ReflexCustom.GetReflexType(Refcstm);
                            //创建实例
                            Object obj = Activator.CreateInstance(RefT);
                            //进参与反参必须是JToken
                            MethodInfo method2 = RefT.GetMethod(Refcstm.MethodName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
                            ParameterInfo[] paramsInfo = method2.GetParameters();//得到指定方法的参数列表   
                            List<IFP_SM_JOBPARAM> target = new List<IFP_SM_JOBPARAM>();
                            foreach (var i in paramsInfo)
                            {
                                IFP_SM_JOBPARAM tmp = new IFP_SM_JOBPARAM();
                                tmp.Gid = Guid.NewGuid().ToString("N");
                                tmp.PGid = model.Gid;
                                tmp.ParamName = i.Name;
                                tmp.ParamType = i.ParameterType.ToString();
                                target.Add(tmp);
                            }

                            model.paramlist = target;


                            _service.Value.SaveOrUpdateJob(model);

                        }
                    }
                }
            }
        }


        public static void StartAllJob()
        {
            LoggerHelper.Info($"初始化启动调度任务。");
            //IJobService _service = AutofacUtil.Qualifier<IJobService>("JobService");
            Lazy<DAL.IFP.Job.Job> _service = Entity.Create<DAL.IFP.Job.Job>();
            List<IFP_SM_JOBINFO> list = _service.Value.JobAllList();
            if (list != null && list.Count > 0)
            {
                foreach (IFP_SM_JOBINFO job in list)
                {
                    StartJob(job);
                }
            }
        }


        /// <summary>
        /// 添加任务
        /// </summary>
        /// <param name="type">类</param>
        /// <param name="jobKey">键</param>
        /// <param name="trigger">触发器</param>
        public static async void StartJob(IFP_SM_JOBINFO jobinfo)
        {
            Init();
            _scheduler = await _schedulerFactory.GetScheduler();
            JobKey key = new JobKey(jobinfo.JobName.Value, jobinfo.DLLPath.Value);
            if (await _scheduler.CheckExists(key))
            {

                LoggerHelper.Info($"恢复任务{key.Group}{key.Name}");
                await _scheduler.ResumeJob(key);
            }
            else
            {
                await _scheduler.Start();
                ITrigger trigger;
                if (jobinfo.Zxfs == 0)
                {
                    trigger = TriggerBuilder.Create()
                                .WithDescription(jobinfo.JobDesc.Value)
                                .WithIdentity(jobinfo.JobName.Value, jobinfo.DLLPath.Value)
                                .WithSchedule(CronScheduleBuilder.CronSchedule(jobinfo.Corn.Value).WithMisfireHandlingInstructionIgnoreMisfires())
                                .Build();
                }
                else
                {
                    trigger = TriggerBuilder.Create()
                               .WithDescription(jobinfo.JobDesc.Value)
                                .WithIdentity(jobinfo.JobName.Value, jobinfo.DLLPath.Value)
                                .WithSimpleSchedule(x => x.WithIntervalInSeconds(Convert.ToInt32(jobinfo.Interval)).RepeatForever().WithMisfireHandlingInstructionIgnoreMisfires())
                                .Build();
                }


                var job = JobBuilder.Create(typeof(IofpJob))
                    .WithIdentity(key)
                    .Build();
                await _scheduler.ScheduleJob(job, trigger);
                LoggerHelper.Info($"启动任务{key.Group}{key.Name}");

            }

        }
        /// <summary>
        /// 停止任务
        /// </summary>
        /// <param name="jobKey">键</param>
        public static async void StopJob(IFP_SM_JOBINFO jobinfo)
        {
            Init();
            JobKey key = new JobKey(jobinfo.JobName.Value, jobinfo.DLLPath.Value);
            _scheduler = await _schedulerFactory.GetScheduler();
            LoggerHelper.Debug($"暂停任务{key.Group}{jobinfo.JobName}");
            await _scheduler.PauseJob(key);
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="jobKey">键</param>
        public static async void DelJob(IFP_SM_JOBINFO jobinfo)
        {
            Init();
            JobKey key = new JobKey(jobinfo.JobName.Value, jobinfo.DLLPath.Value);
            _scheduler = await _schedulerFactory.GetScheduler();
            LoggerHelper.Debug($"删除任务{key.Group}{jobinfo.JobName}");
            await _scheduler.DeleteJob(key);
        }

        /// <summary>
        /// 立即执行任务
        /// </summary>
        /// <param name="jobKey">键</param>
        public static void RunJob(IFP_SM_JOBINFO jobinfo)
        {
            List<IFP_SM_JOBPARAM> cslist = jobinfo.paramlist;

            //获取程序集所在的目录
            ReflexCustom Refcstm = new ReflexCustom();
            Refcstm.DLLPath = jobinfo.DLLPath.Value;
            Refcstm.MethodPath = jobinfo.ClassPath.Value;
            Refcstm.MethodName = jobinfo.MethodName.Value;
            //调方法名。
            Type RefT = ReflexCustom.GetReflexType(Refcstm);
            //创建实例
            Object obj = Activator.CreateInstance(RefT);
            //进参与反参必须是JToken
            MethodInfo method = RefT.GetMethod(Refcstm.MethodName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);

            ParameterInfo[] paramsInfo = method.GetParameters();//得到指定方法的参数列表   
            if (cslist == null)
            {
                Object tmp1 = method.Invoke(obj, null);
                return;
            }
            object[] obj1 = new object[cslist.Count];

            for (int i = 0; i < cslist.Count; i++)
            {

                Type tType = paramsInfo[i].ParameterType;

                //如果它是值类型,或者String   

                if (tType.Equals(typeof(string)) || (!tType.IsInterface && !tType.IsClass))
                {
                    //改变参数类型   
                    obj1[i] = (cslist[i].ParamValue.Value == null || cslist[i].ParamValue.Value == "") ? null : Convert.ChangeType(cslist[i].ParamValue, tType);
                }

                else if (tType.IsClass)//如果是类,将它的json字符串转换成对象   
                {
                    //obj1[i] = (cslist[i].ParamValue == null || cslist[i].ParamValue == "") ? null : Newtonsoft.Json.JsonConvert.DeserializeObject(cslist[i].ParamValue, tType);
                    obj1[i] = (cslist[i].ParamValue.Value == null || cslist[i].ParamValue.Value == "") ? null : JsonConvert.DeserializeObject(cslist[i].ParamValue.Value, tType);
                }
            }

            if (paramsInfo.Length > 0 && obj1.Length == 0)
            {
                throw new Exception($"调用方法：{jobinfo.ClassPath}.{jobinfo.MethodName}未能查询到所需的参数，请设置。");
            }

            Object tmp = method.Invoke(obj, obj1);       // 调用方法，有参数，有返回值
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="jobKey">键</param>
        public static async void UpdateJob(IFP_SM_JOBINFO jobinfo)
        {
            Init();
            _scheduler = await _schedulerFactory.GetScheduler();
            TriggerKey triggerKey = new TriggerKey(jobinfo.JobName.Value, jobinfo.DLLPath.Value);
            if (jobinfo.Zxfs == 0)
            {
                if (!(await _scheduler.GetTrigger(triggerKey) is ICronTrigger trigger))
                {
                    return;
                }
                string oldCron = trigger.CronExpressionString;
                if (!oldCron.Equals(jobinfo.Corn.Value, StringComparison.OrdinalIgnoreCase))
                {
                    var newTrigger = TriggerBuilder.Create()
                        .WithIdentity(jobinfo.JobName.Value, jobinfo.DLLPath.Value)
                        .WithSchedule(CronScheduleBuilder.CronSchedule(jobinfo.Corn.Value).WithMisfireHandlingInstructionIgnoreMisfires())
                        .Build() as ICronTrigger;
                    await _scheduler.RescheduleJob(triggerKey, newTrigger);
                }
            }
            else
            {
                if (!(await _scheduler.GetTrigger(triggerKey) is ISimpleTrigger trigger))
                {
                    return;
                }
                var newTrigger = TriggerBuilder.Create()
                         .WithIdentity(jobinfo.JobName.Value, jobinfo.DLLPath.Value)
                         .WithSimpleSchedule(x => x.WithIntervalInSeconds(Convert.ToInt32(jobinfo.Interval)).RepeatForever().WithMisfireHandlingInstructionIgnoreMisfires())
                         .Build() as ISimpleTrigger;
                await _scheduler.RescheduleJob(triggerKey, newTrigger);
            }

        }



        /// <summary>
        /// 初始化
        /// </summary>
        private static void Init()
        {
            if (_schedulerFactory == null)
            {
                _schedulerFactory = new StdSchedulerFactory();
            }
        }

    }
}
