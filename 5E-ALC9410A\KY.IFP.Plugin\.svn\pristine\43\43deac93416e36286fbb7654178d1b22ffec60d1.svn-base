﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班次详情</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page id="app">
        <ifp-toolbar close>
            <ifp-button code="B1" @click="onSave" :disabled="disable.viewDisabled">保存</ifp-button>
        </ifp-toolbar>

        <el-form style="margin:1rem;" ref="form" :model="formdata" :rules="rules" label-width="120px" :disabled="disable.viewDisabled">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="班次名称：" prop="Bname">
                        <ifp-input v-model="formdata.Bname"></ifp-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="采样周期(s)：" prop="CYZQ" >
                        <ifp-input v-model="formdata.CYZQ" oninput="value=value.replace(/[^0-9.]/g,'')"></ifp-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="运行班次：" prop="Sfyxbc1099">
                        <el-radio-group v-model="formdata.Sfyxbc1099" @change="onSelect">
                            <el-radio :label=10990001>是</el-radio>
                            <el-radio :label=10990002>否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="开始时间：" prop="Begindate">
                        <el-select v-model="formdata.Begindate" placeholder="请选择" style="width: 120px;padding-right:2px;">
                            <el-option label="前一天" :value=-1></el-option>
                            <el-option label="当天" :value=0></el-option>
                            <el-option label="后一天" :value=1></el-option>
                        </el-select>

                        <el-time-picker v-model="formdata.Kssj"
                                        :picker-options="{
                                          selectableRange: '00:00:00 - 23:59:59'
                                        }"
                                        placeholder="选择时间" style="width:120px;">
                        </el-time-picker>

                    </el-form-item>
                    

                </el-col>

                <el-col :span="12">
                    <el-form-item label="结束时间：" prop="Enddate">
                        <el-select v-model="formdata.Enddate" placeholder="请选择" style="width: 120px; padding-right: 2px; ">
                            <el-option label="前一天" :value=-1></el-option>
                            <el-option label="当天" :value=0></el-option>
                            <el-option label="后一天" :value=1></el-option>
                        </el-select>

                        <el-time-picker v-model="formdata.Jssj"
                                         :picker-options="{
                                          selectableRange: '00:00:00 - 23:59:59'
                                        }"
                                        placeholder="选择时间" style="width: 120px;">
                        </el-time-picker>

                    </el-form-item>
                    
                </el-col>

            </el-row>
            <el-row>
                <el-col :span="9">
                    <el-form-item label="启停状态：" prop="Zfbz">
                        <el-radio-group v-model="formdata.Zfbz">
                            <el-radio :label="0">启用</el-radio>
                            <el-radio :label="1">停用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="期别"
                                  prop="QB">
                        <ifp-select-ywlx v-model="formdata.QB" :ywlx="4007" ></ifp-select-ywlx>
                    </el-form-item>
                </el-col>
                <el-col :span="7">
                    <el-form-item label="业务编码：" prop="Ywbm">
                        <ifp-input v-model="formdata.Ywbm"></ifp-input>
                    </el-form-item>
                </el-col>
            </el-row>
            
            <el-row>
                <el-col :span="24">
                    <el-form-item label="备注：" prop="Beizhu">
                        <ifp-input v-model="formdata.Beizhu" type="textarea" :rows="4" ></ifp-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>