(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "mathjs"], factory);
    }
})(function (require, exports) {
    "use strict";
    exports.__esModule = true;
    exports.thousands = exports.makeNumber = exports.percent = exports.parseFloat = exports.format = exports.render = void 0;
    var mathjs_1 = require("mathjs");
    var config = {
        // Default type of number
        // Available options: 'number' (default), 'BigNumber', or 'Fraction'
        number: 'BigNumber',
        // Number of significant digits for BigNumbers
        precision: 64
    };
    var math = mathjs_1.create(mathjs_1.all, config);
    /**
     * 使用对象格式化
     * @param template
     * @param data
     * @returns
     */
    function render(template, data) {
        //少于两个参数否则直接返回原始字符或空
        if (!data) {
            return template || "";
        }
        return template.replace(/\{\s*([0-9a-zA-Z_\s]+)\s*\}/g, function (substring) {
            var args = [];
            for (var _i = 1; _i < arguments.length; _i++) {
                args[_i - 1] = arguments[_i];
            }
            return data[args[0]] && data[args[0]].toString() || "";
        });
    }
    exports.render = render;
    /**
     * 使用占位符格式化
     * @param template
     * @param args
     * @returns
     */
    function format(template) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        //少于两个参数否则直接返回原始字符或空
        if (!args.length) {
            return template || "";
        }
        //{0} 第二个参数 不是对象 或 参数数量多于两个
        return template.replace(/\{\s*([0-9]+)\s*\}/g, function (substring) {
            var args = [];
            for (var _i = 1; _i < arguments.length; _i++) {
                args[_i - 1] = arguments[_i];
            }
            return args[Number(args[0])] || "";
        });
    }
    exports.format = format;
    /**
     * 字符串转数字并，保留小数位数
     * @param value 值
     * @param d 小数位，默认3
     * @returns
     */
    function parseFloat(value, d) {
        if (d === void 0) { d = 3; }
        if (value === "" || value === null || value === undefined) {
            value = "0";
        }
        if (typeof value == "number") {
            value = value.toString();
        }
        return math.evaluate(value).toFixed(d);
    }
    exports.parseFloat = parseFloat;
    /**
     * 百分比
     * @param v
     * @returns
     * @example
     * percent(0.1) // => 10%
     */
    function percent(v) {
        return isNaN(Number.parseFloat(v)) ? "" : (math.evaluate(v + "*" + "100").toFixed());
    }
    exports.percent = percent;
    /**
     * 字符串转数值
     * @param n
     * @returns
     * @example
     * makeNumber(1) // => 1
     * makeNumber("1") // => 1
     */
    function makeNumber(n) {
        return Number(n);
    }
    exports.makeNumber = makeNumber;
    /**
     * 千分位
     * @param n
     * @returns
     * @example
     * thousands(10000) // => "10,000"
     * thousands(-10000.000003) // => "-10,000.000003"
     */
    function thousands(n) {
        var v = Number(n);
        if (isNaN(v)) {
            return "";
        }
        var isn = v >= 0;
        if (!isn) {
            v = v * -1;
        }
        var str = v.toString();
        var re = /\d{1,3}(?=(\d{3})+$)/g;
        var n1 = str.replace(/(\d+)((\.\d+)?)$/, function (s, s1, s2) {
            return s1.replace(re, "$&,") + s2;
        });
        if (!isn) {
            n1 = "-" + n1;
        }
        return n1;
    }
    exports.thousands = thousands;
});
//# sourceMappingURL=data:application/json;base64,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