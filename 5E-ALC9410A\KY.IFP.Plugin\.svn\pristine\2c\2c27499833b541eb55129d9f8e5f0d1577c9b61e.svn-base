﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础资料修改</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div id="app" class="flex padding">
        <el-form ref="form" :model="edit" label-width="80px">
            <el-row :gutter="28">
                <el-col :span="11">
                    <el-form-item label="对象ID" prop="Gid">
                        <ifp-input v-model="edit.Gid" :disabled="true"></ifp-input>
                    </el-form-item>
                </el-col>
                <el-col :span="11">
                    <el-form-item label="对象类型" prop="YWLX">
                        <template slot-scope="scope">
                            <ifp-select v-model="edit.YWLX" :items="listObjectType"
                                        value-key="id"
                                        label-key="text">
                            </ifp-select>
                        </template>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="28">
                <el-col :span="11">
                    <el-form-item label="名称" prop="Bname">
                        <ifp-input v-model="edit.Bname"></ifp-input>
                    </el-form-item>
                </el-col>
                <el-col :span="11">
                    <el-form-item label="简称" prop="Sname">
                        <ifp-input v-model="edit.Sname"></ifp-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="28">
                <el-col :span="11">
                    <el-form-item label="作废标志">
                        <template slot-scope="scope">
                            <ifp-select v-model="edit.ZFBZ" :items="statueList"
                                        value-key="id"
                                        label-key="text"
                                      >
                            </ifp-select>
                        </template>
                    </el-form-item>

                   
                </el-col>
                <el-col :span="11">
                    <el-form-item label="业务编码" prop="YWBM">
                        <ifp-input v-model="edit.YWBM"></ifp-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item>
                <el-button v-if="!$btnRight.B1" type="primary" @click="onSubmit">保存</el-button>
                <el-button @click="onCancel">取消</el-button>
            </el-form-item>
        </el-form>
    </div>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>