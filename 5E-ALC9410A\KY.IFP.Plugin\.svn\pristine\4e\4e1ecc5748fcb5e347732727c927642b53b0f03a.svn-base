define([
    "iofp/common",
    "iofp/components/iofpsocket",
    "iofp/services/service.plc",
    "jquery",
    "jquery.ky.dialog"
], function (common, iofpsocket, plcService, $) {
    return {
        el: "#app",
        data: function () {
            return {
                //信号灯样式
                //信号灯样式，"0"灰灯"1"绿灯"2"橘灯"3"红灯
                states: {
                    "0": "",
                    "1": "runing",
                    "2": "warning",
                    "3": "stoped",
                    //采集系统罢工
                    "4":"collectSystemError"
                },
                showstate: {
                    //Auth: 1,
                    PLC: "0",
                    Service: "0",
                    Auth: "0",
                    Delay: -1,
                    //AutoReconnectMode: 3
                },
                //AutoReconnectModeChange: false,
            }
        },
        created: function () {
            iofpsocket.onCmdMsg("Open", data => {
                var result = JSON.parse(data.C);
                //if (this.showstate.AutoReconnectMode != result.AutoReconnectMode && this.AutoReconnectModeChange == true) {
                //    this.action1('SET_RECONNECT_MODE', { AutoReconnectMode: this.showstate.AutoReconnectMode });
                //    this.AutoReconnectModeChange = false;
                //}
                //else {
                //    this.showstate.AutoReconnectMode = result.AutoReconnectMode;
                //}
                if (this.showstate.Auth !== result.Auth) {
                    this.showstate.Auth = result.Auth;
                }
                if (this.showstate.PLC !== result.PLC) {
                    this.showstate.PLC = result.PLC;
                }
                if (this.showstate.Service != result.Service) {
                    this.showstate.Service = result.Service;
                }
                if (parseInt(this.showstate.Delay) != parseInt(result.Delay)) {
                    this.showstate.Delay = parseInt(result.Delay);
                }
            })

            iofpsocket.onCmdMsg("RControl", data => {
                var result = JSON.parse(data.C);
                if (result.Result != 1) {
                    $.bootoast.danger(result.Msg);
                }
                console.log(JSON.stringify(data), "接收");
            })

            //iofpsocket.sendCommand("SConfig", { PageID: "tsjm", Interval: "500" }, "tsjm");

            //iofpsocket.onCmdMsg("RConfig", data => {
            //    console.log(JSON.stringify(data), "接收");
            //})
        },
        methods: {
            action: function (mandatory) {
                var content = common.createCommandArgs({ Mandatory: mandatory });
                iofpsocket.sendCommand("SAuth", content);
                //$.bootoast.success("发送命令内容：\n" + JSON.stringify(content, null, 4))
            },
            action1: function (CMD, Parameter) {
                var content = common.createCommandArgs({ CMD: CMD, Parameter: Parameter });
                iofpsocket.sendCommand("SControl", content);
            },
            resetService: function () {
                plcService.ReStart({}).then(data => {

                })
            },
            //daiabin废弃
            //ChangeAutoReconnectMode: function (el) {
            //    if (el.target.tagName === 'INPUT') return;
            //    this.AutoReconnectModeChange = true;
            //}

        }
    }
})