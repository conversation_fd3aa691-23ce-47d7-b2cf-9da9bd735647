{"version": 3, "file": "dialog-paremeter.js", "sourceRoot": "", "sources": ["../../../packages/kyadmin/src/libs/dialog-paremeter.ts"], "names": [], "mappings": ";;;;;;;;;;;;IAAA,oCAAwC;IACxC,sCAA4C;IAC5C,+BAAiC;IACjC,wCAAuC;IAEvC,cAAc;IACd,SAAgB,mBAAmB;QAC/B,IAAI,OAAO,GAA6B,eAAS,CAAC,QAAQ,IAAI,eAAS,CAAC,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC;QAC/F,IAAI,EAAE,GAAG,kBAAW,CAAC,IAAI,CAAC,CAAC;QAC3B,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,SAAS,IAAI,IAAI,CAAA;IAC7D,CAAC;IAJD,kDAIC;IACO,WAAW;IACnB,SAAgB,gBAAgB;QAC5B,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,IAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAC;YACtB,IAAI,KAAK,GAAkB,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAS,IAAI;gBAC5E,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAiB,CAAA;YAC1C,CAAC,CAAC,CAAA;YACF,GAAG,GAAG,iBAAS,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;SAC/B;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IATD,4CASC;IAED,oBAAoB;IACpB,SAAgB,gBAAgB;QAC5B,OAAO,cAAM,CAAC,IAAI,EAAC,EAAE,EAAC,gBAAgB,EAAE,EAAC,mBAAmB,EAAE,CAAC,CAAA;IACnE,CAAC;IAFD,4CAEC", "sourcesContent": ["import { topWindow } from \"../utils/dom\"\r\nimport { queryString } from \"../utils/util\";\r\nimport { fromPairs } from \"ramda\"\r\nimport { extend } from \"../utils/zutil\"\r\n\r\n//获取 dialog 传参\r\nexport function queryDialogParmeter (){\r\n    var dialogs:Global__dialogDialogsItem = topWindow.__dialog && topWindow.__dialog.dialogs || {};\r\n    var id = queryString(\"_w\");\r\n    return id && dialogs[id] && dialogs[id].parameter || null\r\n}\r\n        //获取 url 传参\r\nexport function queryUrlParmeter (){\r\n    var rev = null;\r\n    if(window.location.search){\r\n        let pairs:[string,any][] = location.search.slice(1).split(\"&\").map(function(item){\r\n            return item.split(\"=\") as [string,any]\r\n        })\r\n        rev = fromPairs(pairs || [])\r\n    }\r\n    return rev;\r\n}\r\n\r\n//获取 dialog 和 url 传值\r\nexport function queryAllParmeter (){\r\n    return extend(true,{},queryUrlParmeter(),queryDialogParmeter())\r\n}"]}