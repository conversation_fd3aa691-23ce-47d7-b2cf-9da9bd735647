﻿using COM.IFP.Common;
using ORM.IFP.DbModel;
using ORM.IFP.www.DbModel.SM;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace API.IFP.BaseInfo
{
    /// <summary>
    /// 实现:系统参数
    /// </summary>
    public class XtcsFenZu
    {
        private Lazy<DAL.IFP.Baseinfo.XtcsFenZu> _service = Entity.Create<DAL.IFP.Baseinfo.XtcsFenZu>();

        /// <summary>
        /// 查询有哪些分组
        /// </summary>
        /// <returns></returns>
        public List<IFP_SM_CSFENZU> FenZuList(JsonElement json)
        {
            return _service.Value.FenZuList();
        }
        /// <summary>
        /// 分组信息查询
        /// </summary>
        /// <returns></returns>
        public List<IFP_SM_CSMAPFENZU> FenZuListAll(JsonElement json)
        {
            IFP_SM_CSFENZU obj = json.GetValue<IFP_SM_CSFENZU>();
            return _service.Value.FenZuListAll(obj);
        }

        /// <summary>
        /// 保存分组
        /// </summary>
        /// <param name="obj"> </param>
        /// <returns></returns>
        public void XtcsGroupSave(JsonElement json)
        {
            List<IFP_SM_CSFENZU> obj = json.GetValue<List<IFP_SM_CSFENZU>>();
            _service.Value.XtcsGroupSave(obj);
        }

        /// <summary>
        /// 保存映射关系
        /// </summary>
        /// <param name="obj"> </param>
        /// <returns></returns>
        public PFActionResult XtcsMapFenZuSave(JsonElement json)
        {
            IFP_SM_CSFENZU obj = json.GetValue<IFP_SM_CSFENZU>();
            return _service.Value.XtcsMapFenZuSave(obj);
        }
    }
}
