(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else if(typeof exports === 'object')
		exports["JSSC"] = factory();
	else
		root["JSSC"] = factory();
})(window, function() {
return /******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/jssc.ts");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./src/jssc.ts":
/*!*********************!*\
  !*** ./src/jssc.ts ***!
  \*********************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";

var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceCreater = exports.fromJQuery = exports.version = exports.contentType = exports.method = exports.dataType = void 0;
var dataType;
(function (dataType) {
    dataType["xml"] = "xml";
    dataType["html"] = "html";
    dataType["script"] = "script";
    dataType["json"] = "json";
    dataType["jsonp"] = "jsonp";
    dataType["text"] = "text";
    dataType["local"] = "local";
})(dataType = exports.dataType || (exports.dataType = {}));
var method;
(function (method) {
    method["get"] = "GET";
    method["post"] = "POST";
})(method = exports.method || (exports.method = {}));
var contentType;
(function (contentType) {
    contentType["formUrlencoded"] = "application/x-www-form-urlencoded";
    contentType["formData"] = "multipart/form-data";
    contentType["json"] = "application/json";
    contentType["text"] = "text/xml";
})(contentType = exports.contentType || (exports.contentType = {}));
function isFunction(s) {
    return s && s.constructor && s.constructor === Function;
}
function isArray(s) {
    return s && s.constructor && s.constructor === Array;
}
function isString(s) {
    return s && typeof s == "string";
}
function isServiceOption(s) {
    return typeof s === "object";
}
var getProps = function (props, args, context) {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    if (props != null) {
                        //处理参数 props 属性
                        if (isFunction(props)) {
                            //若props为函数,将实参列表传入props,在props函数中this指向services,this.context指向当前配置，
                            return Promise.resolve(props.apply(context, args))
                                .then(resolve)["catch"](reject);
                        }
                        else if (isArray(props)) {
                            //若props为string[]，将实参列表传入props
                            var rev = {};
                            props.forEach(function (iprop, index) {
                                rev[iprop] = args[index] || null;
                            });
                            return resolve(rev);
                        }
                        else {
                            resolve(props);
                        }
                    }
                    else {
                        resolve(args[0]);
                    }
                })];
        });
    });
};
exports.version = "1.0.0";
var ajaxProps = ["contentType", "dataType", "type"];
function filterAjaxOption(obj) {
    var rev = {};
    for (var a in obj) {
        if (ajaxProps.indexOf(a) > -1) {
            rev[a] = obj[a];
        }
    }
    return rev;
}
function fromJQuery(jquery, option) {
    return new ServiceCreater({
        ajax: function (url, option) {
            return new Promise(function (resolve) {
                jquery.ajax(url, Object.assign(option, {
                    success: function () {
                        resolve();
                    }
                }));
            });
        },
        ajaxOption: option
    });
}
exports.fromJQuery = fromJQuery;
var ServiceCreater = /** @class */ (function () {
    function ServiceCreater(option, ajaxOpt) {
        if (ajaxOpt === void 0) { ajaxOpt = {}; }
        this.ajaxOption = {
            dataType: "json",
            contentType: "application/x-www-form-urlencoded"
        };
        this.ajax = option.ajax;
        Object.assign(this.ajaxOption, option.ajaxOption, ajaxOpt);
    }
    ServiceCreater.prototype.createServices = function (setting) {
        var rev = {};
        for (var a in setting) {
            rev[a] = this.createService(setting[a]);
        }
        return rev;
    };
    ServiceCreater.prototype.createServiceFromUrl = function (url) {
        var _this = this;
        return function () { return _this.ajax(url, _this.ajaxOption); };
    };
    ServiceCreater.prototype.createServiceFromMethod = function (then) {
        return function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            return Promise.resolve(then.apply(void 0, args));
        };
    };
    ServiceCreater.prototype.createService = function (option) {
        var opt;
        if (option == null) {
            throw "createService 参数错误";
        }
        else if (isFunction(option)) {
            return this.createServiceFromMethod(option);
        }
        else if (isString(option)) {
            return this.createServiceFromUrl(option);
        }
        else if (isServiceOption(option)) {
            opt = option;
        }
        else {
            throw "option 参数只能未以下类型之一：String|Function|Objcet，当前为：" + (typeof option);
        }
        var salf = this;
        return function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            var _thisService = this;
            var rev = Promise.resolve();
            if (opt.props) {
                rev = getProps(opt.props, args, this);
            }
            if (opt.usemock) {
                if (isFunction(opt.mock)) {
                    rev = rev.then(function (props) { return Promise.resolve(opt.mock.call(_thisService, props)); });
                }
                else {
                    rev = rev.then(function (props) { return opt.mock; });
                }
            }
            else if (isString(opt.url)) {
                var url_1 = opt.url;
                var pars_1 = Object.assign({}, salf.ajaxOption, filterAjaxOption(opt), opt.ajax);
                rev = rev.then(function (data) { return salf.ajax(url_1, typeof data === "undefined" ? pars_1 : (pars_1.data = data, pars_1)); });
            }
            if (isFunction(opt.then)) {
                rev = rev.then(opt.then);
            }
            if (isFunction(opt.catch)) {
                rev = rev.catch(opt.catch);
            }
            else {
                rev = rev.catch(function (error) {
                    throw error;
                });
            }
            return rev;
        };
    };
    ServiceCreater.create = function (option) {
        return new ServiceCreater(option);
    };
    ServiceCreater.prototype.setAjaxOption = function (ajaxOption) {
        Object.assign(this.ajaxOption, ajaxOption);
    };
    return ServiceCreater;
}());
exports.ServiceCreater = ServiceCreater;


/***/ })

/******/ });
});
//# sourceMappingURL=jssc.js.map