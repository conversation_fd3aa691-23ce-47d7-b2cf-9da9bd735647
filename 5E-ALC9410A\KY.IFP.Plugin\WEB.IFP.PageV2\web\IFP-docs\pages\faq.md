# 常见问题汇总

## vue

### 属性配置无效

请检查

* 属性名含有大写字母？   
属性名含有大写字母，html 中属性需要使用 - 分隔

### 如何快速调试 Vue 代码

使用chrome 插件 Vue-DevTool 来调试代码

插件地址：[项目根目录]/.webdev/f0335734c07647aeb369ccd42cf3f319.crx

安装步骤：

1. chrome 右上角 设置按钮 -> 更多工具 -> 扩展程序
2. 在右上角开启开发者模式
3. 将插件拖进这个页面，安装后即可

安装完毕后，打开含有VUE的页面，F12 打开调试工具，上面会出现 Vue 页签

## element-ui

### dialog
#### [dialog 嵌套弹窗异常](/pages/controls/dialog?id=_1-%e5%b5%8c%e5%a5%97%e5%bc%b9%e7%aa%97%e5%bc%82%e5%b8%b8)

## kyadmin

## ifp

## 开发环境相关

### 修改js、html后需要重新编译？

1. 运行 `[项目根目录]/run-web.bat`
1. 在浏览器中将端口从 5009 改为 5010 访问即可