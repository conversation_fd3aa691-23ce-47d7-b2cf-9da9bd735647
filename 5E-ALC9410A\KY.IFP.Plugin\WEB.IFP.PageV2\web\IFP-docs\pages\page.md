# IFP 页面开发

## 一个完整的页面

**html部分**

```HTML
<ifp-page id="app">
    <ifp-toolbar close lxsz="table1">
        <ifp-button code="B1" @click="onCreate">新增</ifp-button>
        <ifp-button code="B2" @click="onView">查看</ifp-button>
        <ifp-button code="B3" @click="onUpdate">修改</ifp-button>
        <ifp-button code="B4" @click="onDetail">停用</ifp-button>
    </ifp-toolbar>
    <ifp-searchbar @search="updateList" :model="query">
        <ifp-form-item label="供应商名称" prop="Bname" v-model="query.Bname"></ifp-form-item>
        <ifp-form-item label="供应商类型" prop="GHDWLX1047" type="ywlx" ywlx="1047" v-model="query.GHDWLX1047"></ifp-form-item>
        <ifp-form-item label="启停状态" prop="Zfbz" placeholder="请选择">
            <el-select clearable v-model="query.Zfbz">
                <el-option v-for="item in StatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </ifp-form-item>
    </ifp-searchbar>
    <ifp-panel-table class="flex-item padding">
        <ifp-table :data="viewer.source"
            style="width:100%;"
            height="100%"
            ref="table1"
            @sort-change="sortChange"
            @current-change="onChange">
            <el-table-column fixed type="index"></el-table-column>
            <el-table-column prop="Gid" label="供应商编码" sortable="custom"></el-table-column>
            <el-table-column prop="Bname" label="供应商名称"></el-table-column>
            <el-table-column prop="Sname" label="供应商简称"></el-table-column>
            <el-table-column label="供应商类型">
                <template slot-scope="scope">
                    <ifp-select-ywlx :canedit="false" v-model="scope.row.GHDWLX1047" :ywlx="1047"></ifp-select-ywlx>
                </template>
            </el-table-column>
            <el-table-column label="状态">
                <template slot-scope="scope">
                    {{scope.row.Zfbz === 0?"启用":"停用"}}
                </template>
            </el-table-column>
            <el-table-column prop="Addtime"
                                sortable="custom"
                                :formatter="$TableFormatter.datetime"
                                label="创建时间">
            </el-table-column>
            <el-table-column prop="Lasttime" label="最后修改时间" sortable="custom" :formatter="$TableFormatter.datetime">
            </el-table-column>
            <el-table-column prop="Creator" label="创建人"></el-table-column>
        </ifp-table>
        <template v-slot:pagination>
            <ifp-pagination @size-change="sizeChange"
                            @current-change="pageChange"
                            :current-page="viewer.paging.page"
                            :page-size="viewer.paging.Size"
                            :total="viewer.paging.Sums">
            </ifp-pagination>
        </template>
    </ifp-panel-table>
    <el-dialog class="subpage" :title="dialogTitle"
                :visible.sync="editor.dialog"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                width="950px">
        <ifp-supplier-detail @cancel="editor.dialog=false;updateList()"
                                @sucess="editor.dialog=false;updateList()"
                                :source="editor.source"
                                :state="editor.state"></ifp-supplier-detail>
    </el-dialog>
</ifp-page>
```

## 容器 ifp-panel

```HTML
<ifp-panel title="标题" class="flex-item"></ifp-panel>
```

## 表格容器 ifp-panel-table

```HTML
<ifp-panel-table>
    <ifp-table height="100%">
        <!-- ... -->
    </ifp-table>
    <!-- 分页栏[可选] -->
    <template v-slot:pagination>
        <ifp-pagination 
        @size-change="sizeChange" 
        @current-change="pageChange"
        :current-page="viewer.paging.page"
        :page-size="viewer.paging.Size"
        :total="viewer.paging.Sums">
        </ifp-pagination>
    </template>
</ifp-panel-table>
```

**Javascript部分**

```Javascript
define(['iofp/api', 'zutil'], function (API, zutil) {
    return {
        el: '#app',
        props: {},
        computed: {},
        data() {
            return {};
        },
        created() {},
        watch: {},
        methods: {
            onSubmit() {},
            
        },
    };
});

```
