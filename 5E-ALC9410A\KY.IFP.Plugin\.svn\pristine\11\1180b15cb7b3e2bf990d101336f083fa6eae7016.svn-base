﻿using LinqToDB;
using LinqToDB.Configuration;
using LinqToDB.Data;
using LinqToDB.DataProvider.SqlServer;
using LinqToDB.Mapping;
using Microsoft.Data.SqlClient;
using System;
using System.Data;
using System.Data.SqlTypes;
using System.Threading.Tasks;

namespace COM.IFP.LinqDB
{
    /// <summary>
    /// 数据引擎，适用与默SqlServer，默认采用2012版本。
    /// </summary>
    public class SqlServer
    {
        /// <summary>
        /// 创建引擎
        /// </summary>
        /// <param name="source">连接字符</param>
        /// <param name="manage">托管事务</param>
        /// <returns>数据引擎</returns>
        public static DConnection Create(string source, bool manage = true)
        {
            return DConnection.Create(SqlServerTools.GetDataProvider(SqlServerVersion.v2012, SqlServerProvider.MicrosoftDataSqlClient), source, manage);
            

            //var current = DTransactionScope.Current;
            //if (current == null || !current.Exists(source))
            //{
            //    var option = new LinqToDbConnectionOptionsBuilder();
            //    var server = SqlServerTools.GetDataProvider(SqlServerVersion.v2012, SqlServerProvider.MicrosoftDataSqlClient);
            //    option.UseConnectionFactory(server, () =>
            //    {
            //        var result = new SqlConnection(source);
            //        //OnOpen(null, result);
            //        return result;
            //    });
            //    var result = DConnection.Create(option.Build(), source);  //new DConnection(option.Build(), source);
            //    if (current != null && current.DConnection == null)
            //    {
            //        //通过DTransaction开启的事务才有可能是DTransaction.Current不为空且DTransaction.Current.DConnection为空
            //        current.DConnection = result;
            //        current.Transaction = result.BeginTransaction();    //只要DTransaction.Current不为空，就表示需要事务，故此处强制开启。
            //    }
            //    //result.OnBeforeConnectionOpen += OnOpen;
            //    return result;
            //}
            //else
            //{
            //    return current.DConnection;
            //}
        }

        /// <summary>
        /// 创建引擎2008版本
        /// </summary>
        /// <param name="source">连接字符</param>
        /// <param name="manage">托管事务</param>
        /// <returns>数据引擎</returns>
        public static DConnection CreateSqlserver2008(string source, bool manage = true)
        {
            return DConnection.Create(SqlServerTools.GetDataProvider(SqlServerVersion.v2008, SqlServerProvider.MicrosoftDataSqlClient), source, manage);
        }

        private static void OnOpen(DataConnection sender, System.Data.IDbConnection source)
        {
            var action = source as SqlConnection;
            var cancel = new System.Threading.CancellationTokenSource();
            cancel.CancelAfter(action.ConnectionTimeout * 1000);    //连接字符串中的connect timeout以秒为单位，此处是以毫秒为单位。
            try
            {
                action.OpenAsync(cancel.Token).Wait();
            }
            catch (Exception ex)
            {
                if (cancel.IsCancellationRequested)
                {
                    throw new Exception($"打开{action.DataSource}上的数据库{action.Database}超时。");
                }
                else
                {
                    throw new Exception($"打开{action.DataSource}上的数据库{action.Database}失败，{ex.Message}");
                }
            }
        }
    }
}
