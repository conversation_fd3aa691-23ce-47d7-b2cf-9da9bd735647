<!DOCTYPE html>
<html lang="zh-<PERSON>">
    <head>
        <meta charset="UTF-8" />
        <title><PERSON><PERSON><PERSON>n</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="description" content="Description" />
        <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
        <link rel="stylesheet" href="./@npm/docsify/lib/themes/vue.css" />
        <link rel="stylesheet" href="../lib/kjlib/element/lib/theme-chalk/index.css" />
        <style>
            :root {
                --docsifytabs-border-color: #ededed;
                --docsifytabs-tab-highlight-color: purple;
            }
        </style>
    </head>
    <body>
        <!-- 导航 -->
        <nav>
            <a href="/">首页</a>
        </nav>
        <div id="app">加载中</div>
        <script src="./@lib/vue-example.js"></script>
        <script>
            window.$docsify = {
                el: '#app',
                name: '',
                repo: '', //https://github.com/docsifyjs/docsify/
                loadSidebar: true, // 加载侧边栏
                subMaxLevel: 2,
                loadNavbar: false, // 加载导航栏
                coverpage: false, // 设置封装面
                plugins: [VueExample],
                // ...
                tabs: {
                    persist: true, // default
                    sync: true, // default
                    theme: 'classic', // default
                    tabComments: true, // default
                    tabHeadings: true, // default
                },
            };
        </script>
        <script src="./@npm/docsify/lib/docsify.min.js"></script>
        <script src="./@npm/vue-run-sfc/vue-run-sfc.umd.js"></script>
        <script src="../lib/bower/vue/vue.min.js"></script>
        <script src="../lib/kjlib/element/lib/index.js"></script>

        <script src="./@npm/docsify-tabs/docsify-tabs.min.js"></script>
        <!-- json 颜色显示 -->
        <script src="./@npm/prismjs/components/prism-json.min.js"></script>
        <script src="./@npm/docsify-copy-code/docsify-copy-code.min.js"></script>
        <script>
            const VueRunSfc = window['vue-run-sfc'];
            Vue.use(VueRunSfc, {
                // 全局配置(非必填),  具体属性含义参考 `props` 说明
                // 注意 全局的 和 局部的 `cssLabs` 和 `jsLabs` 是 merge的关系, 不是替换
                cssLabs: [],
                jsLabs: [],
                row: true,
                reverse: true,
                height: '400px',
                open: true,
                isHideHeader: false,
                themeColor: 'green',
            });
        </script>
    </body>
</html>
