;
define([ "require"], function(require) {
	return {
		//在node节点中查找jggrid中指定class的DOM元素，找到后存放到list这个数组中
		findJqgridDom : function(node, className, nodeName, list){
			var _this = this;
			if(node.childNodes.length=0){
				return;
			}
			for(var i=0; i<node.childNodes.length; i++){
				var childNode = node.childNodes[i];
				if(childNode.nodeName==nodeName && childNode.className.indexOf(className)>-1){
					list.push(childNode);
				} else {
					_this.findJqgridDom(childNode, className, nodeName, list);
				}
			}
		},
		
		//将list中的DOM对象移除
		removeDom : function(list){
			for(var i=0; i<list.length; i++){
				list[i].parentNode.removeChild(list[i]);
			}
		},
		
		//删除jggrid数据行中隐藏的TD
		removeHiddenTD : function(node){
			var _this = this;
			if(node.nodeName=="TR" && node.className.indexOf("ui-jqgrid-labels")>-1){
				//去掉表头隐藏的TH
				var hiddenTD = [];
				for(var i=0; i<node.childNodes.length; i++){
					var td = node.childNodes[i];
					if(td.nodeName=="TH" && td.attributes["style"]!=undefined && td.attributes["style"].nodeValue.replace(/ /g,'').indexOf("display:none")>-1){
						hiddenTD.push(td);
					}
				}
				_this.removeDom(hiddenTD);
			}else if(node.nodeName=="TR" && node.className.indexOf("jqgrow ui-row-ltr")>-1){
				//去掉表体隐藏的TD
				var hiddenTD = [];
				for(var i=0; i<node.childNodes.length; i++){
					var td = node.childNodes[i];
					if(td.nodeName=="TD" && td.attributes["style"]!=undefined && td.attributes["style"].nodeValue.replace(/ /g,'').indexOf("display:none")>-1){
						hiddenTD.push(td);
					}
				}
				_this.removeDom(hiddenTD);
			} else {
				var childNodes = node.childNodes;
				if(childNodes.length=0){
					return;
				} else {
					for(var i=0; i<childNodes.length; i++){
						_this.removeHiddenTD(childNodes[i]);
					}
				}
			}
		},
		
		//删除jggrid表格的checkbox列
		removeCheckBoxTD : function(node){
			var _this = this;
			if(node.nodeName=="TR" && node.className.indexOf("ui-jqgrid-labels")>-1){
				var checkBoxTD = [];
				for(var i=0; i<node.childNodes.length; i++){
					var td = node.childNodes[i];
					if(td.nodeName=="TH" && td.innerHTML.indexOf("<input role=\"checkbox\"")>-1 && td.innerHTML.indexOf("type=\"checkbox\"")>-1){
						checkBoxTD.push(td);
					}
				}
				_this.removeDom(checkBoxTD);
			}else if(node.nodeName=="TR" && node.className.indexOf("jqgrow ui-row-ltr")>-1){
				var checkBoxTD = [];
				for(var i=0; i<node.childNodes.length; i++){
					var td = node.childNodes[i];
					if(td.nodeName=="TD" && td.innerHTML.indexOf("<input role=\"checkbox\"")>-1 && td.innerHTML.indexOf("type=\"checkbox\"")>-1){
						checkBoxTD.push(td);
					}
				}
				_this.removeDom(checkBoxTD);
			} else {
				var childNodes = node.childNodes;
				if(childNodes.length=0){
					return;
				} else {
					for(var i=0; i<childNodes.length; i++){
						_this.removeCheckBoxTD(childNodes[i]);
					}
				}
			}
		},
		
		//所有INPUT控件处理
		input : function(domList,controls){
			for(var i=0; i<domList.length; i++){
				var input = domList[i];
				//构造一个<p>，用来输出input的内容
				var p=document.createElement("p");
				//input的内容
				var inputValue = "";
				if((input.classList.contains("radio") || input.classList.contains("checkbox")) && controls!=null){
					inputValue=controls[input.id].text();
				}else{
					inputValue = input.value;
				}
				var node=document.createTextNode(inputValue);
				p.appendChild(node);
				//移除原来的input
				var parentNode = input.parentNode;
				parentNode.removeChild(input);
				//追加<p>区域
				parentNode.appendChild(p);
			}
		},
		
		//为导出按钮exportLinkBtn绑定导出事件，参数exportDiv是需要导出的DOM对象，参数filename是导出文件名
		excelExportEvent : function(exportDiv, filename, controls){
			var _this = this;
			//导出区域
			var exportNode = $("#"+exportDiv)[0].cloneNode(true);

			//删除隐藏区域
			domList = [];
			_this.findJqgridDom(exportNode, "hidden hideInput", "DIV", domList);
			_this.removeDom(domList);
			
			//删除表格上方“添加行，删除行”这些按钮
			domList = [];
			_this.findJqgridDom(exportNode, "btn-group btn-group-xs", "DIV", domList);
			_this.removeDom(domList);
			var domList = [];
			_this.findJqgridDom(exportNode, "btn-group", "DIV", domList);
			_this.removeDom(domList);
			//================================================================================
			

			//删除loading row节点
			var domList = [];
			_this.findJqgridDom(exportNode, "loading row", "DIV", domList)
			_this.removeDom(domList);
			
			//删除表格头部调整列宽的SPAN区域
			domList = [];
			_this.findJqgridDom(exportNode, "ui-jqgrid-resize ui-jqgrid-resize-ltr", "SPAN", domList);
			_this.removeDom(domList);

			//删除表格数据区域上方的空行
			domList = [];
			_this.findJqgridDom(exportNode, "jqgfirstrow", "TR", domList)
			_this.removeDom(domList);

			//删除翻页区域
			domList = [];
			_this.findJqgridDom(exportNode, "ui-jqgrid-pager", "DIV", domList);
			_this.removeDom(domList);

			//不知道这里叫什么区域了
			domList = [];
			_this.findJqgridDom(exportNode, "ui-jqgrid-resize-mark", "DIV", domList);
			_this.removeDom(domList);

			//删除jggrid数据行中隐藏的TD
			_this.removeHiddenTD(exportNode);
			
			//删除jggrid表格的checkbox列
			_this.removeCheckBoxTD(exportNode);
			//================================================================================
			//FORM区域，select2控件清除按钮×去掉
			domList = [];
			_this.findJqgridDom(exportNode, "select2-selection__clear", "SPAN", domList);
			_this.removeDom(domList);
			//FORM区域，select2控件下拉区域去掉
			domList = [];
			_this.findJqgridDom(exportNode, "form-control select2-hidden-accessible", "SELECT", domList);
			_this.removeDom(domList);
			
			//FORM区域，TEXTAREA控件处理
			domList = [];
			_this.findJqgridDom(exportNode, "form-control", "TEXTAREA", domList)
			_this.input(domList,controls);
			
			//FORM区域，INPUT控件处理
			domList = [];
			_this.findJqgridDom(exportNode, "form-control", "INPUT", domList)
			_this.input(domList,controls);
			
			//FORM区域，radio控件处理
			domList = [];
			_this.findJqgridDom(exportNode, "radio", "DIV", domList)
			_this.input(domList,controls);
			
			//FORM区域，checkbox控件处理
			domList = [];
			_this.findJqgridDom(exportNode, "checkbox", "DIV", domList)
			_this.input(domList,controls);
			//================================================================================

			//生成下载的link
			var html = "<html><head><meta charset='utf-8' /></head><body>" + exportNode.outerHTML + "</body></html>";
			var html = html.replace(/<table/g,'<table border="1" ');
			var html = html.replace(/border="0"/g,'border="1"');
			var blob = new Blob([html], {type: "application/vnd.ms-excel"});

			//IE处理方式
			if("msSaveOrOpenBlob" in navigator){
				window.navigator.msSaveOrOpenBlob(blob, filename);
			}else{
				var href = window.URL.createObjectURL(blob);
				if($("#export_excel_link_1478965").length==0){
					$(document.body).append("<a href=\"" + href + "\" id=\"export_excel_link_1478965\" style=\"display:none\">导出链接</a>");
				}
				$("#export_excel_link_1478965").attr("href",href);
				$("#export_excel_link_1478965")[0].download = filename;
				$("#export_excel_link_1478965")[0].click();
			}
		},



		
		//===============读取普通<TABLE>的行列信息，转换成导出需要的格式  BEGIN=========================================
		//读取<TD>中元素（textbox, numberbox, datepicker, select2）的值
		getTDValue : function(td, controls){
			//TD中没有任何元素
			if(td.childNodes.length==0){
				return "";
			}
			for(var i=0; i<td.childNodes.length; i++){
				//TD中存放的是<select2>控件
				if(td.childNodes[i].nodeName=="SPAN" && td.childNodes[i].innerHTML.startsWith("<select")){
					var id = td.childNodes[i].id;
					return controls[id].gettext();
				}
				//TD中存放的是<datepick>控件
				if(td.childNodes[i].nodeName=="DIV" && td.childNodes[i].outerHTML.indexOf("input-group date picker")>-1){
					var id = td.childNodes[i].id;
					return controls[id].value();
				}
				//TD中存放的是<textbox>控件
				if(td.childNodes[i].nodeName=="INPUT" && td.childNodes[i].outerHTML.indexOf("controls/textbox")>-1){
					var id = td.childNodes[i].id;
					return controls[id].value();
				}
				//TD中存放的是<number>控件
				if(td.childNodes[i].nodeName=="INPUT" && td.childNodes[i].outerHTML.indexOf("controls/number")>-1){
					var id = td.childNodes[i].id;
					return controls[id].value();
				}
				//TD中存放的是<label>控件
				if(td.childNodes[i].nodeName=="LABEL" && td.childNodes[i].className.indexOf("form-label")>-1){
					var id = td.childNodes[i].id;
					return controls[id].value();
				}
				//TD中存放的是<TEXTAREA >控件
				if(td.childNodes[i].nodeName=="TEXTAREA"){
					return td.childNodes[i].value.trim();
				}
			}
			//TD中存放的是普通字符串
			if(td.childNodes[0].nodeName=="#text"){
				return td.childNodes[0].nodeValue.trim();
			}
			return td.childNodes[0].innerText.trim();
		},
		
		/*
		 * 找到普通的<table>元素中的<tr>和<td>的二维数组信息，返回格式：
		 * [
		 *  [{rowspan:1,colspan:1,value:"A"},{rowspan:1,colspan:1,value:"B"}],
		 *  [{rowspan:1,colspan:1,value:"C"},{rowspan:1,colspan:1,value:"D"},{rowspan:1,colspan:1,value:"E"}],
		 *  [{rowspan:1,colspan:1,value:"F"}]
		 * ]
		 */
		getTableRowCol : function(exportTabId, controls){
			var _this = this;
			var exportTable = $("#"+exportTabId)[0].cloneNode(true);
			//1.找到TABLE中的TBODY
			var nodes = exportTable.childNodes;
			var tbody = null;
			for(var i=0; i<nodes.length; i++){
				if(nodes[i].nodeName=="TBODY"){
					tbody = nodes[i];
				}
			}
			//2.找TBODY中的<TR>
			var trList = [];
			for(var i=0; i<tbody.childNodes.length; i++){
				if(tbody.childNodes[i].nodeName=="TR"){
					trList.push(tbody.childNodes[i]);
				}
			}
			//3.将<TR>中非<TD>的对象删除掉
			for(var i=0; i<trList.length; i++){
				var delList = [];
				for(var j=0; j<trList[i].childNodes.length; j++){
					if(trList[i].childNodes[j].nodeName!="TD" && trList[i].childNodes[j].nodeName!="TH"){
						delList.push(trList[i].childNodes[j]);
					}
				}
				for(var k=0; k<delList.length; k++){
					trList[i].removeChild(delList[k]);
				}
				trList[i] = trList[i].childNodes;
			}
			//4.将<TD>DOM元素转换成{rowspan:2, colspan:1, value:"A"}这种普通的对象结构
			var res = [];
			for(var i=0; i<trList.length; i++){
				for(var j=0; j<trList[i].length; j++){
					var td = trList[i][j];
            		var rowspan=td.attributes.hasOwnProperty("rowspan") ? Number(td.attributes.getNamedItem("rowspan").nodeValue) : 1;
            		var colspan=td.attributes.hasOwnProperty("colspan") ? Number(td.attributes.getNamedItem("colspan").nodeValue) : 1;
            		var value = _this.getTDValue(td,controls);
            		if(j==0){
            			res[i]=[{rowspan:rowspan, colspan:colspan, value:value}];
            		}else{
            			res[i].push({rowspan:rowspan, colspan:colspan, value:value});
            		}
				}
			}
			return res;
		},
		
		/*
		 * 构造一个二维数组[[col1value,col2value,col3value,....],[col1value,col2value,col3value,....],...]
		 * <tr>行是第一维
		 * <td>列是第二维
		 */
		getTableExportTemplate : function(exportTabId, controls){
			var _this = this;
			//得到行列信息
			var rowCol = _this.getTableRowCol(exportTabId, controls);
			//如果单元格有colspan属性，就要往本行后面插入单元格，做列扩展
			for(var i=0; i<rowCol.length; i++){
				var row = [];
				for(var j=0; j<rowCol[i].length; j++){
					var rowspan = rowCol[i][j].rowspan;
					var colspan = rowCol[i][j].colspan;
					var value = rowCol[i][j].value;
					for(var k=1; k<=colspan; k++){
						row.push({rowspan:rowspan, colspan:1, value:value});
					}
				}
				rowCol[i]=row;
			}
			//如果单元格有rowspan属性，就要往本行下面插入单元格，做行扩展
			for(var i=0; i<rowCol.length; i++){
				for(var j=0; j<rowCol[i].length; j++){
					var rowspan = rowCol[i][j].rowspan;
					var value = rowCol[i][j].value;
					for(var k=1; k<=rowspan-1; k++){
						rowCol[i+k].splice(j,0,{rowspan:1,colspan:1,value:value})
					}
					rowCol[i][j].rowspan=1;
				}
			}
			//构造返回结果
			var template = new Array();
	        for(var i=0; i<rowCol.length; i++){
	        	template[i] = new Array();
	            for(var j=0; j<rowCol[i].length; j++){
	            	template[i][j] = rowCol[i][j].value;
	            }
	        }
	        return template;
		},
		//===============读取普通<TABLE>的行列信息，转换成导出需要的格式  END=========================================
		
		
		//===============分析报表页面的信息，转换成导出需要的格式  BEGIN=========================================
		//找到报表打印区域中的forms/report.title, forms/report.header, forms/report.group, forms/report.footer区域列表
		getReportExportDiv : function(exportDivId){
			var exportDiv = $("#"+exportDivId)[0].cloneNode(true);
			var nodes = exportDiv.childNodes;
			var res = [];
			for(var i=0; i<nodes.length; i++){
				if(nodes[i].nodeName=="DIV"){
					res.push(nodes[i]);
				}
			}
			return res;
		},
		
		//得到forms/report.title区域的标题
		getReportTitle : function(title, controls){
			var content = "";
			var nodes = title.childNodes;
			for(var i=0; i<nodes.length; i++){
				if(nodes[i].nodeName=="#text"){
					content += nodes[i].nodeValue.trim();
				}
				if(nodes[i].nodeName=="INPUT" || nodes[i].nodeName=="LABEL"){
					content += controls[nodes[i].id].value();
				}
			}
			return content;
		},
		
		//得到某一forms/report.header区域的结构
		getHeaderRow : function(headerRow, controls){
			var nodes = headerRow.childNodes;

			var left = nodes[0];
			var leftName =  left.childNodes.length>0 ? left.childNodes[0].nodeValue.trim() : "";
			var leftValue = left.childNodes.length>1 ? controls[left.childNodes[1].id].value() : "";

			var center = nodes[1];
			var centerName =  center.childNodes.length>0 ? center.childNodes[0].nodeValue.trim() : "";
			var centerValue = center.childNodes.length>1 ? controls[center.childNodes[1].id].value() : "";

			var right = nodes[2];
			var rightName =  right.childNodes.length>0 ? right.childNodes[0].nodeValue.trim() : "";
			var rightValue = right.childNodes.length>1 ? controls[right.childNodes[1].id].value() : "";

			var data = {
						"left"  :{"name":(leftName==""? "null1":leftName), "value":leftValue},
						"middle":{"name":(centerName==""? "null2":centerName), "value":centerValue},
						"rigth" :{"name":(rightName==""? "null3":rightName), "value":rightValue}
						};
			return data;
		},
		
		//得到某一forms/report.group区域的结构（jqgrid）
		getGroupGridRow : function(gridId, controls){
			var header = F.common.getTableHeaderMap(controls).get(gridId);
			var dataKey = F.common.getTableColMap(controls).get(gridId);
			var mergeKey = F.common.getTableMergeColname(gridId);
			var userData = controls[gridId].value();
			//检查列中是否有百分号显示的列，如果有的话，需要转换userData中的值
			var colModelList = $("#"+gridId).jqGrid("getGridParam").colModel;
			for(var i=0; i<colModelList.length; i++){
				if(colModelList[i].formater && colModelList[i].formater=="percent"){
					var colname = colModelList[i].name;
					for(var j=0; j<userData.length; j++){
						if(userData[j][colname]!=""){
							userData[j][colname] = userData[j][colname]*100 + "%";
						}
					}
				}
			}
			var data = {
					 "header":header, 
					 "dataKey":dataKey,
					 "mergeKey" : mergeKey,
					 "userData":userData
			}
			return data;
		},
		
		//得到某一forms/report.group区域的结构（普通表格）
		getGroupTableRow : function(tableId, controls){
			var _this = this;
			var data = _this.getTableExportTemplate(tableId, controls);
			return data;
		},
		
		//得到某一forms/report.footer区域的结构
		getFooterRow : function(footerRow, controls){
			var nodes = footerRow.childNodes;

			var left = nodes[0];
			var leftName =  left.childNodes.length>0 ? left.childNodes[0].nodeValue.trim() : "";
			var leftValue = left.childNodes.length>1 ? controls[left.childNodes[1].id].value() : "";

			var center = nodes[1];
			var centerName =  center.childNodes.length>0 ? center.childNodes[0].nodeValue.trim() : "";
			var centerValue = center.childNodes.length>1 ? controls[center.childNodes[1].id].value() : "";

			var right = nodes[2];
			var rightName =  right.childNodes.length>0 ? right.childNodes[0].nodeValue.trim() : "";
			var rightValue = right.childNodes.length>1 ? controls[right.childNodes[1].id].value() : "";

			var data = {
					"left"  :{"name":(leftName==""? "null1":leftName), "value":leftValue},
					"middle":{"name":(centerName==""? "null2":centerName), "value":centerValue},
					"rigth" :{"name":(rightName==""? "null3":rightName), "value":rightValue}
					};
			return data;
		},
		
		//得到报表需要的导出结构
		getReportExportTempldate : function(repname, exportDivId, controls){
			var _this = this;
			var exportDivDom = _this.getReportExportDiv(exportDivId);
			
			var option = {};
			option.fileName = "";
			option.dataList = [{
					"tabName" : "",
					"sheetData" : [{
						 "titleName" : "",
						 "topInfo" :[]
					 }]
			}];

			//构造标题forms/report.title
			var title = "";
			for(var i=0; i<exportDivDom.length; i++){
				var div = exportDivDom[i];
				if(div.className.indexOf("report-title")>-1){
					title = _this.getReportTitle(div, controls);
				}
			}
			option.fileName = title+".xls";
			option.dataList[0].tabName = title;
			option.dataList[0].sheetData[0].titleName = title;

			//构造头部信息forms/report.header
			var topInfo = [];
			for(var i=0; i<exportDivDom.length; i++){
				var div = exportDivDom[i];
				if(div.className.indexOf("report-header")>-1){
					for(var j=0; j<div.childNodes.length; j++){
						if(div.childNodes[j].nodeName=='DIV' && div.childNodes[j].className.indexOf("report-header-content")>-1){
							var headerRow = _this.getHeaderRow(div.childNodes[j], controls);
							topInfo.push(headerRow);
							break;
						}
					}
				}
			}
			option.dataList[0].sheetData[0].topInfo = topInfo;

			//构造jqGrid表格和普通的表格
			for(var i=0; i<exportDivDom.length; i++){
				var div = exportDivDom[i];
				//forms/report.group区域
				if(div.className.indexOf("report-group")>-1){
					for(var j=0; j<div.childNodes.length; j++){
						//jqGrid表格
						if(div.childNodes[j].nodeName=='DIV' && div.childNodes[j].className.indexOf("ui-jqgrid")>-1){
							var jgridRow = _this.getGroupGridRow(div.childNodes[j].id.substr(5), controls);
							//如果sheet中{第1个数据区域}不存在jqgrid或普通表格，就将该表格添加到{第一个数据区域}
							if(!option.dataList[0].sheetData[0].hasOwnProperty("header") && 
							   !option.dataList[0].sheetData[0].hasOwnProperty("topTab") ){
								option.dataList[0].sheetData[0]["header"] = jgridRow["header"];
								option.dataList[0].sheetData[0]["dataKey"] = jgridRow["dataKey"];
								option.dataList[0].sheetData[0]["mergeKey"] = jgridRow["mergeKey"];
								option.dataList[0].sheetData[0]["userData"] = jgridRow["userData"];
							}else{
								option.dataList[0].sheetData.push(jgridRow)
							}
							break;
						}
						//普通表格
						if(div.childNodes[j].nodeName=='TABLE'){
							var tableRow = _this.getGroupTableRow(div.childNodes[j].id, controls);
							//如果sheet中{第1个数据区域}不存在jqgrid或普通表格，就将该表格以topTab数据添加到{第一个数据区域}
							if(!option.dataList[0].sheetData[0].hasOwnProperty("header") && 
							   !option.dataList[0].sheetData[0].hasOwnProperty("topTab") ){
								option.dataList[0].sheetData[0]["topTab"] = {rows:tableRow};
							}else{
								option.dataList[0].sheetData.push({footTab:{rows:tableRow}})
							}
							break;
						}
					}
				}
			}
			
			//构造脚本信息forms/report.footer
			var footInfo = [];
			for(var i=0; i<exportDivDom.length; i++){
				var div = exportDivDom[i];
				if(div.className.indexOf("report-footer")>-1){
					for(var j=0; j<div.childNodes.length; j++){
						if(div.childNodes[j].nodeName=='DIV' && div.childNodes[j].className.indexOf("report-footer-content")>-1){
							var footRow = _this.getFooterRow(div.childNodes[j], controls);
							footInfo.push(footRow);
							break;
						}
					}
				}
			}
			//先计算sheet中有几块数据区域，然后将footInfo添加到最后一块数据区域里
			var sheetDataNum = option.dataList[0].sheetData.length;
			option.dataList[0].sheetData[sheetDataNum-1]["footInfo"] = footInfo;
			return option;
		}
		//===============分析报表页面的信息，转换成导出需要的格式  END=========================================
		
		
	}
});