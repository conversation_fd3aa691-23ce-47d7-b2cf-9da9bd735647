# 在线测水样瓶架

## 使用

### 在 kyadmin/ifp 下使用

示例 `/iofp/components/zxcsypj/ifp-zxcsypj.html`

### 单独使用示例

示例 `test.html`

## 开发

使用 parcel 构建

```bash
# 安装依赖
yarn

# 原型设计
yarn run design

# 开发
yarn run dev

# 构建
yarn run build
```

## 控件说明

### 瓶位布局

从上至下从做到右排列

目前右两种型号货架

#### 5行4列

1. 1  2  3  4
2. 5  6  7  8
3. 9  10 11 12
4. 13 14 15 16
5. 17 18 19 20

#### 7行3列

1. 1  2  3
2. 4  5  6
3. 7  8  9
4. 10 11 12
5. 13 14 15
6. 16 17 18
7. 19 20 21

### 瓶位状态

* 无瓶
* 待化验
* 化验中
* 待回传
* 待复检
* 异常
