define(["js-yaml"],function(yaml){

    /**
     * Registers a callback for DOM ready. If DOM is already ready, the
     * callback is called immediately.
     * @param {Function} callback
     */
    function comp() {
        //console.log("comp:")
        //console.log(arguments)
    }

    comp.version = '0.0.1';

    comp.load = function (name, req, onLoad, config) {
        require(["text!"+name],function(code){
            var doc = yaml.load(code);
            onLoad(doc)
        },function(err){
            throw err
        });
    };

    return comp;
})