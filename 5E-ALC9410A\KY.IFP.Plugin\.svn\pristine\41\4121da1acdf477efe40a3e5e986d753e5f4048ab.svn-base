﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace KY.IFP.Runtime
{
    /// <summary>
    /// 加解密库
    /// </summary>
    public class Secure
    {
        private const string vector = "<EMAIL>";   //必须16位非中文字符

        /// <summary>
        /// 默认密钥字符串
        /// </summary>
        public static string Secret { get; set; } = "123456";

        /// <summary>
        /// 加密字符串，验证地址 http://tool.chacuo.net/cryptaes (CBC/pkcs7padding/128/默认密码/<EMAIL>/base64/utf8)，参考资料 https://github.com/brix/crypto-js/blob/master/docs/QuickStartGuide.wiki#AES
        /// </summary>
        /// <param name="source">待加密字符串</param>
        /// <returns>加密后字符串</returns>
        public static string Encrypt(string source)
        {
            return Encrypt(source, Secret);
        }

        /// <summary>
        /// 加密字符串，验证地址 http://tool.chacuo.net/cryptaes (CBC/pkcs7padding/128/密码参数/<EMAIL>/base64/utf8)，参考资料 https://github.com/brix/crypto-js/blob/master/docs/QuickStartGuide.wiki#AES
        /// </summary>
        /// <param name="source">待加密字符串</param>
        /// <param name="secret">密钥字符串</param>
        /// <returns>加密后字符串</returns>
        public static string Encrypt(string source, string secret)
        {
            string result = source;
            try
            {
                using Aes aes = Aes.Create("AES");
                var key = new byte[16];
                var tmp = Encoding.UTF8.GetBytes(secret);
                Array.Copy(tmp, key, Math.Min(tmp.Length, 16));
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                aes.KeySize = 128;
                aes.Key = key;
                aes.IV = Encoding.UTF8.GetBytes(vector);
                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);
                using MemoryStream msEncrypt = new();
                using CryptoStream csEncrypt = new(msEncrypt, encryptor, CryptoStreamMode.Write);
                using (StreamWriter swEncrypt = new(csEncrypt))
                {
                    swEncrypt.Write(source);
                }
                result = global::System.Convert.ToBase64String(msEncrypt.ToArray());
            }
            catch
            {
                //可能会加密失败，加密失败直接返回明文还是""？
            }
            return result;
        }

        /// <summary>
        /// 解密字符串，验证地址 http://tool.chacuo.net/cryptaes (CBC/pkcs7padding/128/默认密码/<EMAIL>/base64/utf8)，参考资料 https://github.com/brix/crypto-js/blob/master/docs/QuickStartGuide.wiki#AES
        /// </summary>
        /// <param name="source">待解密字符串</param>
        /// <returns>解密后字符串</returns>
        public static string Decrypt(string source)
        {
            return Decrypt(source, Secret);
        }

        /// <summary>
        /// 解密字符串，验证地址 http://tool.chacuo.net/cryptaes (CBC/pkcs7padding/128/密码参数/<EMAIL>/base64/utf8)，参考资料 https://github.com/brix/crypto-js/blob/master/docs/QuickStartGuide.wiki#AES
        /// </summary>
        /// <param name="source">待解密字符串</param>
        /// <param name="secret">密钥字符串</param>
        /// <returns>解密后字符串</returns>
        public static string Decrypt(string source, string secret)
        {
            string result = source;
            try
            {
                using Aes aes = Aes.Create("AES");
                var key = new byte[16];
                var tmp = Encoding.UTF8.GetBytes(secret);
                Array.Copy(tmp, key, Math.Min(tmp.Length, 16));
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                aes.KeySize = 128;
                aes.Key = key;
                aes.IV = Encoding.UTF8.GetBytes(vector);
                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);
                using MemoryStream msDecrypt = new(System.Convert.FromBase64String(source));
                using CryptoStream csDecrypt = new(msDecrypt, decryptor, CryptoStreamMode.Read);
                using StreamReader srDecrypt = new(csDecrypt);
                result = srDecrypt.ReadToEnd();
            }
            catch
            {
                //可能会解密失败，解密失败直接返回密文还是""？
            }
            return result;
        }

        /// <summary>
        /// 解密格式器
        /// </summary>
        public class Convert : IFormatProvider, ICustomFormatter
        {
            string ICustomFormatter.Format(string format, object arg, IFormatProvider provider)
            {
                if (!string.IsNullOrWhiteSpace(format) && format.Equals("AES", StringComparison.OrdinalIgnoreCase))
                {
                    return Decrypt(arg.ToString());
                }
                else
                {
                    return arg.ToString();
                }
            }

            object IFormatProvider.GetFormat(Type formatType)
            {
                if (formatType == typeof(ICustomFormatter))
                    return this;
                else
                    return null;
            }


            private class Resolve : IFormatProvider, ICustomFormatter
            {
                public Dictionary<int, string> format = new();

                public string Format(string format, object arg, IFormatProvider provider)
                {
                    this.format[(int)arg] = format;
                    return null;
                }

                public object GetFormat(Type formatType)
                {
                    return this;
                }
            }

            /// <summary>
            /// 格式字符分析
            /// </summary>
            /// <param name="format">格式字符串</param>
            /// <returns>序号及格式</returns>
            public static Dictionary<int, string> Analyse(string format)
            {
                var resolve = new Resolve();
                _ = string.Format(resolve, format, Enumerable.Range(0, 100).Cast<object>().ToArray());  //最多支持100个参数
                return resolve.format;
            }
        }
    }
}