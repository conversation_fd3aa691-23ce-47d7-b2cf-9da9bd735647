﻿define(["iofp/api",

    "iofp/lib/excelExportElement", "platform/vue"], function (API, excelExport, pVue) {

        const components = pVue.createAsyncComponents({
            ifpDetail: "./pointAdd.js",//对应标签ifp-role-detail

        });
        return {
            el: "#app",
            components: components,
            data() {
                return {
                    zmList: [

                    ],
                    sjlxList: [

                    ],
                    ioShowList: [
                        { text: "不可展示", id: 0 },
                        { text: "可展示", id: 1 },
                    ],

                    filter: {
                        name: null,
                        groupCode: null,
                        block: null,
                    },

                    data: [
                        {}
                    ],
                    paging: {
                        size: 20,
                        page: 1,
                        records: 100,
                    },

                    loading: false,//
                    selectedRow: null,
                    //ctrlstate: 0,
                    editor: {
                        show: false,
                        title: '新增',
                        data: null,
                        list: [],
                    }
                }
            },

            methods: {
                indexMethod: function (index) {
                    let curpage = this.paging.page;   //单前页码，具体看组件取值
                    let limitpage = this.paging.size;    //每页条数，具体是组件取值
                    return (index + 1) + (curpage - 1) * limitpage;
                },

                onSelect: function () {

                    let param = [
                        {
                            Name: { Value: this.filter.name, Match: 'HAS' },
                            GroupCode: { Value: this.filter.groupCode, Match: '==' },
                            Block: { Value: this.filter.block, Match: '==' },
                        },

                    ];
                    this.loading = true;
                    //todo
                    return API.GetAction("/API/IFP/PLC/PLCPoint/Select", {
                        "filter": param, "paging": this.paging
                    }).then(x => {
                        this.loading = false;
                        this.$set(this, "data", x.rows);
                        this.paging.records = x.records;

                        //if (x.success) {

                        //} else {
                        //    this.$message.error("查询失败");

                        //}
                    }).catch(e => {
                        this.loading = false;
                        this.$message.error("查询出错");
                        console.error(e);
                    });
                },

                checkSelect() {
                    if (this.selectedRow == null) {
                        this.$message.warning("请选择一行数据");
                        return false;
                    }
                    return true;
                },

                add() {
                    let data = {
                        Gid: null,
                        Name: '',
                        GroupCode: null,
                        DataType: null,
                        Block: 0,
                        ByteOffset: 0,
                        BitOffset: 0,
                        IOShow: 0,
                        Description: '',
                    };
                    this.editor.title = "新增";
                    this.editor.data = data;
                    this.editor.show = true;
                },
                modify() {
                    if (this.checkSelect() == false) {
                        return;
                    }
                    this.editor.data = this.selectedRow;
                    this.editor.title = "修改";
                    this.editor.show = true;

                },
                remove() {
                    if (this.checkSelect() == false) {
                        return;
                    }

                    this.$confirm('是否删除?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        API.GetAction("API/IFP/PLC/PLCPoint/PLCPointDelete", this.selectedRow).then(x => {
                            if (x.success) {
                                this.$message.success("删除成功");
                                this.onSelect();
                            } else {
                                this.$message.error("删除失败" + x.msg);
                            }

                        }).catch(e => {
                            this.$message.error("删除出错");
                            console.error(e);
                        });
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });
                    });
                    return
                },
                initData() {
                    API.GetAction("/API/IFP/PLC/PLCPoint/GetGroupNameEnum", {}).then(x => {
                        this.zmList = x;
                    }).catch(e => {
                        this.$message.error(e);
                        //console.error(e);
                    });
                    API.GetAction("/API/IFP/PLC/PLCPoint/GetDataTypeEnum", {}).then(x => {
                        this.sjlxList = x;
                    }).catch(e => {
                        this.$message.error(e);
                        //console.error(e);
                    });
                },
                //导出
                onExport: function () {
                    /*
                     * 指定列模型
                     * DataType：数据类型（1 字符, 2 数字, 3 时间）
                     * Align：对齐方式（1 left, 2 center, 3 right）
                     * DecimalDigits：小数位
                     * Ywlx：基础资料的类型
                     * Formater：格式转换符号(percent：百分比格式，thousands：千分符显示，useridtoname：用户登录账号转名称)
                     * Data：自定义下拉控件的选项值
    */
                    var colModels = [
                        { Name: "AlarmCode", DataType: 1, Align: 2 },
                        { Name: "AlarmName", DataType: 1, Align: 2 },
                        { Name: "AlarmDate", DataType: 3, Align: 2 },
                        { Name: "RemoveTime", DataType: 3, Align: 2, Formater: "YYYY-MM-DD HH:mm:SS" },
                        { Name: "UserId", DataType: 1, Align: 2 },
                        { Name: "AlarmLevel", DataType: 1, Align: 2, Data: this.bjjbList },
                        { Name: "AlarmStatus", DataType: 1, Align: 2, Data: this.bjztList },
                        //{ Name: "YTSL", DataType: 2, Align: 3, DecimalDigits: 0 },
                        ////{ Name: "ZYLYL", DataType: 2, Align: 3, DecimalDigits: 0 },
                        //{ Name: "CYZQ", DataType: 2, Align: 3, DecimalDigits: 0 },
                        //{ Name: "CYKSSJ", DataType: 3, Align: 2, Formater: "YYYY-MM-DD HH:mm:ss" },
                        //{ Name: "CYJSSJ", DataType: 3, Align: 2, Formater: "YYYY-MM-DD  HH:mm:ss" },
                        //{ Name: "SFCS", DataType: 1, Align: 3 },
                        //{ Name: "SFJG", DataType: 1, Align: 3 },
                        //{ Name: "YCSJ", DataType: 1, Align: 3 },
                        //{ Name: "TQ1321", DataType: 1, Align: 3, Ywlx: 1321, },
                        //{ Name: "WD", DataType: 2, Align: 3, DecimalDigits: 0 },
                        //{ Name: "SD", DataType: 2, Align: 3, DecimalDigits: 2 },
                        ////{ Name: "CYY", DataType: 1, Align: 2, Formater: "useridtoname" },
                        ////{ Name: "JDY", DataType: 1, Align: 2, Formater: "useridtoname" },
                        //{ Name: "CYLX1059", DataType: 1, Align: 2, Ywlx: 1059, }
                        /*   { Name: "SJLY", DataType: 3, Align: 2, Data: [{ id: 0, text: "人工录入" }, { id: 1, text: "自动上传" }] }*/
                    ];

                    //表格头
                    var Heads = excelExport.getHeads(this.$refs["tableRef"]);

                    //表格数据
                    var Datas = excelExport.getDatas(this.$refs["tableRef"], colModels, this.data);

                    //需要合并的列索引
                    var MergeColumnIndex = [];

                    var title = "报警记录";

                    var ExcelWorkbook = {
                        FileName: title + ".xls",
                        Sheets: [
                            {
                                SheetName: title,
                                Tables: [
                                    {
                                        TableType: 1,
                                        Title: title,
                                        Heads: Heads,
                                        Datas: Datas,
                                        MergeColumnIndex: MergeColumnIndex
                                    }
                                ]
                            }
                        ]
                    };

                    //调用通用导出方法
                    excelExport.export(ExcelWorkbook);

                },

                sizeChange(v) {
                    this.paging.size = v;
                    this.onSelect();
                },

                pageChange(v) {
                    this.paging.page = v;
                    this.onSelect();
                },

                //重置
                onReset: function () {
                    //this.filter.start = moment().startOf("day").format();
                    //this.filter.end = moment().startOf("day").format();
                    this.filter.name = null;
                    this.filter.groupCode = null;
                    this.filter.block = null;
                    this.onSelect();
                },
                //当前行变化事件
                rowClick: function (row) {
                    this.selectedRow = row;
                },
            },
            created() {
                this.initData();
                this.onReset();

            },
            mounted() {
                this.$on('opened', x => {
                    this.onReset();
                });
            }
        }
    })