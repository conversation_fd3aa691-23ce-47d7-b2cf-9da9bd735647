define([
    'exports',
    "ELEMENT",
    "platform/vue",
    "vuex",
    "./ifp-select",
    "./ifp-button-lxsz",
    "./ifp-table",
    "./ifp-chart",
    "./ifp-button",
    "/iofp/legend/index.js",
    "./ifp-camera/index",
    "css!./index.css"
], function (
    exports,
    Element, pVue, { mapState },
    ifpSelects,
    ifpButtonLxsz,
    ifpTable,
    ifpChart,
    ifpButton,
    legends,
    ifpCamera
) {
    Object.assign(exports,{
        ifpChart,
        ifpCamera,
        ifpButtonLxsz,
        ...ifpSelects,
        ...ifpTable,
        ...ifpButton,
        ...legends
    })

    // 异步组件
    Object.assign(exports,pVue.createAsyncComponents({
        IfpTableColumnShow: "/pages/sys/liexian/columnTree.js",
        IfpBarCodePrint: "/pages/sys/barCodePrint.js"
    }));

    /**
     * 退出按钮
     */
    exports.ifpButtonClose = {
        props: {
            auto: { default: true },
            icon: { default: "kjicon kjicon-tuichu" },
            text: { default: '退出' }
        },
        created() {
            this.$on('click', this.$click_handler)
        },
        render(h) {
            if (!this.$slots.default) {
                this.$slots.default = this.text;
            }
            return Element.Button.render.call(this, h);
        },
        extends: Element.Button,
        methods: {
            $click_handler(...args) {
                this.$emit('close');
                let page = this.$getPageOrCompVM();
                page.$emit("close");
            }
        }
    }



    function getClassFromAttrs(vm) {
        return (vm.$attrs.flex !== undefined ? ' flex' : '')
            + (vm.$attrs['flex-row'] !== undefined ? ' flwx flex-row' : '')
            + (vm.$attrs['flex-item'] !== undefined ? ' flex-item' : '')
            + (vm.$attrs.class ? (' ' + vm.$attrs.class) : '');
    }

    function makeClassAndStyle(vm, clas, styl) {
        return {
            class: clas + getClassFromAttrs(vm),
            style: styl + (vm.$attrs.style || '')
        }
    }

    exports.ifpPage = {
        name: "ifpPage",
        provide() {
            return {
                ifpPage: this,
            }
        },
        created() {

        },
        mixins: [{
            methods: {
                getPageId() {
                    return this.$getPageId()
                }
            }
        }],
        render(h) {
            return h('div', { class: 'flex flex-item ifp-page' }, [
                this.$slots.default
            ])
        }
    }

    exports.ifpLayoutTable = {
        props: {
        },
        computed: {
        },
        render(h) {
            return h('div', {
                class: "flex margin"
                    + (this.$attrs.class ? (' ' + this.$attrs.class) : ''),
                style: (this.$attrs.style || '')
                    + (this.$attrs.height && (";height:" + this.$attrs.height + ";"))
            }, [
                this.$slots.header ? h('div', {
                    class: "border padding",
                    style: 'border:1px solid #ccc;border-bottom:0;'
                }, this.$slots.header) : '',
                h('div', {
                    class: "flex-item"
                }, this.$slots.default),
                this.$slots.pagination
            ])
        }
    }

    exports.ifpToolbar = {
        inject: {
            ifpPage: {
                default: null
            }
        },
        props: {
            close: { type: [String, Boolean], default: false },
            lxsz: { type: [String, Boolean], default: false }
        },
        computed: {
            showLXSZ() {
                return this.lxsz !== false;
            },
            ...mapState({
                lxszlist(state) {
                    const pageid = this.$getPageInfo().id;
                    const page = state.lxsz.items.find(item => item.id == pageid);
                    if (!page) { return [] }
                    return page.tables;
                }
            })
        },
        render(h) {
            let lx = [];

            if (this.lxsz !== false) {
                if (this.lxsz === '') {
                    lx = this.lxszlist.map(table => table.id);
                } else if (typeof this.lxsz === "string") {
                    lx.push(this.lxsz)
                }
            }


            return h('div', {
                class: "ifp-toolbar toolbar"
            }, [
                this.$slots.default,
                lx.map(tableid => [
                    " ",
                    h('ifp-button-lxsz', {
                        props: {
                            table: tableid
                        }
                    })]
                ),
                this.close !== false && [
                    " ",
                    h('ifp-button-close', {
                        on: {
                            close: () => {
                                if (this.ifpPage) {
                                    if (this.ifpPage.$parent.onCancel) {
                                        this.ifpPage.$parent.onCancel();
                                    }
                                }
                                this.$emit('close')
                                this.$emit('cancel')
                            }
                        }
                    }, this.close ? this.close : undefined)
                ]
            ])
        }
    }

    exports.ifpForm = {
        inject: {
            ifpPanel: { default: '' },
            ifpSearchbar: { default: '' },
            ifpPanelHeader: { default: '' }
        },
        props: {
            //inline:{default:true}
        },
        extends: Element.Form,
        render(h) {
            let rev = Element.Form.render.call(this, h)
            if (!this.ifpSearchbar && this.ifpPanel && !rev.data.staticClass.split(" ").includes("padding")) {
                rev.data.staticClass += " padding"
            }
            return rev;
        }
    }


    exports.ifpFormItem = {
        model: {
            prop: 'value',
            event: 'input'
        },
        props: {
            //width:{type:String,default:'180px'},
            value: { type: [Number, String], default: "" },
            placeholder: { default: "" },
            type: { default: "text" },

            /* ywlx 相关参数 */
            ywlx: { default: "" },
            filter: Function,
            filterArg: { type: [String, Number, Boolean, Object], default: '' },

            /* ifp-select 相关参数 */
            url: { default: '' },
            items: { default: () => [] },
            post: { type: [Object], default: undefined },
            labelKey: { default: 'id' },
            valueKey: { default: 'text' },
        },
        data() {
            return {

            }
        },
        methods: {
            input(v) {
                this.$emit("input", v);
                console.log(v + "-" + Date.now())
            }
        },
        render(h) {
            let self = this;
            if (!this.$slots.default || (this.$slots.default && this.$slots.default.__ifp_form_item)) {
                if (this.type === "text") {
                    this.$slots.default = [
                        h('ifp-input', {
                            style: {
                                width: this.width
                            },
                            attrs: {
                                "placeholder": this.placeholder
                            },
                            model: {
                                value: (self.value),
                                callback: function ($$v) { self.input($$v); }
                            },
                            on: {
                                ...self.$listeners
                            }
                        })
                    ]
                }

                if (this.type === "select") {
                    this.$slots.default = [
                        h('ifp-select', {
                            style: {
                                width: this.width
                            },
                            props: {
                                url: this.url,
                                items: this.items,
                                labelKey: this.labelKey,
                                valueKey: this.valueKey,
                                post: this.post
                            },
                            model: {
                                value: (self.value),
                                callback: function ($$v) { self.input($$v); }
                            },
                            on: {
                                ...self.$listeners
                            }
                        })
                    ]
                }

                if (this.type === "ywlx") {
                    this.$slots.default = [
                        h('ifp-select-ywlx', {
                            style: {
                                width: this.width
                            },
                            props: {
                                ywlx: self.ywlx,
                                filter: this.filter,
                                filterArg: this.filterArg
                            },
                            model: {
                                value: (self.value),
                                callback: function ($$v) { self.input($$v); }
                            },
                            on: {
                                ...self.$listeners
                            }
                        })
                    ]
                }

                if (this.$slots.default) {
                    this.$slots.default.__ifp_form_item = true;
                }
            }

            return Element.FormItem.render.call(this, h)
        },
        extends: Element.FormItem
    }

    // search bar
    exports.ifpSearchbar = {
        provide() {
            return {
                ifpSearchbar: this,
            }
        },
        props: {
            reset: { type: Boolean, default: false },
            search: { type: Boolean, default: false },
            model: { type: Object, default: null },
            // reset: { type: Boolean, default: false }
        },
        computed: {
            showreset() {
                return this.reset || (this._events.reset && this._events.reset.length) || this.model
            },
            showsearch() {
                return this.search || this._events.search && this._events.search.length
            }
        },
        template: `
      <div class="searchbar">
        <ifp-form :inline="true" ref="form" :model="model||{}" class="flex-item" style="flex-shrink:1">
          <slot></slot>
          <div style="display:inline-block; white-space:nowrap;">
            <slot name="buttons"></slot>
            <ifp-button v-if="showsearch" type="primary" icon='el-icon-search' @click.prevent="$emit('search')">查询</ifp-button>
            <ifp-button v-if="showreset" icon='el-icon-refresh-left' @click="resetField">重置</ifp-button>
          </div>
        </ifp-form>
      </div>
      `,
        methods: {
            resetField() {
                if (this.model) {
                    this.$refs.form.resetFields();
                }
                this.$emit("reset")
            }
        }
    }

    exports.ifpLayoutFooter = {
        render(h) {
            return h('div',
                makeClassAndStyle(this, "ifp-layout-footer padding"),
                this.$slots.default)
        }
    }
    exports.ifpSearchbarItem = exports.ifpFormItem;

    // 标题栏
    exports.ifpPanelHeader = {
        provide() {
            return {
                ifpPanelHeader: this,
            }
        },
        props: {
            title: { default: '' },
            titleRight: { default: '' },
            border: { type: Boolean, default: false },
        },
        render(h) {
            if (!this.title && (!this.$slots.default || this.$slots.default.length === 0)) {
                return;
            }
            return h("div", {
                class: "ifp-panel-header" + (this.border ? ' border' : '')
            }, [
                this.title && h('span', { class: "ifp-panel-header-title" }, this.title),
                this.$slots.default && h('span', { class: "ifp-panel-header-center" }, this.$slots.default),
                this.titleRight && h('span', { class: "ifp-panel-header-right" }, this.titleRight)
            ]);
        }
    }

    exports.ifpPanel = {
        provide() {
            return {
                ifpPanel: this,
            }
        },
        props: {
            title: { default: "" },
            margin: { type: Boolean },
            padding: { type: Boolean },
            borderless: { type: Boolean, default: false },
            titleRight: { default: "" },
            border: { type: Boolean, default: true },
            bodyClass: { default: null },
            bodyStyle:{type:String,default:''},
            boxClass: { default: "" },
            flexRow: { type: Boolean, default: false },
            flex: { type: Boolean, default: false },
            flexItem: { type: Boolean, default: false }
        },
        computed: {
            hasBorder() {
                return this.border;
            },
            hasHeader() {
                return this.title || (this.$slots.header && this.$slots.header.length);
            }
        },
        render(h) {
            let container_class = [];
            let body_class = [];
            if (this.margin) {
                container_class.push('margin')
            }
            if (this.padding) {
                body_class.push('padding')
            }
            if (this.bodyClass) {
                body_class.push(this.bodyClass)
            }
            if (this.flex) {
                container_class.push('flex')
                body_class.push('flex-item flex')
                if (this.flexRow) {
                    body_class.push('flex-row')
                }
            }
            if (this.flexItem) {
                container_class.push('flex-item flex')
                body_class.push('flex-item')
            }
            if (this.borderless) {
                container_class.push('theme-borderless')
            } else if (this.border) {
                container_class.push('border')
            }
            if (this.boxClass) {
                container_class.push(this.boxClass)
            }

            return h('div', makeClassAndStyle(this,
                "ifp-panel"
                + (container_class.length ? (' ' + container_class.join(' ')) : '')
            ), [
                h('ifp-panel-header', {
                    props: {
                        title: this.title,
                        titleRight: this.titleRight,
                        border: this.bodder
                    }
                }, this.$slots.header),
                h("div", {
                    class: "ifp-panel-body"
                        + (body_class.length ? (' ' + body_class.join(' ')) : ''),
                    style:this.bodyStyle
                }, this.$slots.default),
                this.$slots.footer && h("div", {
                    class: "ifp-panel-footer"
                }, this.$slots.footer)
            ])
        }
    }

    exports.block = {
        props:{
            tag:{type:String,default:'div'},
            border:{type:Boolean,default:false},
            margin:{type:Boolean,default:false},
            padding:{type:Boolean,default:false},
            flex:{type:Boolean,default:false},
            flexRow:{type:Boolean,default:false},
            flexItem:{type:Boolean,default:false},
        },
        render(h){
            return h(this.tag,{
                class:{
                    border:this.border,
                    margin:this.margin,
                    padding:this.padding,
                    flex:this.flex,
                    flexRow:this.flexRow,
                    flexItem:this.flexItem
                }
            })
        }
    }

    /**
     * Table 容器
     */
    exports.ifpPanelTable = {

        provide() {
            return {
                ifpTableHeight: "100%",
                ifpPanelTable: this
            }
        },

        props: {
            border: { type: Boolean, default: true },
            borderBody: { type: Boolean, default: false },
            pagination: { type: Boolean, default: false },
            paginationCurrentPage: { default: 1 },
            paginationTotal: { default: 0 },
            print: { type: Boolean, default: false },
            lxsz: { type: Boolean, default: false },
            tools: { default: () => [] },
            boxClass: { default: "ifp-panel-table" },
            bodyStyle:{default:"border:0;"},
            flex: { type: Boolean, default: true },
            flexItem: { type: Boolean, default: false },
            flexRow: { type: Boolean, default: false }
        },

        computed: {
            _tools() {
                let rev = [];
                if (this.print) {
                    rev.push({ name: 'print', icon: 'el-icon-printer', title: "打印" });
                }
                if (this.lxsz) {
                    rev.push({
                        name: 'setting', icon: 'el-icon-setting', title: "设置", type: 'ifp-button-lxsz', props: {
                            table: () => this.getTable().getTableId(),
                            type: "text",
                            text: ""
                        }
                    });
                }
                return [...rev, ...this.tools];
            }
        },

        created() {
            if (this.print) {
                this.$on("tool-print-click", () => {
                    let table = this.getTable();
                    table && table.print();
                })
                this.$on("tool-setting-click", () => {
                    let table = this.getTable();
                    table && table.print();
                })
            }
        },

        extends: exports.ifpPanel,
        methods: {
            getTable() {
                return this.$children.find(item => item.$options.name == "ElTable");
            }
        },

        render(h) {
            let self = this;

            let footers = [];
            /* 分页栏 */

            if (this.$slots.pagination && this.$slots.pagination.length) {
                footers.push(this.$slots.pagination)
            } else if (this.pagination) {
                let ons = {};
                if (this.$listeners["pagination-current-change"]) {
                    ons["current-change"] = this.$listeners["pagination-current-change"]
                }
                if (this.$listeners["pagination-size-change"]) {
                    ons["size-change"] = this.$listeners["pagination-size-change"]
                }
                footers.push(h('ifp-pagination', {
                    on: ons
                }))
            }

            if (this._tools && this._tools.length) {
                footers.push(h('div', {
                    class: 'flex-item',
                    style: "text-align:right;padding-right:1rem;",
                }, this._tools.map((item, index) => item.type ? h(item.type, { props: item.props }) : h('el-button', {
                    props: { type: "text" },
                    domProps: {
                        title: item.title
                    },
                    on: {
                        click() {
                            self.$emit('toolClick', item, index)
                            if (item.name === "print") {
                                self.$emit('tool-' + item.name + '-click', item, index)
                            }
                        }
                    }
                }, [h('i', { class: item.icon }), ""]))) || false)
            }

            if (this.$slots.tool) {
                footers.push(this.$slots.tool)
            }

            if (footers.length) {
                this.$slots.footer = [
                    h('div', { class: "flex flex-row", style: "justify-content: space-between;" }, footers)
                ]
            }

            return exports.ifpPanel.render.call(this, h);
        }
    }

    exports.ifpPanelLegend = {
        props:{
            padding:{type: Boolean,default:true},
            flex:{type: Boolean,default:true},
            flexItem:{type: Boolean,default:false},
            flexRow:{type: Boolean,default:true},
            bodyStyle:{default:"justify-content: space-between;"}
        },
        extends:exports.ifpPanel
    }

    exports.ifpPagination = {
        props: {
            layout: { default: "total, sizes, prev, pager, next, jumper" },
            currentPage: { default: 1 },
            pageSizes: { default: () => [10, 15, 20, 50, 100] },
            pageSize: { default: 20 },
            total: { type: [Number, null], default: null }
        },
        extends: Element.Pagination
    }

    exports.ifpInput = {
        props: {
            clearable: { default: true }
        },
        extends: Element.Input
    }

    exports.ifpInputNumber = {
        props: {
            controls: { default: false },
            clearable: { default: true }
        },
        extends: Element.InputNumber
    }

    exports.ifpDatePicker = {
        props: {
            //format:{default:"yyyy-MM-dd"} // 修改默认值后会导致 type="datatiem" 异常，时间控件中显示 日期
        },
        extends: Element.DatePicker
    }

    exports.ifpDialog = {
        name: "IFPDialog",

        provide() {
            return {
                ifpDialog: this
            }
        },

        props: {
            closeOnClickModal: { type: Boolean, default: false },
            closePressEscape: { type: Boolean, default: false },
            fullscreen: { type: Boolean, default: false },
            appendToBody: { type: Boolean, default: true },
            customClass: { default: "subpage" }
        },
        extends: Element.Dialog,
        created() {
            this.$on('open', () => {

            })
            this.$on('opened', () => {

            })
        }
    }

    exports.ifpCheckbox = { extends: Element.Checkbox }
    exports.ifpCheckboxGroup = { extends: Element.CheckboxGroup }
    exports.ifpRadio = { extends: Element.Radio }
    exports.ifpRadioGroup = { extends: Element.RadioGroup }

    /**
     * ifp-tabs
     * @extends Element.Tabs
     */
    exports.ifpTabs = {
        provide() {
            return {
                ifpTabs: this
            }
        },
        props:{
            flex:{ type:Boolean,default:false }
        },
        extends: Element.Tabs,
        render(){
            // 使用 el-tabs
            let vnode = Element.Tabs.render.call(this,...arguments);

            // flex == trur 才修改 vnode
            if(!this.flex){
                return vnode
            }



            let contentIndex = 1,headerIndex=0;

            vnode.data.class = vnode.data.class || {};
            vnode.data.class.flex = true;

            if(this.tabPosition==='bottom'){
                contentIndex=0;
                headerIndex=1;
                vnode.children[headerIndex].children[0].data.style="margin-bottom:0;"
            }

            if(["left","right"].includes(this.tabPosition)){
                vnode.data.class['flex-row'] = true;

                if(this.tabPosition==="right"){
                    vnode.data.style = "flex-direction: row-reverse"
                }
            }

            vnode.children[contentIndex].data.class=vnode.children[contentIndex].data.class||'';
            vnode.children[contentIndex].data.class+=' flex flex-item';

            
            return vnode;
        }
    }

    /**
     * ifp-tab-pane
     */
    exports.ifpTabPane = {
        ...Element.TabPane,
        inject:{'ifpTabs':{
            form:'ifpTabs',
            default:null
        }},
        render(h){
            let vnode = Element.TabPane.render.call(this,h);
            if(this.ifpTabs?.flex){
                vnode.data.class = vnode.data.class || {};
                vnode.data.class['flex-item']=this.ifpTabs.flex;
            }
            return vnode;
        }
    }
})