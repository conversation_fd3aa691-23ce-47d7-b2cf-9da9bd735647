﻿using COM.IFP.SqlSugarN;
using SqlSugar;
using System;
using System.Collections.Generic;
namespace ORM.IFP.www.DbModel.UM
{

    /// <summary>
	/// 部门表
	/// </summary>
	[SugarTable("IFP_UM_DEPARTMENT")]
    public partial class IFP_UM_DEPARTMENT
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(36)")]
        public Field<string> Gid { get; set; }

        /// <summary>
        /// 父部门编码
        /// </summary>
        [SugarColumn(ColumnName = "DPMFATHERCODE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DPMFatherCode { get; set; }

        /// <summary>
        /// 部门GUID
        /// </summary>
        [SugarColumn(ColumnName = "DPMGUID", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DpmGuid { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        [SugarColumn(ColumnName = "DPMCODE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DpmCode { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        [SugarColumn(ColumnName = "DPMNAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DpmName { get; set; }

        /// <summary>
        /// 部门全称
        /// </summary>
        [SugarColumn(ColumnName = "DPMALLNAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DpmAllName { get; set; }

        /// <summary>
        /// 部门说明
        /// </summary>
        [SugarColumn(ColumnName = "DPMDESCRIPTION", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DPMDescription { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [SugarColumn(ColumnName = "DPMSERNUM", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> DPMSerNum { get; set; }

        /// <summary>
        /// 部门负责人
        /// </summary>
        [SugarColumn(ColumnName = "DPMMAINUSER", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DPMMainUser { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "CREATOR", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(128)")]
        public Field<string> Creator { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "CREATETIME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "REMARK", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(3000)")]
        public Field<string> Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "DELT", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> DELT { get; set; }

    }

    /// <summary>
    /// 部门树节点
    /// </summary>
    public class DepartmentTreeNode
    {
        public string Gid { get; set; }
        public string DpmCode { get; set; }
        public string DpmName { get; set; }
        public string DpmFatherCode { get; set; }
        public int DpmSerNum { get; set; }
        public string DpmMainUser { get; set; }
        public int UserCount { get; set; }
        public int Delt {  get; set; }
        public DateTime CreateTime { get; set; }
        public string Remark { get; set; }
        public List<DepartmentTreeNode> Children { get; set; }

        public DepartmentTreeNode()
        {
            Children = new List<DepartmentTreeNode>();
        }
    }

    /// <summary>
    /// 用户部门信息
    /// </summary>
    public class UserDepartmentInfo
    {
        public string DeptGid { get; set; }
        public string DeptCode { get; set; }
        public string DeptName { get; set; }
        public string DeptFullName { get; set; }
        public bool IsPrimary { get; set; }
        public DateTime AssignTime { get; set; }
    }

    /// <summary>
    /// 部门统计信息
    /// </summary>
    public class DepartmentStatistics
    {
        public int DirectUserCount { get; set; }  // 直接用户数
        public int TotalUserCount { get; set; }   // 包含子部门的总用户数
        public int SubDepartmentCount { get; set; } // 子部门数
        public int TotalSubDepartmentCount { get; set; } // 所有层级子部门数
    }
}
