define(function(){
    return {
        el:"#app",
        data(){
            return {
                search:{
                    name1:"",
                    name2:"",
                    region:""
                },
                data:[
                    {mark:'表格适应容器高度',col2:'<'+'ifp-table height=\'100%\''},
                    {mark:'集成列显设置',col1:'<'+'ifp-panel-table :lxsz=\'true\'',col2:'<'+'ifp-table :lxsz=\'true\''},
                    {mark:'集成打印表格',col1:'<'+'ifp-panel-table :print=\'true\''}
                ],
                pagination:{
                    total:75,
                    size:30,
                    currentPage:1
                }
            }
        },
        methods:{
            update(){

            },
            onPaginationCurrentChange(event){
                this.currentPage = event;
                this.$message(`查询：${event} 页`)
            },
            onPaginationSizeChange(event){
                this.$message(`查询：${event} 条`)
            }
        }
    }
})