﻿define(["iofp/api", "platform/vue"], function (API, pVue) {
    return {
        el: "#app",

        props: {
            //父页面传入的GID
            gid: { default: "" },

            //父页面传入的操作类型（add, view, modify）
            czlx: { default: "" },

            //当前页面的显示隐藏标志
            visible: {default: false }
        },

        watch: {
            visible: function (newVal, oldValue) {
                if (newVal)
                {
                    this.getData();
                }
            }
        },

        data() {
            return {
                formdata: {
                    Gid: null,
                    Pgid: null,
                    Bname: null,
                    Sname: null,
                    Ywlx: '4013',
                    Compid: null,
                    Addtime: null,
                    Lasttime: null,
                    Zfbz: 0,
                    Ywbm: null,
                    Beizhu: null,
                    Zzjgdm: null,
                    Lxr: null,
                    Lxdh: null,
                    Ysdwbm: null
                },

                rules: {
                    Bname: [
                        { required: true, message: '请输入运输单位名称', trigger: 'blur' }
                    ]
                }
            }
        },

        created() {
            this.getData();
        },

        methods: {

            getData() {
                //先清空原来的值
                this.clearForm();

                //如果是修改，或查询的话，重新取值
                if (this.czlx != 'add') {
                    API.GetAction("API/ICS/BaseData/Ywdx4013API/GetByGId", { Gid: this.gid }).then(x => {
                        this.$set(this, "formdata", x);
                    }).catch(e => {
                        this.$message.error(e);
                    });
                }
            },

            //保存
            onSave() {
                this.$refs["form"].validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                    API.GetAction("API/ICS/BaseData/Ywdx4013API/Submit", this.formdata).then(x => {
                        if (x.success) {
                            this.$message.success(x.msg);
                            this.$emit("success");
                        } else {
                            this.$message.error(x.msg);
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
                });
            },

            //退出
            onExit() {
                this.$emit("cancel")
            },

            //清空表单内容
            clearForm: function () {
                this.formdata.Gid = null;
                this.formdata.Pgid = null;
                this.formdata.Bname = null;
                this.formdata.Sname = null;
                this.formdata.Ywlx = '4013';
                this.formdata.Compid = null;
                this.formdata.Addtime = null;
                this.formdata.Lasttime = null;
                this.formdata.Zfbz = 0;
                this.formdata.Ywbm = null;
                this.formdata.Beizhu = null;
                this.formdata.Zzjgdm = null;
                this.formdata.Lxr = null;
                this.formdata.Lxdh = null;
                this.formdata.Ysdwbm = null;
            }
        }
    }
})