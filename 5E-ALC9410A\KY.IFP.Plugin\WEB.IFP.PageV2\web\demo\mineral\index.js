define(["platform/vue"],function(pVue){

  const enum_edit_state = {
    "view":"查看",
    "edit":"修改",
    "add":"新增",
    "delete":"删除"
  }

  // 创建异步组件列表，放在这里的好处是：主页面的加载不依赖子页面
  const components = pVue.createAsyncComponents({
      IfpKdDetail: "./detail.js"//对应标签ifp-kd-detail，VUE会把IfpKdDetail 转换成 全小写， 大写字母全部变成 -小写字母
  })

    return /* Vue */{
        el:"#app",
        components:components,
        data(){
          return {
            loading:true,
            message:"测试",
            search:{
              fz:""
            },
            editor:{
              header:"查看",
              state:"view", // update delete view
              dialog:false,
              source:{}
            },
            viewer: {
              filter: {
                Gid: { Value: null, Match: '==' },
                Bname: { Value: null, Match: 'HAS', Order:'DESC' }
              },
              source: [],
              select: null,
              paging: {
                size: 20,
                page: 1,
                records: 20,
              }
            }
          }
        },
        computed:{//计算属性
          searchModel(){
            return {
              fz:''
            }
          },
          dialogTitle(){
            return (enum_edit_state[this.editor.state]||"")+'-矿点信息'
          }
        },
        created(){
          
          this.$on("ifp-load",()=>{
            //this.$update(4002)
          })

          this.updateList();
        },
        methods:{
          getData(){
            return new Promise((resolve,reject)=>{
              setTimeout(()=>{
                this.$api.get("./data.json",{ 
                  "filter": this.viewer.filter, 
                  "paging": this.viewer.paging 
                }).then(x => {
                  resolve(x);
                }).catch(reject);
              },Math.random()*1000+500)
            })
          },
          updateList(){
            this.loading=true;

            return this.getData().then(x=>{
              this.viewer.source = x.rows;
              this.viewer.paging.records = x.records;
              this.loading=false;
            }).catch(_=>{
              this.loading=false;
            })
          
          },
          sizeChange(v){
            this.viewer.paging.size = v;
            this.updateList();
          },
          pageChange(v){
            this.viewer.paging.page = v;
            this.updateList();
          },
          onSelect: function () {
            this.updateList();
          },
          onChange: function (obj) {
            this.viewer.select = obj;
          },
          onCreate: function () {
            this.editor.state = 'add';
            this.editor.dialog = true;
          },
          checkInput:function(){
            if (this.viewer.select === null) {
              this.$message.warning('请先选择一个矿点！');
              return false;
            }
            return true;
          },
          onUpdate: function () {
            if(this.checkInput()){
              this.editor.source = this.viewer.select;
              this.editor.state = 'edit';
              this.editor.dialog = true;
            }
          },
          onDelete: function () {
            if(this.checkInput()){
              this.$confirm('确定要删除矿点‘' + this.viewer.select.MineName + '’吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).catch(() => { throw new Error(); })
                .then(() => this.$api.post("API/ICS/BaseData/Mineral/Delete", this.viewer.select))
                .then(x => {
                  this.$message.success('删除完成。');
                  this.updateList();
                }).catch(e => {
                  this.$message.error(e);
                });
            }
          }
        }
    }
})