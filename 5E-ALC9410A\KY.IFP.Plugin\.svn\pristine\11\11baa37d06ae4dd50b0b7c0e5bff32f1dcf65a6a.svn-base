define(["require","jquery","controls/modal"],function(require,$,Modal,SelectorConfig){
	
	var defaultOption ={
		layoutfull:false,
		height:600
	}
	
	return F.class(Modal,{
		name:"modal-iframe",
		createDefaultOption:function(container,option){
			return $.extend(true,this.base.apply(this,arguments),defaultOption);
		},
		createBody:function(){
			var body  = $("<div class='modal-body' style='padding:0px;'><iframe src='"+ this.option.url +"' style='height:100%;width:100%;'></iframe>");
			
			return body;
		},
		createFooter:function(){
			return null;
		}
	})
});
