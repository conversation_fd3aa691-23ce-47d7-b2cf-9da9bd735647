
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <script src="iconfont.js"></script>

    <style type="text/css">
        .icon {
          /* 通过设置 font-size 来改变图标大小 */
          width: 1em; height: 1em;
          /* 图标和文字相邻时，垂直对齐 */
          vertical-align: -0.15em;
          /* 通过设置 color 来改变 SVG 的颜色/fill */
          fill: currentColor;
          /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
             normalize.css 中也包含这行 */
          overflow: hidden;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-jiesuangl"></use>
                    </svg>
                    <div class="name">结算</div>
                    <div class="fontclass">#dicb-jiesuangl</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-qingjia1"></use>
                    </svg>
                    <div class="name">请假</div>
                    <div class="fontclass">#dicb-qingjia1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-hetonggl"></use>
                    </svg>
                    <div class="name">合同</div>
                    <div class="fontclass">#dicb-hetonggl</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-caijue1"></use>
                    </svg>
                    <div class="name">采掘</div>
                    <div class="fontclass">#dicb-caijue1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-xixuan"></use>
                    </svg>
                    <div class="name">洗选</div>
                    <div class="fontclass">#dicb-xixuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-qingjia2"></use>
                    </svg>
                    <div class="name">请假</div>
                    <div class="fontclass">#dicb-qingjia2</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-caijue"></use>
                    </svg>
                    <div class="name">挖矿</div>
                    <div class="fontclass">#dicb-caijue</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-ziliao"></use>
                    </svg>
                    <div class="name">资料</div>
                    <div class="fontclass">#dicb-ziliao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-qingjia3"></use>
                    </svg>
                    <div class="name">请假</div>
                    <div class="fontclass">#dicb-qingjia3</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-chuchai"></use>
                    </svg>
                    <div class="name">出差</div>
                    <div class="fontclass">#dicb-chuchai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-chidao"></use>
                    </svg>
                    <div class="name">迟到</div>
                    <div class="fontclass">#dicb-chidao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-shangpinmei"></use>
                    </svg>
                    <div class="name">矿业</div>
                    <div class="fontclass">#dicb-shangpinmei</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-gongyingshanggl"></use>
                    </svg>
                    <div class="name">供应商</div>
                    <div class="fontclass">#dicb-gongyingshanggl</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-zaotui"></use>
                    </svg>
                    <div class="name">早退</div>
                    <div class="fontclass">#dicb-zaotui</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-yinyong"></use>
                    </svg>
                    <div class="name">引用</div>
                    <div class="fontclass">#dicb-yinyong</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-caimei"></use>
                    </svg>
                    <div class="name">挖矿</div>
                    <div class="fontclass">#dicb-caimei</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-shenhe"></use>
                    </svg>
                    <div class="name">考核标准</div>
                    <div class="fontclass">#dicb-shenhe</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-tongjibaobiao"></use>
                    </svg>
                    <div class="name">统计报表</div>
                    <div class="fontclass">#dicb-tongjibaobiao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-shangjia"></use>
                    </svg>
                    <div class="name">向上加</div>
                    <div class="fontclass">#dicb-shangjia</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-xiajia"></use>
                    </svg>
                    <div class="name">向下加</div>
                    <div class="fontclass">#dicb-xiajia</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-yidaka"></use>
                    </svg>
                    <div class="name">已打卡</div>
                    <div class="fontclass">#dicb-yidaka</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-weidaka"></use>
                    </svg>
                    <div class="name">未打卡</div>
                    <div class="fontclass">#dicb-weidaka</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-yinsi"></use>
                    </svg>
                    <div class="name">因私证件</div>
                    <div class="fontclass">#dicb-yinsi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-bofang"></use>
                    </svg>
                    <div class="name">播放</div>
                    <div class="fontclass">#dicb-bofang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-zanting"></use>
                    </svg>
                    <div class="name">暂停</div>
                    <div class="fontclass">#dicb-zanting</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-chuchai1"></use>
                    </svg>
                    <div class="name">出差</div>
                    <div class="fontclass">#dicb-chuchai1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-kehu"></use>
                    </svg>
                    <div class="name">客户管理</div>
                    <div class="fontclass">#dicb-kehu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-chengbenhs"></use>
                    </svg>
                    <div class="name">成本</div>
                    <div class="fontclass">#dicb-chengbenhs</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-taizhanggl"></use>
                    </svg>
                    <div class="name">台账管理</div>
                    <div class="fontclass">#dicb-taizhanggl</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-jihuagl"></use>
                    </svg>
                    <div class="name">计划管理</div>
                    <div class="fontclass">#dicb-jihuagl</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-delete-list"></use>
                    </svg>
                    <div class="name">删除列表</div>
                    <div class="fontclass">#dicb-delete-list</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-chuansongdai"></use>
                    </svg>
                    <div class="name">皮带运输</div>
                    <div class="fontclass">#dicb-chuansongdai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-xuhao"></use>
                    </svg>
                    <div class="name">序号</div>
                    <div class="fontclass">#dicb-xuhao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-zaitu"></use>
                    </svg>
                    <div class="name">在途管理</div>
                    <div class="fontclass">#dicb-zaitu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-huoqu"></use>
                    </svg>
                    <div class="name">获取</div>
                    <div class="fontclass">#dicb-huoqu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-yujing"></use>
                    </svg>
                    <div class="name">预警</div>
                    <div class="fontclass">#dicb-yujing</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-qizi"></use>
                    </svg>
                    <div class="name">旗子</div>
                    <div class="fontclass">#dicb-qizi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-yingong2"></use>
                    </svg>
                    <div class="name">因公</div>
                    <div class="fontclass">#dicb-yingong2</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-yingong1"></use>
                    </svg>
                    <div class="name">因公外出</div>
                    <div class="fontclass">#dicb-yingong1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-yingong"></use>
                    </svg>
                    <div class="name">icon-因公</div>
                    <div class="fontclass">#dicb-yingong</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#dicb-hebing"></use>
                    </svg>
                    <div class="name">合并</div>
                    <div class="fontclass">#dicb-hebing</div>
                </li>
            
        </ul>


        <h2 id="symbol-">symbol引用</h2>
        <hr>

        <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
        这种用法其实是做了一个svg的集合，与另外两种相比具有如下特点：</p>
        <ul>
          <li>支持多色图标了，不再受单色限制。</li>
          <li>通过一些技巧，支持像字体那样，通过<code>font-size</code>,<code>color</code>来调整样式。</li>
          <li>兼容性较差，支持 ie9+,及现代浏览器。</li>
          <li>浏览器渲染svg的性能一般，还不如png。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-symbol-">第一步：引入项目下面生成的symbol代码：</h3>
        <pre><code class="lang-js hljs javascript"><span class="hljs-comment">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;</span></code></pre>
        <h3 id="-css-">第二步：加入通用css代码（引入一次就行）：</h3>
        <pre><code class="lang-js hljs javascript">&lt;style type=<span class="hljs-string">"text/css"</span>&gt;
.icon {
   width: <span class="hljs-number">1</span>em; height: <span class="hljs-number">1</span>em;
   vertical-align: <span class="hljs-number">-0.15</span>em;
   fill: currentColor;
   overflow: hidden;
}
&lt;<span class="hljs-regexp">/style&gt;</span></code></pre>
        <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
        <pre><code class="lang-js hljs javascript">&lt;svg <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"icon"</span> aria-hidden=<span class="hljs-string">"true"</span>&gt;<span class="xml"><span class="hljs-tag">
  &lt;<span class="hljs-name">use</span> <span class="hljs-attr">xlink:href</span>=<span class="hljs-string">"#dicb-xxx"</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">use</span>&gt;</span>
</span>&lt;<span class="hljs-regexp">/svg&gt;
        </span></code></pre>
    </div>
</body>
</html>
