﻿
using COM.IFP.Common;
using DAL.IFP;
using Newtonsoft.Json;
using ORM.IFP;
using ORM.IFP.DbModel;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace API.ICS.BasicData
{
    /// <summary>
    /// 供应商管理
    /// </summary>
    public class TrainModel
    {
        private Lazy<DAL.ICS.BasicData.TrainModel> service = COM.IFP.Common.Entity.Create<DAL.ICS.BasicData.TrainModel>();


        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PageModel<YWDX4017> TrainModelList(JsonElement json)
        {
            var entity = json.GetValue<YWDX4017>("filter");
            var paging = json.GetValue<PageModel<YWDX4017>>("paging");
            return service.Value.TrainModelList(entity, paging);
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public YWDX4017 SelectTrainModeByid(JsonElement json)
        {
            var gid = json.GetValue<long>("filter");
            return service.Value.SelectTrainModeByid(gid);
        }


        /// <summary>
        /// 新增与修改
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public PFActionResult SubmitTrainModel(JsonElement json)
        {
            YWDX4017 obj = json.GetValue<YWDX4017>();
            return service.Value.SubmitTrainModel(obj);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public PFActionResult DeleteTrainModel(JsonElement json)
        {
            YWDX4017 obj = json.GetValue<YWDX4017>();
            return service.Value.DeleteTrainModel(obj);
        }
    }
}
