(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "kyadmin/utils/dom", "kyadmin/utils/util", "ramda", "kyadmin/utils/zutil"], factory);
    }
})(function (require, exports) {
    "use strict";
    exports.__esModule = true;
    exports.queryAllParmeter = exports.queryUrlParmeter = exports.queryDialogParmeter = void 0;
    var dom_1 = require("kyadmin/utils/dom");
    var util_1 = require("kyadmin/utils/util");
    var ramda_1 = require("ramda");
    var zutil_1 = require("kyadmin/utils/zutil");
    //获取 dialog 传参
    function queryDialogParmeter() {
        var dialogs = dom_1.topWindow.__dialog && dom_1.topWindow.__dialog.dialogs || {};
        var id = util_1.queryString("_w");
        return id && dialogs[id] && dialogs[id].parameter || null;
    }
    exports.queryDialogParmeter = queryDialogParmeter;
    //获取 url 传参
    function queryUrlParmeter() {
        var rev = null;
        if (window.location.search) {
            var pairs = location.search.slice(1).split("&").map(function (item) {
                return item.split("=");
            });
            rev = ramda_1.fromPairs(pairs || []);
        }
        return rev;
    }
    exports.queryUrlParmeter = queryUrlParmeter;
    //获取 dialog 和 url 传值
    function queryAllParmeter() {
        return zutil_1.extend(true, {}, queryUrlParmeter(), queryDialogParmeter());
    }
    exports.queryAllParmeter = queryAllParmeter;
});
//# sourceMappingURL=data:application/json;base64,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