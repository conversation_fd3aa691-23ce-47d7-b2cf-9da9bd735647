<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>vue-component-menutabs</title>
    <style>
        html,body{
            height: 100%;
        }
        body{margin:0;}
    </style>
</head>
<body controller option="{platform:'vue'}">
    <div id="app" class="menutabs-wraper" style="display:flex;flex-direction: column;flex-grow: 1;flex-shrink: 1;">
        <div class="menutabs-tabs-wrapper" v-on:mousewheel="onScroll" :class="{hasscroll:hasScroll}">
            <div ref="leftbtn" :class="{disabled:disabledLeft}" @click="scrollLeftClick" v-if="hasScroll" class="scrollbtn">
                <span class="glyphicon glyphicon-chevron-left"></span>
            </div>
            <div class="kjtabs-header-wrap" ref="listwrap">
                <transition-group name="list-complete" tag="ul" class="kjtabs nav nav-tabs" style="display:inline-block">
                    <li v-for="item in tabs" 
                        class="list-complete-item"
                        :class="{active:item.id===currentid}"
                        :key="item.id"
                        v-on:click="currentid=item.id"
                    >
                        <a href="javascript:void(0);">{{item.title}}</a>
                        <span v-if="!item.noClose"  @click.stop="deleteTab(item.id)" class="menutitle badge">×</span>
                    </li>
                </transition-group>
            </div>
            <div ref="rightbtn" :class="{disabled:disabledRight}" @click="scrollRightClick" v-if="hasScroll" class="scrollbtn">
                <span class="glyphicon glyphicon-chevron-right"></span>
            </div>
        </div>
        
        <div style="height:8px;"></div>
        
        <div style="display: flex;flex-grow: 1;flex-shrink: 1;" class="menutabs-content-wrapper">
            <transition-group name="list-complete-y" tag="div" style="display: flex;flex-grow: 1;flex-shrink: 1;position: relative;">
                <iframe v-if="item.rendered"
                    v-show="item.id===currentid"
                    v-for="item in renderitems"
                    style="height: 100%;width:100%;flex-shrink: 0;"
                    class="list-complete-y-item"
                    :src="item.url"
                    :key="item.id"
                    :data-key="item.id"
                >
                </iframe>
            </transition-group>
        </div>
    </div>
</body>
</html>