define(['exports','ELEMENT'],function(exports,Element){
    // 配置默认图标
    let defaultButtons = {
        "保存": { icon: "kjicon kjicon-baocun", type: "primary" },
        "新增": { icon: "el-icon-plus", type: "primary" },
        "查看": { icon: "el-icon-view" },
        "刷新": { icon: "el-icon-refresh" },
        "设置": { icon: "el-icon-setting" },
        "修改": { icon: "el-icon-edit" },
        "删除": { icon: "el-icon-delete" },
        "查询": { icon: "el-icon-refresh-search" },
        "重置": { icon: "el-icon-refresh-left" },
        "退出": { icon: "kjicon kjicon-tuichu" },
        "导出": { icon: "kjicon kjicon-daochu" },
        "导入": { icon: "kjicon kjicon-daoru" }
    }


    /*
    <ifp-pagination 
      :current-page="20"
      :page-sizes="[10,15,20,50,100]"
      :page-size="20"
      layout="total, sizes, prev, pager, next, jumper"
      :total="15">
    </ifp-pagination>
    */
    exports.ifpIcon = {
        props: {
            icon: { default: "" }
        },
        render(h) {
            let role = this.role || (this.$slots.default && this.$slots.default[0].text);
            let def = role && defaultButtons[role];
            return h('span', {
                class: this.icon || def.icon
            })
        },
    }

    exports.ifpButton = {
        inject: {
            btnRight:{default:[]},
            ifpPanelHeader:{default:''}
        },
        props:{
        iconBtn:{type:Boolean,default:false},
        code:{type:String,default:''},
        role:{type:String,default:''},
        plain:{type:Boolean,default(){
            if(this.ifpPanelHeader){
                return true;
            }else{
                return false
            }
        }}
        },
        // extends: Element.Button,
        computed:{
        isShow(){
            // 如果 配置了code 且 code 出现在禁用列表中，则返回false;
            if(this.btnRight && this.code && this.btnRight.includes(this.code)){
            return false;
            }
            return true;
        }
        },
        //mixins:[Element.Button],
        render(h){
        // 配置了 code ，且 code 无权限
        if(!this.isShow){
            return;
        }

        let opt = {
            attrs:{
            title:this.$slots.default?.text
            },

            class:'ifp-button'+(this.iconBtn?' ifp-button-iconbtn':''),
            
            props:{plain:this.plain,...this.$attrs},

            // 传递事件
            on:{
            ...this._events
            }
        }

        /* 默认属性 */
        let role =  this.role || (this.$slots.default && this.$slots.default[0].text);
        let def = role && defaultButtons[role];

        if(def){
            if(def.type && !opt.props.type){
            opt.props.type = def.type
            }
            if(def.icon && !opt.props.icon){
            opt.props.icon = def.icon
            }
        }

        return h('el-button',opt,this.iconBtn?'':this.$slots.default)
        // return Element.Button.render.call(this, h('el-button',opt,this.iconBtn?'':this.$slots.default));
        }
    }
})