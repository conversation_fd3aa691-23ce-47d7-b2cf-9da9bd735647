define(["require","jquery","controls/modal","controls/page","jclass"],function(require,$,<PERSON><PERSON>,Page,jclass){
	
	var defaultOption ={
		layoutfull:true
	}
	
	return jclass(Modal,{
		name:"modal-iframe",
		createDefaultOption:function(container,option){
			return $.extend(true,this.base.apply(this,arguments),defaultOption);
		},
		createBody:function(){
			return $("<div class='modal-body' style='padding:0px;'></div>");
		},
		createFooter:function(){
			return null;
		},
		init:function(option){
			this.base.apply(this,arguments);
			new Page(this.$body,{url:option.url});
			//this.
		}
	})
});
