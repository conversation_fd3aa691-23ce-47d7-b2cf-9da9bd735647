define(["commons/print","lodop"],function(printjs,LodopFuncs){
  
  var tables = [
    `<div id="div1">
    <DIV style="LINE-HEIGHT: 30px" class=size16 align=center><STRONG><font color="#0000FF">销售发货单-01</font></STRONG></DIV>        
    <TABLE border=0 cellSpacing=0 cellPadding=0 width="100%">
      <TBODY>
      <TR>
        <TD width="43%"><font color="#0000FF">所在店铺：<SPAN 
          id=rpt_Pro_Order_List_ctl00_lbl_eShop_Name>雅瑞专卖店</SPAN></font></TD>
        <TD width="33%"><font color="#0000FF">发货单号：<SPAN >2011050810372</SPAN></font></TD>
        <TD><font color="#0000FF">快递单号：</font></TD></TR>
      <TR>
        <TD><font color="#0000FF">收 件 人：<SPAN >王斌</SPAN></font></TD> 
        <TD><font color="#0000FF">网店单号：<SPAN>74235823905643</SPAN></font><font color="#0000FF"></font></TD>
        <TD><font color="#0000FF">发货日期：2011-5-10</font></TD></TR>
      <TR>
        <TD><font color="#0000FF">电话号码：<SPAN>13935429860　</SPAN></font></TD>
        <TD><font color="#0000FF">收件人ID：<SPAN>云星王斌</SPAN></font></TD>
        <TD><font color="#0000FF">&nbsp;</font></TD></TR></TBODY></TABLE>
    </div>
    `,
    function(){
        
        return `<TABLE border=1 cellSpacing=0 cellPadding=1 width="100%" style="border-collapse:collapse" bordercolor="#333333">
        <thead>
          <TR>
            <TD width="10%">
              <DIV align=center><b>表格页眉</b></DIV></TD>
            <TD width="25%">
              <DIV align=center><b>品名</b></DIV></TD>
            <TD width="10%">
              <DIV align=center><b>颜色</b></DIV></TD>
            <TD width="10%">
              <DIV align=center><b>规格</b></DIV></TD>
            <TD width="10%">
              <DIV align=center><b>数量</b></DIV></TD>
            <TD width="15%">
              <DIV align=center><b>单价</b></DIV></TD>
            <TD width="20%">
              <DIV align=center><b>金额</b></DIV></TD></TR>
        </thead>      
          <TBODY>      
          <TR>
            <TD >&nbsp;TB_AMJ006</TD>
            <TD >名称01</TD>
            <TD >浅灰色</TD>
            <TD >185/10</TD>
            <TD >1</TD>
            <TD >248.00</TD>
            <TD>248.00</TD>
        </TR>`
        +(function(){
            var body = [];
            for(var i=0;i<200;i++){
                body.push(`<TR>
                <TD >&nbsp;</TD>
                <TD >名称02</TD>
                <TD >&nbsp;</TD>
                <TD >&nbsp;</TD>
                <TD >2</TD>
                <TD >50</TD>
                <TD>100</TD>
            </TR>`)
            }
            return body.join("\n")
        }())+
          `<tfoot>
          <tr>
            <TD ><b>表格页脚</b></TD>
            <TD ><b>本页动态合计</b></TD>
            <TD ><b>&nbsp;</b></TD>
            <TD tdata="pageNO" format="#" align="left">
              <p align="center"><b>第<font color="#0000FF">#</font>页</b></p>
            </TD>
            <TD tdata="pageCount" format="#" align="left">
              <p align="center"><b>总<font color="#0000FF">##</font>页</b></TD>    
            <TD width="14%" align="right">　</TD>
            <TD width="19%" tdata="subSum" format="#,##0.00" align="right"><font color="#0000FF">###元</font></TD>    
         </tr>
          </tfoot>
        </TABLE>`
    }()
    ,
    ` <DIV style="LINE-HEIGHT: 30px" 
    align=center><font color="#0000FF">感谢您对我们雅瑞专卖店的支持，(发货单01的表格外“页脚”，紧跟表格)</font></DIV>`
    ,
    `<DIV style="LINE-HEIGHT: 30px" class=size16 align=center><STRONG><font color="#FF0000">销售发货单-02</font></STRONG></DIV>         
    <TABLE border=0 cellSpacing=0 cellPadding=0 width="100%">
      <TBODY>
      <TR>
        <TD width="43%"><font color="#FF0000">店铺名称：<SPAN 
          id=rpt_Pro_Order_List_ctl00_lbl_eShop_Name><b>黄阁专卖店</b></SPAN></font></TD>
        <TD width="33%"><font color="#FF0000">物流单号：<SPAN >2011050810373</SPAN></font></TD>
      </TR>
      <TR>
        <TD><font color="#FF0000">收 货 人：<SPAN >刘波</SPAN></font></TD>                                                  
        <TD><font color="#FF0000">网店订单：<SPAN>74235823905644</SPAN></font></TD>
      </TR>
      </TBODY></TABLE>
    </div>`
]

function PreviewMytable(LODOP,tables){
  LODOP.PRINT_INIT("打印控件功能演示_Lodop功能_分页打印综合表格");
  var strStyle="<style> table,td,th {border-width: 1px;border-style: solid;border-collapse: collapse}</style>"
  LODOP.ADD_PRINT_TABLE(128,"5%","90%",314,strStyle+tables[1]);
  LODOP.SET_PRINT_STYLEA(0,"Vorient",3);		
  LODOP.ADD_PRINT_HTM(26,"5%","90%",109,tables[0]);
  LODOP.SET_PRINT_STYLEA(0,"ItemType",1);
  LODOP.SET_PRINT_STYLEA(0,"LinkedItem",1);	
    LODOP.ADD_PRINT_HTM(444,"5%","90%",54,tables[2]);
  LODOP.SET_PRINT_STYLEA(0,"ItemType",1);
      LODOP.SET_PRINT_STYLEA(0,"LinkedItem",1);	
      
  LODOP.NewPageA();
    LODOP.ADD_PRINT_TABLE(128,"5%","90%",328,tables[1]);
  LODOP.SET_PRINT_STYLEA(0,"Vorient",3);	
  LODOP.ADD_PRINT_HTM(26,"5%","90%",80,tables[3]);
  LODOP.SET_PRINT_STYLEA(0,"ItemType",1);
  LODOP.SET_PRINT_STYLEA(0,"LinkedItem",4);	
  LODOP.ADD_PRINT_TEXT(460,96,"76.25%",20,"真诚祝您好远，欢迎下次再来！(发货单02的表格外“页脚”，紧跟表格)");
  LODOP.SET_PRINT_STYLEA(0,"LinkedItem",4);
  LODOP.SET_PRINT_STYLEA(0,"FontSize",12);
  LODOP.SET_PRINT_STYLEA(0,"FontColor","#FF0000");
  LODOP.SET_PRINT_STYLEA(0,"Alignment",2);
  LODOP.SET_PRINT_STYLEA(0,"ItemType",1);
  LODOP.SET_PRINT_STYLEA(0,"Horient",3);	
  LODOP.ADD_PRINT_HTM(1,600,300,100,"总页号：<font color='#0000ff' format='ChineseNum'><span tdata='pageNO'>第##页</span>/<span tdata='pageCount'>共##页</span></font>");

  LODOP.SET_PRINT_STYLEA(0,"ItemType",1);

  LODOP.SET_PRINT_STYLEA(0,"Horient",1);	
  LODOP.ADD_PRINT_TEXT(3,34,196,20,"总页眉：《两个发货单的演示》");
  LODOP.SET_PRINT_STYLEA(0,"ItemType",1);		
  LODOP.PREVIEW();
};


    return {
        el:"#app",
        data(){
          return {
            message:"测试",
            search:{
              fz:""
            },
            editor:{
              header:"查看",
              state:"view", // update delete view
              dialog:false,
              source:{}
            },
            viewer: {
              filter: {
                Gid: { Value: null, Match: '==' },
                Bname: { Value: null, Match: 'HAS', Order:'DESC' }
              },
              source: [],
              select: null,
              paging: {
                size: 20,
                page: 1,
                records: 20,
              }
            }
          }
        },
        created(){
          this.updateList();
        },
        methods:{
          checkLodop(){
            LodopFuncs.getSysInfo().then(function(txt){
              console.log(txt)
            })
          },
          printJSON(){
            LodopFuncs.loadLodop().then(function(LODOP){
              PreviewMytable(LODOP,tables);
            })
          },
          updateList(){
            this.$api.get("../mineral/data.json",{ 
              "filter": this.viewer.filter, 
              "paging": this.viewer.paging 
            }).then(x => {
              this.viewer.source = x.rows;
              this.viewer.paging.records = x.records;
            }).catch(function(ex){
              console.error(ex)
            });
          }
        }
    }
})