# iofpsocket

## 基于 kywebsocket 的扩展内容

### 方法
#### onCmdMsg 绑定事件
#### triggerMsg 触发事件
#### offCmdMsg 移除事件
#### onceCmdMsg 单次绑定

### 响应顺序

```js
iofpsocket.sendCommand("SConfig",data)

iofpsocket.onCmdMsg("RConfig",function(){

})
```
例:服务端返回 {H:"Open",I:"",C:"{}"}   
依次触发以下订阅
1. Open:33389

依次触发以下订阅
1. Open:33389
2. Open

例:服务端返回 {H:"Open",I:"SConfig:assMain",C:"{}"}   

依次触发以下订阅
1. Open:ass:page1:module1
2. Open:ass:page1
3. Open:ass
4. Open

### 示例

#### 订阅

```js
iofpsocket.onCmdMsg("Open") // 所有 H == Open
iofpsocket.onCmdMsg("RControl:TESTI") // 所有 H == RControl I==TESTI
iofpsocket.onceCmdMsg("Open") // 单次 H == Open
iofpsocket.onceCmdMsg("RControl:TESTI") // 单次 H == RControl I==TESTI
```

#### 发送

```js

// 服务器有回复时推荐使用此方式
iofpsocket.sendCommand("SConfig",data);
iofpsocket.sendCommand("SConfig",data,"context");
// 服务器有回复时推荐使用此方式
iofpsocket.sendCommand("SConfig",data,function(){
    // callback 只会触发一次
});
```

#### 综合示例

* 修改服务器数据发送频率

```js

$("#btnUpdataIntever").click(function(){
    iofpsocket.sendCommand("SConfig",{interval:2000});
})

iofpsocket.onCmdMsg("RConfig",function(data){
    // 更新数据展示
});

```

* 发送 操作命令

```js

iofpsocket.sendCommand("SConfig",{test:"data"},function(data){
    // alert("数据发送成功")
});

```

* debug

```js

// 请求服务端发送数据
iofpsocket.sendCommand("SConfig",{test:"data"},"page1debug");
iofpsocket.onCmdMsg("SConfig:page1debug",function(){
    // 只有 I === debug 才会触发
});

```

#### 其他示例

自定义扩展

```js
// 自定义事件分发
iofpsocket.onCmdMsg("RConfig",function(data){
    var C = JSON.parse(data.C);
    if(C.state=="opened"){
        this.triggerMsg("ControlOpened",C.message)
    }
    if(C.state=="closed"){
        this.triggerMsg("ControlClosed",C.message)
    }
})

iofpsocket.onCmdMsg("ControlOpened",function(){
    // 设备已开启
})
iofpsocket.onCmdMsg("ControlOpened",function(){
    // 设备已关闭
})
```


## kywebsocket

### 构造函数

### 事件

#### log 日志
#### ready 首次连接成功
#### open 连接成功
#### reconnected 重新连接成功
#### message 消息
#### close 关闭
#### error 异常

### ifpsocket 扩展事件

### 方法

#### refresh 重新连接
#### open 连接
#### send 发送指令，如果未连接成功，则在连接成功后执行
#### sendnow 发送指令，无视当前连接状态，若连接异常，则会触发error事件

### ifpsocket 扩展方法

### 示例

```js
var socket = new kywebsocket("ws://127.0.0.1:8181",{
    debug: false,
    autoOpen: true,
    reconnectInterval: 1000,
    maxReconnectInterval: 30000,
    reconnectDecay: 1.5,
    timeoutInterval: 2000,
    maxReconnectAttempts: null,
    binaryType: 'blob'
});

socket.connect()
.then(function(ws){
    console.log("连接成功");
});

socket.on("log",console.log,console);
socket.on("ready",console.log,console);

```
