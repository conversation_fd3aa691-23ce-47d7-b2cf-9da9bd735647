# 单元测试

## 开发方式

## 添加测试模块

* 在 module 中添加 js 文件,例如: module1.js
* 在 unit.test.json 中添加地址，module/module1.js

## 参考代码

```
define(["commons/converter"],function(converters){
    return function(QUnit){
        QUnit.module('common/converter.js', function() {
            QUnit.test('千分位', function(assert) {
                assert.equal(converters.thousands(99999999), "99,999,999");
            });
            QUnit.test('千分位-负数', function(assert) {
                assert.equal(converters.thousands(-99999999), "-99,999,999");
            });
        });
    }
})
```