﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <OutputType>Library</OutputType>
    <IsPackable>true</IsPackable>
    <AssemblyTitle>Build In $(Configuration) Mode With Machine $([System.Environment]::MachineName)</AssemblyTitle>
    <InformationalVersion>5.0.0.0</InformationalVersion>
    <AssemblyVersion>$([System.DateTime]::Now.ToString(yyyy.M.d)).$([System.TimeSpan]::FromHours($([System.DateTime]::Now.Hour)).Add($([System.TimeSpan]::FromMinutes($([System.DateTime]::Now.Minute)))).TotalMinutes)</AssemblyVersion>
    <FileVersion>$([System.DateTime]::Now.ToString(yyyy.M.d)).$([System.TimeSpan]::FromHours($([System.DateTime]::Now.Hour)).Add($([System.TimeSpan]::FromMinutes($([System.DateTime]::Now.Minute)))).TotalMinutes)</FileVersion>
    <Version>$([System.DateTime]::Now.ToString(yyyy.M.d)).$([System.TimeSpan]::FromHours($([System.DateTime]::Now.Hour)).Add($([System.TimeSpan]::FromMinutes($([System.DateTime]::Now.Minute)))).TotalMinutes)</Version>
    <Authors>刘慧</Authors>
    <Company>长沙开元仪器有限公司</Company>
    <Product>IFP</Product>
    <IncludeSymbols>true</IncludeSymbols>
    <PackageId>KY.IFP.Service</PackageId>
    <Description>IFP站点服务库

2021/10/18 刘慧
1、Service库建立上下文的用户标识改为依次从Cookie、Header和UToken取值（任意取到一个即可）。

2021/09/08 刘慧
1、Service库增加静态文件多路径、多目录虚拟合并支持，取消原页面目录配置项。

2021/09/07 刘慧
1、Service库取消原Pack参数设置，改为通过Header中的Content-Packet参数动态决定是否封包。

2021/08/16 刘慧
1、Runtime库升级至2021.8.16.709。

2021/08/05 刘慧
1、修复全局缓存Basics.Extend失效问题。

2021/08/04 刘慧
1、Runtime库升级至2021.8.4.948。
2、实现Runtime中的Http上下文抽象类并添加到全局缓存。

2021/08/02 刘慧
1、Service库开启GET方法支持。
2、Service库Server增加Pack配置，是否启用接口结果封装（{Success:bool是否成功,Message:string简要信息,Content:object调用结果/错误详情}）。
3、Service库Server增加Mime配置，可以自定义静态文件支持的后缀和ContentType。

2021/07/02 刘慧
1、Runtime库升级至2021.7.2.1043。

2021/05/05 刘慧
1、Runtime库升级至2021.5.5.1019。

2021/04/30 刘慧
1、Runtime库升级至2021.4.30.840。

2021/04/29 刘慧
1、Runtime库升级至2021.4.29.950。

2021/04/19 刘慧
1、Runtime库升级至2021.4.19.1025。
2、修复请求拦截为空时报错的问题。

2021/04/14 刘慧
1、Runtime库升级至2021.4.14.816。
2、优化了全局日志捕获机制。

2021/04/12 刘慧
1、增加Socket调用前台接口功能。
2、Runtime库升级至2021.4.12.1105。
3、修复接口Json序列化默认设置不生效的问题。

2021/04/10 刘慧
1、Runtime库更新至2021.4.10.700。

2021/04/07 刘慧
1、增加全局异常日志记录。

2021/04/02 刘慧
1、Runtime更新至版本2021.4.2.562。
2、更新Nuget包，自动输出.pdb文件。

2021/03/31 刘慧
1、Runtime更新至版本2021.3.31.930。
2、取消全局错误捕获。
3、增加HttpContext作为上下文的扩展对象。

2021/03/22 刘慧
1、Runtime更新至版本2021.3.12.1031。

2021/03/17 刘慧
1、增加Runtime更新至版本2021.3.17.614。
2、增加Runtime更新至版本2021.3.17.907。

2021/03/14 刘慧
1、增加日志截获功能。
2、增加监听端口、站点目录、页面目录配置，优化页面读取。
3、调整项目类型，简化引用依赖。

2021/03/13 刘慧
1、此版本文件上传功能尚未完善。
    </Description>    
    <Copyright>Copyright © 长沙开元仪器有限公司</Copyright> 
  </PropertyGroup>
	
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>KY.IFP.Service.xml</DocumentationFile>
  </PropertyGroup>
	
  <ItemGroup>
    <None Remove="KY.IFP.Service.xml" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="KY.IFP.Service.targets">
      <Pack>true</Pack>
      <PackagePath>buildTransitive\</PackagePath>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\KY.IFP.Runtime\KY.IFP.Runtime.csproj" />
  </ItemGroup>

  <Target Name="PushPackage" AfterTargets="Pack">
    <Exec WorkingDirectory="$(MSBuildProjectDirectory)" Command="dotnet nuget push $(PackageOutputPath)$(PackageId).$(Version).symbols.nupkg --api-key 123456 --source http://www.pfrj.com:8000/nuget" />
  </Target>
</Project>