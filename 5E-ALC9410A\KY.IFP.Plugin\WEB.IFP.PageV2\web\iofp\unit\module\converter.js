define(["commons/converter"],function(converters){

    var data = [
        ["千分位","thousands",[
            [[99999999],"99,999,999","正整数"],
            [[99999999.01],"99,999,999.01","正小整数"],
            [[-99999999],"-99,999,999","负整数"]
        ]],
        ["百分比","percent",[
            [[0.1],"10","正数"],
            [[-0.1],"-10","负数"]
        ]],
        ["自定义格式","custom",[
            [[200,"# 万元"],"200 万元","后缀加万元"]
        ]],
        ["固定小数长度","fixeddecimal",[
            [[30,5],"30.00000","固定5未小数"],
            [[-30,2],"-30.00","固定5未小数"]
        ]],
        ["条件公式转换","tjgsFormater",[
            [["1 = 1"],"1 = 1","不懂"],
            [[" 1==1"],"无","不懂"],
            [["2=1"],"2=1","啥意思？"]
        ]]
    ]

    return function(QUnit){
        QUnit.module('common/converter.js', function() {
            data.forEach(function(m){
                let name = m[0];
                let fn = m[1];
                let list = m[2];
                QUnit.test(name, function(assert) {
                    list.forEach(function(t){
                        let args = t[0];
                        let rev = t[1];
                        let text = JSON.stringify(args);
                        text=text.substr(1,text.length-2);
                        assert.equal(converters[fn].apply(converters,args),rev,`${fn}(${text}) => ${rev}`);
                    })
                });
            })
            
            /*
            QUnit.test('千分位', function(assert) {
                assert.equal(converters.thousands(99999999), "99,999,999","正整数");
                assert.equal(converters.thousands(99999999.01), "99,999,999.01","正小整数");
                assert.equal(converters.thousands(-99999999), "-99,999,999","负整数");
            });
            */
        });
    }
})