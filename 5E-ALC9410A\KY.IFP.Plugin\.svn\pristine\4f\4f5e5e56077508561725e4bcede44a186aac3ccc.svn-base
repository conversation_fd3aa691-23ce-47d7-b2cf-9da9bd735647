﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应商详情信息</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <div class="flex flex-item padding" id="app">
        <el-form ref="form" :rules="rules" :model="editor.source" label-width="120px">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="供应商名称" prop="Bname">
                        <ifp-input :disabled="state&&state=='view'" v-model="editor.source.Bname" placeholder="必填"></ifp-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="供应商简称">
                        <ifp-input :disabled="state&&state=='view'" v-model="editor.source.Sname"></ifp-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="组织机构代码">
                        <ifp-input :disabled="state&&state=='view'" v-model="editor.source.ZZJGDM"></ifp-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="供应商类型"
                                  prop="GHDWLX1047">
                        <ifp-select-ywlx v-model="editor.source.GHDWLX1047" :disabled="state&&state=='view'" :ywlx="1047"></ifp-select-ywlx>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="启停状态">
                        <el-radio-group v-model="editor.source.Zfbz">
                            <el-radio :disabled="state&&state=='view'" v-model="editor.source.Zfbz" :label="0">启用</el-radio>
                            <el-radio :disabled="state&&state=='view'" v-model="editor.source.Zfbz" :label="1">停用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="创建人">
                        <ifp-input v-model="editor.source.CREATOR" disabled></ifp-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="创建时间">
                        <el-date-picker style="width:182px" v-model="editor.source.Addtime"
                                        type="datetime"
                                        disabled>
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="更新时间">
                        <el-date-picker style="width:182px" v-model="editor.source.Lasttime"
                                        type="datetime"
                                        disabled>
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-form-item label="备注">
                    <ifp-input :disabled="state&&state=='view'"  type="textarea"
                              :autosize="{ minRows: 3, maxRows: 3}"
                              v-model="editor.source.REMARK">
                    </ifp-input>
                </el-form-item>
            </el-row>
            <el-form-item>
                <el-button :disabled="state&&state=='view'&&!$btnRight.B1" type="primary" @click="onSubmit">保存</el-button>
                <el-button @click="onCancel">取消</el-button>
            </el-form-item>
        </el-form>
    </div>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>