define(["iofp/vue-plugins/ifp-api"],function(api){
// 页面状态



    return {
        namespaced: true,
        state:{
            items:[
                {
                    id:"page001",
                    menukey:"",
                    pageurl:"",
                    pagejs:"",
                    tables:[
                        {
                            id:"",
                            hidecolumns:[], // 隐藏列id列表

                            // 结构化列配置
                            originColumns:[
                                {
                                    id:"",name:"col1",show:true,lable:"日期"
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        getters: {
            map(state){
                return Object.fromEntries(state.items.map(item=>[
                    item.id,Object.fromEntries(item.tables.map(t=>[t.id,{
                        hidecolumns:t.hidecolumns,
                        originColumns:t.originColumns
                    }]))
                ]))
            },
            // ...
            getPage: (state) => (pageid) => {
                const item = state.items.find(item=>item.id===pageid);
                if(!item) { return null; }
                return item;
            },
            // ...
            getTable: (state) => (pageid,tableid) => {
                const item = state.items.find(item=>item.id===pageid);
                if(!item) { return null; }
                let table = item.tables.find(t=>t.id==tableid);
                if(!table) { return null; }
                return table;
            }
        },
        mutations: {
            // 添加页面
            addPage(state,{id,url,menuurl,menukey,js}){
                const item = state.items.find(item=>item.id===id);
                if(!item){
                    state.items.splice(state.items.length-1,0,{
                        id,url,menukey,menuurl,js,tables:[]
                    })
                }
            },
            // 添加 table
            addTable(state,{pageid,id,hidecolumns,originColumns,columns}){
                const item = state.items.find(item=>item.id===pageid);
                if(!item) {
                    throw `state lxsz error : page ${pageid} 不存在`
                }

                let table = item.tables.find(t=>t.id==id);
                if(table) { return }
                
                item.tables.push({
                    id:id,
                    hidecolumns:hidecolumns||[],
                    originColumns,
                    columns
                })
            },
            // 设置隐藏列
            setHideColumns(state,{pageid,tableid,hidecolumns}){
                const item = state.items.find(item=>item.id===pageid);
                if(!item) { throw `state lxsz error : page ${pageid} 不存在` }
                let table = item.tables.find(t=>t.id==tableid);
                if(!table) { throw `state lxsz error : table ${tableid} 不存在` }
                table.hidecolumns.splice(0,table.hidecolumns.length,...hidecolumns)
            }
        },
        actions:{
            addPageAndTable({commit,dispatch},{page,table}){
                table.pageid = page.id;
                commit('addPage',page)
                commit('addTable',table)
                return dispatch('updateHideColumns',{pageid:page.id,tableid:table.id})
            },
            setHideColumns({commit,dispatch},{pageid,tableid,hidecolumns,save}){
                commit('setHideColumns',{pageid,tableid,hidecolumns})
                if(save){
                    return dispatch("submitHideColumns",{pageid,tableid});
                }
            },
            updateHideColumns({commit,dispatch,getters},{pageid,tableid}){
                let table = getters.getTable(pageid,tableid);
                return api.post("/API/IFP/www/sys/XslszAPI/Select", { 
                    Pageurl: pageid,//pageid, 
                    Tableid: tableid//tableid 
                }).then(x=>{
                    let columnArray = [];
                    if (x != null && x.Columns != null && x.Columns != "") {
                        columnArray = x.Columns.split(",");
                    }
                    table.hidecolumns.splice(0,table.hidecolumns.length,...columnArray)
                })
            },
            // 保存 table 隐藏列设置 到服务器
            submitHideColumns({commit,dispatch,getters},{pageid,tableid}){
                let table = getters.getTable(pageid,tableid);
                var formData = {
                    Pageurl: pageid,
                    Tableid: tableid,
                    Columns: table.hidecolumns.join(",")
                };
                return api.post("/API/IFP/www/sys/XslszAPI/Save", formData)
            }
        }
    }
})