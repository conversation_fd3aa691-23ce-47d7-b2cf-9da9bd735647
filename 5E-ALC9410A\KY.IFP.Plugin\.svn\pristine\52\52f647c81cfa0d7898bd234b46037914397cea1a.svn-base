define(function(){
    var WebSocketReadyState = {
        0:"CONNECTING",//正在建立连接连接
        1:"OPEN",//连接成功建立，可以进行通信
        2:"CLOSING",//连接正在进行关闭握手，即将关闭
        3:"CLOSED"//连接已经关闭或者根本没有建立
    }

    function KYWebSocket(host){
        this.webSocket = null;
        this.events={};
        this.onceevents = {};
        this.option={
            host:host||null
        }
    }

    KYWebSocket.prototype.connect = function(host){
        this.option.host = this.option.host || host;
        if(!this.option.host){
            throw "调用KYWebSocket.connect之前必须指定 ws 地址"
        }
        var _this = this;
        try{
            this.webSocket = new WebSocket("ws://"+this.option.host);
        }catch(ex){
            this.trigger("error",[ex])
            return;
        }
        ["open","message","close"].forEach(function(fnanme){
            _this.webSocket["on"+fnanme]=function(){
                _this.trigger(fnanme,Array.prototype.slice.call(arguments,0))
            }
        })
    }
    KYWebSocket.prototype.readyState = function(){
        if(!this.webSocket){
            return -1;
        }else{
            return this.webSocket.readyState
        }
    }
    KYWebSocket.prototype.trigger = function(name,args){
        var _this = this;
        this.events[name] && this.events[name].forEach(function(fn){
            fn.apply(_this,args)
        })

        if(this.onceevents[name]){
            this.onceevents[name].forEach(function(fn){
                fn.apply(_this,args)
            })
            this.onceevents[name] = [];
        }
    }
    KYWebSocket.prototype.on = function(name,callback){
        this.events[name]=this.events[name]||[];
        this.events[name].push(callback);
        if(name=="open" && this.readyState()==1){
            callback();
            return ;
        }
    }

    KYWebSocket.prototype.once = function(name,callback){
        this.onceevents[name]=this.onceevents[name]||[];
        this.onceevents[name].push(callback);
    }
    
    KYWebSocket.prototype.send = function(msg){
        var _this = this;
        return new Promise(function(resolve,reject){
            if(_this.readyState()!=1){
                _this.once("open",function(){
                    _this.webSocket.send(msg);
                    resolve(_this);
                })
            }else{
                _this.webSocket.send(msg);
                resolve(_this);
            }
        })
    }

    return KYWebSocket;
})