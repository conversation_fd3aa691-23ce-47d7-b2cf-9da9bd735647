﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using System.Text.Json;
using System.Threading.Tasks;

namespace KY.IFP.Runtime
{
    /// <summary>
    /// 插件组件
    /// </summary>
    public class Plugin
    {
        #region 公共对象
        /// <summary>
        /// 操作结果
        /// </summary>
        public class Return
        {
            /// <summary>
            /// 成功标识
            /// </summary>
            public bool Success { get; set; }
            /// <summary>
            /// 提示消息，一般为错误消息
            /// </summary>
            public string Message { get; set; }
            /// <summary>
            /// 调用结果
            /// </summary>
            public object Content { get; set; }

            /// <summary>
            /// 构造函数
            /// </summary>
            public Return() : this(true) { }

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="success">成功标识</param>
            public Return(bool success)
            {
                Success = success;
            }

            /// <summary>
            /// 设置成功标识
            /// </summary>
            /// <param name="success">成功标识</param>
            /// <returns>结果对象</returns>
            public Return SetSuccess(bool success)
            {
                Success = success;
                return this;
            }

            /// <summary>
            /// 设置提示消息，同时将Success设置为False
            /// </summary>
            /// <param name="message">提示消息</param>
            /// <returns>结果对象</returns>
            public Return SetMessage(string message)
            {
                return SetMessage(message, false);
            }

            /// <summary>
            /// 设置提示消息
            /// </summary>
            /// <param name="message">提示消息</param>
            /// <param name="success">成功标识</param>
            /// <returns>结果对象</returns>
            public Return SetMessage(string message, bool success)
            {
                Success = success;
                Message = message;
                return this;
            }

            /// <summary>
            /// 设置调用结果，同时将Success设置为True
            /// </summary>
            /// <param name="content">调用结果</param>
            /// <returns>结果对象</returns>
            public Return SetContent(object content)
            {
                return SetContent(content, true);
            }

            /// <summary>
            /// 设置调用结果
            /// </summary>
            /// <param name="content">调用结果</param>
            /// <param name="success">成功标识</param>
            /// <returns>结果对象</returns>
            public Return SetContent(object content, bool success)
            {
                Success = success;
                Content = content;
                return this;
            }

            /// <summary>
            /// 捕获异常填充
            /// </summary>
            /// <param name="catched">异常信息</param>
            /// <returns>结果对象</returns>
            public Return SetCatched(Exception catched)
            {
                Success = false;
                Message = catched.Message;
                Content = catched.StackTrace ?? Environment.StackTrace;
                return this;
            }
        }

        /// <summary>
        /// 通讯接口
        /// </summary>
        public interface Socket
        {
            /// <summary>
            /// 同步发送
            /// </summary>
            /// <param name="result">发送内容</param>
            void Send(object result);

            /// <summary>
            /// 异步发送
            /// </summary>
            /// <param name="result">发送内容</param>
            /// <returns>是否成功</returns>
            Task<bool> SendAsync(object result);
        }

        /// <summary>
        /// 加载对象
        /// </summary>
        public class Loaded
        {
            /// <summary>
            /// 已加载文件
            /// </summary>
            public static List<string> Sources { get; } = new List<string>();
            /// <summary>
            /// 已加载实例
            /// </summary>
            //public static Dictionary<string, List<Entity>> Entitys { get; private set; } = new Dictionary<string, List<Entity>>(StringComparer.OrdinalIgnoreCase);
            public static Dictionary<Type, Entity> Entitys { get; private set; } = new Dictionary<Type, Entity>();
            internal static List<Entity> entitys = new();
            /// <summary>
            /// 已加载方法
            /// </summary>
            public static Dictionary<string, List<Method>> Methods { get; private set; } = new Dictionary<string, List<Method>>(StringComparer.OrdinalIgnoreCase);
            internal static List<Method> methods = new();

            /// <summary>
            /// 已加载接口
            /// </summary>
            public static class Actions
            {
                /// <summary>
                /// 无参接口字典
                /// </summary>
                public static Dictionary<string, Action> Simples { get; } = new Dictionary<string, Action>(StringComparer.OrdinalIgnoreCase);
                /// <summary>
                /// 带参接口字典
                /// </summary>
                public static Dictionary<string, Action> Normals { get; } = new Dictionary<string, Action>(StringComparer.OrdinalIgnoreCase);
                /// <summary>
                /// 获取无参接口
                /// </summary>
                /// <param name="method">接口名称</param>
                /// <param name="action">接口对象</param>
                /// <returns>true成功，false失败</returns>
                public static bool GetSimple(string method, out Action action)
                {
                    return Simples.TryGetValue(method, out action);
                }
                /// <summary>
                /// 获取有参接口
                /// </summary>
                /// <param name="method">接口名称</param>
                /// <param name="action">接口对象</param>
                /// <returns>true成功，false失败</returns>
                public static bool GetNormal(string method, out Action action)
                {
                    return Normals.TryGetValue(method, out action);
                }
            }
            /// <summary>
            /// 已加载回调
            /// </summary>
            public static class Actives
            {
                /// <summary>
                /// 无参回调
                /// </summary>
                public static Dictionary<string, Action> Simples { get; } = new Dictionary<string, Action>(StringComparer.OrdinalIgnoreCase);
                /// <summary>
                /// 有参回调
                /// </summary>
                public static Dictionary<string, Action> Normals { get; } = new Dictionary<string, Action>(StringComparer.OrdinalIgnoreCase);
                /// <summary>
                /// 获取无参回调
                /// </summary>
                /// <param name="method">回调名称</param>
                /// <param name="action">回调对象</param>
                /// <returns>true成功，false失败</returns>
                public static bool GetSimple(string method, out Action action)
                {
                    if (Simples.TryGetValue(method, out action))
                    {
                        return true;
                    }
                    action = null;
                    return false;
                }
                /// <summary>
                /// 获取有参回调
                /// </summary>
                /// <param name="method">回调名称</param>
                /// <param name="action">回调对象</param>
                /// <returns>true成功，false失败</returns>
                public static bool GetNormal(string method, out Action action)
                {
                    return Normals.TryGetValue(method, out action);
                }
            }

            /// <summary>
            /// 刷新加载项
            /// </summary>
            public static void Refresh()
            {
                //Entitys = entitys.GroupBy(x => x.Location).ToDictionary(x => x.Key, x => x.OrderByDescending(y => y.Assembly.GetName().Version).ToList(), StringComparer.OrdinalIgnoreCase);
                foreach (var pair in Entitys)
                {
                    var types = pair.Key.BaseType is null ? pair.Key.GetInterfaces() : pair.Key.GetInterfaces().Append(pair.Key.BaseType);
                    foreach (var type in types)
                    {
                        if (Entitys.TryGetValue(type, out Entity entity) && !entity.Deriveds.Contains(pair.Value))
                        {
                            entity.Deriveds.Add(pair.Value);
                        }
                    }
                }
                Methods = methods.GroupBy(x => x.Location).ToDictionary(x => x.Key, x => x.OrderByDescending(y => y.Assembly.GetName().Version).ToList(), StringComparer.OrdinalIgnoreCase);
                foreach (var pair in Methods)
                {
                    foreach (var method in pair.Value)
                    {
                        method.Instance = method.Instance.Search(); //替换为最远派生
                    }
                }
            }
        }

        /// <summary>
        /// 通用接口
        /// </summary>
        public class Action
        {
            #region 内部对象
            /// <summary>
            /// 无参无返回接口
            /// </summary>
            class Func01 : Action
            {
                public Func01(Lazy<object> target, MethodInfo method) : base(target, method, typeof(System.Action)) { }

                public override object Invoke(JsonElement argument, Delegate callback = null)
                {
                    (Native as System.Action)();
                    return null;
                }
            }

            /// <summary>
            /// 无参有返回接口
            /// </summary>
            /// <typeparam name="O">返回参数</typeparam>
            class Func02<O> : Action
            {
                public Func02(Lazy<object> target, MethodInfo method) : base(target, method, typeof(Func<O>)) { }

                public override object Invoke(JsonElement argument, Delegate callback = null)
                {
                    return (Native as Func<O>)();
                }
            }

            /// <summary>
            /// 有参无返回接口
            /// </summary>
            /// <typeparam name="I">输入参数</typeparam>
            class Func03<I> : Action
            {
                public Func03(Lazy<object> target, MethodInfo method) : base(target, method, typeof(Action<I>)) { }

                public override object Invoke(JsonElement argument, Delegate callback = null)
                {
                    (Native as Action<I>)(argument.GetValue<I>());
                    return null;
                }
            }

            /// <summary>
            /// 有参有返回接口
            /// </summary>
            /// <typeparam name="I">输入参数</typeparam>
            /// <typeparam name="O">返回参数</typeparam>
            class Func04<I, O> : Action
            {
                public Func04(Lazy<object> target, MethodInfo method) : base(target, method, typeof(Func<I, O>)) { }

                public override object Invoke(JsonElement argument, Delegate callback = null)
                {
                    return (Native as Func<I, O>)(argument.GetValue<I>());
                }
            }

            /// <summary>
            /// 无参无返回回调
            /// </summary>
            /// <typeparam name="P">回调类型</typeparam>
            class Func05<P> : Action where P : Delegate
            {
                public Func05(Lazy<object> target, MethodInfo method) : base(target, method, typeof(Action<P>)) { }

                public override object Invoke(JsonElement argument, Delegate callback = null)
                {
                    (Native as Action<P>)((P)callback);
                    return null;
                }
            }

            /// <summary>
            /// 无参有返回回调
            /// </summary>
            /// <typeparam name="P">回调类型</typeparam>
            /// <typeparam name="O">返回参数</typeparam>
            class Func06<P, O> : Action where P : Delegate
            {
                public Func06(Lazy<object> target, MethodInfo method) : base(target, method, typeof(Func<P, O>)) { }

                public override object Invoke(JsonElement argument, Delegate callback = null)
                {
                    return (Native as Func<P, O>)((P)callback);
                }
            }

            /// <summary>
            /// 有参无返回回调
            /// </summary>
            /// <typeparam name="I">输入参数</typeparam>
            /// <typeparam name="P">回调类型</typeparam>
            class Func07<I, P> : Action where P : Delegate
            {
                public Func07(Lazy<object> target, MethodInfo method) : base(target, method, typeof(Action<I, P>)) { }

                public override object Invoke(JsonElement argument, Delegate callback = null)
                {
                    (Native as Action<I, P>)(argument.GetValue<I>(), (P)callback);
                    return null;
                }
            }

            /// <summary>
            /// 有参有返回回调
            /// </summary>
            /// <typeparam name="I">输入参数</typeparam>            
            /// <typeparam name="P">回调类型</typeparam>
            /// <typeparam name="O">返回参数</typeparam>
            class Func08<I, P, O> : Action where P : Delegate
            {
                public Func08(Lazy<object> target, MethodInfo method) : base(target, method, typeof(Func<I, P, O>)) { }

                public override object Invoke(JsonElement argument, Delegate callback = null)
                {
                    return (Native as Func<I, P, O>)(argument.GetValue<I>(), (P)callback);
                }
            }
            #endregion

            /// <summary>
            /// 创建接口
            /// </summary>
            /// <param name="target">调用对象</param>
            /// <param name="method">调用方法</param>
            /// <returns>通用接口</returns>
            public static Action Create(Lazy<object> target, MethodInfo method)
            {
                var inputs = method.GetParameters().Select(x => x.ParameterType).ToArray();
                var output = method.ReturnType;
                if (inputs.Length == 0)
                {
                    if (output == typeof(void))
                    {
                        return new Func01(target, method);
                    }
                    else
                    {
                        return (Action)Activator.CreateInstance(typeof(Func02<>).MakeGenericType(output), target, method);
                    }
                }
                else if (inputs.Length == 1)
                {
                    if (!inputs[0].IsAssignableTo(typeof(Delegate)))
                    {
                        if (output == typeof(void))
                        {
                            return (Action)Activator.CreateInstance(typeof(Func03<>).MakeGenericType(inputs[0]), target, method);
                        }
                        else
                        {
                            return (Action)Activator.CreateInstance(typeof(Func04<,>).MakeGenericType(inputs[0], output), target, method);
                        }
                    }
                    else
                    {
                        if (output == typeof(void))
                        {
                            return (Action)Activator.CreateInstance(typeof(Func05<>).MakeGenericType(inputs[0]), target, method);
                        }
                        else
                        {
                            return (Action)Activator.CreateInstance(typeof(Func06<,>).MakeGenericType(inputs[0], output), target, method);
                        }
                    }
                }
                else if (inputs.Length == 2)
                {
                    if (output == typeof(void))
                    {
                        return (Action)Activator.CreateInstance(typeof(Func07<,>).MakeGenericType(inputs[0], inputs[1]), target, method);
                    }
                    else
                    {
                        return (Action)Activator.CreateInstance(typeof(Func08<,,>).MakeGenericType(inputs[0], inputs[1], output), target, method);
                    }
                }
                else
                {
                    throw new Exception("参数个数不正确");
                }
            }

            /// <summary>
            /// 调用目标
            /// </summary>
            public object Target { get; }
            /// <summary>
            /// 调用方法
            /// </summary>
            public MethodInfo Method { get; }
            /// <summary>
            /// 动态委托
            /// </summary>
            internal Delegate Native { get; set; }
            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="target">调用对象</param>
            /// <param name="method">调用方法</param>
            /// <param name="action">委托类型</param>
            internal Action(Lazy<object> target, MethodInfo method, Type action)
            {
                Target = target;
                Method = method;
                Native = Delegate.CreateDelegate(action, method.IsStatic ? null : target.Value, method);
            }

            /// <summary>
            /// 执行方法
            /// </summary>
            /// <param name="argument">普通参数</param>
            /// <param name="callback">回调参数</param>
            /// <returns></returns>
            public virtual object Invoke(JsonElement argument, Delegate callback = null)
            {
                return null;
            }
        }

        /// <summary>
        /// 通用回调
        /// </summary>
        public class Active
        {
            #region 内部对象
            interface Invoke
            {
                Delegate Action { get; }
            }

            class Invoke<T> : Invoke
            {
                readonly Socket Socket;

                public Invoke(Socket socket)
                {
                    Socket = socket;
                }

                public Delegate Action
                {
                    get
                    {
                        return new Action<T>(x => Socket.Send(x));
                    }
                }
            }
            #endregion

            /// <summary>
            /// 创建委托
            /// </summary>
            /// <param name="action">委托类型</param>
            /// <param name="socket">通讯代理</param>
            /// <returns>委托对象</returns>
            public static Delegate Create(Type action, Socket socket)
            {
                var inputs = action.GetMethod("Invoke").GetParameters();
                if (inputs.Length == 0)
                {
                    return new System.Action(() => socket.Send(null));
                }
                else if (inputs.Length == 1)
                {
                    if (inputs[0].ParameterType == typeof(object))
                    {
                        return new Action<object>(x => socket.Send(x));
                    }
                    else
                    {
                        return ((Invoke)Activator.CreateInstance(typeof(Invoke<>).MakeGenericType(inputs[0].ParameterType), socket)).Action;
                    }
                }
                else
                {
                    throw new Exception("不支持多参数回调。");

                    #region 多参数回调，已弃用
                    //var delegateInvoke = action.GetMethod("Invoke");
                    //var delegateReturn = delegateInvoke.ReturnType;
                    //var delegateParams = delegateInvoke.GetParameters();

                    //var genericTypes = new List<Type>();
                    //if (delegateReturn != typeof(void)) genericTypes.Add(delegateReturn);
                    //Object = new JsonElement();
                    //var parameterExpressions = new List<ParameterExpression>();
                    //foreach (var parameter in delegateParams)
                    //{
                    //    Object = Object.SetValue(parameter.Name, null);
                    //    genericTypes.Add(parameter.ParameterType);
                    //    parameterExpressions.Add(Expression.Parameter(parameter.ParameterType));
                    //}

                    //var methodTarget = Expression.Constant(this);
                    //var methodBody = Expression.Call(methodTarget, "Callback", genericTypes.ToArray(), parameterExpressions.ToArray());
                    //var lambdaExpression = Expression.Lambda(action, methodBody, parameterExpressions.ToArray());
                    //return lambdaExpression.Compile();
                    #endregion
                }
            }

            #region 多参数回调，已弃用
            //private void ExecuteAsync(object[] arguments)
            //{
            //    object argument = null;
            //    if (arguments.Length == 1)
            //    {
            //        argument = arguments[0];
            //    }
            //    else
            //    {
            //        for (int i = 0; i < arguments.Length; i++)
            //        {
            //            Object = Object.SetValue(Object.EnumerateObject().ElementAt(i).Name, arguments[i]);
            //        }
            //        argument = Object;
            //    }
            //    Method.ExecuteAsync(argument);  //Method.ExecuteAsync(argument).Wait();
            //}

            //private void Callback<T1>(T1 t1)
            //{
            //    ExecuteAsync(new object[] { t1 });
            //}
            //private void Callback<T1, T2>(T1 t1, T2 t2)
            //{
            //    ExecuteAsync(new object[] { t1, t2 });
            //}
            //private void Callback<T1, T2, T3>(T1 t1, T2 t2, T3 t3)
            //{
            //    ExecuteAsync(new object[] { t1, t2, t3 });
            //}
            //private void Callback<T1, T2, T3, T4>(T1 t1, T2 t2, T3 t3, T4 t4)
            //{
            //    ExecuteAsync(new object[] { t1, t2, t3, t4 });
            //}
            //private void Callback<T1, T2, T3, T4, T5>(T1 t1, T2 t2, T3 t3, T4 t4, T5 t5)
            //{
            //    ExecuteAsync(new object[] { t1, t2, t3, t4, t5 });
            //}
            //private void Callback<T1, T2, T3, T4, T5, T6>(T1 t1, T2 t2, T3 t3, T4 t4, T5 t5, T6 t6)
            //{
            //    ExecuteAsync(new object[] { t1, t2, t3, t4, t5, t6 });
            //}
            //private void Callback<T1, T2, T3, T4, T5, T6, T7>(T1 t1, T2 t2, T3 t3, T4 t4, T5 t5, T6 t6, T7 t7)
            //{
            //    ExecuteAsync(new object[] { t1, t2, t3, t4, t5, t6, t7 });
            //}
            //private void Callback<T1, T2, T3, T4, T5, T6, T7, T8>(T1 t1, T2 t2, T3 t3, T4 t4, T5 t5, T6 t6, T7 t7, T8 t8)
            //{
            //    ExecuteAsync(new object[] { t1, t2, t3, t4, t5, t6, t7, t8 });
            //}
            //private void Callback<T1, T2, T3, T4, T5, T6, T7, T8, T9>(T1 t1, T2 t2, T3 t3, T4 t4, T5 t5, T6 t6, T7 t7, T8 t8, T9 t9)
            //{
            //    ExecuteAsync(new object[] { t1, t2, t3, t4, t5, t6, t7, t8, t9 });
            //}
            #endregion
        }

        #endregion

        #region 实体注入
        /// <summary>
        /// 实体注入
        /// </summary>
        public class Entity
        {
            /// <summary>
            /// 来源类库
            /// </summary>
            public Assembly Assembly { get; internal set; }
            /// <summary>
            /// 实体全名
            /// </summary>
            public string Location { get; internal set; }
            /// <summary>
            /// 实体对象
            /// </summary>
            public object Instance { get; internal set; }
            /// <summary>
            /// 实体类型
            /// </summary>
            public Type Describe { get; internal set; }
            /// <summary>
            /// 派生实体
            /// </summary>
            public List<Entity> Deriveds { get; internal set; }


            internal Entity(Assembly assembly, string location, Type describe)
            {
                Assembly = assembly;
                Location = location;
                Describe = describe;
                Deriveds = new List<Entity>();
            }

            internal Entity Search(string expect = null)
            {
                return Search(this, expect);
            }

            internal static Entity Search(Entity entity, string expect)
            {
                if (!string.IsNullOrWhiteSpace(expect))
                {
                    if (entity.Location.Equals(expect, StringComparison.OrdinalIgnoreCase)) return entity;
                    foreach (var derive in entity.Deriveds)
                    {
                        var target = Search(derive, expect);
                        if (target != null)
                        {
                            return target;
                        }
                    }
                }
                else
                {
                    if (entity.Deriveds.Count > 0)
                    {
                        return Search(entity.Deriveds[0], expect);
                    }
                    else
                    {
                        return entity;
                    }
                }
                return null;
            }

            internal Lazy<object> Create(params object[] args)
            {
                if (args.Length > 0)
                {
                    return new Lazy<object>(() => Assembly.CreateInstance(Location, true, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic, null, args, null, null));
                }
                else
                {
                    return new Lazy<object>(() => (Instance ??= Assembly.CreateInstance(Location, true, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic, null, null, null, null)));
                }
            }

            /// <summary>
            /// 创建实体
            /// </summary>
            /// <typeparam name="T">实体泛型</typeparam>
            /// <param name="name">优先类名</param>
            /// <returns>实体对象</returns>
            public static Lazy<T> Create<T>(string name = null)
            {
                return Create<T>(name, null);
            }

            /// <summary>
            /// 创建实体
            /// </summary>
            /// <typeparam name="T">实体泛型</typeparam>
            /// <param name="args">构造参数</param>
            /// <returns>实体对象</returns>
            public static Lazy<T> Create<T>(params object[] args)
            {
                return Create<T>(null, args);
            }

            /// <summary>
            /// 创建实体
            /// </summary>
            /// <typeparam name="T">实体泛型</typeparam>
            /// <param name="name">优先类名</param>
            /// <param name="args">构造参数</param>
            /// <returns>实体对象</returns>
            public static Lazy<T> Create<T>(string name, params object[] args)
            {
                //return new Lazy<T>(() =>
                //{
                //    var instance = default(T);
                //    if (Loaded.Entitys.TryGetValue(typeof(T).FullName, out List<Entity> entitys) && entitys.Count > 0)
                //    {
                //        foreach (var target in entitys)
                //        {
                //            if (target.Describe.IsAssignableTo(typeof(T)))
                //            {
                //                instance = (T)(target.Instance ?? target.Create().Value);
                //                break;
                //            }
                //        }
                //    }
                //    return instance;
                //});

                return new Lazy<T>(() =>
                {
                    if (Loaded.Entitys.TryGetValue(typeof(T), out Entity entity))
                    {
                        entity = entity.Search(name);
                        if (entity is null) throw new Exception($"找不到类型[{typeof(T)}]的实例[{name}]。");
                        return args?.Length > 0 ? (T)entity.Create(args).Value : (T)(entity.Instance ?? entity.Create().Value);
                    }
                    throw new Exception($"找不到对应的类型[{typeof(T)}]的实例。");
                });
            }
        }
        #endregion

        #region 方法注入
        /// <summary>
        /// 方法注入
        /// </summary>
        public class Method
        {
            /// <summary>
            /// 定义类库
            /// </summary>
            public Assembly Assembly { get; internal set; }
            /// <summary>
            /// 定义全名
            /// </summary>
            public string Location { get; internal set; }
            /// <summary>
            /// 实现实体
            /// </summary>
            public Entity Instance { get; internal set; }
            /// <summary>
            /// 方法信息
            /// </summary>
            public MethodInfo Function { get; internal set; }
            /// <summary>
            /// 方法委托
            /// </summary>
            public Delegate Invocate { get; internal set; }

            internal Method(Assembly assembly, Entity instance, MethodInfo function, string location)
            {
                Assembly = assembly;
                Instance = instance;
                Function = function;
                Location = location;
            }

            private static readonly List<Type> Delegate = new()
            {
                typeof(System.Action),
                typeof(Action<>),
                typeof(Action<,>),
                typeof(Action<,,>),
                typeof(Action<,,,>),
                typeof(Action<,,,,>),
                typeof(Action<,,,,,>),
                typeof(Action<,,,,,,>),
                typeof(Action<,,,,,,,>),
                typeof(Action<,,,,,,,,>),
                typeof(Action<,,,,,,,,,>),
                typeof(Action<,,,,,,,,,,>),
                typeof(Action<,,,,,,,,,,,>),
                typeof(Action<,,,,,,,,,,,,>),
                typeof(Action<,,,,,,,,,,,,,>),
                typeof(Action<,,,,,,,,,,,,,,>),
                typeof(Action<,,,,,,,,,,,,,,,>),
                typeof(Func<>),
                typeof(Func<,>),
                typeof(Func<,,>),
                typeof(Func<,,,>),
                typeof(Func<,,,,>),
                typeof(Func<,,,,,>),
                typeof(Func<,,,,,,>),
                typeof(Func<,,,,,,,>),
                typeof(Func<,,,,,,,,>),
                typeof(Func<,,,,,,,,,>),
                typeof(Func<,,,,,,,,,,>),
                typeof(Func<,,,,,,,,,,,>),
                typeof(Func<,,,,,,,,,,,,>),
                typeof(Func<,,,,,,,,,,,,,>),
                typeof(Func<,,,,,,,,,,,,,,>),
                typeof(Func<,,,,,,,,,,,,,,,>),
                typeof(Func<,,,,,,,,,,,,,,,,>),
            };

            /// <summary>
            /// 创建方法
            /// </summary>
            /// <typeparam name="T">方法泛型</typeparam>
            /// <typeparam name="E">所在实体</typeparam>
            /// <param name="function">方法名称</param>
            /// <returns>方法委托</returns>
            public static T Create<T, E>(Func<E, string> function) where T : Delegate
            {
                return Create<T>(typeof(E).FullName + "." + function(default));
            }

            /// <summary>
            /// 创建方法
            /// </summary>
            /// <typeparam name="T">方法泛型</typeparam>
            /// <param name="location">方法全名</param>
            /// <returns>方法委托</returns>
            public static T Create<T>(string location) where T : Delegate
            {
                var invocate = default(T);
                if (Loaded.Methods.TryGetValue(location, out List<Method> methods))
                {
                    foreach (var method in methods)
                    {
                        if (method.Invocate == null)
                        {
                            var entity = method.Instance.Instance ?? method.Instance.Create().Value;
                            var target = default(Type);
                            var inputs = method.Function.GetParameters().Select(x => x.ParameterType).ToArray();
                            var output = method.Function.ReturnType;
                            var offset = inputs.Length;
                            if (output != typeof(void))
                            {
                                inputs = inputs.Append(output).ToArray();
                                offset += 17;
                            }
                            target = Delegate[offset].MakeGenericType(inputs);
                            method.Invocate = method.Function.CreateDelegate(target, entity);
                        }
                        if (typeof(T).IsAssignableFrom(method.Invocate.GetType()))
                        {
                            invocate = (T)method.Invocate;
                            break;
                        }
                    }
                }
                return invocate;
            }

            /// <summary>
            /// 创建方法
            /// </summary>
            /// <typeparam name="T">方法泛型</typeparam>
            /// <param name="location">方法全名</param>
            /// <param name="invocate">方法输出</param>
            /// <returns>成功标识</returns>
            public static bool Create<T>(string location, out T invocate) where T : Delegate
            {
                invocate = Create<T>(location);
                return invocate != default(T);
            }


            ///// <summary>
            ///// 创建方法
            ///// </summary>
            ///// <param name="location">方法全名</param>
            ///// <param name="argument"></param>
            ///// <returns>方法委托</returns>
            //internal static Action Create(string location, JsonElement argument)
            //{
            //    if (Loaded.Methods.TryGetValue(location, out List<Method> methods))
            //    {
            //        foreach (var method in methods)
            //        {
            //            var xxx = method.Function.GetParameters();
            //            var action = default(Action);
            //            if (argument.ValueKind == JsonValueKind.Undefined && xxx.Length == 0)
            //            {
            //                action = Action.Create(new Lazy<object>(), method.Function);
            //            }
            //            else if (xxx.Length == 1 && xxx[0].ParameterType == typeof(JsonElement))
            //            {
            //                action = Action.Create(new Lazy<object>(), method.Function);
            //            }
            //            else if (xxx.Length == 2 && xxx[0].ParameterType == typeof(JsonElement) && xxx[1].ParameterType.IsAssignableTo(typeof(Delegate)))
            //            {
            //                action = Action.Create(new Lazy<object>(), method.Function);
            //            }
            //            if (action != null)
            //            {
            //                return action;
            //            }
            //        }
            //    }
            //    return null;
            //}
        }
        #endregion

        #region 控件加载
        private static Dictionary<string, string> depend = new();

        /// <summary>
        /// 导入插件，目录'api'下的所有dll，依赖路径为'com'
        /// </summary>
        public static void Import()
        {
            Import("api", "*.dll", "com");
        }

        /// <summary>
        /// 导入插件
        /// </summary>
        /// <param name="path">插件目录</param>
        /// <param name="part">匹配字符</param>
        /// <param name="deps">依赖目录</param>
        public static void Import(string path, string part, string deps)
        {
            try
            {
                var list = deps.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var fold in list)
                {
                    if (System.IO.Directory.Exists(fold))
                    {
                        foreach (var file in System.IO.Directory.GetFiles(fold, "*.dll"))
                        {
                            depend[System.IO.Path.GetFileNameWithoutExtension(file)] = System.IO.Path.GetFullPath(file);
                        }
                    }
                    else
                    {
                        Trace.Fail("目录'" + System.IO.Path.GetFullPath(fold) + "'不存在。");
                    }
                }
                if (System.IO.Directory.Exists(path))
                {
                    Import(System.IO.Directory.GetFiles(path, part, System.IO.SearchOption.AllDirectories));
                }
                else
                {
                    Trace.Fail("目录'" + System.IO.Path.GetFullPath(path) + "'不存在。");
                }
            }
            catch (Exception ex)
            {
                Trace.Fail(ex.Message, ex.InnerException + ex.StackTrace ?? Environment.StackTrace);
            }
        }

        /// <summary>
        /// 导入插件
        /// </summary>
        /// <param name="files">文件列表</param>
        public static void Import(params string[] files)
        {
            //var loader = new AssemblyLoadContext(null, true);
            var loader = AssemblyLoadContext.Default;
            loader.Resolving += Lanuch;
            foreach (var item in files)
            {
                var file = item;
                try
                {
                    file = System.IO.Path.GetFullPath(item);
                    if (Loaded.Sources.Contains(file)) continue;
                    //var assembly = Assembly.LoadFrom(file);                    
                    var assembly = loader.LoadFromAssemblyPath(file);
                    foreach (var type in assembly.GetExportedTypes())
                    {
                        try
                        {
                            var instance = new Entity(assembly, type.FullName, type);
                            //Loaded.entitys.Add(instance);
                            if (Loaded.Entitys.ContainsKey(type)) continue;
                            Loaded.Entitys.Add(type, instance);
                            if (!type.IsClass || type.IsAbstract || type.IsInterface || type.IsAssignableTo(typeof(Delegate))) continue;
                            //foreach (var function in type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.Static | BindingFlags.DeclaredOnly))
                            foreach (var function in type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.Static))
                            {
                                if (function.IsSpecialName || function.DeclaringType == typeof(object)) continue;   //排除属性的get/set方法和object的方法
                                var fullname = type.FullName + "." + function.Name;
                                Loaded.methods.Add(new Method(assembly, instance, function, fullname));

                                //daiabin
                                //Console.WriteLine(function.Name);
                                var argument = function.GetParameters();
                                if (argument.Length > 2) continue;
                                var invocate = new Func<Action>(() => Action.Create(instance.Create(), function));
                                if (argument.Length == 0)
                                {
                                    Loaded.Actions.Simples[fullname] = invocate();
                                }
                                else if (argument.Length == 1)
                                {
                                    if (argument[0].ParameterType == typeof(JsonElement))
                                    {
                                        Loaded.Actions.Normals[fullname] = invocate();
                                    }
                                    else if (argument[0].ParameterType.IsAssignableTo(typeof(Delegate)))
                                    {
                                        Loaded.Actives.Simples[fullname] = invocate();
                                    }
                                }
                                else if (argument.Length == 2)
                                {
                                    if (argument[0].ParameterType == typeof(JsonElement) && argument[0].ParameterType.IsAssignableTo(typeof(Delegate)))
                                    {
                                        Loaded.Actives.Normals[fullname] = invocate();
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Trace.Fail("插件'" + file + "'" + ex.Message, ex.InnerException + ex.StackTrace ?? Environment.StackTrace);
                        }
                    }
                    Loaded.Sources.Add(file);
                    Trace.TraceInformation("插件'" + file + "'加载完成。");
                }
                catch (Exception ex)
                {
                    Trace.Fail(ex.Message, ex.InnerException + ex.StackTrace ?? Environment.StackTrace);
                }
            }
            Loaded.Refresh();
        }

        private static Assembly Lanuch(AssemblyLoadContext load, AssemblyName name)
        {
            if (depend.ContainsKey(name.Name))
            {
                return load.LoadFromAssemblyPath(depend[name.Name]);
            }
            return null;    //表示转到默认加载
        }
        #endregion

        #region 获取接口
        /// <summary>
        /// 执行方法
        /// </summary>
        /// <param name="location">方法地址</param>
        /// <param name="argument">方法参数</param>
        /// <returns>执行结果</returns>
        public static object GetAction(string location, JsonElement argument)
        {
            location = location.Replace('/', '.');
            if (argument.ValueKind == JsonValueKind.Undefined)
            {
                //if (Loaded.Actions.GetSimple(location, out Action invocate))
                //{
                //    return invocate.Invoke(argument);
                //}
                if (Method.Create(location, out Func<object> returned))
                {
                    return returned();
                }
                if (Method.Create(location, out System.Action noreturn))
                {
                    noreturn();
                    return null;
                }
            }
            //else  //即时传入空参也可以尝试匹配下有参方法
            {
                //if (Loaded.Actions.GetNormal(location, out Action invocate))
                //{
                //    return invocate.Invoke(argument);
                //}
                if (Method.Create(location, out Func<JsonElement, object> returned))
                {
                    return returned(argument);
                }
                if (Method.Create(location, out Action<JsonElement> noreturn))
                {
                    noreturn(argument);
                    return null;
                }
            }
            throw new Exception("找不到对应的接口方法 " + location + "。");
        }
        #endregion

        #region 设置回调
        /// <summary>
        /// 设置回调
        /// </summary>
        /// <param name="location">注册方法</param>
        /// <param name="argument">注册参数</param>
        /// <param name="callback">事件句柄</param>
        /// <returns></returns>
        public static object SetActive(string location, JsonElement argument, Socket callback = null)
        {
            location = location.Replace('/', '.');
            if (callback == null) return GetAction(location, argument);
            if (argument.ValueKind == JsonValueKind.Undefined)
            {
                if (Loaded.Actives.GetSimple(location, out Action invocate))
                {
                    return invocate.Invoke(argument, Active.Create(invocate.Method.GetParameters().Last().ParameterType, callback));
                }
            }
            //else
            {
                if (Loaded.Actives.GetNormal(location, out Action invocate))
                {
                    return invocate.Invoke(argument, Active.Create(invocate.Method.GetParameters().Last().ParameterType, callback));
                }
            }
            throw new Exception("找不到对应的注册方法 " + location + "。");
        }
        #endregion
    }
}
