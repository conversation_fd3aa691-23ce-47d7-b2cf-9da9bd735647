﻿//using COM.IFP.LinqDB;
//using LinqToDB;
//using LinqToDB.Mapping;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace ORM.IFP
{
    /// <summary>
    /// 班次
    /// </summary>
    [SugarTable("IFP_BS_YWDX4005")]
    public partial class YWDX4005 : BaseData<YWDX4005>
    {
        /// <summary>
        /// GID
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public override Field<long> Gid { get; set; }

        /// <summary>
        /// 是否运行班次
        /// </summary>
        [SugarColumn(ColumnName = "SFYXBC1099", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "bigint")]
        public Field<long> Sfyxbc1099 { get; set; }

        /// <summary>
        /// 期别集合
        /// </summary>
        [SugarColumn(ColumnName = "QB4007", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(500)")]
        public Field<string> QB4007 { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [SugarColumn(ColumnName = "KSSJ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(10)")]
        public Field<string> Kssj { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [SugarColumn(ColumnName = "JSSJ", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "nvarchar(10)")]
        public Field<string> Jssj { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        [SugarColumn(ColumnName = "BEGINDATE", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Begindate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        [SugarColumn(ColumnName = "ENDDATE", SqlParameterDbType = typeof(FieldTypeConverter), ColumnDataType = "int")]
        public Field<int> Enddate { get; set; }
    }

    //[Table(Name = "IFP_BS_YWDX4005")]
    //public partial class YWDX4005 : BaseData<YWDX4005>
    //{
    //	/// <summary>
    //	/// GID
    //	/// </summary>
    //	[Column(Name = "GID", IsPrimaryKey = true, DataType = DataType.Int64, DbType = "bigint")]
    //	public override Field<long> Gid { get; set; }

    //	/// <summary>
    //	/// 是否运行班次
    //	/// </summary>
    //	[Column(Name = "SFYXBC1099", DataType = DataType.Int64, DbType = "bigint")]
    //	public Field<long> Sfyxbc1099 { get; set; }

    //	/// <summary>
    //	/// 期别集合
    //	/// </summary>
    //	[Column(Name = "QB4007", DataType = DataType.NVarChar, DbType = "nvarchar(500)")]
    //	public Field<string> QB4007 { get; set; }

    //	/// <summary>
    //	/// 开始时间
    //	/// </summary>
    //	[Column(Name = "KSSJ", DataType = DataType.NVarChar, DbType = "nvarchar(10)")]
    //	public Field<string> Kssj { get; set; }

    //	/// <summary>
    //	/// 结束时间
    //	/// </summary>
    //	[Column(Name = "JSSJ", DataType = DataType.NVarChar, DbType = "nvarchar(10)")]
    //	public Field<string> Jssj { get; set; }

    //	/// <summary>
    //	/// 开始日期
    //	/// </summary>
    //	[Column(Name = "BEGINDATE", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> Begindate { get; set; }

    //	/// <summary>
    //	/// 结束日期
    //	/// </summary>
    //	[Column(Name = "ENDDATE", DataType = DataType.Int32, DbType = "int")]
    //	public Field<int> Enddate { get; set; }
    //}

    /// <summary>
    /// 班次
    /// </summary>
    public partial class YWDX4005
    {
		/// <summary>
		/// 期别4007 来自子表的数据
		/// </summary>
		public Field<long> QB { get; set; }

		/// <summary>
		/// 采样周期
		/// </summary>
		public Field<int> CYZQ { get; set; }

		/// <summary>
		/// 对应子表的Gid
		/// </summary>
		public Field<string> CYZQGid { get; set; }
	}


	/* 以下为对应的Json对象
	{
		Gid: null,		//GID
		Sfyxbc1099: null,		//是否运行班次
		Kssj: null,		//开始时间
		Jssj: null,		//结束时间
		Begindate: null,
		Enddate: null
	}
	*/
}
