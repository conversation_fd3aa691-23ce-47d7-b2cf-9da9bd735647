/*
 * 列出当前目录下的字体
 * 供 kyadmin-ui-vlue.less 使用 
 */
/**
  * kyadmin-ui-vue 
  * 基于 element-ui 的样式
  */
body.kyadmin-ui.kyadmin-ui-vue {
  /*
    .el-table {
        th{
            background:#E6EAED;
            color:#333;
        }
        .el-table__header{
        }
    }
        */
}
body.kyadmin-ui.kyadmin-ui-vue label.el-radio {
  margin-bottom: unset;
  margin-right: 20px;
}
body.kyadmin-ui.kyadmin-ui-vue .el-dialog__wrapper.subpage > .el-dialog,
body.kyadmin-ui.kyadmin-ui-vue .el-dialog__wrapper > .el-dialog.subpage {
  /*
        .el-dialog__body{
            padding:0;
        }
        */
}
body.kyadmin-ui.kyadmin-ui-vue .el-dialog__wrapper.subpage > .el-dialog > .el-dialog__header,
body.kyadmin-ui.kyadmin-ui-vue .el-dialog__wrapper > .el-dialog.subpage > .el-dialog__header {
  background: var(--bg-main);
  padding: 8px;
}
body.kyadmin-ui.kyadmin-ui-vue .el-dialog__wrapper.subpage > .el-dialog > .el-dialog__header > .el-dialog__title,
body.kyadmin-ui.kyadmin-ui-vue .el-dialog__wrapper > .el-dialog.subpage > .el-dialog__header > .el-dialog__title {
  color: var(--color-main);
  font-size: 16px;
}
body.kyadmin-ui.kyadmin-ui-vue .el-button .dicb {
  font-weight: 400;
  line-height: 0;
  font-size: unset;
  vertical-align: baseline;
}
body.kyadmin-ui.kyadmin-ui-vue .el-button .dicb + span {
  margin-left: 5px;
}
body.kyadmin-ui.kyadmin-ui-vue .el-button .glyphicons {
  font-weight: 400;
  line-height: 0;
  font-size: unset;
  vertical-align: baseline;
}
body.kyadmin-ui.kyadmin-ui-vue .el-button .glyphicons + span {
  margin-left: 5px;
}
body.kyadmin-ui.kyadmin-ui-vue .el-button .kjicon {
  font-weight: 400;
  line-height: 0;
  font-size: unset;
  vertical-align: baseline;
}
body.kyadmin-ui.kyadmin-ui-vue .el-button .kjicon + span {
  margin-left: 5px;
}
body.kyadmin-ui.kyadmin-ui-vue .el-button .glyphicon {
  font-weight: 400;
  line-height: 0;
  font-size: unset;
  vertical-align: baseline;
}
body.kyadmin-ui.kyadmin-ui-vue .el-button .glyphicon + span {
  margin-left: 5px;
}
body.kyadmin-ui.kyadmin-ui-vue .toolbar .el-button + .el-button {
  margin-left: 0;
}
body.kyadmin-ui.kyadmin-ui-vue {
  /*
    .toolbar .el-input,
    .searchbar .el-input{
        width: 180px;
    }
    .toolbar,.searchbar{
        flex-shrink: 0;
    }
    .toolbar .el-input,
    .searchbar .el-input{
        width: 180px;
    }
    */
  /*
    .el-form-item{
        display:flex;
        .el-form-item__content{
            flex:1;
        }
    }
    */
}
body.kyadmin-ui.kyadmin-ui-vue .el-main {
  padding: 1rem;
}
body.kyadmin-ui.kyadmin-ui-vue .subpage .el-dialog__body {
  padding: 0;
}
body.kyadmin-ui.kyadmin-ui-vue .kya-searchbar {
  padding: 0;
  position: relative;
}
body.kyadmin-ui.kyadmin-ui-vue .kya-searchbar .kya-searchbar-folder {
  text-align: right;
  position: absolute;
  height: 1.2rem;
  width: 100%;
  padding-right: 1rem;
  color: #999;
}
body.kyadmin-ui.kyadmin-ui-vue .kya-searchbar .kya-searchbar-folder > span {
  line-height: 1rem;
  top: -3px;
}
body.kyadmin-ui.kyadmin-ui-vue .kya-searchbar .kya-searchbar-folder:hover {
  background-color: var(--bg-primary);
  color: var(--color-primary);
}
body.kyadmin-ui.kyadmin-ui-vue .kya-searchbar + * {
  margin-top: 0;
}
