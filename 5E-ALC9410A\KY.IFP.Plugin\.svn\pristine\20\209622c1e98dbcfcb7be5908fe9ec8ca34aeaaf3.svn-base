define(["jclass","controller"],function(jclass,base){
    return jclass(base,{
        selectid:function(){
            alert(this.controls.tabs.select().id)
        },
        select2:function(){
            this.controls.tabs.selectIndex(1)
        },
        onLoad:function(){
            console.log(this.controls.tabs.select().id)

            this.controls.log.value(this.controls.tabs.select().id);
            this.controls.tabs.bind("show",(ename,id)=>{
                this.controls.log.value(id);
            })
        }
    })
})