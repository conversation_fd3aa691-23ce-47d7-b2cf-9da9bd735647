﻿using COM.IFP.Common;
using ORM.IFP.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace API.ICS.BaseData
{
    public class Mineral
    {
        readonly Lazy<DAL.ICS.BaseData.Mineral> lazy = Entity.Create<DAL.ICS.BaseData.Mineral>();

        readonly Lazy<DAL.IFP.BaseData> baseLazy = Entity.Create<DAL.IFP.BaseData>();
        /// <summary>
        /// 查询矿点，一般用于矿点与供应商联动
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public List<ORM.IFP.YWDX4001> GetYwlxSelect(JsonElement json)
        {
            ORM.IFP.YWDX4001 param = json.GetValue<ORM.IFP.YWDX4001>();

            List<ORM.IFP.YWDX4001> list = baseLazy.Value.Select<ORM.IFP.YWDX4001>(new[] { param });

            return list;
        }

        public object Select(JsonElement json)
        {
            var kdxx = json.GetValue<List<ORM.IFP.YWDX4001>>("filter.kdxx");
            var ghdw = json.GetValue<List<ORM.IFP.BaseData>>("filter.ghdw");
            var fzxx = json.GetValue<List<ORM.IFP.BaseData>>("filter.fzxx");
            var page = json.GetValue<PageModel>("paging");
            return lazy.Value.Select(kdxx, ghdw, fzxx, page);
        }

        public void Submit(JsonElement json)
        {
            lazy.Value.Submit(json.GetValue<List<ORM.IFP.YWDX4001>>());
        }

        public void Delete(JsonElement json)
        {
            lazy.Value.Delete(json.GetValue<List<ORM.IFP.YWDX4001>>());
        }
    }
}
