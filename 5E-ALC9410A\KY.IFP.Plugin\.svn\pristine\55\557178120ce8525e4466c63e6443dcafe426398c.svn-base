define(["jclass","form"],function(jclass,base){
	return jclass(base,{
		render:function(){
			this.$container.addClass("report-group");
			if(this.option.title){
				this.$container.prepend("<div class='report-group-header'>"+ this.option.title +"</div>")
			}
		},
		init:function(ele,inputer,controler){
			var rev = this.base.apply(this,arguments);
			//$(ele).addClass("area-toolbar padding");
			return rev;
		}
	})
});
