/**
 * ifp 的 Vue 插件
 */
define([
"module",
'sysSetting',
    "iofp/mixins/components/index",
    "iofp/vue-plugins/ifp-api",
    "iofp/vue-plugins/table-formatter",
    "iofp/mixins/page",
    "iofp/btnRight"
],function(module,sysSetting,components,api,TableFormatter,page,btnRight){
    let ifpRequire = module.config();

    let useBtnRight = true,debug=false;
    // 页面配置了 option="{btnRight:false}"
    if(sysSetting.btnRight===false){
        useBtnRight = false;
        debug&&console.log('页面配置了 option="{btnRight:false}"');
    } 
    // starter.js 配置了 require.config['/iofp/vue-plugin-ifp.js']={useBtnRight:false}
    else if(ifpRequire.useBtnRight==false){
        useBtnRight = false;
        debug&&console.log(`starter.js 配置了 require.config['/iofp/vue-plugin-ifp.js']={useBtnRight:false}`);
    }


    return {
        install:function(Vue,Config){

            for(var a in components){
                Vue.component(a,components[a]);
            }

            Vue.prototype.$api = api;
            
            Vue.prototype.$TableFormatter = TableFormatter;
            
            Vue.mixin(page)

            if(useBtnRight){
                Vue.mixin(btnRight)
            }
            
            console.log("vue-plugin-ifp installed")
        }
    }
})