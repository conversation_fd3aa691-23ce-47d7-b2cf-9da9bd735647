﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ORM.IFP;
using COM.IFP.SqlSugarN;
using SqlSugar;
using ORM.IFP.DbModel;

namespace COM.IFP.Dictionary
{
    /// <summary>
    /// 数据字典辅助方法
    /// </summary>
    public class DictionaryHelper
    {
        public bool IsDescendant(IFP_BS_BASEINFO_DICTIONARY item, long ancestorId)
        {
            using SqlSugarClient db = DB.Create();
            if (item.Pgid.HasValue)
            {
                if (item.Pgid.Value == ancestorId)
                {
                    return true;
                }

                //递归检查
                var parent = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>()
                    .First(i => i.Gid == item.Pgid);
                if (parent != null)
                {
                    return IsDescendant(parent, ancestorId);
                }
            }
            return false;
        }

        public void UpdateChildrenTreeCode(long parentId, string oldPrefix, string newPrefix)
        {
            using SqlSugarClient db = DB.Create();
            //查找所有直接子节点
            var children = db.Queryable<IFP_BS_BASEINFO_DICTIONARY>()
                .Where(i => i.Pgid == parentId)
                .ToList();

            foreach (var child in children)
            {
                //更新子节点的TreeCode
                if (!string.IsNullOrEmpty(child.TreeCode.Value) && child.TreeCode.Value.StartsWith(oldPrefix))
                {
                    child.TreeCode = child.TreeCode.Value.Replace(oldPrefix, newPrefix);
                    db.Updateable(child).ExecuteCommand();

                    //递归更新子节点的子节点
                    UpdateChildrenTreeCode(child.Gid.Value, oldPrefix, newPrefix);

                }
            }
        }

    }
}
