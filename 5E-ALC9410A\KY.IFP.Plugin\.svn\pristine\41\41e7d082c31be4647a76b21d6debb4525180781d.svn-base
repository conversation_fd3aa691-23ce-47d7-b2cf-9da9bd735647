﻿using COM.IFP.Common;
//using COM.IFP.LinqDB;
using COM.IFP.SqlSugarN;
using ORM.IFP;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace API.ICS.BaseData
{
    public class Ywdx4030API
    {
        readonly Lazy<DAL.IFP.BaseData> lazy = Entity.Create<DAL.IFP.BaseData>();

        public PageModel<YWDX4030> Select(JsonElement json)
        {
            IList<YWDX4030> filter = json.GetValue<IList<YWDX4030>>("filter");
            PageModel<YWDX4030> page = json.GetValue<PageModel<YWDX4030>>("paging");
            lazy.Value.Select(filter, page);
            return page;
        }

        //根据GID查找对象
        public YWDX4030 GetByGId(JsonElement json)
        {
            YWDX4030 entity = json.GetValue<ORM.IFP.YWDX4030>();
            entity.Gid = new Field<long>(entity.Gid.Value) { Match = Match.EQU };
            List<YWDX4030> list = lazy.Value.Select<YWDX4030>(new[] { entity }, null);
            return list[0];
        }

        //保存
        public PFActionResult Submit(JsonElement json)
        {
            PFActionResult result = new PFActionResult();

            //待保存的对象
            YWDX4030 entity = json.GetValue<ORM.IFP.YWDX4030>();
            //如果简称没有填值，默认和名称一样
            if (!entity.Sname.HasValue || string.IsNullOrEmpty(entity.Sname.Value))
            {
                entity.Sname = new Field<string>(entity.Bname.Value);
            }
            if (!entity.Gid.HasValue)
            {
                entity.Addtime = new Field<DateTime>(DateTime.Now);
            }
            entity.Lasttime = new Field<DateTime>(DateTime.Now);

            //保存前，判断名称是否重复
            YWDX4030 param = new YWDX4030();
            param.Ywlx = new Field<string>(entity.Ywlx.Value) { Match = Match.EQU };
            param.Bname = new Field<string>(entity.Bname.Value) { Match = Match.EQU };
            List<YWDX4030> list = lazy.Value.Select<YWDX4030>(new[] { param }, null);
            if (list.Count > 0 && (!entity.Gid.HasValue || entity.Gid.Value != list[0].Gid.Value))
            {
                result.success = false;
                result.msg = "班次名称不能重复。";
                return result;
            }
            
            entity.KSSJ = Convert.ToDateTime(entity.KSSJ).ToString("HH:mm:ss");
            entity.JSSJ = Convert.ToDateTime(entity.JSSJ).ToString("HH:mm:ss");
            //保存实体
            lazy.Value.Submit(entity);

            result.success = true;
            result.msg = "保存成功。";
            result.data = entity;

            return result;
        }

        public void Delete(JsonElement json)
        {
            var item = json.GetValue<ORM.IFP.YWDX4030>();
            lazy.Value.Delete(item);
        }
    }
}
