define(["module","lodop","ramda","jquery"],function(module,LodopFuncs,R,$){
	var printConfig = $.extend(true,{
		css:"/kyadmin/css/print.report.css",//"print.report.css"
	},module.config());
	
	/*
	var json = [
		{
			type:"title",
			text:"标题"
		},
		{
			type:"row",
			items:[
				{text:"生产部",width:"33%"},
				{text:"2018-11-06",width:"33%",style:"text-align:center"},
				{text:"制单人：张三",width:"33%",style:"text-align:right"}
			]
		},
		{
			type:"table",
			head:[
				[{text:"序号",class:"center"},"列1","列2","列3"]
			],
			body:[
				["序号","列1","列2",""],
				["序号","列1","列2","列3"],
				["序号","列1","列2","列3"],
				["序号","列1","列2","列3"],
				["序号","列1","列2","列3"],
				["序号","列1","列2","列3"],
				["序号","列1","列2","列3"],
				["序号","列1","列2","列3"],
				["序号","列1","列2","列3"]
			]
		},
		{
			type:"datatable",
			rownumber:false,
			cols:[
				{name:"c1",title:"列1",thstyle:"width:100px;",tdstyle:"text-align:center;"},
				{name:"c1",title:"列2"}
			],
			data:[
				{c1:1,index:2}
			]
		},
		{type:"grid",id:"grid"},
		{type:"echart",id:"mcshow"},
		{type:"html",html:""}
	]*/
	
	
	//生成单个element元素代码
	var cEle=function(item,pTagName){
		var tagName = pTagName || "span"
		var opt = {
			style:[],
			text:"",
			"class":[]
		};
		if(typeof pTagName == "object"){
			var $ele = $(pTagName);
			tagName = $ele[0].tagName;
			var sty = $ele.attr("pintstyle");
			if(sty){
				opt.style.push(sty);
			}
		}
		var rev = ["<"+tagName];
		if(!item){
			opt.text = "&nbsp;"
		} else if(typeof item == "string"){
			opt.text = item;
		} else if(item.nodeType){
			var $ele = $(item);
			if(item.id){
				opt.id = item.id;
			}else{
				var printStyle = $ele.data("print-style");
				if(printStyle){
					opt.style.push(printStyle)
				}
			}
		} else {
			opt.text = typeof item.text === "number"?item.text:(item.text||"");
			if(item.id){
				rev.push("id=\""+item.id+"\"");
			}
			if(item.width){
				opt.style.push("width:"+item.width)
			}
			if(item.style){
				opt.style.push(item.style)
			}
			if(item["class"]){
				opt["class"].push(item["class"])
			}
			if(item.attrs){
				rev.push(item.attrs.join(" "));
			}
			if(item.format){
				var fn;
				if(typeof item.format == "function"){
					fn = item.format;
				}
				if(typeof item.format == "string"){
					//fn = this[item.format]||F.common.converter[item.format]||null
				}
				opt.text = fn&&fn(opt.text)||opt.text;
			}
		}
		if(opt.style.length){
			rev.push("style=\""+opt.style.join(";")+"\"");
		}
		if(opt["class"].length){
			rev.push("class=\""+opt["class"].join(" ")+"\"");
		}
		rev.push(">"+(opt.text&&opt.text.replace&&opt.text.replace(/\n/g,"<br/>")||opt.text)+"</"+tagName+">");
		return rev.join("\n")
	}
	
	//生成html代码片段
	var creater = {
		row:function(controller,opt){
			return "<table><tr>"+opt.items.map(function(item){
				return cEle.call(controller,item,"td")
			}).join("\n")+"</tr></table>"
		},
		title:function(controller,opt){
			return cEle.call(controller,{text:opt.text,"class":'report-title'},"div")
		},
		datatable:function(controller,opt){
			var rev = ["<table style='"+ (opt.style||"") +"' class='datatable "+ (opt.class||"") +"'>"]
			opt.cols&&opt.cols.length&&rev.push("<thead><tr>"+opt.cols.map(function(col){
				var attr = "";
				col.thstyle&&(attr+=" style='"+ col.thstyle +"'")
				return "<th "+ attr +">"+(col.title||"&nbsp")+"</th>"
			}).join("\n")+"</tr></thead>");
			
			opt.data&&opt.data.length&&rev.push("<tbody>"+opt.data.map(function(item){
				return "<tr>"+opt.cols.map(function(col){
					var attr = "";
					col.tdstyle&&(attr+=" style='"+ col.tdstyle +"'")
					return "<td"+attr+">"+(item[col.name]||"&nbsp")+"</td>"
				}).join("\n")+"</tr>"
			}).join("\n")+"</tr></tbody>");
			rev.push("</table>");
			return rev.join("");
		},
		table:function(controller,opt){
			var rev = ["<table style='"+ (opt.style||"") +"' class='datatable "+ (opt.class||"") +"'>"]
			opt.head&&opt.head.length&&rev.push("<thead>"+opt.head.map(function(tr){
				return "<tr>"+tr.map(function(col){
					return cEle.call(controller,col,"th");
				}).join("\n")+"</tr>"
			}).join("\n")+"</tr></thead>");
			
			opt.body&&opt.body.length&&rev.push("<tbody>"+opt.body.map(function(tr){
				return "<tr>"+tr.map(function(col){
					return cEle.call(controller,col,"td");
				}).join("\n")+"</tr>"
			}).join("\n")+"</tr></tbody>");
			rev.push("</table>");
			
			return rev.join("");
		},
		grid:function(controller,opt){
			var _this = this;
			var grid = controller.controls[opt.id];
			
			var cols = grid.grid("getGridParam", "colModel").map(function(item,i){
				var rev = $.extend(true,{
					title: grid.option.colNames[i]||"&nbsp"
				},item);
				return rev;
			});
			
			var value = opt.value || grid.getVO();
			var colcount = 0;

			var container = grid.getGridContainer();
			var thead = container.find(".ui-jqgrid-htable");
			var rev = thead.html();
			var headbox = $(rev).wrap("div");
			/*
			if(grid.option.multiselect){
				headbox.find("tr:eq(0)").remove();
			}
			*/
			colcount = thead.find("tr:eq(0) th:visible").length;
			
			var pageheader = "";
			if(opt.pageheader){
				pageheader = "<tr><th class='grid-header' colspan='" + colcount + "'>" + opt.pageheader.map(function(itemopt){
					return _this[itemopt.type](controller,itemopt)
				}).join("\n") + "</th></tr>";
			}
			

			var pagefooter = [];
			if(opt.printPageNo)
			{
				pagefooter.push("<tr><th class='grid-footer' colspan='" + colcount + "'>" +
					'当前是第<font tdata="PageNO" format="ChineseNum" color="blue">##</font>'+
					'页/共<font tdata="PageCount" format="ChineseNum" color="blue">##</font>页，'+
					'本页从第<font color="blue" format="00" tdata="Count-SubCount+1">##</font>行到第<font color="blue" tdata="Count">##</font>行'+
				"</th></tr>");
			}
			//headbox.find("tr.jqg-first-row-header").remove();
			headbox.find("tr:first th").each(function(i,item){
				if(cols[i].name == "rn"){
					//
				}else if(cols[i].print&&cols[i].print.width){
					$(this).width(cols[i].print.width);
				}else{
					$(this).width("auto");
				}
				$(this).html($(this).text().trim()||"&nbsp;");
			});
			
			//删除选择行
			headbox.find("#"+grid.id+"_cb").remove();
			
			thead = "<thead>"+pageheader+headbox.html()+"</thead>";
			var tbody = "";
			if(grid.option.pager){
				tbody = "<tbody>"+value.map(function(item,i){
					var rev = "<tr>"
					return rev+cols.filter(function(col){
						return col.hidden == false && col.name!="cb" && col.name!="gid";
					}).map(function(col){
						return "<td>"+(item[col.name]||"&nbsp;")+"</td>"
					}).join("\n")+"</tr>"
				}).join("\n")+"</tbody>"
			}else{
				var gettext = function(ele){
					return $("<div>" + ele.html().replace(/<br>/g,"####") + "</div>")
					.text()
					.replace(/\#\#\#\#/g,"<br/>")
				}
				var tbody = container.find(".ui-jqgrid-btable");
				if(JSON.stringify(grid.footerData()) != "{}"){
					tbody = "<tbody>" + tbody.find("tbody").html() + container.find(".ui-jqgrid-ftable tbody").html() + "</tbody>";
				}else{
					tbody = tbody.html()
				}
				var bodybox = $(tbody).wrap("div");
				bodybox.find("tr.jqgfirstrow").remove();
				bodybox.find("td[aria-describedby="+ grid.id +"_cb]").remove();
				
				
				bodybox.find("td").each(function(){
					$(this).html(gettext($(this)).trim()||"&nbsp;");
				});
				tbody = "<tbody>"+bodybox.html()+"</tbody>";
				
			}
			
			var caption = ""
			if(opt.caption){
				caption+=cEle.call(controller,opt.caption,"div");
			}
			caption="<caption>"+caption+"</caption>";
			var tfoot = "";
			if(pagefooter.length) {
				tfoot = "<tfoot>"+ pagefooter.join("\n") +"</tfoot>"
			}
			return "<table id='"+ grid.id +"' class='datatable grid'>"+caption+thead+tbody+tfoot+"</table>";
		},
		echart:function(controller,opt){
			var echartbox = $("#"+opt.id);
			var chart = echartbox.data().control.echart;
			var data = chart.getDataURL({
				pixelRatio:700/echartbox.width()
			});
			return "<image style='margin-top:10px' src='"+ data +"' />";
		},
		html:function(controller,item){
			return item.html;
		}
	}
	
	var util = {
		//获取html by json 配置
		getPrintHtmlByJson : function(name,option,data,css){
			return Promise.all(data.filter(function(item){
				return creater[item.type]||(typeof item == "string")
			}).map(function(item,i){
				if(typeof item == "string"){
					item = {type:"html",html:item}
				}
				return Promise.resolve(creater[item.type](option,item))
			})).then(function(data){
				return data.join("\n");
			});
		},
		getPrintHtml:function(name,option,data,css){
			var _this = this;
			return new Promise(function(resolve,reject){
				Promise.resolve(data).then(function(json){
					_this.getPrintHtmlByJson(name,option,json,css)
					.then(function(html){
						resolve(html);
					})
				})
			})
		},
		getLodop:function(option,name){
			var name = name || "";
			return LodopFuncs.loadLodop()
			.then(function(lodop){
				//var printOpt = controller.getPrintOption();
				lodop.PRINT_INIT(document.title);
				lodop.SET_PRINT_PAGESIZE(
					option.page.orient,
					option.page.width,
					option.page.height,
					option.page.name
				);
				return lodop;
			})
		},
		getPrint:function(name,option,data,css){
			css = css || "";
			var _this = this;
			
			var promiseGetHtml = null;
			if(R.find(
				function(item){
					return R[item[0]]($.isArray,data)==item[1]
				},
				[["findIndex",0],["findLastIndex",data.length-1]]
			)){
				promiseGetHtml = Promise.all(data.map(function(item){
					return _this.getPrintHtml(name,option,item,css)
				}))
			}else{
				promiseGetHtml = this.getPrintHtml(name,option,data,css)
			}
			return Promise.all([
				this.getLodop(option,name),
				this.getPrintCss(option),
				promiseGetHtml
			]).then(function(data){
				var printOpt = option;
				if(printOpt.fontname){
					data[0].SET_PRINT_STYLE("FontName",printOpt.fontname);
					data[0].SET_PRINT_STYLE("FontSize",50);
				}

				//var z = controller.option.print.zoom || 1;
				var zoomstyle = "";//z!=1?" style='zoom:"+ z +"'":"";
				
				var htmls = $.isArray(data[2])?data[2]:[data[2]]
				
				return {
					Lodop:data[0],
					PrintArgs:htmls.map(function(item){
						return [
							printOpt.top,
							printOpt.left,
							"RightMargin:"+printOpt.right,
							"BottomMargin:"+printOpt.bottom,
							[
								"<style>",data[1],css,";" +"</style>",
								"<body"+ zoomstyle +"><div'>",item,"</div></body>"
							].join("\n")
						]
					})
				}
			});
		},
		printPages:function(Lodop,pages,method){
			method = method || "ADD_PRINT_HTM";
			pages.forEach(function(item,i){
				i!==0&&Lodop.NewPage();
				Lodop[method].apply(Lodop,item);
			});
		},
		print:function(name,option,data,css){
			var _this = this;
			this.getPrint(name,option,data,css)
			.then(function(data){
				_this.printPages(data.Lodop,data.PrintArgs);
				data.Lodop.PRINT();
			})
		},
		printView:function(name,option,data,css){
			var _this = this;
			this.getPrint(name,option,data,css)
			.then(function(data){
				_this.printPages(data.Lodop,data.PrintArgs);
				data.Lodop.PREVIEW();
			})
		},
		printImgView:function(name,option,data,css){
			var _this = this;
			this.getPrint(name,option,data,css)
			.then(function(data){
				data.Lodop.ADD_PRINT_IMAGE.apply(data.Lodop,data.PrintArgs)
				LODOP.SET_PRINT_STYLEA(0,"Stretch",1);//(可变形)扩展缩放模式
				data.Lodop.PREVIEW();
			})
		},
		printGrid:function(name,option,data,css){
			var _this = this;
			this.getPrint(name,option,data,css)
			.then(function(data){
				_this.printPages(data.Lodop,data.PrintArgs,"ADD_PRINT_TABLE");
				//data.Lodop.ADD_PRINT_TABLE.apply(data.Lodop,data.PrintArgs)
				data.Lodop.PREVIEW();
			})
		},
		genReportPageJson:function(option){
			var _this = controller;
			// 如"<td>名称：<input /></td>"
			var getText = function(ele){
				var rev = "";
				var childrens = $(ele)[0].childNodes;
				for(var i=0;i<childrens.length;i++){
					var it = childrens[i];
					if(it.nodeType==3){
						rev+=it.textContent.replace(/^\s+|\s+$/g,"");;
					}
					if(it.nodeType==1 && it.tagName == "INPUT" && it.id && _this.controls[it.id]){
						var control = _this.controls[it.id]
						var text = control[control.gettext?"gettext":(control.text?"text":"value")]();
						rev+=(text === null?"":text);
					}
				}
				return rev;
			}
			var getHeader = function(report,name){
				name = name || "header";
				return report.find("[form='forms/report."+ name +"']").map(function(i,item){
					return {
						type:"row",
						items:[].concat.apply([],["left","center","right"].map(function(tditem){
							return $(item).find(".report-"+ name +"-"+tditem).map(function(di,d){
								return {
									text:getText(d),
									class:"report-"+ name +"-"+tditem
								}
							}).toArray();
						}))
					}
				}).toArray();
			}
			var getFooter = function(report){
				return getHeader(report,"footer");
			}
			var getGrid = function(childen){
				return {
					type : "grid",
					id : childen.attr("id")
				}
			}
			var getTable = function(childen){
				var attrs = [];
				var classs= ['datatable'];
				var styles = [];
				if(childen.attr("id")){
					attrs.push("id='"+ childen.attr("id") +"'");
				}
				if(childen.data("print-style")){
					styles.push(childen.data("print-style"));
				}
				if(childen.data("print-class")){
					classs.push(childen.data("print-class"));
				}
				attrs.push("class='"+ classs.join(" ") +"'");
				styles.length&&attrs.push("style='"+ styles.join(";") +"'");
				return {
					type:"html",
					html:"<table "+ attrs.join(" ") +">"+childen.children().map(function(i,item){
						var tagName = item.tagName;
						return "<"+tagName+">"+
							$(item).children().map(function(j,tr){
								return "<tr>"+
									$(tr).children().map(function(l,td){
										var tagName = td.tagName;
										var arrts = [];
										if(td.colSpan&&td.colSpan!=1){arrts.push("colspan="+td.colSpan);}
										if(td.rowSpan&&td.rowSpan!=1){arrts.push("rowspan="+td.rowSpan);}
										if(td.id&&td.rowSpan!=1){arrts.push("id="+td.id);}
										return cEle.call(option,{
											style:$(td).data("print-style"),
											class:$(td).data("print-class"),
											attrs:arrts,
											text:getText(td)
										},td.tagName);
									}).toArray().join("\n")
								+"</tr>"
							}).toArray().join("\n")
						+"</"+tagName+">"
					}).toArray().join("\n")+"</table>"
				}
			}
			var getChart = function(childen){
				return {
					type:"echart",
					id:childen.attr("id")
				}
			}
			
			return new Promise(function(resolve,reject){
				var rev = [];
				$.get(window.location.href,function(data){
					var report = $(data).find(".bill");
					if(report.length==0){
						report = $(data).filter("[form='forms/report']");
					}
					if(report.length==0){
						$.bootoast.danger("未获取到打印内容");
						return 
					}
					var title = _this.$container.find("[form='forms/report.title']").html();
					if(title){
						rev.push({type:"title",text:title});
					}
					rev = rev.concat(getHeader(report));
					
					rev = rev.concat(report.children("[form='forms/report.group']").map(function(i,ele){
						var childen = $(ele).children().eq(0);
						if(!childen.length){return null}
						if(childen.attr("control")=="controls/grid"){
							return getGrid(childen);
						}else if(childen[0].tagName=="TABLE"){
							return getTable(childen);
						}else if(childen.attr("control")=="controls/echart"){
							return getChart(childen);
						}
						return null;
					}).toArray().filter(function(item){return !!item}));
					rev = rev.concat(getFooter(report));
					resolve(rev);
				});
			})
		},
		getPrintCss:function(option,name){
			//var z = controller.option.print.zoom || 1;
			var zoomstyle = "";//z!=1?" body {width:100%;}":"";
			return Promise.all([
				new Promise(function(resolve,reject){
					if(printConfig.css){
						$.get(printConfig.css,function(css){
							resolve(css);
						})
					}else{
						resolve("");
					}
				})
				//,Promise.resolve(controller.getPrintCss())
			]).then(function(data){
				var printstyle = $("style[media='print']").map(function(){
					return $(this).html();
				}).toArray().join("\n");
				if(printstyle){
					data.push(printstyle);
				}
				return data.join("\n")+zoomstyle;
			})
		},
		printDebug:function(option,data){
			var box = $("#printDebugBox");
			if(!box.length){
				var html = [
					'<div class="modal fade" id="printDebugBox" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">',
					  '<div class="modal-dialog" role="document">',
					    '<div class="modal-content">',
					      '<div class="modal-header">',
					        '<button type="button" class="close" data-dismiss="modal" aria-label="Close">',
					        '<span aria-hidden="true">&times;</span></button>',
					        '<h4 class="modal-title" id="myModalLabel">打印预览</h4>',
					      '</div>',
					      '<div class="modal-body" style="text-align:center;">',
					        '<iframe id="ifra" style="width:105mm;height:148mm;border:1px solid #333;"></iframe>',
					      '</div>',
					      '<div class="modal-footer">',
					        '<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>',
					        '<button id="viewprint" type="button" class="btn btn-primary">打印</button>',
					      '</div>',
					    '</div>',
					  '</div>',
					'</div>'
					].join("\n")
				box = $(html).appendTo($("body"));
			}
			return Promise.all([
				util.getPrintCss(option),
				util.getPrintHtml("debug",option,data,null)
			]).then(function(data){
				box.modal({});
				var ifrdoc = $("#ifra")[0].contentWindow.document;
				ifrdoc.open(); //打开流
				ifrdoc.write("<html>"+[
					"<style>",data[0],"\n body{padding:0px;margin:0px;}","</style>",
					"<body style='zoom:0.5;padding:5%;'>",data[1],"</body>"
				].join("\n")+"</html>"); 
				ifrdoc.close(); //关闭流
			});
		}
	}
	

	var defaultOption = {
		fontname:"宋体",
		zoom:1,
		left:"4%",
		top:"4%",
		right:"4%",
		bottom:"4%",
		page:{
			//1---纵向打印，固定纸张； 
			//2---横向打印，固定纸张；  
			//3---纵向打印，宽度固定，高度按打印内容的高度自适应(见样例18)；
			//0---方向不定，由操作者自行选择或按打印机缺省设置。
			orient:0,
			width:0,//默认单位 0.1mm
			height:0,//默认单位 0.1mm
			name:""//默认A4 一般不用设置
		}
	}
	return {
		getPrintOption:function(option){
			return $.extend(true,{},defaultOption,option)
		},
		getPrintCss:function(){
			//可重写此方法
			return "";
		},
		printGrid:function(opt,css){
			opt.type = "grid";
			opt.printPageNo = true;
			var data = [opt]
			util.printGrid("页面1",this.getPrintOption(opt),data,css);
		},
		printDebugGrid:function(opt,css){
			opt.type = "grid";
			opt.printPageNo = true;
			var data = [opt]
			util.printDebug(this.getPrintOption(opt),data,css)
		},
		printReport:function(opt){
			util.printView("页面1",this.getPrintOption(opt),util.genReportPageJson(this),"");
		},
		printViewReport:function(opt){
			util.printView("页面1",this.getPrintOption(opt),util.genReportPageJson(this),"");
		},
		printDebugReport:function(){
			util.printDebug(this.getPrintOption(opt),util.genReportPageJson(this))
		},
		printJson:function(json,opt){
			util.printView("页面1",this.getPrintOption(opt),json,"")
		},
		printViewJson:function(json,opt){
			util.printView("页面1",this.getPrintOption(opt),json,"")
		},
		printDebugJson:function(json,opt){
			util.printDebug(this.getPrintOption(opt),json)
		}
	};
});
