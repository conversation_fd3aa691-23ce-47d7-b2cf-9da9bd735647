//F.controls.textbox
define(["jclass","./picker","jquery"],function(jclass,base,$){
	var defaultOption = {
		icon:"glyphicon-option-horizontal",
		selectonly:true,
		dialogOption:{
			title:"请选择",
			url:null,
			full:false,
			width:1080,
			height:600
		}
	}
	return jclass(base,{
		name:"control-selectorbase",
		option:null,
		idinput:null,
		textinput:null,
		btn:null,
		render:function(){
			this.base.apply(this,arguments);
			this.selectonly(this.option.selectonly);
		},
		selectonly:function(selectonly){
			if(selectonly !== false){
				this.textinput.prop("readonly",true)
			}else{
				this.textinput.prop("readonly",false)
			}
		},
		data:function(v){
			if(arguments.length=0){
				return this._data;
			}else{
				this._data = v;
			}
		},
		value:function(v){
			if(arguments.length==0){
				return this.idinput.val();
			}else{
				this.idinput.val(v);
			}
		},
		text:function(v){
			if(arguments.length==0){
				return this.textinput.val();
			}else{
				this.textinput.val(v);
				this.textinput.attr("title",v);
			}
		},
		disable:function(state){
			this.base.apply(this,arguments);
			this.btn.off("click");
			var _this = this;
			if(arguments.length==0 || state ){
				this.idinput.attr("disabled","disabled");
				this.textinput.attr("disabled","disabled");
				this.$container.addClass("inputDis");
			}else{
				this.idinput.removeAttr("disabled","disabled");
				this.textinput.removeAttr("disabled","disabled");
				this.$container.removeClass("inputDis");
				this.btn.on("click",function(){
					_this.trigger("click",this)
				});
			}
		},
		clear:function(){
			this.value("");
			this.text("");
			this.data(null);
		},
		createDefaultOption:function(container,option){
			return $.extend({},this.base.apply(this,arguments),defaultOption);
		},
		createContainer:function(container){
			var arr = this.option.icon.split("-");
			var icotype = arr.length>1?arr[0]:"";
			
			var rev = this.base.apply(this,arguments);
			this.pickerbox.addClass("colorpicker-component");
			this.addon.append($('<span class="'+ icotype +' '+ this.option.icon +'"></span>'))
			this.btn = this.addon;
			this.idinput = $('<input type="hidden"/>').appendTo(this.pickerbox);
			this.textinput = this.input;
			return rev;
		},
		required:function(b){
			this.option.required = b;
			if(this.option.required){
				this.textinput.attr("placeholder","必填");
			}else{
				this.textinput.attr("placeholder","");
			}
		},
		init:function(){
			var _this = this;
			return this.base.apply(this,arguments).then(function(){
				_this.btn.click(function(){
					_this.trigger("click",this);
				});
			});
		}
	});
});