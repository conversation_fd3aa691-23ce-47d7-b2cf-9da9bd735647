define(['css!./index.css'],function(){

    const Message = {
        props:{
            data:{type:Array,default(){return []}},
            idKey:{type:String,default:''},
            labelKey:{type:String,default:''},
            typeKey:{type:String,default:''},
            contentKey:{type:String,default:''}
        },
        computed:{
            // 生成key
            renderIdKey(){
                if(this.idKey){return this.idKey}
                if(this.labelKey){return this.labelKey}
                if(this.data.length){
                    if(Object.keys(this.data[0])[0]){
                        return Object.keys(this.data[0])[0];
                    }
                }
                return Math.round();
            },
            // 生成TypeKey
            renderTypeKey(){
                if(this.typeKey){return this.typeKey}
                if(this.data.length){
                    if(Object.keys(this.data[0])[1]){
                        return Object.keys(this.data[0])[1];
                    }
                }
                return '';
            },
            // 生成Labelkey
            renderLabelKey(){
                if(this.labelKey){return this.labelKey}
                if(this.data.length){
                    if(Object.keys(this.data[0])[2]){
                        return Object.keys(this.data[0])[2];
                    }
                }
                return '';
            },
            // 生成Labelkey
            renderContentKey(){
                if(this.contentKey){return this.contentKey}
                if(this.data.length){
                    if(Object.keys(this.data[0])[3]){
                        return Object.keys(this.data[0])[3];
                    }
                }
                return '';
            }
        },
        data(){
            return {}
        },
        template:`
        <div class="message-wrapper">
            <div class="message-item" :class="renderTypeKey?'message-'+item[renderTypeKey]:''"
                v-for="item in data">
                <div v-if="renderLabelKey" class="message-item__title">{{item[renderLabelKey]}}</div>
                <div v-if="renderContentKey" class="message-item__content">{{item[renderContentKey]}}</div>
            </div>
        </div>
        `
    }

    return Message;
})