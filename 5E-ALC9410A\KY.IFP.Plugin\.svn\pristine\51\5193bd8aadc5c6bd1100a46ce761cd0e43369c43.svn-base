﻿using COM.IFP.SqlSugarN;
using SqlSugar;
using System;
namespace ORM.IFP.www.DbModel.UM
{

    /// <summary>
	/// 
	/// </summary>
	[SugarTable("IFP_UM_USER_ROLE")]
    public partial class IFP_UM_USER_ROLE
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(36)")]
        public Field<string> Gid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "USIGUID", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> UsiGuid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "ROLEGUID", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> RoleGuid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "CREATOR", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(128)")]
        public Field<string> Creator { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "CREATETIME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "REMARK", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(3000)")]
        public Field<string> Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "DELT", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> Delt { get; set; }

    }


    /* 以下为对应的Json对象
	{
		GID: null,
		USIGUID: null,
		ROLEGUID: null,
		CREATOR: null,
		CREATETIME: null,
		REMARK: null,
		DELT: null
	}
	*/

}
