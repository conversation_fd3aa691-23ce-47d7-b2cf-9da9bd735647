# 弹出页

## 约定

页面组件应该实现这两个事件

* `success` ：数据有更新，通知父组件更新，一般写在提交数据成功后
* `cancel` : 组件请求关闭/退出页面

页面组件应该订阅 `open` 或 `opened` ，来清空之前填写的数据

关于 `:visible.sync`

ifp-detail 内部通过 `$emit('update:visible',false)` 来更新 `visible`

## 示例

### 父页面

html

```html
<div id="app">
    <ifp-dialog class="subpage" title="标题"
        :visible.sync="dialog.visable"
        width="800px">
        
        <!--
            ifp-kd-detail 说明
            高度在这里设置：style="height:200px"
            建议控件实现事件：cancel/success
        -->
        <ifp-kd-detail 
            @cancel=""
            @sucess="editor.dialog=false;"
        ></ifp-kd-detail>
    </ifp-dialog>
</div>
```

js

```js

define(["platform/vue"],function(pVue){
    return {
        el:"#app",
        components:pVue.createAsyncComponents({
            IfpKdDetail: "./detail.js"
        }),
        data(){
            return {
                dialog:{
                    visible:false
                }
            }
        }
    });
});
```

### 子页面

```html
<!--以下两种方式都可以-->
<button @click="$emit('close')">退出</button>
<button @click="$emit('cancel')">取消</button>
<button @click="submit">提交</button>
```

```js
{
    el:'#app',

    props:{
        // 传入的表单参数
        formData:{default:(){return {}}},
    },
    data(){
        return {
            // 页面使用的表单参数示例
            myFormData:{
                userid:'',
                username:''
            }
        }
    },
    watch:{
        //formData
    },

    mounted(){
        this.$on('open',()=>{

        })
    },
    methods:{
        submit(){
            // 提交
            api.submitXXXX()
            .then(()=>{
                // 成功后，通知父组件更新数据
                this.$emit('success')
            })
        },
        resetFormData(){
            for(let prop in this.)
        },
        updateFormData(){

        }
    }
}
```

## 弹窗问题

### 1. 嵌套弹窗异常

添加 `append-to-body` 属性即可，如下

```html
    <el-dialog append-to-body>
```
