//F.controls.textbox
define(["./base.input","jquery","jclass"],function(base,$,jclass){
	return jclass(base,{
		name:"textbox.encrypt",
		_value:"",
		value:function(v){
			if(arguments.length){
				this._value = v;
			}else{
				return this._value;
			}
		},
		clear:function(){
			this._value = "";
		},
		createContainer:function(container,option){
			var $container = $(container);
			var newContainer = $("<input type='text' disabled='disabled' id='"+ this.id +"' value='***' />");
			$container.replaceWith(newContainer);
			newContainer.addClass("form-control");
			return newContainer;
		}
	});
});