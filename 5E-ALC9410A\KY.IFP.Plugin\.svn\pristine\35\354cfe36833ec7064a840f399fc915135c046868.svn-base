/**
* @fileOverview 输入控件基类
* <AUTHOR>
* @version 0.1
*/

"use strict";
define([
	"jclass","renderer","log","./base","enum","commons/filter","configs/config.service"
],function(jclass,renderer,log,base,coreEnum,commonFilter,serviceurl){
	var ywlxfield = serviceurl._http_getYwlxCombox_ywlxfieldname;

	var defaultOption = {
		isFormControl:true, //是否form控件，用于controller.formData 取值赋值判断
		required:false,
		showName:"",
		width:200,
		style:null,// 样式
		_containerStyle:null,//这个最初的容器样式，占位符；
		grid:null, // 在grid中渲染时指向grid控件
		gridArg:null // 在grid中渲染时，由grid渲染时附加一些参数
	}
	
	return jclass(base,{
		name:"control-inputbase",
		createDefaultOption:function(container,option){
			return $.extend(true,this.base.apply(this,arguments),defaultOption);
		},
		value:function(data){
		},
		
		checkInput:function(){
			var rev = !(this.option.required && 
					(this.value()==null || this.value() == undefined || 
							(this.value()!=null && this.value() != undefined && this.value().toString().trim().length == 0)));
			if(!rev){
				this.setInputState(1);
			}else{
				this.setInputState(0);
			}
			
			return rev;
		},
		
		setInputState:function(state){
			this.$container.removeClass("state_required");
			if(state==1){
				this.$container.addClass("state_required");
			}
		},
		
		// 转换ID
		converterid:function(ids){
			var url = "";
			var postData = {};
			if(this.option[ywlxfield]){
				url = serviceurl.ywlx_idtoname;
				postData[serviceurl._http_getYwlxCombox_idsfieldname]=this.option["ywlxfield"];
			} else if(this.option.idtotext &&  this.option.idtotext.url){
				url = this.option.idtotext.url;
			} else if (this.option.url){
				url = this.option.url;
			}
			
			if(this.option.idtotext && this.option.idtotext.postData){
				postData = this.option.idtotext.postData;
			}
			
			if(this.option.idtotext && this.option.idtotext.idsfield){
				postData[this.option.idtotext.idsfield]=ids;
			}else{
				postData[serviceurl._http_getYwlxCombox_idsfieldname]=ids;
			}
			
			if(!url){
				return null;
			}
			return coreEnum.getList(url,postData,false);
		},
		
		getDataFilter:function(name){
			name = name || "datafilter";
			var df =  this.option[name];
			if(typeof df == "string"){
				df = this.controller&&this.controller[df]
					||commonFilter[df]
					|| null;
			}
			return df;
		},
		
		createHtmlOption:function(container,option){
			var rev = this.base.apply(this,arguments) || {};
			rev._containerStyle = $(container).attr("style");
			return rev;
		},

		//根据参数获取data数据,返回promise对象
		getOptionData:function(nocache,opt){
			opt = opt || this.option;
			var _this = this;
			return new Promise(function(resolve,reject){
				if(opt.service&&opt.methodname){
					require(opt.service)[opt.methodname](opt.postData)
					.then(function(data){
						resolve(data);
					})["catch"](function(){
						_this.log.error("接口访问失败:"+opt.service+" -> "+opt.methodname);
						reject();
					});
				}else if(_this.option.methodname&& _this.controller[opt.methodname]){
					Promise.resolve(_this.controller[opt.methodname].call(_this.controller,_this,opt.postData)).then(resolve);
				}else if(opt[ywlxfield]){
					var postData = opt.postData||{};

					if(typeof postData[serviceurl._http_getYwlxCombox_usedfieldname] == "undefined"){
						postData[serviceurl._http_getYwlxCombox_usedfieldname] = serviceurl._http_getYwlxCombox_usedfieldenum[1];
					}
					resolve(coreEnum.getList(opt[ywlxfield],postData,nocache));
				} else if(opt.url){
					resolve(coreEnum.getList(opt.url,opt.postData||{},nocache));
				//这里只能使用同步方法；
				} else if(opt.data){
					resolve(opt.data);
				} else{
					resolve([]);
				}
			}).then(function(data){
				var df = _this.getDataFilter();
				var dfall = _this.getDataFilter("datafilterall");
				if(df && data && data.filter){
					data = data.filter(df);
				}
				if(dfall){
					return Promise.resolve(dfall.call(_this.controller,data));
				}
				return data;
			});
		},
		reload:function(opt,nocache){
			opt = opt || {};
			var _this = this;
			$.extend(true,this.option,opt);
			if(opt.postData){
				this.option.postData = opt.postData;
			}
			return this.getOptionData(nocache).then(function(data){
				_this.option.data  = data;
				_this._data = _this.option.data;
				_this.data(_this.option.data);
				return _this;
			})
		},
		createModelOption:function(container,option){
			var rev = this.base.apply(this,arguments);
			var model = this.getInputerModel();
			var setItem = function(revname,modname){
				if(typeof model[modname] ==="undefined" || model[modname]===null){return;}
				if(modname == 'isDisabled'){
					//禁用
					rev[revname] = (model[modname]==1);
					return;
				}
				rev[revname]=model[modname];
			}
			setItem("required","required");
			setItem("value","defValue");
			setItem("disabled","isDisabled");
			return rev;
		},
		beforeRender:function(){
			var _this = this;
			return Promise.all([
				this.getOptionData()
				.then(function(data){
					_this.option.data = data;
				})
			]);
		},
		render:function(){
			this.base.apply(this,arguments);
			if(this.option._containerStyle){
				this.$container.css(this.option._containerStyle)
			}
			if(this.option.style){
				this.$container.css(this.option._containerStyle)
			}
			this.$container.addClass(this.name);
		},
		afterRender:function(){
			if(this.option.value&&this.value){
				this.value(this.option.value);
			}
			if(this.option.data){
				this._data = this.option.data;
			}
			if(this.option.disabled){
				this.disable();
			}
			if(this.option.required){
				this.required(true);
			}
			if(this.option.width){
				this.$container.css("width",this.option.width);
			}
		},
		required:function(b){
			
		},
	});
});
