﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace API.IFP.SignalR
{
    /// <summary>
    /// 从客户端到服务器的通信接口
    /// </summary>
    public interface IClient2Server
    {
        /// <summary>
        /// 处理客户端发送的消息
        /// </summary>
        /// <param name="receiveMsg">接收到的消息</param>
        /// <param name="client">客户端信息</param>
        /// <returns>响应消息</returns>
        public SignalRMessage CommonRun(SignalRMessage receiveMsg, SignalRClient client);
    }


}
