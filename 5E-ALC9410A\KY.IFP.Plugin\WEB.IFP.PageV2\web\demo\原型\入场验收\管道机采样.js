define(["iofp/lib/createApi"],function({createApis}){
    let apis = createApis({
        getRunMessages:{
            // 启用url
            // url:'/xxx/xxx/xx'
            link:"data1",
            mock:[
                {id:'001',time:'2021-01-01 01:01:01 1254',content:"#1 采样机已启动"},
                {id:'002',time:'2021-01-01 01:01:01 1254',content:"#1 采样机已启动"}
            ]
        },
        getDat1:{
            mock:[
                {id:'001',name:'采样机1'},
                {id:'002',name:'采样机1'}
            ]
        },
        getDat2:{
            mock:[
                {id:'001',name:'采样机1'},
                {id:'002',name:'采样机1'}
            ]
        }
    });
    
    return {
        el:"#app",
        $api:{
            getRunMessages:{
                // 启用url
                // url:'/xxx/xxx/xx'
                link:"data1",
                mock:[
                    {id:'001',time:'2021-01-01 01:01:01 1254',content:"#1 采样机已启动"},
                    {id:'002',time:'2021-01-01 01:01:01 1254',content:"#1 采样机已启动"}
                ]
            },
            getDat1:{
                mock:[
                    {id:'001',name:'采样机1'},
                    {id:'002',name:'采样机1'}
                ]
            },
            getDat2:{
                mock:[
                    {id:'001',name:'采样机1'},
                    {id:'002',name:'采样机1'}
                ]
            }
        },
        data(){
            return {
                runMessages:[],
                data1:[],
                data2:[]
            }
        },
        created(){
            this.$api.$update()
            this.$api.$update('data1')
        }
    }
})